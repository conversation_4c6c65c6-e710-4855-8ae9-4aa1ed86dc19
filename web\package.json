{"name": "@likes-love/web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "android": "node scripts/start-android.js"}, "dependencies": {"@ducanh2912/next-pwa": "^10.2.9", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.3.2", "@metamask/detect-provider": "^2.0.0", "@mui/icons-material": "^5.15.21", "@mui/material": "^5.17.1", "@stripe/stripe-js": "7.3.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@types/react-webcam": "^1.1.0", "antd": "5.26.0", "axios": "^1.9.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "12.17.0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "mapbox-gl": "3.12.0", "next": "^15.3.3", "next-auth": "4.24.11", "next-i18next": "15.4.2", "next-seo": "6.8.0", "node-fetch": "3.3.2", "postcss": "8.5.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "14.3.8", "react-error-boundary": "6.0.0", "react-helmet-async": "2.0.5", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-image-crop": "11.0.10", "react-intersection-observer": "9.16.0", "react-map-gl": "8.0.4", "react-swipeable": "^7.0.2", "react-toastify": "11.0.5", "react-use": "17.6.0", "react-virtualized": "9.22.6", "react-webcam": "^7.2.0", "react-window": "1.8.11", "semver": "7.7.2", "sharp": "0.34.2", "socket.io-client": "4.8.1", "swr": "2.3.3", "undici": "7.10.0", "workbox-webpack-plugin": "^7.3.0", "yup": "^1.4.0", "zod": "^3.25.62"}, "devDependencies": {"@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/eslint": "^9.6.1", "@types/express": "^4.17.21", "@types/jest": "29.5.14", "@types/leaflet": "^1.9.12", "@types/mapbox-gl": "^3.4.1", "@types/node": "^22.15.21", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "@types/react-redux": "^7.1.25", "@types/react-test-renderer": "^18.0.7", "@types/semver": "^7.5.6", "@types/superagent": "^8.1.9", "@types/supertest": "^6.0.2", "@types/trusted-types": "^2.0.7", "@types/use-sync-external-store": "^1.2.0", "@types/validator": "^13.12.2", "autoprefixer": "10.4.21", "babel-jest": "29.7.0", "eslint": "^8.57.1", "eslint-config-next": "^15.1.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-themes": "^0.4.4", "react-icons": "^5.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.3.3", "vitest": "^3.2.2", "@vitest/ui": "^3.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}