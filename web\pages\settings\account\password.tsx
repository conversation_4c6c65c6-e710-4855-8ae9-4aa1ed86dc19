import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  TextField,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  InputAdornment,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  ArrowBack,
  Lock as LockIcon,
  Visibility,
  VisibilityOff,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Save as SaveIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountIcon,
  Check as CheckIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для формы
interface PasswordChangeForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Схема валидации
const passwordChangeSchema = yup.object({
  currentPassword: yup.string()
    .required('Текущий пароль обязателен'),
  newPassword: yup.string()
    .required('Новый пароль обязателен')
    .min(8, 'Минимум 8 символов')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Пароль должен содержать строчные и заглавные буквы, цифры')
    .test('different', 'Новый пароль должен отличаться от текущего', function(value) {
      return value !== this.parent.currentPassword;
    }),
  confirmPassword: yup.string()
    .required('Подтверждение пароля обязательно')
    .oneOf([yup.ref('newPassword')], 'Пароли не совпадают')
});

const ChangePasswordPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    reset
  } = useForm<PasswordChangeForm>({
    resolver: yupResolver(passwordChangeSchema),
    mode: 'onChange',
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  const watchedNewPassword = watch('newPassword');

  // Функция для проверки силы пароля
  const getPasswordStrength = (password: string) => {
    let strength = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    strength = Object.values(checks).filter(Boolean).length;
    
    return {
      score: strength,
      checks,
      label: strength < 2 ? 'Слабый' : strength < 4 ? 'Средний' : 'Сильный',
      color: strength < 2 ? 'error' : strength < 4 ? 'warning' : 'success'
    };
  };

  const passwordStrength = getPasswordStrength(watchedNewPassword || '');

  const handlePasswordChange = async (data: PasswordChangeForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для смены пароля
      // await changePassword(data.currentPassword, data.newPassword);

      setSuccess('Пароль успешно изменен!');
      reset();
    } catch (err: any) {
      setError(err.message || 'Ошибка при смене пароля');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/settings/account');
  };

  if (!user) {
    return (
      <Layout title="Смена пароля">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Смена пароля - Likes & Love</title>
        <meta name="description" content="Изменение пароля в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Смена пароля">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/account" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/account'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <AccountIcon fontSize="small" />
                Аккаунт
              </Link>
              <Typography color="text.primary">Смена пароля</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <LockIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Смена пароля
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Обновите пароль для повышения безопасности аккаунта
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Рекомендации по безопасности */}
            <Alert severity="info" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Рекомендации:</strong> Используйте уникальный пароль, который вы не используете на других сайтах. 
                Пароль должен содержать минимум 8 символов, включая заглавные и строчные буквы, цифры.
              </Typography>
            </Alert>

            <Card>
              <CardContent>
                <form onSubmit={handleSubmit(handlePasswordChange)}>
                  {/* Текущий пароль */}
                  <Controller
                    name="currentPassword"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Текущий пароль"
                        type={showCurrentPassword ? 'text' : 'password'}
                        fullWidth
                        error={!!errors.currentPassword}
                        helperText={errors.currentPassword?.message}
                        sx={{ mb: 3 }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                edge="end"
                              >
                                {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                    )}
                  />

                  {/* Новый пароль */}
                  <Controller
                    name="newPassword"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Новый пароль"
                        type={showNewPassword ? 'text' : 'password'}
                        fullWidth
                        error={!!errors.newPassword}
                        helperText={errors.newPassword?.message}
                        sx={{ mb: 2 }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowNewPassword(!showNewPassword)}
                                edge="end"
                              >
                                {showNewPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                    )}
                  />

                  {/* Индикатор силы пароля */}
                  {watchedNewPassword && (
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="body2">
                          Сила пароля:
                        </Typography>
                        <Typography 
                          variant="body2" 
                          color={`${passwordStrength.color}.main`}
                          fontWeight="bold"
                        >
                          {passwordStrength.label}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={(passwordStrength.score / 5) * 100}
                        color={passwordStrength.color as any}
                        sx={{ mb: 2 }}
                      />
                      <List dense>
                        <ListItem>
                          <ListItemIcon>
                            {passwordStrength.checks.length ? <CheckIcon color="success" /> : <CloseIcon color="error" />}
                          </ListItemIcon>
                          <ListItemText primary="Минимум 8 символов" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            {passwordStrength.checks.lowercase ? <CheckIcon color="success" /> : <CloseIcon color="error" />}
                          </ListItemIcon>
                          <ListItemText primary="Строчные буквы (a-z)" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            {passwordStrength.checks.uppercase ? <CheckIcon color="success" /> : <CloseIcon color="error" />}
                          </ListItemIcon>
                          <ListItemText primary="Заглавные буквы (A-Z)" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            {passwordStrength.checks.numbers ? <CheckIcon color="success" /> : <CloseIcon color="error" />}
                          </ListItemIcon>
                          <ListItemText primary="Цифры (0-9)" />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            {passwordStrength.checks.special ? <CheckIcon color="success" /> : <CloseIcon color="error" />}
                          </ListItemIcon>
                          <ListItemText primary="Специальные символы (!@#$%^&*)" />
                        </ListItem>
                      </List>
                    </Box>
                  )}

                  {/* Подтверждение пароля */}
                  <Controller
                    name="confirmPassword"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Подтвердите новый пароль"
                        type={showConfirmPassword ? 'text' : 'password'}
                        fullWidth
                        error={!!errors.confirmPassword}
                        helperText={errors.confirmPassword?.message}
                        sx={{ mb: 4 }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                edge="end"
                              >
                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                    )}
                  />

                  {/* Кнопки */}
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button onClick={handleBack} disabled={loading}>
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading || !isValid}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Сохранение...' : 'Сохранить пароль'}
                    </Button>
                  </Box>
                </form>
              </CardContent>
            </Card>

            {/* Дополнительная информация */}
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Советы по безопасности
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Используйте уникальный пароль"
                      secondary="Не используйте этот пароль на других сайтах"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Регулярно меняйте пароль"
                      secondary="Рекомендуется менять пароль каждые 3-6 месяцев"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText 
                      primary="Включите двухфакторную аутентификацию"
                      secondary="Дополнительная защита вашего аккаунта"
                    />
                  </ListItem>
                </List>
                <Button
                  onClick={() => router.push('/settings/security')}
                  variant="outlined"
                  sx={{ mt: 2 }}
                >
                  Настройки безопасности
                </Button>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ChangePasswordPage;
