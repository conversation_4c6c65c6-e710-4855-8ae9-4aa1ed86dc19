import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  Button,
  Grid,
  Chip,
  IconButton,
  Stack,
  Paper,
  Alert,
  Skeleton
} from '@mui/material';
import {
  FaHeart,
  FaMapMarkerAlt,
  FaBirthdayCake,
  FaGraduationCap,
  FaBriefcase,
  FaArrowLeft,
  FaFlag,
  FaShare,
  FaComments
} from 'react-icons/fa';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import PhotoViewer from '../../components/PhotoViewer/PhotoViewer';
import { ProfileActionButtons } from '../../components/Profile/ProfileActionButtons';

interface UserProfile {
  id: string;
  userId: string;
  displayName: string;
  age: number;
  bio?: string;
  photos: Array<{
    id: string;
    url: string;
    order: number;
    status: string;
  }>;
  interests: Array<{
    id: string;
    name: string;
    category: string;
  }>;
  city?: string;
  isOnline: boolean;
  lastSeen: Date;
  subscription: {
    id: string;
    status: 'active' | 'inactive';
    planId: string;
  };
  stats: {
    profileViews: number;
    likesReceived: number;
    matchesCount: number;
  };
}

const UserProfilePage: NextPage = () => {
  const router = useRouter();
  const { userId } = router.query;
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [photoModalOpen, setPhotoModalOpen] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  useEffect(() => {
    if (userId) {
      loadUserProfile(userId as string);
    }
  }, [userId]);

  const loadUserProfile = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock данные для демонстрации
      const mockProfile: UserProfile = {
        id: `profile-${id}`,
        userId: id,
        displayName: id === 'user1' ? 'Анна Петрова' : id === 'user2' ? 'Мария Сидорова' : 'Елена Козлова',
        age: id === 'user1' ? 25 : id === 'user2' ? 28 : 26,
        bio: id === 'user1' 
          ? 'Люблю путешествия и фотографию. Ищу серьезные отношения с интересным человеком.'
          : id === 'user2'
          ? 'Маркетолог, обожаю активный отдых и новые знакомства.'
          : 'Дизайнер, люблю искусство и кофе. Ценю честность и чувство юмора.',
        photos: [
          {
            id: 'photo1',
            url: '/images/avatars/anna.jpg',
            order: 1,
            status: 'APPROVED'
          },
          {
            id: 'photo2',
            url: '/images/avatars/maria.jpg',
            order: 2,
            status: 'APPROVED'
          }
        ],
        interests: [
          { id: 'int1', name: 'Путешествия', category: 'Хобби' },
          { id: 'int2', name: 'Фотография', category: 'Творчество' },
          { id: 'int3', name: 'Книги', category: 'Хобби' },
          { id: 'int4', name: 'Йога', category: 'Спорт' }
        ],
        city: 'Москва',
        isOnline: Math.random() > 0.5,
        lastSeen: new Date(),
        subscription: {
          id: 'sub1',
          status: Math.random() > 0.5 ? 'active' : 'inactive',
          planId: 'basic'
        },
        stats: {
          profileViews: Math.floor(Math.random() * 500) + 100,
          likesReceived: Math.floor(Math.random() * 200) + 50,
          matchesCount: Math.floor(Math.random() * 50) + 10
        }
      };

      // Симуляция загрузки
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile(mockProfile);
    } catch (err) {
      setError('Ошибка загрузки профиля');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    try {
      // TODO: Интеграция с реальным API лайков
      console.log('Like user:', userId);

      // Симуляция API запроса
      const response = await fetch('/api/dating/like', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetUserId: userId, likeType: 'REGULAR' })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.isMatch) {
          alert(`🎉 Взаимная симпатия с ${profile?.displayName}!`);
          // Перенаправляем на чат при матче
          router.push(`/chats/${userId}`);
        } else {
          alert('Лайк отправлен! 💕');
        }
      }
    } catch (error) {
      console.error('Error liking user:', error);
      alert('Ошибка при отправке лайка');
    }
  };



  const handlePass = () => {
    // Логика пропуска
    console.log('Pass user:', userId);
    router.back();
  };

  const handleReport = () => {
    // Логика жалобы
    console.log('Report user:', userId);
    alert('Жалоба отправлена. Спасибо за обратную связь!');
  };

  const handleShare = async () => {
    try {
      if (navigator.share && profile) {
        await navigator.share({
          title: `Профиль ${profile.displayName}`,
          text: `Посмотри профиль ${profile.displayName} в Likes Love`,
          url: window.location.href
        });
      } else {
        // Fallback - копируем ссылку в буфер обмена
        await navigator.clipboard.writeText(window.location.href);
        alert('Ссылка скопирована в буфер обмена!');
      }
    } catch (error) {
      console.error('Error sharing:', error);
      alert('Ошибка при попытке поделиться');
    }
  };

  const handlePhotoClick = (index: number) => {
    setSelectedPhotoIndex(index);
    setPhotoModalOpen(true);
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка профиля - Likes Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Layout>
          <Container maxWidth="lg" sx={{ py: 4 }}>
            <Skeleton variant="rectangular" height={400} sx={{ mb: 3 }} />
            <Grid container spacing={4}>
              <Grid item xs={12} md={4}>
                <Skeleton variant="rectangular" height={300} />
              </Grid>
              <Grid item xs={12} md={8}>
                <Skeleton variant="rectangular" height={200} sx={{ mb: 2 }} />
                <Skeleton variant="rectangular" height={150} />
              </Grid>
            </Grid>
          </Container>
        </Layout>
      </>
    );
  }

  if (error || !profile) {
    return (
      <>
        <Head>
          <title>Ошибка - Likes Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Layout>
          <Container maxWidth="lg" sx={{ py: 4 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              {error || 'Профиль не найден'}
            </Alert>
            <Button onClick={() => router.back()} startIcon={<FaArrowLeft />}>
              Назад
            </Button>
          </Container>
        </Layout>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{profile.displayName} - Likes Love</title>
        <meta name="description" content={`Профиль ${profile.displayName} в приложении Likes Love`} />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          {/* Навигация */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Button onClick={() => router.back()} startIcon={<FaArrowLeft />}>
              Назад
            </Button>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton onClick={handleShare}>
                <FaShare />
              </IconButton>
              <IconButton onClick={handleReport} color="error">
                <FaFlag />
              </IconButton>
            </Box>
          </Box>

          <Grid container spacing={4}>
            {/* Основная информация */}
            <Grid item xs={12} md={4}>
              <Card sx={{ position: 'sticky', top: 100 }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Box sx={{ position: 'relative', display: 'inline-block', mb: 2 }}>
                    <Avatar
                      src={profile.photos[0]?.url}
                      sx={{ width: 150, height: 150, mx: 'auto' }}
                    />
                    {profile.isOnline && (
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 10,
                          right: 10,
                          width: 20,
                          height: 20,
                          bgcolor: '#4CAF50',
                          borderRadius: '50%',
                          border: '3px solid white'
                        }}
                      />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
                    <Typography variant="h5" fontWeight="bold">
                      {profile.displayName}, {profile.age}
                    </Typography>
                  </Box>

                  {profile.subscription.status === 'active' && (
                    <Chip
                      label="Premium"
                      sx={{
                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                        color: 'white',
                        fontWeight: 'bold',
                        mb: 2
                      }}
                    />
                  )}

                  <Stack spacing={1} sx={{ textAlign: 'left', mt: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FaBirthdayCake color="#666" />
                      <Typography variant="body2">{profile.age} лет</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FaMapMarkerAlt color="#666" />
                      <Typography variant="body2">{profile.city}</Typography>
                    </Box>
                  </Stack>

                  {/* Кнопки действий */}
                  <Box sx={{ mt: 3 }}>
                    <ProfileActionButtons
                      userId={userId as string}
                      userName={profile.displayName}
                      isOwnProfile={false}
                      hasActiveSubscription={profile.subscription.status === 'active'}
                      onLike={handleLike}
                      onPass={handlePass}
                      onReport={handleReport}
                      onShare={handleShare}
                      variant="full"
                      showPremiumFeatures={true}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Детальная информация */}
            <Grid item xs={12} md={8}>
              <Stack spacing={3}>
                {/* О себе */}
                {profile.bio && (
                  <Paper sx={{ p: 3 }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      О себе
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {profile.bio}
                    </Typography>
                  </Paper>
                )}

                {/* Интересы */}
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Интересы
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {profile.interests.map((interest) => (
                      <Chip
                        key={interest.id}
                        label={interest.name}
                        variant="outlined"
                        color="primary"
                      />
                    ))}
                  </Box>
                </Paper>

                {/* Фотографии */}
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Фотографии
                  </Typography>
                  <Grid container spacing={2}>
                    {profile.photos.map((photo, index) => (
                      <Grid item xs={6} sm={4} md={3} key={photo.id}>
                        <Box
                          component="img"
                          src={photo.url}
                          alt={`Фото ${index + 1}`}
                          onClick={() => handlePhotoClick(index)}
                          sx={{
                            width: '100%',
                            height: 150,
                            objectFit: 'cover',
                            borderRadius: 2,
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              opacity: 0.8,
                              transform: 'scale(1.05)'
                            }
                          }}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Paper>
              </Stack>
            </Grid>
          </Grid>
        </Container>

        {/* Просмотр фотографий */}
        <PhotoViewer
          open={photoModalOpen}
          onClose={() => setPhotoModalOpen(false)}
          photos={profile.photos.map(p => p.url)}
          initialIndex={selectedPhotoIndex}
          userName={profile.displayName}
        />
      </Layout>
    </>
  );
};

export default UserProfilePage;
