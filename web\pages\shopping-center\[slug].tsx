/**
 * Отдельная страница торгового центра
 * Enterprise-grade страница для партнерских отношений и монетизации
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardMedia,
  Chip,
  IconButton,
  Avatar,
  Badge,
  Button,
  Paper,
  Divider,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Rating,
  Alert,
  Breadcrumbs,
  Link,
  Tooltip,
  Fab,
  CardContent,
  CardActions
} from '@mui/material';
import {
  LocationOn,
  People,
  Star,
  Directions,
  Phone,
  Schedule,
  Verified,
  PhotoLibrary,
  Info,
  Store,
  Event,
  Reviews,
  AccessTime,
  LocalParking,
  Wifi,
  Restaurant,
  LocalMovies,
  FitnessCenter,
  ShoppingBag,
  NavigateNext,
  Home,
  Favorite,
  Share,
  Map,
  Email,
  Language,
  Security,
  Accessible,
  LocalOffer,
  Business,
  TrendingUp
} from '@mui/icons-material';
import { FaMapMarkerAlt, FaClock, FaUsers, FaHeart, FaRoute, FaShoppingCart } from 'react-icons/fa';
import Layout from '../../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../../components/UI';
import { ShoppingCenter } from '../../src/types/shopping-center.types';
import { useShoppingCenter } from '../../src/hooks/useShoppingCenters';

interface ShoppingCenterPageProps {
  center: ShoppingCenter | null;
  slug: string;
}

const ShoppingCenterPage: React.FC<ShoppingCenterPageProps> = ({ center: initialCenter, slug }) => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);

  const {
    center,
    loading,
    error,
    reviews,
    events,
    stores,
    loadAllData
  } = useShoppingCenter(initialCenter?.id);

  const currentCenter = center || initialCenter;

  useEffect(() => {
    if (currentCenter?.id) {
      loadAllData(currentCenter.id);
    }
  }, [currentCenter?.id, loadAllData]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  const handleShare = async () => {
    if (navigator.share && currentCenter) {
      try {
        await navigator.share({
          title: currentCenter.name,
          text: currentCenter.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else if (currentCenter) {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const formatWorkingHours = (workingHours?: any) => {
    if (!workingHours) return 'Время работы не указано';
    
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const todayIndex = new Date().getDay() === 0 ? 6 : new Date().getDay() - 1;
    const todayKey = days[todayIndex] as keyof typeof workingHours;
    
    return workingHours[todayKey] || 'Закрыто';
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography>Загрузка...</Typography>
        </Container>
      </Layout>
    );
  }

  if (error || !currentCenter) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="error">
            {error || 'Торговый центр не найден'}
          </Alert>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>{currentCenter.seoTitle || `${currentCenter.name} - Торговый центр | Likes & Love`}</title>
        <meta name="description" content={currentCenter.seoDescription || currentCenter.description} />
        <meta name="keywords" content={currentCenter.seoKeywords?.join(', ') || ''} />
        <meta property="og:title" content={currentCenter.name} />
        <meta property="og:description" content={currentCenter.description} />
        <meta property="og:image" content={currentCenter.images[0]?.url} />
        <meta property="og:type" content="place" />
        <link rel="canonical" href={`${process.env.NEXT_PUBLIC_SITE_URL}/shopping-center/${currentCenter.slug}`} />
      </Head>

      {/* Хлебные крошки */}
      <Container maxWidth="lg" sx={{ py: 2 }}>
        <Breadcrumbs separator={<NavigateNext fontSize="small" />}>
          <Link color="inherit" href="/">
            <Home sx={{ mr: 0.5 }} fontSize="inherit" />
            Главная
          </Link>
          <Link color="inherit" href="/shopping-centers">
            Торговые центры
          </Link>
          <Typography color="text.primary">{currentCenter.name}</Typography>
        </Breadcrumbs>
      </Container>

      {/* Главное изображение и заголовок */}
      <Box sx={{ position: 'relative', height: 400, mb: 4 }}>
        <CardMedia
          component="img"
          height="100%"
          image={currentCenter.images[0]?.url || 'https://picsum.photos/1200/400?random=101'}
          alt={currentCenter.name}
          sx={{ objectFit: 'cover' }}
        />
        
        {/* Градиент и информация */}
        <Box sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
          p: 4,
          color: 'white'
        }}>
          <Container maxWidth="lg">
            <Grid container spacing={3} alignItems="flex-end">
              <Grid item xs={12} md={8}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h3" fontWeight="bold" sx={{ mr: 2 }}>
                    {currentCenter.name}
                  </Typography>
                  {currentCenter.isVerified && (
                    <Verified sx={{ color: 'success.main', fontSize: 32 }} />
                  )}
                  {currentCenter.isFeatured && (
                    <Star sx={{ color: 'warning.main', fontSize: 32, ml: 1 }} />
                  )}
                </Box>
                
                <Typography variant="h6" sx={{ mb: 2, opacity: 0.9 }}>
                  {currentCenter.location?.address || currentCenter.address}
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Rating value={currentCenter.rating} precision={0.1} size="small" readOnly />
                    <Typography variant="body1" sx={{ ml: 1 }}>
                      {currentCenter.rating} ({currentCenter.reviewsCount} отзывов)
                    </Typography>
                  </Box>
                  
                  <Chip
                    label={currentCenter.status === 'ACTIVE' ? 'Открыт' : 'Закрыт'}
                    color={currentCenter.status === 'ACTIVE' ? 'success' : 'error'}
                    sx={{ color: 'white' }}
                  />
                  
                  <Chip
                    label={`${currentCenter.userCount} активных пользователей`}
                    color="primary"
                    icon={<People />}
                    sx={{ color: 'white' }}
                  />
                  
                  <Typography variant="body2">
                    <AccessTime sx={{ mr: 1, fontSize: 16 }} />
                    {formatWorkingHours(currentCenter.workingHours)}
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                  <Tooltip title="Добавить в избранное">
                    <IconButton
                      onClick={handleToggleFavorite}
                      sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', color: 'white' }}
                    >
                      <Favorite sx={{ color: isFavorite ? 'error.main' : 'inherit' }} />
                    </IconButton>
                  </Tooltip>
                  
                  <Tooltip title="Поделиться">
                    <IconButton
                      onClick={handleShare}
                      sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', color: 'white' }}
                    >
                      <Share />
                    </IconButton>
                  </Tooltip>
                  
                  <EnhancedButton
                    variant="contained"
                    romantic
                    href={`/discover?shopping_center=${currentCenter.id}`}
                    startIcon={<FaHeart />}
                  >
                    Знакомства ({currentCenter.userCount})
                  </EnhancedButton>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Box>
      </Box>

      <Container maxWidth="lg" sx={{ pb: 4 }}>
        {/* Навигационные вкладки */}
        <Paper sx={{ mb: 4 }}>
          <Tabs
            value={selectedTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Обзор" icon={<Info />} />
            <Tab label="Фотографии" icon={<PhotoLibrary />} />
            <Tab label="Магазины" icon={<Store />} />
            <Tab label="События" icon={<Event />} />
            <Tab label="Отзывы" icon={<Reviews />} />
            <Tab label="Партнерство" icon={<Business />} />
            <Tab label="Аналитика" icon={<TrendingUp />} />
          </Tabs>
        </Paper>

        {/* Содержимое вкладок */}
        {selectedTab === 0 && (
          <Grid container spacing={4}>
            {/* Основная информация */}
            <Grid item xs={12} md={8}>
              <EnhancedCard romantic sx={{ mb: 4 }}>
                <CardContent>
                  <EnhancedTypography variant="h5" romantic gutterBottom>
                    О торговом центре
                  </EnhancedTypography>
                  <EnhancedTypography variant="body1" readable paragraph>
                    {currentCenter.description}
                  </EnhancedTypography>
                  
                  {currentCenter.tags && currentCenter.tags.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Теги:
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {currentCenter.tags.map((tag, index) => (
                          <Chip key={index} label={tag} size="small" variant="outlined" />
                        ))}
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </EnhancedCard>

              {/* Услуги и удобства */}
              <EnhancedCard romantic sx={{ mb: 4 }}>
                <CardContent>
                  <EnhancedTypography variant="h5" romantic gutterBottom>
                    Услуги и удобства
                  </EnhancedTypography>
                  <Grid container spacing={2}>
                    {currentCenter.services?.map((service) => (
                      <Grid item xs={12} sm={6} md={4} key={service.id}>
                        <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                          <Typography variant="h4" sx={{ mb: 1 }}>
                            {service.icon}
                          </Typography>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {service.name}
                          </Typography>
                          {service.description && (
                            <Typography variant="body2" color="text.secondary">
                              {service.description}
                            </Typography>
                          )}
                          {service.isPaid && service.price && (
                            <Chip
                              label={`от ${service.price} ₽`}
                              size="small"
                              color="primary"
                              sx={{ mt: 1 }}
                            />
                          )}
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </EnhancedCard>

              {/* Популярные пользователи */}
              <EnhancedCard romantic>
                <CardContent>
                  <EnhancedTypography variant="h5" romantic gutterBottom>
                    Популярные пользователи
                  </EnhancedTypography>
                  <Grid container spacing={2}>
                    {currentCenter.popularUsers?.map((user) => (
                      <Grid item xs={6} sm={4} md={3} key={user.id}>
                        <Paper sx={{ p: 2, textAlign: 'center' }}>
                          <Badge
                            overlap="circular"
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                            variant="dot"
                            color={user.online ? 'success' : 'default'}
                          >
                            <Avatar
                              src={user.avatar}
                              alt={user.name}
                              sx={{ width: 60, height: 60, mx: 'auto', mb: 1 }}
                            />
                          </Badge>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {user.name}, {user.age}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.lastVisit}
                          </Typography>
                          {user.verified && (
                            <Verified sx={{ color: 'success.main', fontSize: 16, ml: 0.5 }} />
                          )}
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                  <Box sx={{ mt: 3, textAlign: 'center' }}>
                    <EnhancedButton
                      variant="contained"
                      romantic
                      size="large"
                      href={`/discover?shopping_center=${currentCenter.id}`}
                    >
                      Смотреть все анкеты ({currentCenter.userCount})
                    </EnhancedButton>
                  </Box>
                </CardContent>
              </EnhancedCard>
            </Grid>

            {/* Боковая панель */}
            <Grid item xs={12} md={4}>
              {/* Контактная информация */}
              <EnhancedCard romantic sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Контактная информация
                  </Typography>
                  
                  <List dense>
                    {(currentCenter.contact?.phone || currentCenter.phone) && (
                      <ListItem>
                        <ListItemIcon>
                          <Phone />
                        </ListItemIcon>
                        <ListItemText 
                          primary={currentCenter.contact?.phone || currentCenter.phone}
                          secondary="Телефон"
                        />
                      </ListItem>
                    )}
                    
                    {(currentCenter.contact?.email || currentCenter.email) && (
                      <ListItem>
                        <ListItemIcon>
                          <Email />
                        </ListItemIcon>
                        <ListItemText 
                          primary={currentCenter.contact?.email || currentCenter.email}
                          secondary="Email"
                        />
                      </ListItem>
                    )}
                    
                    {(currentCenter.contact?.website || currentCenter.website) && (
                      <ListItem>
                        <ListItemIcon>
                          <Language />
                        </ListItemIcon>
                        <ListItemText 
                          primary={
                            <Link href={currentCenter.contact?.website || currentCenter.website} target="_blank">
                              Веб-сайт
                            </Link>
                          }
                          secondary="Официальный сайт"
                        />
                      </ListItem>
                    )}
                  </List>
                  
                  <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      href={`tel:${currentCenter.contact?.phone || currentCenter.phone}`}
                      startIcon={<Phone />}
                    >
                      Позвонить
                    </Button>
                    <Button
                      variant="outlined"
                      fullWidth
                      href={`https://maps.google.com/?q=${encodeURIComponent(currentCenter.location?.address || currentCenter.address)}`}
                      target="_blank"
                      startIcon={<Directions />}
                    >
                      Маршрут
                    </Button>
                  </Box>
                </CardContent>
              </EnhancedCard>

              {/* Время работы */}
              <EnhancedCard romantic sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Время работы
                  </Typography>
                  
                  {currentCenter.workingHours && (
                    <List dense>
                      {Object.entries(currentCenter.workingHours).map(([day, hours]) => {
                        const dayNames: { [key: string]: string } = {
                          monday: 'Понедельник',
                          tuesday: 'Вторник',
                          wednesday: 'Среда',
                          thursday: 'Четверг',
                          friday: 'Пятница',
                          saturday: 'Суббота',
                          sunday: 'Воскресенье'
                        };
                        
                        return (
                          <ListItem key={day}>
                            <ListItemText 
                              primary={dayNames[day]}
                              secondary={hours || 'Закрыто'}
                            />
                          </ListItem>
                        );
                      })}
                    </List>
                  )}
                </CardContent>
              </EnhancedCard>

              {/* Статистика */}
              <EnhancedCard romantic>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Статистика
                  </Typography>
                  
                  <List dense>
                    <ListItem>
                      <ListItemIcon>
                        <Store />
                      </ListItemIcon>
                      <ListItemText 
                        primary={currentCenter._count?.stores || 0}
                        secondary="Магазинов"
                      />
                    </ListItem>
                    
                    <ListItem>
                      <ListItemIcon>
                        <LocalParking />
                      </ListItemIcon>
                      <ListItemText 
                        primary={currentCenter.location?.parkingInfo?.totalSpaces || 0}
                        secondary="Парковочных мест"
                      />
                    </ListItem>
                    
                    <ListItem>
                      <ListItemIcon>
                        <People />
                      </ListItemIcon>
                      <ListItemText 
                        primary={currentCenter.userCount}
                        secondary="Активных пользователей"
                      />
                    </ListItem>
                    
                    {currentCenter.totalArea && (
                      <ListItem>
                        <ListItemIcon>
                          <Info />
                        </ListItemIcon>
                        <ListItemText 
                          primary={`${currentCenter.totalArea.toLocaleString()} м²`}
                          secondary="Общая площадь"
                        />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </EnhancedCard>
            </Grid>
          </Grid>
        )}

        {/* Остальные вкладки - заглушки для будущего развития */}
        {selectedTab === 1 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <PhotoLibrary sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              Фотогалерея
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Здесь будет расширенная фотогалерея с возможностью загрузки фото пользователями
            </Typography>
          </Box>
        )}

        {selectedTab === 2 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Store sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              Магазины и бренды
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Здесь будет каталог всех магазинов с партнерскими предложениями
            </Typography>
          </Box>
        )}

        {selectedTab === 3 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Event sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              События и акции
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Здесь будут отображаться все события и специальные предложения
            </Typography>
          </Box>
        )}

        {selectedTab === 4 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Reviews sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              Отзывы посетителей
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Здесь будет система отзывов и рейтингов
            </Typography>
          </Box>
        )}

        {selectedTab === 5 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Business sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              Партнерство
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Информация о партнерских программах и возможностях сотрудничества
            </Typography>
          </Box>
        )}

        {selectedTab === 6 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <TrendingUp sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" color="text.secondary" gutterBottom>
              Аналитика
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Статистика посещений и эффективности партнерских программ
            </Typography>
          </Box>
        )}
      </Container>

      {/* Плавающая кнопка "Наверх" */}
      <Fab
        color="primary"
        size="medium"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1000
        }}
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
      >
        <NavigateNext sx={{ transform: 'rotate(-90deg)' }} />
      </Fab>
    </Layout>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
  const { slug } = params!;
  
  try {
    // В production здесь будет запрос к API
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'}/api/shopping-centers/slug/${slug}`);
    
    if (!response.ok) {
      return {
        notFound: true,
      };
    }
    
    const center = await response.json();
    
    return {
      props: {
        center,
        slug,
      },
    };
  } catch (error) {
    console.error('Error fetching shopping center:', error);
    return {
      notFound: true,
    };
  }
};

export default ShoppingCenterPage;
