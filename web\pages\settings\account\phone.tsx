import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  TextField,
  Alert,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>abel,
  StepContent,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment
} from '@mui/material';
import {
  ArrowBack,
  Phone as PhoneIcon,
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Send as SendIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountIcon,
  Sms as SmsIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для форм
interface PhoneChangeForm {
  countryCode: string;
  phoneNumber: string;
  password: string;
}

interface VerificationForm {
  verificationCode: string;
}

// Коды стран
const countryCodes = [
  { code: '+7', country: 'Россия', flag: '🇷🇺' },
  { code: '+1', country: 'США', flag: '🇺🇸' },
  { code: '+44', country: 'Великобритания', flag: '🇬🇧' },
  { code: '+49', country: 'Германия', flag: '🇩🇪' },
  { code: '+33', country: 'Франция', flag: '🇫🇷' },
  { code: '+380', country: 'Украина', flag: '🇺🇦' },
  { code: '+375', country: 'Беларусь', flag: '🇧🇾' },
  { code: '+7', country: 'Казахстан', flag: '🇰🇿' }
];

// Схемы валидации
const phoneChangeSchema = yup.object({
  countryCode: yup.string().required('Выберите код страны'),
  phoneNumber: yup.string()
    .required('Номер телефона обязателен')
    .matches(/^\d{10,15}$/, 'Номер должен содержать от 10 до 15 цифр')
    .test('different', 'Новый номер должен отличаться от текущего', function(value) {
      const fullNumber = `${this.parent.countryCode}${value}`;
      return fullNumber !== this.parent.currentPhone;
    }),
  password: yup.string()
    .required('Пароль обязателен')
    .min(6, 'Минимум 6 символов')
});

const verificationSchema = yup.object({
  verificationCode: yup.string()
    .required('Код подтверждения обязателен')
    .length(6, 'Код должен содержать 6 цифр')
    .matches(/^\d+$/, 'Код должен содержать только цифры')
});

const ChangePhonePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [newPhone, setNewPhone] = useState('');

  // Форма смены телефона
  const {
    control: phoneControl,
    handleSubmit: handlePhoneSubmit,
    formState: { errors: phoneErrors },
    setValue: setPhoneValue,
    watch
  } = useForm<PhoneChangeForm>({
    resolver: yupResolver(phoneChangeSchema),
    defaultValues: {
      countryCode: '+7',
      phoneNumber: '',
      password: ''
    }
  });

  // Форма подтверждения
  const {
    control: verificationControl,
    handleSubmit: handleVerificationSubmit,
    formState: { errors: verificationErrors },
    reset: resetVerification
  } = useForm<VerificationForm>({
    resolver: yupResolver(verificationSchema),
    defaultValues: {
      verificationCode: ''
    }
  });

  const watchedCountryCode = watch('countryCode');
  const watchedPhoneNumber = watch('phoneNumber');

  // Установка текущего телефона для валидации
  React.useEffect(() => {
    if (user?.phoneNumber) {
      setPhoneValue('currentPhone', user.phoneNumber);
    }
  }, [user?.phoneNumber, setPhoneValue]);

  const steps = [
    'Ввод нового номера',
    'SMS подтверждение',
    'Завершение'
  ];

  const formatPhoneNumber = (countryCode: string, phoneNumber: string) => {
    return `${countryCode}${phoneNumber}`;
  };

  const handlePhoneChange = async (data: PhoneChangeForm) => {
    try {
      setLoading(true);
      setError(null);

      const fullPhoneNumber = formatPhoneNumber(data.countryCode, data.phoneNumber);

      // Здесь будет вызов API для инициации смены телефона
      // await initiatePhoneChange(fullPhoneNumber, data.password);

      setNewPhone(fullPhoneNumber);
      setActiveStep(1);
      setSuccess('SMS код отправлен на новый номер телефона');
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке SMS кода');
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async (data: VerificationForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для подтверждения смены телефона
      // await confirmPhoneChange(data.verificationCode);

      // Обновляем профиль пользователя
      await updateProfile({
        phoneNumber: newPhone,
        phoneVerified: true
      });

      setActiveStep(2);
      setSuccess('Номер телефона успешно изменен!');
    } catch (err: any) {
      setError(err.message || 'Неверный код подтверждения');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для повторной отправки SMS
      // await resendPhoneVerificationCode(newPhone);

      setSuccess('SMS код отправлен повторно');
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке SMS');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
      setError(null);
      setSuccess(null);
    } else {
      router.push('/settings/account');
    }
  };

  if (!user) {
    return (
      <Layout title="Смена телефона">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Смена номера телефона - Likes & Love</title>
        <meta name="description" content="Изменение номера телефона в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Смена телефона">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/account" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/account'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <AccountIcon fontSize="small" />
                Аккаунт
              </Link>
              <Typography color="text.primary">Смена телефона</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <PhoneIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Смена номера телефона
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Текущий номер: {user.phoneNumber || 'Не указан'}
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Предупреждение */}
            <Alert severity="warning" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Важно:</strong> После смены номера телефона вам потребуется подтвердить новый номер по SMS. 
                Убедитесь, что у вас есть доступ к новому номеру.
              </Typography>
            </Alert>

            {/* Stepper */}
            <Card>
              <CardContent>
                <Stepper activeStep={activeStep} orientation={isMobile ? 'vertical' : 'horizontal'}>
                  {steps.map((label, index) => (
                    <Step key={label}>
                      <StepLabel>{label}</StepLabel>
                      {isMobile && (
                        <StepContent>
                          {index === 0 && (
                            <Box sx={{ mt: 2 }}>
                              <form onSubmit={handlePhoneSubmit(handlePhoneChange)}>
                                <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                                  <Controller
                                    name="countryCode"
                                    control={phoneControl}
                                    render={({ field }) => (
                                      <FormControl sx={{ minWidth: 120 }}>
                                        <InputLabel>Код</InputLabel>
                                        <Select {...field} label="Код">
                                          {countryCodes.map((country) => (
                                            <MenuItem key={country.code + country.country} value={country.code}>
                                              {country.flag} {country.code}
                                            </MenuItem>
                                          ))}
                                        </Select>
                                      </FormControl>
                                    )}
                                  />
                                  <Controller
                                    name="phoneNumber"
                                    control={phoneControl}
                                    render={({ field }) => (
                                      <TextField
                                        {...field}
                                        label="Номер телефона"
                                        fullWidth
                                        error={!!phoneErrors.phoneNumber}
                                        helperText={phoneErrors.phoneNumber?.message}
                                        placeholder="9001234567"
                                      />
                                    )}
                                  />
                                </Box>
                                {watchedCountryCode && watchedPhoneNumber && (
                                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    Полный номер: {formatPhoneNumber(watchedCountryCode, watchedPhoneNumber)}
                                  </Typography>
                                )}
                                <Controller
                                  name="password"
                                  control={phoneControl}
                                  render={({ field }) => (
                                    <TextField
                                      {...field}
                                      label="Текущий пароль"
                                      type="password"
                                      fullWidth
                                      error={!!phoneErrors.password}
                                      helperText={phoneErrors.password?.message}
                                      sx={{ mb: 2 }}
                                    />
                                  )}
                                />
                                <Button
                                  type="submit"
                                  variant="contained"
                                  disabled={loading}
                                  startIcon={loading ? <CircularProgress size={20} /> : <SmsIcon />}
                                >
                                  {loading ? 'Отправка...' : 'Отправить SMS код'}
                                </Button>
                              </form>
                            </Box>
                          )}
                          {index === 1 && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                SMS код отправлен на номер: <strong>{newPhone}</strong>
                              </Typography>
                              <form onSubmit={handleVerificationSubmit(handleVerification)}>
                                <Controller
                                  name="verificationCode"
                                  control={verificationControl}
                                  render={({ field }) => (
                                    <TextField
                                      {...field}
                                      label="SMS код"
                                      fullWidth
                                      error={!!verificationErrors.verificationCode}
                                      helperText={verificationErrors.verificationCode?.message}
                                      sx={{ mb: 2 }}
                                      placeholder="123456"
                                    />
                                  )}
                                />
                                <Box sx={{ display: 'flex', gap: 2 }}>
                                  <Button
                                    type="submit"
                                    variant="contained"
                                    disabled={loading}
                                    startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                                  >
                                    {loading ? 'Проверка...' : 'Подтвердить'}
                                  </Button>
                                  <Button
                                    onClick={handleResendCode}
                                    disabled={loading}
                                  >
                                    Отправить повторно
                                  </Button>
                                </Box>
                              </form>
                            </Box>
                          )}
                          {index === 2 && (
                            <Box sx={{ mt: 2 }}>
                              <Alert severity="success">
                                <Typography variant="body2">
                                  Номер телефона успешно изменен! Теперь вы можете использовать новый номер.
                                </Typography>
                              </Alert>
                              <Button
                                onClick={() => router.push('/settings/account')}
                                variant="contained"
                                sx={{ mt: 2 }}
                              >
                                Вернуться к настройкам
                              </Button>
                            </Box>
                          )}
                        </StepContent>
                      )}
                    </Step>
                  ))}
                </Stepper>

                {/* Контент для desktop */}
                {!isMobile && (
                  <Box sx={{ mt: 4 }}>
                    {activeStep === 0 && (
                      <form onSubmit={handlePhoneSubmit(handlePhoneChange)}>
                        <Typography variant="h6" sx={{ mb: 3 }}>
                          Введите новый номер телефона
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                          <Controller
                            name="countryCode"
                            control={phoneControl}
                            render={({ field }) => (
                              <FormControl sx={{ minWidth: 150 }}>
                                <InputLabel>Код страны</InputLabel>
                                <Select {...field} label="Код страны">
                                  {countryCodes.map((country) => (
                                    <MenuItem key={country.code + country.country} value={country.code}>
                                      {country.flag} {country.code} {country.country}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            )}
                          />
                          <Controller
                            name="phoneNumber"
                            control={phoneControl}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Номер телефона"
                                fullWidth
                                error={!!phoneErrors.phoneNumber}
                                helperText={phoneErrors.phoneNumber?.message}
                                placeholder="9001234567"
                              />
                            )}
                          />
                        </Box>
                        {watchedCountryCode && watchedPhoneNumber && (
                          <Typography variant="body1" sx={{ mb: 3 }}>
                            Полный номер: <strong>{formatPhoneNumber(watchedCountryCode, watchedPhoneNumber)}</strong>
                          </Typography>
                        )}
                        <Controller
                          name="password"
                          control={phoneControl}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Текущий пароль для подтверждения"
                              type="password"
                              fullWidth
                              error={!!phoneErrors.password}
                              helperText={phoneErrors.password?.message}
                              sx={{ mb: 3 }}
                            />
                          )}
                        />
                        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                          <Button onClick={handleBack}>
                            Отмена
                          </Button>
                          <Button
                            type="submit"
                            variant="contained"
                            disabled={loading}
                            startIcon={loading ? <CircularProgress size={20} /> : <SmsIcon />}
                          >
                            {loading ? 'Отправка...' : 'Отправить SMS код'}
                          </Button>
                        </Box>
                      </form>
                    )}

                    {activeStep === 1 && (
                      <Box>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          Подтверждение номера телефона
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 3 }}>
                          Мы отправили SMS код на номер: <strong>{newPhone}</strong>
                        </Typography>
                        <form onSubmit={handleVerificationSubmit(handleVerification)}>
                          <Controller
                            name="verificationCode"
                            control={verificationControl}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Введите 6-значный SMS код"
                                fullWidth
                                error={!!verificationErrors.verificationCode}
                                helperText={verificationErrors.verificationCode?.message}
                                sx={{ mb: 3 }}
                                placeholder="123456"
                              />
                            )}
                          />
                          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                            <Button onClick={handleResendCode} disabled={loading}>
                              Отправить повторно
                            </Button>
                            <Button onClick={handleBack}>
                              Назад
                            </Button>
                            <Button
                              type="submit"
                              variant="contained"
                              disabled={loading}
                              startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                            >
                              {loading ? 'Проверка...' : 'Подтвердить'}
                            </Button>
                          </Box>
                        </form>
                      </Box>
                    )}

                    {activeStep === 2 && (
                      <Box sx={{ textAlign: 'center' }}>
                        <CheckCircleIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
                        <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
                          Номер телефона успешно изменен!
                        </Typography>
                        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                          Ваш новый номер телефона: <strong>{newPhone}</strong>
                        </Typography>
                        <Button
                          onClick={() => router.push('/settings/account')}
                          variant="contained"
                          size="large"
                        >
                          Вернуться к настройкам аккаунта
                        </Button>
                      </Box>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ChangePhonePage;
