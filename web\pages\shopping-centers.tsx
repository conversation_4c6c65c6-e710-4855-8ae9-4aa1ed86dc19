import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import {
  Box,
  Container,
  Grid,
  Card,
  CardMedia,
  Typography,
  Chip,
  TextField,
  InputAdornment,
  Avatar,
  Badge,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ImageList,
  ImageListItem,
  ImageListItemBar,
  Tabs,
  Tab,
  Rating,
  Skeleton,
  Alert,
  Snackbar,
  Breadcrumbs,
  Link,
  Tooltip,
  Fab,
  CardContent,
  CardActions
} from '@mui/material';
import {
  Search,
  LocationOn,
  People,
  Star,
  Directions,
  Phone,
  Schedule,
  Verified,
  Close,
  PhotoLibrary,
  Info,
  Store,
  Event,
  Reviews,
  AccessTime,
  LocalParking,
  Wifi,
  Restaurant,
  LocalMovies,
  FitnessCenter,
  ShoppingBag,
  NavigateNext,
  Home,
  Favorite,
  Share,
  Map,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaMapMarkerAlt, FaClock, FaUsers, FaHeart, FaRoute, FaShoppingCart } from 'react-icons/fa';
import { useShoppingCenters } from '../src/hooks/useShoppingCenters';
import { ShoppingCenter } from '../src/types/shopping-center.types';

interface ShoppingCenterImage {
  id: string;
  url: string;
  alt?: string;
  type: 'MAIN' | 'GALLERY' | 'LOGO' | 'INTERIOR' | 'EXTERIOR';
  isVerified: boolean;
  uploadedBy?: string;
  uploadedAt: string;
}

interface ShoppingCenterCategory {
  id: string;
  name: string;
  icon?: string;
}

interface ShoppingCenterService {
  id: string;
  name: string;
  icon?: string;
  description?: string;
}

interface WorkingHours {
  monday?: string;
  tuesday?: string;
  wednesday?: string;
  thursday?: string;
  friday?: string;
  saturday?: string;
  sunday?: string;
}

interface ShoppingCenter {
  id: string;
  name: string;
  slug: string;
  address: string;
  description: string;
  city: {
    id: string;
    name: string;
    region: string;
  };
  latitude?: number;
  longitude?: number;
  totalArea?: number;
  retailArea?: number;
  storesCount?: number;
  parkingSpaces?: number;
  phone?: string;
  email?: string;
  website?: string;
  workingHours?: WorkingHours;
  status: 'ACTIVE' | 'INACTIVE' | 'UNDER_CONSTRUCTION' | 'CLOSED';
  rating?: number;
  reviewsCount?: number;
  yearOpened?: number;
  developer?: string;
  manager?: string;
  nearestMetro?: string;
  metroDistance?: number;
  isVerified: boolean;
  isFeatured: boolean;
  logoUrl?: string;
  images: ShoppingCenterImage[];
  categories: ShoppingCenterCategory[];
  services: ShoppingCenterService[];
  userCount: number;
  distance: number;
  popularUsers: User[];
  _count: {
    stores: number;
    events: number;
    reviews: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface User {
  id: string;
  name: string;
  age: number;
  avatar: string;
  verified: boolean;
  online: boolean;
  lastVisit: string;
}

const ShoppingCentersPage: React.FC = () => {
  const [selectedCenter, setSelectedCenter] = useState<ShoppingCenter | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [expandedFeatures, setExpandedFeatures] = useState<Set<string>>(new Set());

  // Используем хук для работы с торговыми центрами
  const {
    centers,
    loading,
    error,
    total,
    favorites,
    filters,
    search,
    toggleFavorite,
    updateFilters,
    resetFilters
  } = useShoppingCenters();




  // Функции для работы с торговыми центрами
  const handleCenterClick = (center: ShoppingCenter) => {
    // Переходим на отдельную страницу торгового центра
    window.location.href = `/shopping-center/${center.slug}`;
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCenter(null);
    setSelectedTab(0);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  const handleToggleFavorite = async (centerId: string) => {
    try {
      await toggleFavorite(centerId);
      setSnackbarOpen(true);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      setSnackbarOpen(true);
    }
  };

  const handleShare = async (center: ShoppingCenter) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: center.name,
          text: center.description,
          url: window.location.href + '/' + center.slug,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback для браузеров без поддержки Web Share API
      navigator.clipboard.writeText(window.location.href + '/' + center.slug);
      setSnackbarOpen(true);
    }
  };

  const toggleExpandFeatures = (centerId: string) => {
    setExpandedFeatures(prev => {
      const newSet = new Set(prev);
      if (newSet.has(centerId)) {
        newSet.delete(centerId);
      } else {
        newSet.add(centerId);
      }
      return newSet;
    });
  };

  const formatWorkingHours = (workingHours?: WorkingHours) => {
    if (!workingHours) return 'Время работы не указано';

    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс'];

    // Получаем индекс текущего дня (0 = воскресенье, 1 = понедельник, ...)
    // Преобразуем в наш формат (0 = понедельник, 6 = воскресенье)
    const todayIndex = new Date().getDay() === 0 ? 6 : new Date().getDay() - 1;
    const todayKey = days[todayIndex] as keyof WorkingHours;

    return workingHours[todayKey] || 'Закрыто';
  };

  // Обработчик поиска
  const handleSearch = (query: string) => {
    search(query);
  };

  return (
    <Layout>
      <Head>
        <title>Торговые центры - Likes & Love</title>
        <meta name="description" content="Найдите людей в популярных торговых центрах Москвы. Знакомьтесь в местах, которые вы часто посещаете." />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <EnhancedTypography variant="h3" romantic gradient shadow sx={{ mb: 2 }}>
            🛍️ Торговые центры
          </EnhancedTypography>
          <EnhancedTypography variant="h6" readable sx={{ mb: 3 }}>
            Знакомьтесь с людьми в популярных местах Москвы
          </EnhancedTypography>
          
          <TextField
            fullWidth
            placeholder="Поиск торговых центров..."
            value={filters.search || ''}
            onChange={(e) => handleSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ maxWidth: 600, mx: 'auto' }}
          />
        </Box>

        {/* Statistics */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 4, flexWrap: 'wrap' }}>
          <Chip
            label={`${centers.length} торговых центров`}
            color="primary"
            icon={<FaShoppingCart />}
          />
          <Chip
            label={`${centers.reduce((sum, center) => sum + center.userCount, 0)} пользователей`}
            color="secondary"
            icon={<People />}
          />
          <Chip
            label="Москва и область"
            color="info"
            icon={<LocationOn />}
          />
        </Box>

        {/* Loading State */}
        {loading && (
          <Grid container spacing={3}>
            {[...Array(6)].map((_, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <Card sx={{ height: 400 }}>
                  <Skeleton variant="rectangular" height={200} />
                  <Box sx={{ p: 2 }}>
                    <Skeleton variant="text" height={32} />
                    <Skeleton variant="text" height={20} />
                    <Skeleton variant="text" height={20} />
                    <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                      <Skeleton variant="rectangular" width={60} height={24} />
                      <Skeleton variant="rectangular" width={60} height={24} />
                      <Skeleton variant="rectangular" width={60} height={24} />
                    </Box>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Shopping Centers Grid */}
        {!loading && !error && (
          <Grid container spacing={3}>
            {centers.map((center) => (
            <Grid item xs={12} md={6} lg={4} key={center.id}>
              <EnhancedCard
                romantic
                hoverable
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 32px rgba(255, 107, 157, 0.3)'
                  }
                }}
                onClick={() => handleCenterClick(center)}
              >
                <Box sx={{ position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height={200}
                    image={center.images[0]?.url || 'https://picsum.photos/400/250?random=101'}
                    alt={center.name}
                    sx={{ objectFit: 'cover' }}
                  />

                  {/* Верификация и избранное */}
                  <Box sx={{
                    position: 'absolute',
                    top: 8,
                    left: 8,
                    right: 8,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start'
                  }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {center.isVerified && (
                        <Chip
                          label="Верифицирован"
                          size="small"
                          color="success"
                          icon={<Verified />}
                          sx={{ bgcolor: 'rgba(76, 175, 80, 0.9)', color: 'white' }}
                        />
                      )}
                      {center.isFeatured && (
                        <Chip
                          label="Рекомендуем"
                          size="small"
                          color="warning"
                          icon={<Star />}
                          sx={{ bgcolor: 'rgba(255, 152, 0, 0.9)', color: 'white' }}
                        />
                      )}
                    </Box>

                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleFavorite(center.id);
                      }}
                      sx={{
                        bgcolor: 'rgba(255, 255, 255, 0.9)',
                        '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
                      }}
                    >
                      <Favorite
                        sx={{
                          color: favorites.includes(center.id) ? 'error.main' : 'grey.400'
                        }}
                      />
                    </IconButton>
                  </Box>

                  {/* Статус работы */}
                  <Box sx={{
                    position: 'absolute',
                    bottom: 8,
                    right: 8
                  }}>
                    <Chip
                      label={center.status === 'ACTIVE' ? 'Открыт' : 'Закрыт'}
                      size="small"
                      color={center.status === 'ACTIVE' ? 'success' : 'error'}
                      sx={{ bgcolor: 'rgba(255, 255, 255, 0.9)' }}
                    />
                  </Box>
                </Box>

                <CardContent sx={{ p: 2, pb: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Box sx={{ flexGrow: 1 }}>
                      <EnhancedTypography variant="h6" romantic sx={{ mb: 0.5, lineHeight: 1.2 }}>
                        {center.name}
                      </EnhancedTypography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5, display: 'flex', alignItems: 'center' }}>
                        <LocationOn sx={{ fontSize: 16, mr: 0.5 }} />
                        {center.location?.address || center.address}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                        <FaRoute style={{ marginRight: 4, fontSize: 12 }} />
                        {center.distance} км • {center.location?.nearestMetro && `м. ${center.location.nearestMetro}`}
                      </Typography>
                    </Box>

                    <Box sx={{ textAlign: 'right', ml: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Rating value={center.rating} precision={0.1} size="small" readOnly />
                        <Typography variant="body2" fontWeight={600} sx={{ ml: 0.5 }}>
                          {center.rating}
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {center.reviewsCount} отзывов
                      </Typography>
                    </Box>
                  </Box>

                  <EnhancedTypography variant="body2" readable sx={{ mb: 1.5, lineHeight: 1.4 }}>
                    {center.description.length > 100
                      ? `${center.description.substring(0, 100)}...`
                      : center.description
                    }
                  </EnhancedTypography>

                  {/* Услуги */}
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 1.5 }}>
                    {center.services.slice(0, expandedFeatures.has(center.id) ? center.services.length : 3).map((service) => (
                      <Chip
                        key={service.id}
                        label={`${service.icon} ${service.name}`}
                        size="small"
                        variant="outlined"
                        color="secondary"
                        sx={{ fontSize: '0.7rem', height: 24 }}
                      />
                    ))}
                    {center.services.length > 3 && (
                      <Chip
                        label={expandedFeatures.has(center.id)
                          ? 'Скрыть'
                          : `+${center.services.length - 3}`
                        }
                        size="small"
                        variant="outlined"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleExpandFeatures(center.id);
                        }}
                        sx={{ fontSize: '0.7rem', height: 24, cursor: 'pointer' }}
                      />
                    )}
                  </Box>

                  {/* Время работы и статистика */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <AccessTime sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2" color="text.secondary">
                        {formatWorkingHours(center.workingHours)}
                      </Typography>
                    </Box>
                    <Chip
                      label={`${center.userCount} чел.`}
                      color="primary"
                      size="small"
                      icon={<People />}
                      sx={{ fontSize: '0.7rem', height: 24 }}
                    />
                  </Box>

                  {/* Популярные пользователи */}
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="caption" color="text.secondary" fontWeight={600}>
                      Популярные пользователи:
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {center.popularUsers.slice(0, 3).map((user) => (
                        <Tooltip key={user.id} title={`${user.name}, ${user.age}`}>
                          <Badge
                            overlap="circular"
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                            variant="dot"
                            color={user.online ? 'success' : 'default'}
                          >
                            <Avatar
                              src={user.avatar}
                              alt={user.name}
                              sx={{ width: 28, height: 28 }}
                            />
                          </Badge>
                        </Tooltip>
                      ))}
                      {center.popularUsers.length > 3 && (
                        <Avatar sx={{ width: 28, height: 28, bgcolor: 'primary.main', fontSize: '0.7rem' }}>
                          +{center.popularUsers.length - 3}
                        </Avatar>
                      )}
                    </Box>
                  </Box>
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0, gap: 1 }}>
                  <EnhancedButton
                    variant="contained"
                    romantic
                    size="small"
                    fullWidth
                    href={`/shopping-center/${center.slug}`}
                    onClick={(e) => e.stopPropagation()}
                  >
                    Подробнее
                  </EnhancedButton>

                  <EnhancedButton
                    variant="outlined"
                    size="small"
                    href={`/discover?shopping_center=${center.id}`}
                    onClick={(e) => e.stopPropagation()}
                  >
                    Знакомства
                  </EnhancedButton>

                  <Tooltip title="Позвонить">
                    <IconButton
                      size="small"
                      href={`tel:${center.contact?.phone || center.phone}`}
                      onClick={(e) => e.stopPropagation()}
                      sx={{ border: '1px solid', borderColor: 'divider' }}
                    >
                      <Phone />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Маршрут">
                    <IconButton
                      size="small"
                      href={`https://maps.google.com/?q=${encodeURIComponent(center.location?.address || center.address)}`}
                      target="_blank"
                      onClick={(e) => e.stopPropagation()}
                      sx={{ border: '1px solid', borderColor: 'divider' }}
                    >
                      <Directions />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Поделиться">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleShare(center);
                      }}
                      sx={{ border: '1px solid', borderColor: 'divider' }}
                    >
                      <Share />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </EnhancedCard>
            </Grid>
          ))}
          </Grid>
        )}

        {!loading && !error && centers.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <EnhancedTypography variant="h5" sx={{ mb: 2 }}>
              Торговые центры не найдены
            </EnhancedTypography>
            <EnhancedTypography variant="body1" readable>
              Попробуйте изменить поисковый запрос
            </EnhancedTypography>
          </Box>
        )}

        {/* Детальный диалог торгового центра */}
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              maxHeight: '90vh'
            }
          }}
        >
          {selectedCenter && (
            <>
              <DialogTitle sx={{ p: 0, position: 'relative' }}>
                {/* Главное изображение */}
                <Box sx={{ position: 'relative', height: 300 }}>
                  <CardMedia
                    component="img"
                    height="100%"
                    image={selectedCenter.images[0]?.url || 'https://picsum.photos/800/300?random=101'}
                    alt={selectedCenter.name}
                    sx={{ objectFit: 'cover' }}
                  />

                  {/* Градиент и заголовок */}
                  <Box sx={{
                    position: 'absolute',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                    p: 3,
                    color: 'white'
                  }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-end' }}>
                      <Box>
                        <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                          {selectedCenter.name}
                          {selectedCenter.isVerified && (
                            <Verified sx={{ ml: 1, color: 'success.main' }} />
                          )}
                        </Typography>
                        <Typography variant="body1" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LocationOn sx={{ mr: 1 }} />
                          {selectedCenter.location?.address || selectedCenter.address}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Rating value={selectedCenter.rating} precision={0.1} size="small" readOnly />
                            <Typography variant="body2" sx={{ ml: 1 }}>
                              {selectedCenter.rating} ({selectedCenter.reviewsCount} отзывов)
                            </Typography>
                          </Box>
                          <Chip
                            label={selectedCenter.status === 'ACTIVE' ? 'Открыт' : 'Закрыт'}
                            size="small"
                            color={selectedCenter.status === 'ACTIVE' ? 'success' : 'error'}
                          />
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          onClick={() => handleToggleFavorite(selectedCenter.id)}
                          sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)' }}
                        >
                          <Favorite
                            sx={{
                              color: favorites.includes(selectedCenter.id) ? 'error.main' : 'white'
                            }}
                          />
                        </IconButton>
                        <IconButton
                          onClick={() => handleShare(selectedCenter)}
                          sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)' }}
                        >
                          <Share sx={{ color: 'white' }} />
                        </IconButton>
                      </Box>
                    </Box>
                  </Box>

                  {/* Кнопка закрытия */}
                  <IconButton
                    onClick={handleCloseDialog}
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
                    }}
                  >
                    <Close />
                  </IconButton>
                </Box>
              </DialogTitle>

              <DialogContent sx={{ p: 0 }}>
                {/* Навигационные вкладки */}
                <Tabs
                  value={selectedTab}
                  onChange={handleTabChange}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
                >
                  <Tab label="Обзор" icon={<Info />} />
                  <Tab label="Фотографии" icon={<PhotoLibrary />} />
                  <Tab label="Магазины" icon={<Store />} />
                  <Tab label="События" icon={<Event />} />
                  <Tab label="Отзывы" icon={<Reviews />} />
                </Tabs>

                {/* Содержимое вкладок */}
                <Box sx={{ p: 3 }}>
                  {/* Вкладка "Обзор" */}
                  {selectedTab === 0 && (
                    <Grid container spacing={3}>
                      <Grid item xs={12} md={8}>
                        <Typography variant="h6" gutterBottom>
                          Описание
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {selectedCenter.description}
                        </Typography>

                        <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                          Услуги и удобства
                        </Typography>
                        <Grid container spacing={2}>
                          {selectedCenter.services.map((service) => (
                            <Grid item xs={12} sm={6} md={4} key={service.id}>
                              <Paper sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                                <Typography variant="h4" sx={{ mb: 1 }}>
                                  {service.icon}
                                </Typography>
                                <Typography variant="subtitle2" fontWeight="bold">
                                  {service.name}
                                </Typography>
                                {service.description && (
                                  <Typography variant="body2" color="text.secondary">
                                    {service.description}
                                  </Typography>
                                )}
                              </Paper>
                            </Grid>
                          ))}
                        </Grid>

                        <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                          Категории магазинов
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          {selectedCenter.categories.map((category) => (
                            <Chip
                              key={category.id}
                              label={`${category.icon} ${category.name}`}
                              variant="outlined"
                              color="primary"
                            />
                          ))}
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Paper sx={{ p: 2, mb: 2 }}>
                          <Typography variant="h6" gutterBottom>
                            Контактная информация
                          </Typography>

                          <List dense>
                            {(selectedCenter.contact?.phone || selectedCenter.phone) && (
                              <ListItem>
                                <ListItemIcon>
                                  <Phone />
                                </ListItemIcon>
                                <ListItemText
                                  primary={selectedCenter.contact?.phone || selectedCenter.phone}
                                  secondary="Телефон"
                                />
                              </ListItem>
                            )}

                            {(selectedCenter.contact?.email || selectedCenter.email) && (
                              <ListItem>
                                <ListItemIcon>
                                  <Info />
                                </ListItemIcon>
                                <ListItemText
                                  primary={selectedCenter.contact?.email || selectedCenter.email}
                                  secondary="Email"
                                />
                              </ListItem>
                            )}

                            {(selectedCenter.contact?.website || selectedCenter.website) && (
                              <ListItem>
                                <ListItemIcon>
                                  <Info />
                                </ListItemIcon>
                                <ListItemText
                                  primary={
                                    <Link href={selectedCenter.contact?.website || selectedCenter.website} target="_blank">
                                      {selectedCenter.contact?.website || selectedCenter.website}
                                    </Link>
                                  }
                                  secondary="Веб-сайт"
                                />
                              </ListItem>
                            )}

                            <ListItem>
                              <ListItemIcon>
                                <LocationOn />
                              </ListItemIcon>
                              <ListItemText
                                primary={selectedCenter.location?.address || selectedCenter.address}
                                secondary="Адрес"
                              />
                            </ListItem>

                            {(selectedCenter.location?.nearestMetro || selectedCenter.nearestMetro) && (
                              <ListItem>
                                <ListItemIcon>
                                  <Map />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`м. ${selectedCenter.location?.nearestMetro || selectedCenter.nearestMetro}`}
                                  secondary={`${selectedCenter.location?.metroDistance || selectedCenter.metroDistance} м пешком`}
                                />
                              </ListItem>
                            )}
                          </List>
                        </Paper>

                        <Paper sx={{ p: 2, mb: 2 }}>
                          <Typography variant="h6" gutterBottom>
                            Время работы
                          </Typography>

                          {selectedCenter.workingHours && (
                            <List dense>
                              {Object.entries(selectedCenter.workingHours).map(([day, hours]) => {
                                const dayNames: { [key: string]: string } = {
                                  monday: 'Понедельник',
                                  tuesday: 'Вторник',
                                  wednesday: 'Среда',
                                  thursday: 'Четверг',
                                  friday: 'Пятница',
                                  saturday: 'Суббота',
                                  sunday: 'Воскресенье'
                                };

                                return (
                                  <ListItem key={day}>
                                    <ListItemText
                                      primary={dayNames[day]}
                                      secondary={hours || 'Закрыто'}
                                    />
                                  </ListItem>
                                );
                              })}
                            </List>
                          )}
                        </Paper>

                        <Paper sx={{ p: 2 }}>
                          <Typography variant="h6" gutterBottom>
                            Статистика
                          </Typography>

                          <List dense>
                            <ListItem>
                              <ListItemIcon>
                                <Store />
                              </ListItemIcon>
                              <ListItemText
                                primary={selectedCenter._count.stores}
                                secondary="Магазинов"
                              />
                            </ListItem>

                            <ListItem>
                              <ListItemIcon>
                                <LocalParking />
                              </ListItemIcon>
                              <ListItemText
                                primary={selectedCenter.location?.parkingInfo?.totalSpaces || selectedCenter.parkingSpaces}
                                secondary="Парковочных мест"
                              />
                            </ListItem>

                            <ListItem>
                              <ListItemIcon>
                                <People />
                              </ListItemIcon>
                              <ListItemText
                                primary={selectedCenter.userCount}
                                secondary="Активных пользователей"
                              />
                            </ListItem>

                            {selectedCenter.totalArea && (
                              <ListItem>
                                <ListItemIcon>
                                  <Info />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`${selectedCenter.totalArea.toLocaleString()} м²`}
                                  secondary="Общая площадь"
                                />
                              </ListItem>
                            )}
                          </List>
                        </Paper>
                      </Grid>
                    </Grid>
                  )}

                  {/* Вкладка "Фотографии" */}
                  {selectedTab === 1 && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Фотогалерея
                      </Typography>
                      <ImageList variant="masonry" cols={3} gap={8}>
                        {selectedCenter.images.map((image) => (
                          <ImageListItem key={image.id}>
                            <img
                              src={image.url}
                              alt={image.alt || selectedCenter.name}
                              loading="lazy"
                              style={{ borderRadius: 8 }}
                            />
                            <ImageListItemBar
                              title={image.alt}
                              subtitle={image.type}
                              actionIcon={
                                image.isVerified && (
                                  <IconButton sx={{ color: 'rgba(255, 255, 255, 0.54)' }}>
                                    <Verified />
                                  </IconButton>
                                )
                              }
                            />
                          </ImageListItem>
                        ))}
                      </ImageList>
                    </Box>
                  )}

                  {/* Остальные вкладки - заглушки */}
                  {selectedTab === 2 && (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      <Store sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        Список магазинов скоро будет доступен
                      </Typography>
                    </Box>
                  )}

                  {selectedTab === 3 && (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      <Event sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        События и мероприятия скоро будут доступны
                      </Typography>
                    </Box>
                  )}

                  {selectedTab === 4 && (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      <Reviews sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" color="text.secondary">
                        Отзывы скоро будут доступны
                      </Typography>
                    </Box>
                  )}
                </Box>
              </DialogContent>

              <DialogActions sx={{ p: 3, gap: 2 }}>
                <EnhancedButton
                  variant="contained"
                  romantic
                  fullWidth
                  href={`/discover?shopping_center=${selectedCenter.id}`}
                  startIcon={<FaHeart />}
                >
                  Смотреть анкеты ({selectedCenter.userCount})
                </EnhancedButton>

                <Button
                  variant="outlined"
                  href={`tel:${selectedCenter.contact?.phone || selectedCenter.phone}`}
                  startIcon={<Phone />}
                >
                  Позвонить
                </Button>

                <Button
                  variant="outlined"
                  href={`https://maps.google.com/?q=${encodeURIComponent(selectedCenter.location?.address || selectedCenter.address)}`}
                  target="_blank"
                  startIcon={<Directions />}
                >
                  Маршрут
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>

        {/* Info Section */}
        <Paper sx={{ mt: 4, p: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
          <EnhancedTypography variant="h6" sx={{ mb: 2 }}>
            ℹ️ Как это работает?
          </EnhancedTypography>
          <Typography variant="body2">
            • Выберите торговый центр, который вы часто посещаете<br/>
            • Посмотрите анкеты людей, которые тоже бывают в этом месте<br/>
            • Знакомьтесь и назначайте встречи в знакомых местах<br/>
            • Используйте общие интересы к шопингу и развлечениям
          </Typography>
        </Paper>
      </Container>

      {/* Уведомления */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity="success"
          variant="filled"
        >
          Действие выполнено успешно!
        </Alert>
      </Snackbar>

      {/* Плавающая кнопка "Наверх" */}
      <Fab
        color="primary"
        size="medium"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          zIndex: 1000
        }}
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
      >
        <NavigateNext sx={{ transform: 'rotate(-90deg)' }} />
      </Fab>
    </Layout>
  );
};

export default ShoppingCentersPage;
