import React from 'react';
import Head from 'next/head';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Button,
  Paper,
  Divider
} from '@mui/material';
import {
  Security,
  VerifiedUser,
  Block,
  Report,
  Visibility,
  Phone,
  LocationOn,
  Warning,
  CheckCircle,
  Info,
  Shield
} from '@mui/icons-material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaShieldAlt, FaExclamationTriangle, FaUserShield, FaEye, FaPhoneAlt } from 'react-icons/fa';

const SafetyPage: React.FC = () => {
  const safetyTips = [
    {
      icon: <VerifiedUser />,
      title: 'Верифицируйте профиль',
      description: 'Подтвердите свою личность, чтобы повысить доверие и безопасность знакомств.',
      color: 'success'
    },
    {
      icon: <Visibility />,
      title: 'Встречайтесь в общественных местах',
      description: 'Первые свидания проводите в людных местах: кафе, ресторанах, торговых центрах.',
      color: 'info'
    },
    {
      icon: <Phone />,
      title: 'Сообщите друзьям о встрече',
      description: 'Расскажите близким, где и с кем встречаетесь. Поделитесь локацией.',
      color: 'warning'
    },
    {
      icon: <Block />,
      title: 'Доверяйте интуиции',
      description: 'Если что-то кажется подозрительным, не игнорируйте свои ощущения.',
      color: 'error'
    }
  ];

  const reportingSteps = [
    {
      step: 1,
      title: 'Откройте профиль пользователя',
      description: 'Перейдите в профиль человека, на которого хотите пожаловаться'
    },
    {
      step: 2,
      title: 'Нажмите на меню (три точки)',
      description: 'В правом верхнем углу профиля найдите кнопку меню'
    },
    {
      step: 3,
      title: 'Выберите "Пожаловаться"',
      description: 'Укажите причину жалобы из предложенного списка'
    },
    {
      step: 4,
      title: 'Опишите ситуацию',
      description: 'Добавьте подробности, если необходимо'
    },
    {
      step: 5,
      title: 'Отправьте жалобу',
      description: 'Мы рассмотрим её в течение 24 часов'
    }
  ];

  const warningSignals = [
    'Просит деньги или финансовую помощь',
    'Отказывается встречаться в общественных местах',
    'Слишком быстро предлагает встретиться',
    'Просит личную информацию (адрес, паспортные данные)',
    'Фотографии выглядят слишком профессионально',
    'Избегает видеозвонков',
    'История не сходится в деталях',
    'Агрессивное поведение в сообщениях'
  ];

  const emergencyContacts = [
    {
      service: 'Служба экстренного реагирования',
      number: '112',
      description: 'Единый номер экстренных служб'
    },
    {
      service: 'Полиция',
      number: '102',
      description: 'При угрозе безопасности'
    },
    {
      service: 'Горячая линия помощи женщинам',
      number: '8-800-7000-600',
      description: 'Психологическая поддержка 24/7'
    }
  ];

  return (
    <Layout>
      <Head>
        <title>Безопасность - Likes & Love</title>
        <meta name="description" content="Рекомендации по безопасности при знакомствах онлайн. Как защитить себя и сообщить о нарушениях." />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <EnhancedTypography variant="h3" romantic gradient shadow sx={{ mb: 2 }}>
            🛡️ Безопасность
          </EnhancedTypography>
          <EnhancedTypography variant="h6" readable sx={{ mb: 3 }}>
            Ваша безопасность - наш приоритет. Следуйте этим рекомендациям для безопасных знакомств
          </EnhancedTypography>
        </Box>

        {/* Safety Alert */}
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="body1">
            <strong>Важно:</strong> Никогда не передавайте деньги незнакомым людям и не сообщайте личную информацию. 
            При любых подозрениях обращайтесь в службу поддержки.
          </Typography>
        </Alert>

        {/* Safety Tips */}
        <EnhancedTypography variant="h4" romantic sx={{ mb: 3, textAlign: 'center' }}>
          Основные правила безопасности
        </EnhancedTypography>
        
        <Grid container spacing={3} sx={{ mb: 6 }}>
          {safetyTips.map((tip, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <EnhancedCard romantic hoverable sx={{ height: '100%', textAlign: 'center' }}>
                <Box sx={{ color: `${tip.color}.main`, mb: 2, fontSize: '2.5rem' }}>
                  {tip.icon}
                </Box>
                <EnhancedTypography variant="h6" romantic sx={{ mb: 2 }}>
                  {tip.title}
                </EnhancedTypography>
                <Typography variant="body2" readable>
                  {tip.description}
                </Typography>
              </EnhancedCard>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={4}>
          {/* Warning Signals */}
          <Grid item xs={12} md={6}>
            <EnhancedCard romantic>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <FaExclamationTriangle style={{ color: '#ff9800', marginRight: '12px', fontSize: '1.5rem' }} />
                <EnhancedTypography variant="h5" romantic>
                  Тревожные сигналы
                </EnhancedTypography>
              </Box>
              
              <Typography variant="body1" readable sx={{ mb: 3 }}>
                Будьте осторожны, если собеседник:
              </Typography>
              
              <List>
                {warningSignals.map((signal, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={signal}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </EnhancedCard>
          </Grid>

          {/* Reporting Process */}
          <Grid item xs={12} md={6}>
            <EnhancedCard romantic>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Report style={{ color: '#f44336', marginRight: '12px', fontSize: '1.5rem' }} />
                <EnhancedTypography variant="h5" romantic>
                  Как пожаловаться
                </EnhancedTypography>
              </Box>
              
              {reportingSteps.map((step, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 2 }}>
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      bgcolor: 'primary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                      mt: 0.5,
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}
                  >
                    {step.step}
                  </Box>
                  <Box>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {step.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {step.description}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </EnhancedCard>
          </Grid>
        </Grid>

        {/* Privacy Settings */}
        <Box sx={{ mt: 6 }}>
          <EnhancedTypography variant="h4" romantic sx={{ mb: 3, textAlign: 'center' }}>
            Настройки приватности
          </EnhancedTypography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <EnhancedCard romantic hoverable sx={{ textAlign: 'center', height: '100%' }}>
                <FaUserShield style={{ fontSize: '2.5rem', color: '#4caf50', marginBottom: '1rem' }} />
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Контроль видимости
                </Typography>
                <Typography variant="body2" readable sx={{ mb: 2 }}>
                  Настройте, кто может видеть ваш профиль и связываться с вами
                </Typography>
                <Button variant="outlined" href="/settings">
                  Настроить
                </Button>
              </EnhancedCard>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <EnhancedCard romantic hoverable sx={{ textAlign: 'center', height: '100%' }}>
                <FaEye style={{ fontSize: '2.5rem', color: '#2196f3', marginBottom: '1rem' }} />
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Блокировка пользователей
                </Typography>
                <Typography variant="body2" readable sx={{ mb: 2 }}>
                  Заблокируйте нежелательных пользователей в один клик
                </Typography>
                <Button variant="outlined" href="/settings">
                  Управлять
                </Button>
              </EnhancedCard>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <EnhancedCard romantic hoverable sx={{ textAlign: 'center', height: '100%' }}>
                <LocationOn style={{ fontSize: '2.5rem', color: '#ff9800', marginBottom: '1rem' }} />
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Геолокация
                </Typography>
                <Typography variant="body2" readable sx={{ mb: 2 }}>
                  Контролируйте точность отображения вашего местоположения
                </Typography>
                <Button variant="outlined" href="/settings">
                  Настроить
                </Button>
              </EnhancedCard>
            </Grid>
          </Grid>
        </Box>

        {/* Emergency Contacts */}
        <Paper sx={{ mt: 6, p: 4, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <FaPhoneAlt style={{ marginRight: '12px', fontSize: '1.5rem' }} />
            <Typography variant="h5" fontWeight="bold">
              Экстренные контакты
            </Typography>
          </Box>
          
          <Grid container spacing={3}>
            {emergencyContacts.map((contact, index) => (
              <Grid item xs={12} sm={4} key={index}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" fontWeight="bold" sx={{ mb: 1 }}>
                    {contact.number}
                  </Typography>
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    {contact.service}
                  </Typography>
                  <Typography variant="body2">
                    {contact.description}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Paper>

        {/* Bottom CTA */}
        <Box sx={{ textAlign: 'center', mt: 6, p: 4, bgcolor: 'background.paper', borderRadius: 2 }}>
          <FaShieldAlt style={{ fontSize: '3rem', color: '#4caf50', marginBottom: '1rem' }} />
          <EnhancedTypography variant="h5" romantic sx={{ mb: 2 }}>
            Нужна помощь?
          </EnhancedTypography>
          <EnhancedTypography variant="body1" readable sx={{ mb: 3 }}>
            Наша команда безопасности работает круглосуточно
          </EnhancedTypography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <EnhancedButton
              variant="contained"
              size="large"
              romantic
              large
              href="mailto:<EMAIL>"
            >
              Сообщить о проблеме
            </EnhancedButton>
            <Button
              variant="outlined"
              size="large"
              href="/help"
            >
              Центр помощи
            </Button>
          </Box>
        </Box>
      </Container>
    </Layout>
  );
};

export default SafetyPage;
