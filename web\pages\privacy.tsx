import React from 'react';
import Head from 'next/head';
import { Box, Typography, Container, <PERSON>, Card } from '@mui/material';
import Layout from '../components/Layout/Layout';
import { FaShieldAlt, FaLock } from 'react-icons/fa';

const PrivacyPage = () => {
  return (
    <>
      <Head>
        <title>Политика конфиденциальности - Likes Love | Защита ваших данных</title>
        <meta name="description" content="Политика конфиденциальности Likes Love. Узнайте, как мы собираем, используем и защищаем ваши персональные данные в соответствии с российским и международным законодательством." />
        <meta name="keywords" content="политика конфиденциальности, защита данных, приватность, GDPR, персональные данные, безопасность" />
        <link rel="canonical" href="https://likes-love.com/privacy" />
        
        <meta property="og:title" content="Политика конфиденциальности - Likes Love" />
        <meta property="og:description" content="Узнайте, как мы защищаем ваши персональные данные" />
        <meta property="og:url" content="https://likes-love.com/privacy" />
        <meta property="og:type" content="website" />
        
        <meta name="robots" content="index, follow" />
      </Head>
      <Layout showHeader={true} showFooter={true}>
        <Box
          sx={{
            py: 8,
            background: (theme) => 
              `linear-gradient(135deg, ${theme.palette.primary.light}20 0%, ${theme.palette.secondary.light}20 100%)`
          }}
        >
          <Container maxWidth="md">
            <Box textAlign="center" mb={6}>
              <Box display="flex" alignItems="center" justifyContent="center" mb={2}>
                <FaLock size={48} style={{ marginRight: 16, color: '#FF6B9D' }} />
                <Typography
                  variant="h3"
                  fontWeight="bold"
                  sx={{
                    background: (theme) => theme.palette.background.gradient,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  Политика конфиденциальности
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Последнее обновление: 19 декабря 2024 года
              </Typography>
            </Box>

            <Card sx={{ p: 4, mb: 4 }}>
              <Typography variant="h5" gutterBottom fontWeight="bold">
                1. Сбор информации
              </Typography>
              <Typography paragraph>
                Мы собираем следующую информацию:
              </Typography>
              <ul>
                <li>Основные данные профиля (имя, email, дата рождения)</li>
                <li>Информация для подбора партнеров (интересы, предпочтения)</li>
                <li>Технические данные (IP-адрес, тип устройства, cookies)</li>
              </ul>

              <Typography variant="h5" gutterBottom fontWeight="bold">
                2. Использование информации
              </Typography>
              <Typography paragraph>
                Ваши данные используются для:
              </Typography>
              <ul>
                <li>Предоставления и улучшения наших услуг</li>
                <li>Персонализации вашего опыта</li>
                <li>Обеспечения безопасности и предотвращения мошенничества</li>
                <li>Коммуникации с вами</li>
              </ul>

              <Typography variant="h5" gutterBottom fontWeight="bold">
                3. Защита данных
              </Typography>
              <Typography paragraph>
                Мы используем современные методы защиты данных, включая:
              </Typography>
              <ul>
                <li>Шифрование передаваемых данных</li>
                <li>Регулярные проверки безопасности</li>
                <li>Ограниченный доступ к персональным данным</li>
              </ul>

              <Typography variant="h5" gutterBottom fontWeight="bold">
                4. Ваши права
              </Typography>
              <Typography paragraph>
                Вы имеете право:
              </Typography>
              <ul>
                <li>Запросить доступ к вашим данным</li>
                <li>Исправить неточности</li>
                <li>Удалить ваш аккаунт и данные</li>
                <li>Отказаться от маркетинговых рассылок</li>
              </ul>

              <Typography variant="h5" gutterBottom fontWeight="bold">
                5. Конфиденциальность пользователей
              </Typography>
              <Typography paragraph>
                Мы гарантируем полную конфиденциальность:
              </Typography>
              <ul>
                <li>Пользовательские страницы недоступны поисковым системам</li>
                <li>Личная информация не индексируется</li>
                <li>Профили видны только авторизованным пользователям</li>
                <li>Имена и фото защищены от публичного доступа</li>
              </ul>

              <Box mt={4} display="flex" alignItems="center">
                <FaShieldAlt size={24} style={{ marginRight: 12, color: '#4ECDC4' }} />
                <Typography variant="body2" color="text.secondary">
                  Используя наш сервис, вы соглашаетесь с нашей Политикой конфиденциальности.
                  Подробнее в <Link href="/terms" color="primary">Условиях использования</Link>.
                </Typography>
              </Box>
            </Card>
          </Container>
        </Box>
      </Layout>
    </>
  );
};

export default PrivacyPage;
