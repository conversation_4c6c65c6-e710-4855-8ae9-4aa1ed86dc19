import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  Badge,
  IconButton,
  Menu,
  MenuItem,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Archive as ArchiveIcon,
  Block as BlockIcon,
  Report as ReportIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import {
  getMatches,
  markMatchesAsRead,
  archiveMatch
} from '../../src/services/likesService';
import { Match, FilterOptions } from '../../src/types/likes.types';
const MatchesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [archiving, setArchiving] = useState<string | null>(null);

  // Pagination and filters
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState<'matchedAt' | 'lastMessageAt' | 'compatibility'>('matchedAt');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [filterRead, setFilterRead] = useState<'all' | 'unread' | 'read'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'archived'>('active');
  const [filterVerified, setFilterVerified] = useState<boolean | undefined>(undefined);
  const [filterOnline, setFilterOnline] = useState<boolean | undefined>(undefined);

  const limit = 20;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadMatches();
  }, [user, router, page, sortBy, sortOrder, filterRead, filterStatus, filterVerified, filterOnline]);

  const loadMatches = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {
        page,
        limit,
        sortBy,
        sortOrder,
        verifiedOnly: filterVerified,
        onlineOnly: filterOnline,
        status: filterStatus === 'all' ? undefined : filterStatus as any
      };

      if (filterRead !== 'all') {
        filters.isRead = filterRead === 'read';
      }

      const response = await getMatches(filters);
      setMatches(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      setError('Ошибка загрузки совпадений');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, match: Match) => {
    setAnchorEl(event.currentTarget);
    setSelectedMatch(match);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedMatch(null);
  };

  const handleViewMatch = (matchId: string) => {
    router.push(`/matches/${matchId}`);
  };

  const handleViewProfile = (userId: string) => {
    router.push(`/users/${userId}`);
    handleMenuClose();
  };

  const handleSendMessage = (matchId: string) => {
    router.push(`/matches/${matchId}?action=message`);
    handleMenuClose();
  };

  const handleArchiveMatch = async (match: Match) => {
    try {
      setArchiving(match.id);
      await archiveMatch(match.id);

      setSuccess('Совпадение архивировано');
      setMatches(prev => prev.filter(m => m.id !== match.id));
      handleMenuClose();
    } catch (err: any) {
      setError('Ошибка архивирования совпадения');
    } finally {
      setArchiving(null);
    }
  };

  const handleMarkAsRead = async (matchIds: string[]) => {
    try {
      await markMatchesAsRead(matchIds);
      setMatches(prev => prev.map(match =>
        matchIds.includes(match.id) ? { ...match, isRead: true } : match
      ));
    } catch (err: any) {
      setError('Ошибка отметки как прочитанное');
    }
  };

  const formatTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getOtherUser = (match: Match) => {
    return match.user1Id === user?.id ? match.user2 : match.user1;
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const unreadCount = matches.filter(m => !m.isRead).length;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Совпадения - Likes & Love</title>
        <meta
          name="description"
          content="Ваши совпадения в приложении знакомств Likes & Love"
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/matches" />
      </Head>

      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <Badge badgeContent={unreadCount} color="primary">
                      <FavoriteIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'error.main' }} />
                    </Badge>
                    Совпадения
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {matches.length > 0 ? `${matches.length} совпадений` : 'Пока нет совпадений'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes/mutual')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Взаимные лайки
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/discover')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Найти еще
                  </Button>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {success}
                </Alert>
              )}

              {/* Filters */}
              <Box sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Сортировка</InputLabel>
                      <Select
                        value={`${sortBy}-${sortOrder}`}
                        label="Сортировка"
                        onChange={(e) => {
                          const [newSortBy, newSortOrder] = e.target.value.split('-');
                          setSortBy(newSortBy as any);
                          setSortOrder(newSortOrder as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="matchedAt-desc">Сначала новые</MenuItem>
                        <MenuItem value="lastMessageAt-desc">По последнему сообщению</MenuItem>
                        <MenuItem value="compatibility-desc">По совместимости</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Статус</InputLabel>
                      <Select
                        value={filterStatus}
                        label="Статус"
                        onChange={(e) => {
                          setFilterStatus(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="active">Активные</MenuItem>
                        <MenuItem value="archived">Архивированные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Прочитанность</InputLabel>
                      <Select
                        value={filterRead}
                        label="Прочитанность"
                        onChange={(e) => {
                          setFilterRead(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="unread">Новые</MenuItem>
                        <MenuItem value="read">Просмотренные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Активность</InputLabel>
                      <Select
                        value={filterOnline === undefined ? 'all' : filterOnline ? 'online' : 'offline'}
                        label="Активность"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFilterOnline(
                            value === 'all' ? undefined : value === 'online'
                          );
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="online">Онлайн</MenuItem>
                        <MenuItem value="offline">Оффлайн</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка совпадений...
                  </Typography>
                </Box>
              ) : matches.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <FavoriteIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    У вас пока нет совпадений
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Начните ставить лайки, чтобы найти совпадения
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/discover')}
                  >
                    Начать знакомства
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Matches grid */}
                    <Grid container spacing={2}>
                      {matches.map((match) => {
                        const otherUser = getOtherUser(match);

                        return (
                          <Grid item xs={12} sm={6} md={4} lg={3} key={match.id}>
                            <Card
                              sx={{
                                position: 'relative',
                                border: !match.isRead ? `2px solid ${theme.palette.primary.main}` : 'none',
                                cursor: 'pointer',
                                '&:hover': {
                                  boxShadow: theme.shadows[4]
                                }
                              }}
                              onClick={() => handleViewMatch(match.id)}
                            >
                              {!match.isRead && (
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: 8,
                                    left: 8,
                                    width: 12,
                                    height: 12,
                                    borderRadius: '50%',
                                    backgroundColor: theme.palette.primary.main,
                                    zIndex: 1
                                  }}
                                />
                              )}

                              {match.unreadMessagesCount > 0 && (
                                <Badge
                                  badgeContent={match.unreadMessagesCount}
                                  color="error"
                                  sx={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    zIndex: 1
                                  }}
                                />
                              )}

                              <CardContent sx={{ pb: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                  <Badge
                                    overlap="circular"
                                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                                    badgeContent={
                                      otherUser.isOnline ? (
                                        <Box
                                          sx={{
                                            width: 12,
                                            height: 12,
                                            borderRadius: '50%',
                                            backgroundColor: 'success.main',
                                            border: '2px solid white'
                                          }}
                                        />
                                      ) : null
                                    }
                                  >
                                    <Avatar
                                      src={otherUser.avatarUrl}
                                      sx={{ width: 56, height: 56 }}
                                    >
                                      {otherUser.firstName[0]}
                                    </Avatar>
                                  </Badge>
                                  <Box sx={{ ml: 2, flexGrow: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      <Typography variant="subtitle2" noWrap>
                                        {otherUser.firstName}
                                      </Typography>
                                      {otherUser.verificationStatus.phone && (
                                        <VerifiedIcon
                                          sx={{
                                            ml: 0.5,
                                            fontSize: 16,
                                            color: 'primary.main'
                                          }}
                                        />
                                      )}
                                    </Box>
                                    <Typography variant="caption" color="text.secondary">
                                      {otherUser.age} лет
                                    </Typography>
                                  </Box>
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleMenuOpen(e, match);
                                    }}
                                  >
                                    <MoreVertIcon />
                                  </IconButton>
                                </Box>

                                <Box sx={{ mb: 1 }}>
                                  <Chip
                                    icon={<CheckCircleIcon />}
                                    label="Совпадение"
                                    size="small"
                                    color="success"
                                    sx={{ mr: 1 }}
                                  />
                                  <Chip
                                    label={`${match.compatibilityScore}% совместимость`}
                                    size="small"
                                    color={getCompatibilityColor(match.compatibilityScore) as any}
                                    variant="outlined"
                                  />
                                </Box>

                                <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
                                  Совпадение: {formatTime(match.matchedAt)}
                                </Typography>

                                {match.lastMessage && (
                                  <Box sx={{ mb: 1 }}>
                                    <Typography variant="body2" noWrap sx={{ fontStyle: 'italic' }}>
                                      "{match.lastMessage.text}"
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {formatTime(match.lastMessage.sentAt)}
                                    </Typography>
                                  </Box>
                                )}

                                {match.commonInterests.length > 0 && (
                                  <Typography variant="caption" color="text.secondary">
                                    Общие интересы: {match.commonInterests.slice(0, 2).join(', ')}
                                    {match.commonInterests.length > 2 && ` +${match.commonInterests.length - 2}`}
                                  </Typography>
                                )}
                              </CardContent>

                              <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
                                <Button
                                  size="small"
                                  variant="contained"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSendMessage(match.id);
                                  }}
                                  startIcon={<MessageIcon />}
                                  fullWidth
                                >
                                  {match.lastMessage ? 'Продолжить чат' : 'Написать'}
                                </Button>
                              </CardActions>
                            </Card>
                          </Grid>
                        );
                      })}
                    </Grid>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Pagination
                          count={totalPages}
                          page={page}
                          onChange={(_, newPage) => setPage(newPage)}
                          color="primary"
                          size={isMobile ? "small" : "medium"}
                        />
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}

              {/* Tips */}
              {matches.length > 0 && (
                <Alert severity="info" sx={{ mt: 4 }}>
                  <Typography variant="body2">
                    💡 <strong>Совет:</strong> Начните разговор с упоминания общих интересов или
                    задайте вопрос о фотографиях в профиле. Это поможет завязать интересную беседу!
                  </Typography>
                </Alert>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Context menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedMatch && handleViewMatch(selectedMatch.id)}>
            <VisibilityIcon sx={{ mr: 1 }} />
            Открыть совпадение
          </MenuItem>
          <MenuItem onClick={() => selectedMatch && handleSendMessage(selectedMatch.id)}>
            <MessageIcon sx={{ mr: 1 }} />
            Написать сообщение
          </MenuItem>
          <MenuItem onClick={() => {
            const otherUser = selectedMatch ? getOtherUser(selectedMatch) : null;
            if (otherUser) handleViewProfile(otherUser.id);
          }}>
            <VisibilityIcon sx={{ mr: 1 }} />
            Посмотреть профиль
          </MenuItem>
          <MenuItem
            onClick={() => selectedMatch && handleArchiveMatch(selectedMatch)}
            disabled={archiving === selectedMatch?.id}
          >
            <ArchiveIcon sx={{ mr: 1 }} />
            Архивировать
          </MenuItem>
          <MenuItem onClick={handleMenuClose}>
            <ReportIcon sx={{ mr: 1 }} />
            Пожаловаться
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default MatchesPage;
