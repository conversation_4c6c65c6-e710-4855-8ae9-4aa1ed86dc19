import React, { useState, useEffect, useRef } from 'react';
import { NextPage } from 'next';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Paper,
  TextField,
  IconButton,
  Avatar,
  Chip,
  Button,
  Alert,
  Skeleton,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Popover,
  Grid,
  Input,
  LinearProgress,
  Tooltip,
  Breadcrumbs,
  Link
} from '@mui/material';
import { RomanticChatTheme, MessageBubble, useRomanticTheme } from '../../components/Chat/RomanticChatTheme';
import { AutomatedUserService } from '../../src/services/AutomatedUserService';
import {
  Send as SendIcon,
  ArrowBack as ArrowBackIcon,
  MoreVert as MoreVertIcon,
  Lock as LockIcon,
  Shield as ShieldIcon,
  Photo as PhotoIcon,
  AttachFile as AttachFileIcon,
  EmojiEmotions as EmojiIcon,
  Videocam as VideocamIcon,
  Call as CallIcon,
  Block as BlockIcon,
  Report as ReportIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: Date;
  isEncrypted: boolean;
  messageType: 'text' | 'image' | 'file';
  status: 'sent' | 'delivered' | 'read';
  encryptionKey?: string;
}

interface ChatUser {
  id: string;
  displayName: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen: Date;
  isVerified: boolean;
  subscription: {
    status: 'active' | 'inactive';
    planId: string;
  };
}

// Популярные эмодзи для быстрого доступа
const POPULAR_EMOJIS = [
  '😊', '😍', '🥰', '😘', '😉', '😂', '🤣', '😭', '🥺', '😢',
  '❤️', '💕', '💖', '💗', '💙', '💜', '🧡', '💛', '💚', '🖤',
  '👍', '👎', '👌', '✌️', '🤞', '🤟', '👏', '🙌', '🤝', '🙏',
  '🔥', '💯', '⭐', '✨', '💫', '🌟', '🎉', '🎊', '🎈', '🎁',
  '😴', '😪', '🥱', '😌', '😇', '🤗', '🤔', '🤨', '😏', '😎'
];

const ChatWithUserPage: NextPage = () => {
  const router = useRouter();
  const { userId } = router.query;
  const { user } = useAuth();
  const [chatUser, setChatUser] = useState<ChatUser | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [partnerProfile, setPartnerProfile] = useState<any>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [encryptionDialog, setEncryptionDialog] = useState(false);
  const [emojiAnchor, setEmojiAnchor] = useState<null | HTMLElement>(null);
  const [fileUpload, setFileUpload] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const photoInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (userId) {
      loadChatData(userId as string);
    }
  }, [userId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadChatData = async (targetUserId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Mock данные для демонстрации
      const mockUser: ChatUser = {
        id: targetUserId,
        displayName: targetUserId === 'user1' ? 'Анна Петрова' : targetUserId === 'user2' ? 'Мария Сидорова' : 'Елена Козлова',
        avatar: '/images/avatars/anna.jpg',
        isOnline: Math.random() > 0.5,
        lastSeen: new Date(Date.now() - Math.random() * 3600000),
        isVerified: Math.random() > 0.3,
        subscription: {
          status: Math.random() > 0.5 ? 'active' : 'inactive',
          planId: 'premium'
        }
      };

      const mockMessages: Message[] = [
        {
          id: 'msg1',
          senderId: targetUserId,
          receiverId: 'currentUser',
          content: 'Привет! Как дела?',
          timestamp: new Date(Date.now() - 3600000),
          isEncrypted: true,
          messageType: 'text',
          status: 'read'
        },
        {
          id: 'msg2',
          senderId: 'currentUser',
          receiverId: targetUserId,
          content: 'Привет! Все отлично, спасибо! А у тебя как?',
          timestamp: new Date(Date.now() - 3000000),
          isEncrypted: true,
          messageType: 'text',
          status: 'read'
        },
        {
          id: 'msg3',
          senderId: targetUserId,
          receiverId: 'currentUser',
          content: 'Тоже хорошо! Хочешь встретиться на выходных?',
          timestamp: new Date(Date.now() - 1800000),
          isEncrypted: true,
          messageType: 'text',
          status: 'read'
        },
        {
          id: 'msg4',
          senderId: 'currentUser',
          receiverId: targetUserId,
          content: 'Да, было бы здорово! Где предлагаешь?',
          timestamp: new Date(Date.now() - 900000),
          isEncrypted: true,
          messageType: 'text',
          status: 'delivered'
        }
      ];

      // Симуляция загрузки
      await new Promise(resolve => setTimeout(resolve, 1000));

      setChatUser(mockUser);
      setMessages(mockMessages);
    } catch (err) {
      setError('Ошибка загрузки чата');
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !chatUser) return;

    const message: Message = {
      id: `msg_${Date.now()}`,
      senderId: 'currentUser',
      receiverId: chatUser.id,
      content: newMessage,
      timestamp: new Date(),
      isEncrypted: true,
      messageType: 'text',
      status: 'sent'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // Симуляция отправки сообщения
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === message.id 
            ? { ...msg, status: 'delivered' as const }
            : msg
        )
      );
    }, 1000);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('ru-RU', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatLastSeen = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    
    if (minutes < 60) return `был(а) ${minutes} мин назад`;
    if (hours < 24) return `был(а) ${hours} ч назад`;
    return `был(а) ${Math.floor(hours / 24)} дн назад`;
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleVideoCall = () => {
    console.log('Видеозвонок с', chatUser?.displayName);
    handleMenuClose();
  };

  const handleVoiceCall = () => {
    console.log('Голосовой звонок с', chatUser?.displayName);
    handleMenuClose();
  };

  const handleBlock = () => {
    console.log('Заблокировать', chatUser?.displayName);
    handleMenuClose();
  };

  const handleReport = () => {
    console.log('Пожаловаться на', chatUser?.displayName);
    handleMenuClose();
  };

  const handleEmojiClick = (emoji: string) => {
    setNewMessage(prev => prev + emoji);
    setEmojiAnchor(null);
  };

  const handleEmojiOpen = (event: React.MouseEvent<HTMLElement>) => {
    setEmojiAnchor(event.currentTarget);
  };

  const handleEmojiClose = () => {
    setEmojiAnchor(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileUpload(file);
      uploadFile(file, 'file');
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setFileUpload(file);
      uploadFile(file, 'image');
    }
  };

  const uploadFile = async (file: File, type: 'file' | 'image') => {
    setIsUploading(true);
    setUploadProgress(0);

    // Симуляция загрузки файла
    const uploadInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(uploadInterval);
          setIsUploading(false);

          // Создаем сообщение с файлом
          const fileMessage: Message = {
            id: `msg_${Date.now()}`,
            senderId: 'currentUser',
            receiverId: chatUser?.id || '',
            content: type === 'image' ? `📷 ${file.name}` : `📎 ${file.name}`,
            timestamp: new Date(),
            isEncrypted: true,
            messageType: type,
            status: 'sent'
          };

          setMessages(prev => [...prev, fileMessage]);
          setFileUpload(null);

          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleAttachFile = () => {
    fileInputRef.current?.click();
  };

  const handleAttachPhoto = () => {
    photoInputRef.current?.click();
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка чата - Likes Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Layout>
          <Container maxWidth="md" sx={{ py: 4 }}>
            <Skeleton variant="rectangular" height={60} sx={{ mb: 2 }} />
            <Skeleton variant="rectangular" height={400} sx={{ mb: 2 }} />
            <Skeleton variant="rectangular" height={60} />
          </Container>
        </Layout>
      </>
    );
  }

  if (error || !chatUser) {
    return (
      <>
        <Head>
          <title>Ошибка - Likes Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Layout>
          <Container maxWidth="md" sx={{ py: 4 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              {error || 'Чат не найден'}
            </Alert>
            <Button onClick={() => router.push('/chats')} startIcon={<ArrowBackIcon />}>
              К списку чатов
            </Button>
          </Container>
        </Layout>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Чат с {chatUser.displayName} - Likes Love</title>
        <meta name="description" content={`Безопасный зашифрованный чат с ${chatUser.displayName}`} />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout>
        <RomanticChatTheme
          isActive={true}
          userGender={userProfile?.gender}
          partnerGender={partnerProfile?.gender}
        >
          <Container maxWidth="md" sx={{ py: 2 }}>
            {/* Breadcrumb навигация */}
            <Breadcrumbs sx={{ mb: 2 }}>
              <Link
                color="inherit"
                href="/dashboard"
                sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
              >
                Главная
              </Link>
              <Link
                color="inherit"
                href="/chats"
                sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
              >
                Чаты
              </Link>
              <Typography color="text.primary">
                {chatUser.displayName}
              </Typography>
            </Breadcrumbs>

          {/* Заголовок чата */}
          <Paper sx={{ mb: 2, p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <IconButton
                  onClick={() => router.push('/chats')}
                  sx={{
                    color: 'primary.main',
                    '&:hover': {
                      backgroundColor: 'primary.light',
                      color: 'white'
                    }
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
                
                <Avatar src={chatUser.avatar} sx={{ width: 40, height: 40 }} />
                
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="h6" fontWeight="bold">
                      {chatUser.displayName}
                    </Typography>
                    {chatUser.isVerified && (
                      <Chip
                        icon={<ShieldIcon />}
                        label="Проверен"
                        size="small"
                        color="primary"
                      />
                    )}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {chatUser.isOnline ? 'Онлайн' : formatLastSeen(chatUser.lastSeen)}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  icon={<LockIcon />}
                  label="E2E шифрование"
                  size="small"
                  color="success"
                  onClick={() => setEncryptionDialog(true)}
                />
                <IconButton onClick={handleMenuOpen}>
                  <MoreVertIcon />
                </IconButton>
              </Box>
            </Box>
          </Paper>

          {/* Сообщения */}
          <Paper sx={{ height: 400, overflow: 'auto', p: 2, mb: 2 }}>
            {messages.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.senderId === 'currentUser' ? 'flex-end' : 'flex-start',
                  mb: 2
                }}
              >
                <MessageBubble
                  isOwn={message.senderId === 'currentUser'}
                  isTyping={false}
                >
                  {message.messageType === 'image' ? (
                    <Box>
                      <Box
                        component="img"
                        src="/images/placeholder-image.jpg"
                        alt="Изображение"
                        sx={{
                          maxWidth: 200,
                          maxHeight: 200,
                          borderRadius: 1,
                          mb: 1,
                          cursor: 'pointer',
                          '&:hover': { opacity: 0.8 }
                        }}
                        onClick={() => console.log('Открыть изображение')}
                      />
                      <Typography variant="caption" display="block">
                        {message.content}
                      </Typography>
                    </Box>
                  ) : message.messageType === 'file' ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AttachFileIcon sx={{ fontSize: 16 }} />
                      <Typography variant="body2" sx={{ textDecoration: 'underline', cursor: 'pointer' }}>
                        {message.content}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body1">
                      {message.content}
                    </Typography>
                  )}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                    <Typography variant="caption" sx={{ opacity: 0.7 }}>
                      {formatTime(message.timestamp)}
                    </Typography>
                    {message.isEncrypted && (
                      <LockIcon sx={{ fontSize: 12, opacity: 0.7 }} />
                    )}
                    {message.senderId === 'currentUser' && (
                      <Typography variant="caption" sx={{ opacity: 0.7 }}>
                        {message.status === 'sent' && '✓'}
                        {message.status === 'delivered' && '✓✓'}
                        {message.status === 'read' && '✓✓'}
                      </Typography>
                    )}
                  </Box>
                </MessageBubble>
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Paper>

          {/* Поле ввода */}
          <Paper sx={{ p: 2 }}>
            {/* Прогресс загрузки файла */}
            {isUploading && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Загрузка файла: {fileUpload?.name}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{ mt: 1 }}
                />
              </Box>
            )}

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Прикрепить файл">
                <IconButton onClick={handleAttachFile} disabled={isUploading}>
                  <AttachFileIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Прикрепить фото">
                <IconButton onClick={handleAttachPhoto} disabled={isUploading}>
                  <PhotoIcon />
                </IconButton>
              </Tooltip>

              <TextField
                fullWidth
                multiline
                maxRows={3}
                placeholder="Введите сообщение..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                variant="outlined"
                size="small"
                disabled={isUploading}
              />

              <Tooltip title="Эмодзи">
                <IconButton onClick={handleEmojiOpen} disabled={isUploading}>
                  <EmojiIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Отправить">
                <IconButton
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim() || isUploading}
                  color="primary"
                >
                  <SendIcon />
                </IconButton>
              </Tooltip>
            </Box>

            {/* Скрытые input для загрузки файлов */}
            <Input
              type="file"
              inputRef={fileInputRef}
              onChange={handleFileUpload}
              sx={{ display: 'none' }}
              inputProps={{ accept: '*/*' }}
            />

            <Input
              type="file"
              inputRef={photoInputRef}
              onChange={handlePhotoUpload}
              sx={{ display: 'none' }}
              inputProps={{ accept: 'image/*' }}
            />
          </Paper>
        </Container>

        {/* Меню действий */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleVideoCall}>
            <VideocamIcon sx={{ mr: 1 }} />
            Видеозвонок
          </MenuItem>
          <MenuItem onClick={handleVoiceCall}>
            <CallIcon sx={{ mr: 1 }} />
            Голосовой звонок
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleBlock}>
            <BlockIcon sx={{ mr: 1 }} />
            Заблокировать
          </MenuItem>
          <MenuItem onClick={handleReport}>
            <ReportIcon sx={{ mr: 1 }} />
            Пожаловаться
          </MenuItem>
        </Menu>

        {/* Попап с эмодзи */}
        <Popover
          open={Boolean(emojiAnchor)}
          anchorEl={emojiAnchor}
          onClose={handleEmojiClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
        >
          <Box sx={{ p: 2, maxWidth: 300 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Популярные эмодзи
            </Typography>
            <Grid container spacing={0.5}>
              {POPULAR_EMOJIS.map((emoji, index) => (
                <Grid item key={index}>
                  <IconButton
                    size="small"
                    onClick={() => handleEmojiClick(emoji)}
                    sx={{
                      fontSize: '1.2rem',
                      '&:hover': {
                        backgroundColor: 'action.hover',
                        transform: 'scale(1.2)'
                      }
                    }}
                  >
                    {emoji}
                  </IconButton>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Popover>

        {/* Диалог о шифровании */}
        <Dialog open={encryptionDialog} onClose={() => setEncryptionDialog(false)}>
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LockIcon color="success" />
              End-to-End шифрование
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography paragraph>
              Ваши сообщения защищены сквозным шифрованием. Только вы и {chatUser.displayName} можете их прочитать.
            </Typography>
            <Typography paragraph>
              • Сообщения шифруются на вашем устройстве
            </Typography>
            <Typography paragraph>
              • Даже мы не можем их прочитать
            </Typography>
            <Typography paragraph>
              • Ключи шифрования хранятся только на ваших устройствах
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEncryptionDialog(false)}>
              Понятно
            </Button>
          </DialogActions>
        </Dialog>
        </RomanticChatTheme>
      </Layout>
    </>
  );
};

export default ChatWithUserPage;
