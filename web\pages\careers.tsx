import React from 'react';
import Head from 'next/head';
import { Box, Typography, Grid, Container, Chip } from '@mui/material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaHeart, FaCode, FaUsers, FaRocket, FaGlobe, FaLaptop } from 'react-icons/fa';

const CareersPage: React.FC = () => {
  const jobOpenings = [
    {
      title: 'Senior Frontend Developer',
      department: 'Разработка',
      location: 'Москва / Удаленно',
      type: 'Полная занятость',
      description: 'Разработка пользовательского интерфейса для платформы знакомств с использованием React, Next.js и TypeScript.',
      requirements: ['React/Next.js', 'TypeScript', 'Material-UI', '3+ лет опыта'],
      salary: '200,000 - 350,000 ₽'
    },
    {
      title: 'Backend Developer (Node.js)',
      department: 'Разработка',
      location: 'Москва / Удаленно',
      type: 'Полная занятость',
      description: 'Разработка серверной части приложения, API и интеграций с внешними сервисами.',
      requirements: ['Node.js/NestJS', 'PostgreSQL', 'Redis', '2+ лет опыта'],
      salary: '180,000 - 320,000 ₽'
    },
    {
      title: 'UX/UI Designer',
      department: 'Дизайн',
      location: 'Москва',
      type: 'Полная занятость',
      description: 'Создание интуитивных и привлекательных интерфейсов для мобильного приложения и веб-платформы.',
      requirements: ['Figma', 'Adobe Creative Suite', 'Опыт в dating apps', '2+ лет опыта'],
      salary: '150,000 - 280,000 ₽'
    }
  ];

  const benefits = [
    {
      icon: <FaHeart />,
      title: 'Работа мечты',
      description: 'Помогаем людям находить любовь и создавать счастливые отношения'
    },
    {
      icon: <FaRocket />,
      title: 'Быстрый рост',
      description: 'Возможности карьерного роста в динамично развивающейся компании'
    },
    {
      icon: <FaLaptop />,
      title: 'Гибкий график',
      description: 'Удаленная работа и гибкий график для лучшего work-life balance'
    }
  ];

  return (
    <Layout>
      <Head>
        <title>Карьера в Likes & Love - Присоединяйтесь к нашей команде</title>
        <meta name="description" content="Работа в Likes & Love. Открытые вакансии, конкурентная зарплата, отличные условия." />
      </Head>

      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff6b9d 0%, #c44569 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          textAlign: 'center'
        }}
      >
        <Container maxWidth="lg">
          <EnhancedTypography variant="h1" romantic gradient shadow sx={{ mb: 3, color: 'white' }}>
            Присоединяйтесь к команде Likes & Love
          </EnhancedTypography>
          <EnhancedTypography variant="h5" readable sx={{ mb: 4, opacity: 0.95, maxWidth: '800px', mx: 'auto' }}>
            Помогайте миллионам людей находить любовь и строить счастливые отношения.
          </EnhancedTypography>
          <EnhancedButton
            variant="contained"
            size="large"
            romantic
            large
            href="#vacancies"
            sx={{
              bgcolor: 'rgba(255,255,255,0.2)',
              color: 'white',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
            }}
          >
            Посмотреть вакансии
          </EnhancedButton>
        </Container>
      </Box>

      {/* Benefits Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
          Почему стоит работать с нами?
        </EnhancedTypography>
        
        <Grid container spacing={4}>
          {benefits.map((benefit, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <EnhancedCard romantic hoverable glassmorphism sx={{ textAlign: 'center', height: '100%' }}>
                <Box sx={{ color: 'primary.main', mb: 2, fontSize: '2.5rem' }}>
                  {benefit.icon}
                </Box>
                <EnhancedTypography variant="h5" romantic sx={{ mb: 2 }}>
                  {benefit.title}
                </EnhancedTypography>
                <EnhancedTypography variant="body1" readable color="text.secondary">
                  {benefit.description}
                </EnhancedTypography>
              </EnhancedCard>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Job Openings Section */}
      <Box sx={{ bgcolor: 'background.default', py: 8 }} id="vacancies">
        <Container maxWidth="lg">
          <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
            Открытые вакансии
          </EnhancedTypography>
          
          <Grid container spacing={4}>
            {jobOpenings.map((job, index) => (
              <Grid item xs={12} md={6} key={index}>
                <EnhancedCard romantic hoverable sx={{ height: '100%' }}>
                  <Box sx={{ mb: 2 }}>
                    <EnhancedTypography variant="h5" romantic sx={{ mb: 1 }}>
                      {job.title}
                    </EnhancedTypography>
                    <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                      <Chip label={job.department} color="primary" size="small" />
                      <Chip label={job.type} color="secondary" size="small" />
                      <Chip label={job.location} variant="outlined" size="small" />
                    </Box>
                  </Box>
                  
                  <EnhancedTypography variant="body1" readable sx={{ mb: 3 }}>
                    {job.description}
                  </EnhancedTypography>
                  
                  <Box sx={{ mb: 3 }}>
                    <EnhancedTypography variant="h6" sx={{ mb: 1, fontSize: '1rem', fontWeight: 600 }}>
                      Требования:
                    </EnhancedTypography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {job.requirements.map((req, reqIndex) => (
                        <Chip key={reqIndex} label={req} variant="outlined" size="small" />
                      ))}
                    </Box>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <EnhancedTypography variant="h6" romantic sx={{ fontSize: '1.1rem' }}>
                      {job.salary}
                    </EnhancedTypography>
                    <EnhancedButton
                      variant="contained"
                      romantic
                      href={`mailto:<EMAIL>?subject=Отклик на вакансию: ${job.title}`}
                    >
                      Откликнуться
                    </EnhancedButton>
                  </Box>
                </EnhancedCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Contact Section */}
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <EnhancedTypography variant="h2" romantic gradient shadow sx={{ mb: 4 }}>
          Не нашли подходящую вакансию?
        </EnhancedTypography>
        <EnhancedTypography variant="h5" readable sx={{ mb: 4, maxWidth: '600px', mx: 'auto' }}>
          Отправьте нам свое резюме! Мы всегда ищем талантливых людей.
        </EnhancedTypography>
        <EnhancedButton
          variant="contained"
          size="large"
          romantic
          large
          href="mailto:<EMAIL>?subject=Инициативное резюме"
        >
          Отправить резюме
        </EnhancedButton>
      </Container>
    </Layout>
  );
};

export default CareersPage;
