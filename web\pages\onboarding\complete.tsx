import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Stack,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Fade,
  Slide,
  Confetti
} from '@mui/material';
import {
  CheckCircle,
  Celebration,
  Explore,
  Settings,
  Share,
  Star,
  Favorite,
  PhotoCamera,
  Person,
  Tune
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import OnboardingProgress from '../../components/Onboarding/OnboardingProgress';
import { useAuth } from '../../src/providers/AuthProvider';
import { useOnboarding } from '../../src/contexts/OnboardingContext';

const CompletePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    data, 
    steps, 
    currentStep, 
    completeOnboarding,
    loading: onboardingLoading 
  } = useOnboarding();

  const [completing, setCompleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    
    // Показываем конфетти при загрузке страницы
    setShowConfetti(true);
    const timer = setTimeout(() => setShowConfetti(false), 3000);
    return () => clearTimeout(timer);
  }, [user, router]);

  const handleStartExploring = async () => {
    try {
      setCompleting(true);
      setError(null);
      
      await completeOnboarding();
      
      // Перенаправляем на главную страницу приложения
      router.push('/discover');
    } catch (err: any) {
      setError('Ошибка завершения регистрации. Попробуйте еще раз.');
      setCompleting(false);
    }
  };

  const handleEditProfile = () => {
    router.push('/profile/edit');
  };

  const handleShareProfile = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Likes & Love',
        text: 'Присоединяйтесь ко мне в приложении для знакомств Likes & Love!',
        url: window.location.origin
      });
    }
  };

  const getCompletionStats = () => {
    const stats = [
      {
        icon: <Person color="primary" />,
        title: 'Профиль',
        value: data.basicInfo.firstName ? '✓ Заполнен' : '⚠ Не заполнен',
        color: data.basicInfo.firstName ? 'success' : 'warning'
      },
      {
        icon: <PhotoCamera color="primary" />,
        title: 'Фотографии',
        value: `${data.photos.photos.length} фото`,
        color: data.photos.photos.length >= 2 ? 'success' : 'warning'
      },
      {
        icon: <Star color="primary" />,
        title: 'Интересы',
        value: `${data.interests.selectedInterests.length} выбрано`,
        color: data.interests.selectedInterests.length >= 3 ? 'success' : 'warning'
      },
      {
        icon: <Tune color="primary" />,
        title: 'Предпочтения',
        value: data.preferences.genderPreference ? '✓ Настроены' : '⚠ Не настроены',
        color: data.preferences.genderPreference ? 'success' : 'warning'
      }
    ];
    return stats;
  };

  const nextSteps = [
    {
      icon: <Explore color="primary" />,
      title: 'Начните знакомиться',
      description: 'Просматривайте профили и находите интересных людей',
      action: 'Перейти к поиску'
    },
    {
      icon: <Settings color="primary" />,
      title: 'Настройте профиль',
      description: 'Добавьте больше информации о себе',
      action: 'Редактировать профиль'
    },
    {
      icon: <Share color="primary" />,
      title: 'Пригласите друзей',
      description: 'Расскажите друзьям о приложении',
      action: 'Поделиться'
    }
  ];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Регистрация завершена - Likes & Love</title>
        <meta 
          name="description" 
          content="Поздравляем! Ваш профиль готов. Начните знакомиться в приложении Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <OnboardingProgress
              steps={steps}
              currentStep={currentStep}
              variant={isMobile ? 'minimal' : 'horizontal'}
              showLabels={!isMobile}
            />

            <Fade in timeout={800}>
              <Paper elevation={3} sx={{ p: { xs: 3, md: 6 }, textAlign: 'center', position: 'relative' }}>
                {/* Confetti effect */}
                {showConfetti && (
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    pointerEvents: 'none',
                    overflow: 'hidden'
                  }}>
                    {/* Simple confetti animation */}
                    {[...Array(20)].map((_, i) => (
                      <Box
                        key={i}
                        sx={{
                          position: 'absolute',
                          width: 10,
                          height: 10,
                          backgroundColor: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][i % 5],
                          animation: `confetti-fall 3s ease-out ${i * 0.1}s`,
                          left: `${Math.random() * 100}%`,
                          '@keyframes confetti-fall': {
                            '0%': {
                              transform: 'translateY(-100vh) rotate(0deg)',
                              opacity: 1
                            },
                            '100%': {
                              transform: 'translateY(100vh) rotate(720deg)',
                              opacity: 0
                            }
                          }
                        }}
                      />
                    ))}
                  </Box>
                )}

                {/* Success header */}
                <Slide in timeout={1000} direction="down">
                  <Box sx={{ mb: 4 }}>
                    <CheckCircle 
                      sx={{ 
                        fontSize: 80, 
                        color: 'success.main', 
                        mb: 2,
                        animation: 'pulse 2s infinite'
                      }} 
                    />
                    <Typography 
                      variant={isMobile ? "h4" : "h3"} 
                      gutterBottom 
                      sx={{ 
                        fontWeight: 700,
                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                        backgroundClip: 'text',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}
                    >
                      Поздравляем! 🎉
                    </Typography>
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                      Ваш профиль готов к знакомствам
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
                      Отличная работа, {user.firstName || user.email}! Теперь вы можете начать знакомиться 
                      с интересными людьми и находить новые связи.
                    </Typography>
                  </Box>
                </Slide>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                {/* Profile summary */}
                <Slide in timeout={1200} direction="up">
                  <Box sx={{ mb: 6 }}>
                    <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
                      Сводка вашего профиля
                    </Typography>
                    <Grid container spacing={3}>
                      {getCompletionStats().map((stat, index) => (
                        <Grid item xs={6} md={3} key={index}>
                          <Card 
                            sx={{ 
                              height: '100%',
                              border: `2px solid ${theme.palette[stat.color as keyof typeof theme.palette].main}`,
                              backgroundColor: `${theme.palette[stat.color as keyof typeof theme.palette].main}10`
                            }}
                          >
                            <CardContent sx={{ textAlign: 'center', p: 2 }}>
                              <Box sx={{ mb: 1 }}>
                                {stat.icon}
                              </Box>
                              <Typography variant="subtitle2" gutterBottom>
                                {stat.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {stat.value}
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                </Slide>

                {/* Next steps */}
                <Slide in timeout={1400} direction="up">
                  <Box sx={{ mb: 6 }}>
                    <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
                      Что дальше?
                    </Typography>
                    <Grid container spacing={3}>
                      {nextSteps.map((step, index) => (
                        <Grid item xs={12} md={4} key={index}>
                          <Card 
                            sx={{ 
                              height: '100%',
                              transition: 'transform 0.2s ease-in-out',
                              '&:hover': {
                                transform: 'translateY(-4px)',
                                boxShadow: theme.shadows[8]
                              }
                            }}
                          >
                            <CardContent sx={{ textAlign: 'center', p: 3 }}>
                              <Box sx={{ mb: 2 }}>
                                {step.icon}
                              </Box>
                              <Typography variant="h6" gutterBottom>
                                {step.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                {step.description}
                              </Typography>
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={
                                  index === 0 ? handleStartExploring :
                                  index === 1 ? handleEditProfile :
                                  handleShareProfile
                                }
                              >
                                {step.action}
                              </Button>
                            </CardContent>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                </Slide>

                {/* Main CTA */}
                <Slide in timeout={1600} direction="up">
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Готовы начать знакомства?
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Тысячи интересных людей ждут знакомства с вами
                    </Typography>
                    
                    <Stack 
                      direction={isMobile ? "column" : "row"} 
                      spacing={2} 
                      justifyContent="center"
                      alignItems="center"
                    >
                      <Button
                        variant="contained"
                        size="large"
                        onClick={handleStartExploring}
                        disabled={completing || onboardingLoading}
                        endIcon={completing ? <CircularProgress size={20} /> : <Explore />}
                        sx={{
                          px: 4,
                          py: 1.5,
                          fontSize: '1.1rem',
                          background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          '&:hover': {
                            background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`
                          }
                        }}
                      >
                        {completing ? 'Завершение...' : 'Начать знакомства'}
                      </Button>
                      
                      <Button
                        variant="text"
                        size="large"
                        onClick={handleEditProfile}
                        startIcon={<Settings />}
                        sx={{ px: 3 }}
                      >
                        Настроить профиль
                      </Button>
                    </Stack>
                  </Box>
                </Slide>

                {/* Welcome message */}
                <Box sx={{ 
                  mt: 4, 
                  pt: 3, 
                  borderTop: `1px solid ${theme.palette.divider}`,
                  textAlign: 'center'
                }}>
                  <Typography variant="body2" color="text.secondary">
                    <Favorite sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle', color: 'error.main' }} />
                    Добро пожаловать в сообщество Likes & Love!
                  </Typography>
                  <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                    Помните: будьте вежливы, честны и открыты для новых знакомств
                  </Typography>
                </Box>
              </Paper>
            </Fade>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CompletePage;
