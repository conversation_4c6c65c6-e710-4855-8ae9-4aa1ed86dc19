import React, { useState } from 'react';
import { useRouter } from 'next/router';
import {
  AppBar,
  Box,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Divider,
  useTheme,
  useMediaQuery,
  Container,
  Tooltip,
  Button
} from '@mui/material';
import {
  Menu as MenuIcon,
  Home,
  Favorite,
  Message,
  Person,
  Settings,
  Logout,
  Notifications,
  Search,
  VideoCall,
  Groups,
  Event,
  LocationOn,
  Security,
  Help,
  Explore
} from '@mui/icons-material';
import { useAuth } from '../../src/providers/AuthProvider';
import Link from 'next/link';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
}

const Layout: React.FC<LayoutProps> = ({ children, title }) => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();
  
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationMenuClose = () => {
    setNotificationAnchor(null);
  };

  const handleLogout = async () => {
    await logout();
    router.push('/auth/login');
  };

  const menuItems = [
    { text: 'Главная', icon: <Home />, path: '/' },
    { text: 'Поиск', icon: <Search />, path: '/discover' },
    { text: 'Лайки', icon: <Favorite />, path: '/likes', badge: 3 },
    { text: 'Сообщения', icon: <Message />, path: '/messages', badge: 5 },
    { text: 'Видеозвонки', icon: <VideoCall />, path: '/calls' },
    { text: 'События', icon: <Event />, path: '/events' },
    { text: 'Группы', icon: <Groups />, path: '/groups' },
    { text: 'Места', icon: <LocationOn />, path: '/places' },
    { text: 'Профиль', icon: <Person />, path: '/profile' },
  ];

  const drawer = (
    <Box>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          Dating App
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={router.pathname === item.path}
              onClick={() => {
                router.push(item.path);
                if (isMobile) setMobileOpen(false);
              }}
            >
              <ListItemIcon>
                {item.badge ? (
                  <Badge badgeContent={item.badge} color="primary">
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      <Divider />
      <List>
        <ListItem disablePadding>
          <ListItemButton onClick={() => router.push('/settings')}>
            <ListItemIcon>
              <Settings />
            </ListItemIcon>
            <ListItemText primary="Настройки" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton onClick={() => router.push('/safety')}>
            <ListItemIcon>
              <Security />
            </ListItemIcon>
            <ListItemText primary="Безопасность" />
          </ListItemButton>
        </ListItem>
        <ListItem disablePadding>
          <ListItemButton onClick={() => router.push('/help')}>
            <ListItemIcon>
              <Help />
            </ListItemIcon>
            <ListItemText primary="Помощь" />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );

  const drawerWidth = 240;

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {title || 'Dating App'}
          </Typography>

          {!isMobile && (
            <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
              <Button
                color="inherit"
                startIcon={<Explore />}
                onClick={() => router.push('/discover')}
              >
                Знакомства
              </Button>
              <Button
                color="inherit"
                startIcon={<Message />}
                onClick={() => router.push('/messages')}
              >
                Чаты
              </Button>
            </Box>
          )}

          <Tooltip title="Уведомления">
            <IconButton
              color="inherit"
              onClick={handleNotificationMenuOpen}
            >
              <Badge badgeContent={4} color="error">
                <Notifications />
              </Badge>
            </IconButton>
          </Tooltip>

          <Tooltip title="Профиль">
            <IconButton
              onClick={handleProfileMenuOpen}
              sx={{ ml: 1 }}
            >
              <Avatar
                alt={user?.profile?.firstName || user?.email}
                src={user?.avatarUrl || undefined}
                sx={{ width: 32, height: 32 }}
              >
                {user?.profile?.firstName?.[0] || user?.email?.[0]}
              </Avatar>
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          minHeight: 'calc(100vh - 64px)',
          backgroundColor: theme.palette.background.default,
        }}
      >
        {children}
      </Box>

      {/* Меню профиля */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => {
          router.push('/profile');
          handleProfileMenuClose();
        }}>
          <ListItemIcon>
            <Person fontSize="small" />
          </ListItemIcon>
          Мой профиль
        </MenuItem>
        <MenuItem onClick={() => {
          router.push('/profile/edit');
          handleProfileMenuClose();
        }}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          Редактировать профиль
        </MenuItem>
        <MenuItem onClick={() => {
          router.push('/subscription');
          handleProfileMenuClose();
        }}>
          <ListItemIcon>
            <Favorite fontSize="small" />
          </ListItemIcon>
          Подписка
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          Выйти
        </MenuItem>
      </Menu>

      {/* Меню уведомлений */}
      <Menu
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: { width: 320, maxHeight: 400 }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6">Уведомления</Typography>
        </Box>
        <Divider />
        <MenuItem onClick={handleNotificationMenuClose}>
          <Box>
            <Typography variant="body2">
              <strong>Анна</strong> лайкнула ваш профиль
            </Typography>
            <Typography variant="caption" color="text.secondary">
              5 минут назад
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={handleNotificationMenuClose}>
          <Box>
            <Typography variant="body2">
              У вас новое совпадение с <strong>Марией</strong>
            </Typography>
            <Typography variant="caption" color="text.secondary">
              1 час назад
            </Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={handleNotificationMenuClose}>
          <Box>
            <Typography variant="body2">
              <strong>Иван</strong> отправил вам сообщение
            </Typography>
            <Typography variant="caption" color="text.secondary">
              2 часа назад
            </Typography>
          </Box>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          router.push('/notifications');
          handleNotificationMenuClose();
        }}>
          <Typography variant="body2" color="primary" align="center" sx={{ width: '100%' }}>
            Показать все уведомления
          </Typography>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default Layout;
