{"name": "@likes-love/mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:all": "npm run build:android && npm run build:ios && npm run build:rustore && npm run build:huawei", "build:android": "eas build --platform android --profile production", "build:ios": "eas build --platform ios --profile production", "build:rustore": "eas build --platform android --profile rustore", "build:huawei": "eas build --platform android --profile huawei", "build:dev:android": "eas build --platform android --profile development", "build:dev:ios": "eas build --platform ios --profile development", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "jest", "test:android": "jest --selectProjects android", "test:ios": "jest --selectProjects ios", "test:web": "jest --selectProjects web", "test:all": "jest --all", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-firebase/analytics": "^22.2.0", "@react-native-google-signin/google-signin": "^13.1.0", "@react-native-community/geolocation": "^3.4.0", "react-native-maps": "^1.11.2", "react-native-camera": "^4.2.1", "@react-native-async-storage/async-storage": "^2.1.2", "react-native-in-app-purchase": "^12.1.0", "@jest/core": "^30.0.4", "@mui/material": "^5.15.21", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/progress-bar-android": "^1.0.5", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.0.11", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.13", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.77.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "^53.0.9", "expo-av": "^15.1.4", "expo-calendar": "^14.1.4", "expo-camera": "^16.1.6", "expo-device": "^7.1.4", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.5", "expo-notifications": "~0.28.9", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.27.6", "expo-status-bar": "^1.7.1", "expo-updates": "^0.18.19", "fbjs": "^3.0.5", "jail-monkey": "^2.8.3", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "^0.75.4", "react-native-confetti-cannon": "^1.5.2", "react-native-device-info": "^14.0.1", "react-native-document-picker": "^9.1.1", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.20.2", "react-native-image-crop-picker": "^0.40.2", "react-native-image-picker": "^7.1.0", "react-native-image-resizer": "^1.4.5", "react-native-mmkv": "^2.11.0", "react-native-permissions": "^4.1.5", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.6.1", "react-native-safe-area-context": "^5.4.1", "react-native-sass-transformer": "^3.0.0", "react-native-screens": "^4.4.0", "react-native-svg": "^14.1.0", "react-native-svg-transformer": "^1.5.1", "react-native-video": "^6.4.0", "react-native-web": "^0.18.4", "react-native-yamap": "^4.8.3", "react-redux": "^9.2.0", "rustore-react-native-appupdate": "^2.1.0", "rustore-react-native-billingclient": "^7.0.0", "rustore-react-native-pushclient": "^6.1.0", "rustore-react-native-review": "^6.1.0", "semver": "^7.7.2", "socket.io-client": "^4.8.1", "styled-components": "^6.1.19", "undici": "^6.21.3", "uuid": "^9.0.1", "react-native-bluetooth-classic": "^1.60.0-rc.5", "react-native-wifi-reborn": "^4.12.0", "react-native-vision-camera": "^4.7.2", "react-native-qrcode-scanner": "^1.5.5", "react-native-keychain": "^8.2.0"}, "devDependencies": {"@babel/core": "7.23.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/runtime": "7.27.4", "@jest/globals": "^29.5.0", "@jest/types": "^29.5.0", "@react-native/babel-preset": "0.73.0", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.73.0", "@react-native/typescript-config": "0.73.0", "@testing-library/jest-native": "^5.4.3", "@types/jest": "^29.5.14", "@types/node": "^24.0.10", "@types/react": "18.3.11", "@types/react-test-renderer": "18.0.7", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^8.57.1", "expo-blur": "^14.1.5", "jest-expo": "^51.0.4", "metro-react-native-babel-preset": "^0.77.0", "react-native-paper": "^5.14.5", "react-native-testing-library": "^2.2.0", "react-test-renderer": "^18.3.1", "typescript": "5.3.3"}, "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}