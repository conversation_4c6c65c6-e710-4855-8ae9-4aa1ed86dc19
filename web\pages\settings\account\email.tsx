import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  TextField,
  Alert,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>l,
  StepContent,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  ArrowBack,
  Email as EmailIcon,
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Send as SendIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для форм
interface EmailChangeForm {
  newEmail: string;
  password: string;
}

interface VerificationForm {
  verificationCode: string;
}

// Схемы валидации
const emailChangeSchema = yup.object({
  newEmail: yup.string()
    .email('Неверный формат email')
    .required('Новый email обязателен')
    .test('different', 'Новый email должен отличаться от текущего', function(value) {
      return value !== this.parent.currentEmail;
    }),
  password: yup.string()
    .required('Пароль обязателен')
    .min(6, 'Минимум 6 символов')
});

const verificationSchema = yup.object({
  verificationCode: yup.string()
    .required('Код подтверждения обязателен')
    .length(6, 'Код должен содержать 6 цифр')
    .matches(/^\d+$/, 'Код должен содержать только цифры')
});

const ChangeEmailPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [newEmail, setNewEmail] = useState('');

  // Форма смены email
  const {
    control: emailControl,
    handleSubmit: handleEmailSubmit,
    formState: { errors: emailErrors },
    setValue: setEmailValue
  } = useForm<EmailChangeForm>({
    resolver: yupResolver(emailChangeSchema),
    defaultValues: {
      newEmail: '',
      password: ''
    }
  });

  // Форма подтверждения
  const {
    control: verificationControl,
    handleSubmit: handleVerificationSubmit,
    formState: { errors: verificationErrors },
    reset: resetVerification
  } = useForm<VerificationForm>({
    resolver: yupResolver(verificationSchema),
    defaultValues: {
      verificationCode: ''
    }
  });

  // Установка текущего email для валидации
  React.useEffect(() => {
    if (user?.email) {
      setEmailValue('currentEmail', user.email);
    }
  }, [user?.email, setEmailValue]);

  const steps = [
    'Ввод нового email',
    'Подтверждение по email',
    'Завершение'
  ];

  const handleEmailChange = async (data: EmailChangeForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для инициации смены email
      // await initiateEmailChange(data.newEmail, data.password);

      setNewEmail(data.newEmail);
      setActiveStep(1);
      setSuccess('Код подтверждения отправлен на новый email адрес');
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке кода подтверждения');
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async (data: VerificationForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для подтверждения смены email
      // await confirmEmailChange(data.verificationCode);

      // Обновляем профиль пользователя
      await updateProfile({
        email: newEmail,
        emailVerified: true
      });

      setActiveStep(2);
      setSuccess('Email успешно изменен!');
    } catch (err: any) {
      setError(err.message || 'Неверный код подтверждения');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для повторной отправки кода
      // await resendEmailVerificationCode(newEmail);

      setSuccess('Код подтверждения отправлен повторно');
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке кода');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
      setError(null);
      setSuccess(null);
    } else {
      router.push('/settings/account');
    }
  };

  if (!user) {
    return (
      <Layout title="Смена email">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Смена email адреса - Likes & Love</title>
        <meta name="description" content="Изменение email адреса в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Смена email">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/account" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/account'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <AccountIcon fontSize="small" />
                Аккаунт
              </Link>
              <Typography color="text.primary">Смена email</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <EmailIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Смена email адреса
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Текущий email: {user.email}
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Предупреждение */}
            <Alert severity="warning" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Важно:</strong> После смены email адреса вам потребуется подтвердить новый адрес. 
                Убедитесь, что у вас есть доступ к новому email.
              </Typography>
            </Alert>

            {/* Stepper */}
            <Card>
              <CardContent>
                <Stepper activeStep={activeStep} orientation={isMobile ? 'vertical' : 'horizontal'}>
                  {steps.map((label, index) => (
                    <Step key={label}>
                      <StepLabel>{label}</StepLabel>
                      {isMobile && (
                        <StepContent>
                          {index === 0 && (
                            <Box sx={{ mt: 2 }}>
                              <form onSubmit={handleEmailSubmit(handleEmailChange)}>
                                <Controller
                                  name="newEmail"
                                  control={emailControl}
                                  render={({ field }) => (
                                    <TextField
                                      {...field}
                                      label="Новый email адрес"
                                      type="email"
                                      fullWidth
                                      error={!!emailErrors.newEmail}
                                      helperText={emailErrors.newEmail?.message}
                                      sx={{ mb: 2 }}
                                    />
                                  )}
                                />
                                <Controller
                                  name="password"
                                  control={emailControl}
                                  render={({ field }) => (
                                    <TextField
                                      {...field}
                                      label="Текущий пароль"
                                      type="password"
                                      fullWidth
                                      error={!!emailErrors.password}
                                      helperText={emailErrors.password?.message}
                                      sx={{ mb: 2 }}
                                    />
                                  )}
                                />
                                <Button
                                  type="submit"
                                  variant="contained"
                                  disabled={loading}
                                  startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                                >
                                  {loading ? 'Отправка...' : 'Отправить код'}
                                </Button>
                              </form>
                            </Box>
                          )}
                          {index === 1 && (
                            <Box sx={{ mt: 2 }}>
                              <form onSubmit={handleVerificationSubmit(handleVerification)}>
                                <Controller
                                  name="verificationCode"
                                  control={verificationControl}
                                  render={({ field }) => (
                                    <TextField
                                      {...field}
                                      label="Код подтверждения"
                                      fullWidth
                                      error={!!verificationErrors.verificationCode}
                                      helperText={verificationErrors.verificationCode?.message}
                                      sx={{ mb: 2 }}
                                    />
                                  )}
                                />
                                <Box sx={{ display: 'flex', gap: 2 }}>
                                  <Button
                                    type="submit"
                                    variant="contained"
                                    disabled={loading}
                                    startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                                  >
                                    {loading ? 'Проверка...' : 'Подтвердить'}
                                  </Button>
                                  <Button
                                    onClick={handleResendCode}
                                    disabled={loading}
                                  >
                                    Отправить повторно
                                  </Button>
                                </Box>
                              </form>
                            </Box>
                          )}
                          {index === 2 && (
                            <Box sx={{ mt: 2 }}>
                              <Alert severity="success">
                                <Typography variant="body2">
                                  Email адрес успешно изменен! Теперь вы можете использовать новый адрес для входа.
                                </Typography>
                              </Alert>
                              <Button
                                onClick={() => router.push('/settings/account')}
                                variant="contained"
                                sx={{ mt: 2 }}
                              >
                                Вернуться к настройкам
                              </Button>
                            </Box>
                          )}
                        </StepContent>
                      )}
                    </Step>
                  ))}
                </Stepper>

                {/* Контент для desktop */}
                {!isMobile && (
                  <Box sx={{ mt: 4 }}>
                    {activeStep === 0 && (
                      <form onSubmit={handleEmailSubmit(handleEmailChange)}>
                        <Controller
                          name="newEmail"
                          control={emailControl}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Новый email адрес"
                              type="email"
                              fullWidth
                              error={!!emailErrors.newEmail}
                              helperText={emailErrors.newEmail?.message}
                              sx={{ mb: 3 }}
                            />
                          )}
                        />
                        <Controller
                          name="password"
                          control={emailControl}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Текущий пароль для подтверждения"
                              type="password"
                              fullWidth
                              error={!!emailErrors.password}
                              helperText={emailErrors.password?.message}
                              sx={{ mb: 3 }}
                            />
                          )}
                        />
                        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                          <Button onClick={handleBack}>
                            Отмена
                          </Button>
                          <Button
                            type="submit"
                            variant="contained"
                            disabled={loading}
                            startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                          >
                            {loading ? 'Отправка...' : 'Отправить код подтверждения'}
                          </Button>
                        </Box>
                      </form>
                    )}

                    {activeStep === 1 && (
                      <Box>
                        <Typography variant="body1" sx={{ mb: 3 }}>
                          Мы отправили код подтверждения на адрес: <strong>{newEmail}</strong>
                        </Typography>
                        <form onSubmit={handleVerificationSubmit(handleVerification)}>
                          <Controller
                            name="verificationCode"
                            control={verificationControl}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Введите 6-значный код"
                                fullWidth
                                error={!!verificationErrors.verificationCode}
                                helperText={verificationErrors.verificationCode?.message}
                                sx={{ mb: 3 }}
                              />
                            )}
                          />
                          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                            <Button onClick={handleResendCode} disabled={loading}>
                              Отправить повторно
                            </Button>
                            <Button onClick={handleBack}>
                              Назад
                            </Button>
                            <Button
                              type="submit"
                              variant="contained"
                              disabled={loading}
                              startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                            >
                              {loading ? 'Проверка...' : 'Подтвердить'}
                            </Button>
                          </Box>
                        </form>
                      </Box>
                    )}

                    {activeStep === 2 && (
                      <Box sx={{ textAlign: 'center' }}>
                        <CheckCircleIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
                        <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
                          Email успешно изменен!
                        </Typography>
                        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                          Ваш новый email адрес: <strong>{newEmail}</strong>
                        </Typography>
                        <Button
                          onClick={() => router.push('/settings/account')}
                          variant="contained"
                          size="large"
                        >
                          Вернуться к настройкам аккаунта
                        </Button>
                      </Box>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ChangeEmailPage;
