import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  TextField,
  Alert,
  CircularProgress,
  <PERSON>per,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ArrowBack,
  Shield as ShieldIcon,
  QrCode as QrCodeIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Save as SaveIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Smartphone as SmartphoneIcon,
  ContentCopy as ContentCopyIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для форм
interface TwoFactorSetupForm {
  verificationCode: string;
}

interface TwoFactorDisableForm {
  password: string;
  verificationCode: string;
}

// Схемы валидации
const setupSchema = yup.object({
  verificationCode: yup.string()
    .required('Код подтверждения обязателен')
    .length(6, 'Код должен содержать 6 цифр')
    .matches(/^\d+$/, 'Код должен содержать только цифры')
});

const disableSchema = yup.object({
  password: yup.string().required('Пароль обязателен'),
  verificationCode: yup.string()
    .required('Код подтверждения обязателен')
    .length(6, 'Код должен содержать 6 цифр')
    .matches(/^\d+$/, 'Код должен содержать только цифры')
});

const TwoFactorPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [secretKey, setSecretKey] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [disableDialogOpen, setDisableDialogOpen] = useState(false);
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);

  // Форма настройки 2FA
  const {
    control: setupControl,
    handleSubmit: handleSetupSubmit,
    formState: { errors: setupErrors },
    reset: resetSetup
  } = useForm<TwoFactorSetupForm>({
    resolver: yupResolver(setupSchema),
    defaultValues: {
      verificationCode: ''
    }
  });

  // Форма отключения 2FA
  const {
    control: disableControl,
    handleSubmit: handleDisableSubmit,
    formState: { errors: disableErrors },
    reset: resetDisable
  } = useForm<TwoFactorDisableForm>({
    resolver: yupResolver(disableSchema),
    defaultValues: {
      password: '',
      verificationCode: ''
    }
  });

  // Проверяем статус 2FA при загрузке
  React.useEffect(() => {
    // Здесь будет проверка статуса 2FA из API
    // const check2FAStatus = async () => {
    //   const status = await get2FAStatus();
    //   setIs2FAEnabled(status.enabled);
    // };
    // check2FAStatus();

    // Мок данные
    setIs2FAEnabled(false);
  }, []);

  const steps = [
    'Скачайте приложение',
    'Сканируйте QR-код',
    'Введите код подтверждения',
    'Сохраните резервные коды'
  ];

  const authenticatorApps = [
    { name: 'Google Authenticator', icon: '🔐', platforms: ['iOS', 'Android'] },
    { name: 'Microsoft Authenticator', icon: '🛡️', platforms: ['iOS', 'Android'] },
    { name: 'Authy', icon: '🔒', platforms: ['iOS', 'Android', 'Desktop'] },
    { name: '1Password', icon: '🗝️', platforms: ['iOS', 'Android', 'Desktop'] }
  ];

  const handleStart2FASetup = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для инициации настройки 2FA
      // const response = await initiate2FASetup();

      // Мок данные
      setQrCodeUrl('https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/Likes%26Love:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Likes%26Love');
      setSecretKey('JBSWY3DPEHPK3PXP');
      setActiveStep(1);
    } catch (err: any) {
      setError(err.message || 'Ошибка при инициации настройки 2FA');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifySetup = async (data: TwoFactorSetupForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для подтверждения настройки 2FA
      // const response = await verify2FASetup(data.verificationCode);

      // Мок данные
      const mockBackupCodes = [
        '12345678', '87654321', '11223344', '44332211',
        '55667788', '88776655', '99001122', '22110099'
      ];

      setBackupCodes(mockBackupCodes);
      setActiveStep(3);
      setSuccess('2FA успешно настроена!');
    } catch (err: any) {
      setError(err.message || 'Неверный код подтверждения');
    } finally {
      setLoading(false);
    }
  };

  const handleComplete2FASetup = async () => {
    try {
      setLoading(true);

      // Обновляем профиль пользователя
      await updateProfile({
        twoFactorEnabled: true
      });

      setIs2FAEnabled(true);
      setActiveStep(0);
      resetSetup();
      setSuccess('Двухфакторная аутентификация успешно включена!');
    } catch (err: any) {
      setError(err.message || 'Ошибка при завершении настройки');
    } finally {
      setLoading(false);
    }
  };

  const handleDisable2FA = async (data: TwoFactorDisableForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для отключения 2FA
      // await disable2FA(data.password, data.verificationCode);

      // Обновляем профиль пользователя
      await updateProfile({
        twoFactorEnabled: false
      });

      setIs2FAEnabled(false);
      setDisableDialogOpen(false);
      resetDisable();
      setSuccess('Двухфакторная аутентификация отключена');
    } catch (err: any) {
      setError(err.message || 'Ошибка при отключении 2FA');
    } finally {
      setLoading(false);
    }
  };

  const handleCopySecret = () => {
    navigator.clipboard.writeText(secretKey);
    setSuccess('Секретный ключ скопирован в буфер обмена');
  };

  const handleDownloadBackupCodes = () => {
    const content = backupCodes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'likes-love-backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    } else {
      router.push('/settings/security');
    }
  };

  if (!user) {
    return (
      <Layout title="Двухфакторная аутентификация">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Двухфакторная аутентификация - Likes & Love</title>
        <meta name="description" content="Настройка двухфакторной аутентификации для повышения безопасности аккаунта" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Двухфакторная аутентификация">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link
                color="inherit"
                href="/"
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link
                color="inherit"
                href="/settings"
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link
                color="inherit"
                href="/settings/security"
                onClick={(e) => { e.preventDefault(); router.push('/settings/security'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SecurityIcon fontSize="small" />
                Безопасность
              </Link>
              <Typography color="text.primary">2FA</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <ShieldIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Двухфакторная аутентификация
                </Typography>
                {is2FAEnabled ? (
                  <Chip icon={<CheckCircleIcon />} label="Включена" color="success" />
                ) : (
                  <Chip icon={<WarningIcon />} label="Отключена" color="warning" />
                )}
              </Box>
              <Typography variant="h6" color="text.secondary">
                Дополнительный уровень защиты вашего аккаунта
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Информация о 2FA */}
            <Alert severity="info" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Что такое 2FA?</strong> Двухфакторная аутентификация добавляет дополнительный уровень безопасности,
                требуя не только пароль, но и код из приложения-аутентификатора на вашем телефоне.
              </Typography>
            </Alert>

            {is2FAEnabled ? (
              // 2FA уже включена
              <Card>
                <CardContent>
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <CheckCircleIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
                    <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
                      2FA включена и активна
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                      Ваш аккаунт защищен двухфакторной аутентификацией
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                      <Button
                        onClick={() => setDisableDialogOpen(true)}
                        variant="outlined"
                        color="error"
                        startIcon={<DeleteIcon />}
                      >
                        Отключить 2FA
                      </Button>
                      <Button
                        onClick={() => router.push('/settings/security')}
                        variant="contained"
                      >
                        Настройки безопасности
                      </Button>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            ) : (
              // Настройка 2FA
              <Card>
                <CardContent>
                  {activeStep === 0 && (
                    <Box>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Настройка двухфакторной аутентификации
                      </Typography>

                      <Typography variant="body1" sx={{ mb: 3 }}>
                        Для настройки 2FA вам потребуется приложение-аутентификатор на вашем смартфоне:
                      </Typography>

                      <List>
                        {authenticatorApps.map((app) => (
                          <ListItem key={app.name}>
                            <ListItemIcon>
                              <Typography sx={{ fontSize: 24 }}>{app.icon}</Typography>
                            </ListItemIcon>
                            <ListItemText
                              primary={app.name}
                              secondary={`Доступно для: ${app.platforms.join(', ')}`}
                            />
                          </ListItem>
                        ))}
                      </List>

                      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                        <Button onClick={handleBack}>
                          Отмена
                        </Button>
                        <Button
                          onClick={handleStart2FASetup}
                          variant="contained"
                          disabled={loading}
                          startIcon={loading ? <CircularProgress size={20} /> : <ShieldIcon />}
                        >
                          {loading ? 'Настройка...' : 'Начать настройку'}
                        </Button>
                      </Box>
                    </Box>
                  )}

                  {activeStep > 0 && (
                    <Stepper activeStep={activeStep - 1} orientation={isMobile ? 'vertical' : 'horizontal'}>
                      {steps.slice(1).map((label, index) => (
                        <Step key={label}>
                          <StepLabel>{label}</StepLabel>
                          {isMobile && (
                            <StepContent>
                              {index === 0 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="h6" sx={{ mb: 2 }}>
                                    Сканируйте QR-код
                                  </Typography>
                                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                                    <img src={qrCodeUrl} alt="QR Code" style={{ maxWidth: '200px' }} />
                                  </Box>
                                  <Typography variant="body2" sx={{ mb: 2 }}>
                                    Или введите секретный ключ вручную:
                                  </Typography>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                                    <TextField
                                      value={secretKey}
                                      fullWidth
                                      InputProps={{ readOnly: true }}
                                      size="small"
                                    />
                                    <IconButton onClick={handleCopySecret}>
                                      <ContentCopyIcon />
                                    </IconButton>
                                  </Box>
                                  <Button onClick={() => setActiveStep(2)} variant="contained">
                                    Продолжить
                                  </Button>
                                </Box>
                              )}
                              {index === 1 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="h6" sx={{ mb: 2 }}>
                                    Введите код из приложения
                                  </Typography>
                                  <form onSubmit={handleSetupSubmit(handleVerifySetup)}>
                                    <Controller
                                      name="verificationCode"
                                      control={setupControl}
                                      render={({ field }) => (
                                        <TextField
                                          {...field}
                                          label="6-значный код"
                                          fullWidth
                                          error={!!setupErrors.verificationCode}
                                          helperText={setupErrors.verificationCode?.message}
                                          sx={{ mb: 2 }}
                                        />
                                      )}
                                    />
                                    <Box sx={{ display: 'flex', gap: 2 }}>
                                      <Button onClick={handleBack}>
                                        Назад
                                      </Button>
                                      <Button
                                        type="submit"
                                        variant="contained"
                                        disabled={loading}
                                        startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                                      >
                                        {loading ? 'Проверка...' : 'Подтвердить'}
                                      </Button>
                                    </Box>
                                  </form>
                                </Box>
                              )}
                              {index === 2 && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="h6" sx={{ mb: 2 }}>
                                    Сохраните резервные коды
                                  </Typography>
                                  <Alert severity="warning" sx={{ mb: 2 }}>
                                    <Typography variant="body2">
                                      Сохраните эти коды в безопасном месте. Они помогут восстановить доступ к аккаунту,
                                      если вы потеряете доступ к приложению-аутентификатору.
                                    </Typography>
                                  </Alert>
                                  <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 1, mb: 2 }}>
                                    {backupCodes.map((code, index) => (
                                      <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace', p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
                                        {code}
                                      </Typography>
                                    ))}
                                  </Box>
                                  <Box sx={{ display: 'flex', gap: 2 }}>
                                    <Button
                                      onClick={handleDownloadBackupCodes}
                                      startIcon={<DownloadIcon />}
                                    >
                                      Скачать коды
                                    </Button>
                                    <Button
                                      onClick={handleComplete2FASetup}
                                      variant="contained"
                                      disabled={loading}
                                    >
                                      Завершить настройку
                                    </Button>
                                  </Box>
                                </Box>
                              )}
                            </StepContent>
                          )}
                        </Step>
                      ))}
                    </Stepper>
                  )}

                  {/* Контент для desktop */}
                  {!isMobile && activeStep > 0 && (
                    <Box sx={{ mt: 4 }}>
                      {activeStep === 1 && (
                        <Box>
                          <Typography variant="h6" sx={{ mb: 3 }}>
                            Сканируйте QR-код в приложении-аутентификаторе
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 4, alignItems: 'center' }}>
                            <Box sx={{ textAlign: 'center' }}>
                              <img src={qrCodeUrl} alt="QR Code" style={{ maxWidth: '200px' }} />
                            </Box>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="body1" sx={{ mb: 2 }}>
                                Или введите секретный ключ вручную:
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                                <TextField
                                  value={secretKey}
                                  fullWidth
                                  InputProps={{ readOnly: true }}
                                />
                                <IconButton onClick={handleCopySecret}>
                                  <ContentCopyIcon />
                                </IconButton>
                              </Box>
                              <Button onClick={() => setActiveStep(2)} variant="contained">
                                Продолжить
                              </Button>
                            </Box>
                          </Box>
                        </Box>
                      )}

                      {activeStep === 2 && (
                        <Box>
                          <Typography variant="h6" sx={{ mb: 3 }}>
                            Введите код из приложения-аутентификатора
                          </Typography>
                          <form onSubmit={handleSetupSubmit(handleVerifySetup)}>
                            <Controller
                              name="verificationCode"
                              control={setupControl}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  label="6-значный код из приложения"
                                  fullWidth
                                  error={!!setupErrors.verificationCode}
                                  helperText={setupErrors.verificationCode?.message}
                                  sx={{ mb: 3, maxWidth: 300 }}
                                />
                              )}
                            />
                            <Box sx={{ display: 'flex', gap: 2 }}>
                              <Button onClick={handleBack}>
                                Назад
                              </Button>
                              <Button
                                type="submit"
                                variant="contained"
                                disabled={loading}
                                startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                              >
                                {loading ? 'Проверка...' : 'Подтвердить'}
                              </Button>
                            </Box>
                          </form>
                        </Box>
                      )}

                      {activeStep === 3 && (
                        <Box>
                          <Typography variant="h6" sx={{ mb: 3 }}>
                            Сохраните резервные коды
                          </Typography>
                          <Alert severity="warning" sx={{ mb: 3 }}>
                            <Typography variant="body2">
                              <strong>Важно!</strong> Сохраните эти коды в безопасном месте. Они помогут восстановить доступ к аккаунту,
                              если вы потеряете доступ к приложению-аутентификатору.
                            </Typography>
                          </Alert>
                          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 2, mb: 3, maxWidth: 600 }}>
                            {backupCodes.map((code, index) => (
                              <Typography
                                key={index}
                                variant="body2"
                                sx={{
                                  fontFamily: 'monospace',
                                  p: 2,
                                  bgcolor: 'grey.100',
                                  borderRadius: 1,
                                  textAlign: 'center'
                                }}
                              >
                                {code}
                              </Typography>
                            ))}
                          </Box>
                          <Box sx={{ display: 'flex', gap: 2 }}>
                            <Button
                              onClick={handleDownloadBackupCodes}
                              startIcon={<DownloadIcon />}
                            >
                              Скачать коды
                            </Button>
                            <Button
                              onClick={handleComplete2FASetup}
                              variant="contained"
                              disabled={loading}
                              startIcon={loading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                            >
                              {loading ? 'Завершение...' : 'Завершить настройку'}
                            </Button>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Диалог отключения 2FA */}
            <Dialog
              open={disableDialogOpen}
              onClose={() => setDisableDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <WarningIcon color="error" />
                  Отключить двухфакторную аутентификацию
                </Box>
              </DialogTitle>
              <DialogContent>
                <Alert severity="warning" sx={{ mb: 3 }}>
                  <Typography variant="body2">
                    Отключение 2FA снизит безопасность вашего аккаунта. Убедитесь, что это действительно необходимо.
                  </Typography>
                </Alert>
                <form onSubmit={handleDisableSubmit(handleDisable2FA)}>
                  <Controller
                    name="password"
                    control={disableControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Текущий пароль"
                        type="password"
                        fullWidth
                        error={!!disableErrors.password}
                        helperText={disableErrors.password?.message}
                        sx={{ mb: 2 }}
                      />
                    )}
                  />
                  <Controller
                    name="verificationCode"
                    control={disableControl}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Код из приложения-аутентификатора"
                        fullWidth
                        error={!!disableErrors.verificationCode}
                        helperText={disableErrors.verificationCode?.message}
                      />
                    )}
                  />
                </form>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDisableDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleDisableSubmit(handleDisable2FA)}
                  color="error"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
                >
                  {loading ? 'Отключение...' : 'Отключить 2FA'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default TwoFactorPage;