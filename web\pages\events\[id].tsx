import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Event as EventIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  Share as ShareIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PersonAdd as PersonAddIcon,
  ExitToApp as LeaveIcon,
  Chat as ChatIcon,
  Star as StarIcon,
  Report as ReportIcon,
  Verified as VerifiedIcon,
  Phone as PhoneIcon,
  Message as MessageIcon,
  CalendarToday as CalendarIcon,
  AttachMoney as MoneyIcon,
  Language as WebsiteIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Twitter as TwitterIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import {
  getEvent,
  registerForEvent,
  cancelRegistration,
  deleteEvent,
  rateEvent,
  reportEvent
} from '../../src/services/eventsService';
import { Event } from '../../src/types/events.types';

const EventDetailsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { id } = router.query;

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Dialog states
  const [ratingDialog, setRatingDialog] = useState(false);
  const [reportDialog, setReportDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);

  // Form states
  const [rating, setRating] = useState(5);
  const [ratingComment, setRatingComment] = useState('');
  const [reportReason, setReportReason] = useState('');
  const [reportDetails, setReportDetails] = useState('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    if (id && typeof id === 'string') {
      loadEvent(id);
    }
  }, [user, router, id]);

  const loadEvent = async (eventId: string) => {
    try {
      setLoading(true);
      setError(null);

      const eventData = await getEvent(eventId);
      setEvent(eventData);
    } catch (err: any) {
      setError('Ошибка загрузки события');
    } finally {
      setLoading(false);
    }
  };

  const handleRegisterForEvent = async () => {
    if (!event) return;

    try {
      setActionLoading('register');
      setError(null);

      await registerForEvent(event.id);
      setSuccess('Вы зарегистрированы на событие');
      loadEvent(event.id); // Reload event data
    } catch (err: any) {
      setError('Ошибка регистрации на событие');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelRegistration = async () => {
    if (!event) return;

    try {
      setActionLoading('cancel');
      setError(null);

      // This would need the registration ID
      // await cancelRegistration(registrationId);
      setSuccess('Регистрация отменена');
      loadEvent(event.id); // Reload event data
    } catch (err: any) {
      setError('Ошибка отмены регистрации');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteEvent = async () => {
    if (!event) return;

    try {
      setActionLoading('delete');
      setError(null);

      await deleteEvent(event.id);
      setSuccess('Событие удалено');
      router.push('/events');
    } catch (err: any) {
      setError('Ошибка удаления события');
    } finally {
      setActionLoading(null);
      setDeleteDialog(false);
    }
  };

  const handleRateEvent = async () => {
    if (!event) return;

    try {
      setActionLoading('rate');
      setError(null);

      await rateEvent(event.id, rating, ratingComment.trim() || undefined);
      setSuccess('Отзыв отправлен');
      setRatingDialog(false);
      setRating(5);
      setRatingComment('');
    } catch (err: any) {
      setError('Ошибка отправки отзыва');
    } finally {
      setActionLoading(null);
    }
  };

  const handleReportEvent = async () => {
    if (!event || !reportReason.trim()) return;

    try {
      setActionLoading('report');
      setError(null);

      await reportEvent(event.id, reportReason.trim(), reportDetails.trim() || undefined);
      setSuccess('Жалоба отправлена');
      setReportDialog(false);
      setReportReason('');
      setReportDetails('');
    } catch (err: any) {
      setError('Ошибка отправки жалобы');
    } finally {
      setActionLoading(null);
    }
  };

  const formatEventDate = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start.toDateString() === end.toDateString()) {
      return {
        date: start.toLocaleDateString('ru-RU', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        time: `${start.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })} - ${end.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}`
      };
    } else {
      return {
        date: `${start.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' })} - ${end.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' })}`,
        time: `${start.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })} - ${end.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}`
      };
    }
  };

  const getEventTypeLabel = (type: Event['type']) => {
    switch (type) {
      case 'conference':
        return 'Конференция';
      case 'workshop':
        return 'Мастер-класс';
      case 'meetup':
        return 'Митап';
      case 'party':
        return 'Вечеринка';
      case 'concert':
        return 'Концерт';
      case 'sports':
        return 'Спорт';
      case 'cultural':
        return 'Культура';
      case 'networking':
        return 'Нетворкинг';
      case 'educational':
        return 'Образование';
      case 'charity':
        return 'Благотворительность';
      default:
        return type;
    }
  };

  const isUserRegistered = () => {
    return event?.participants.some(p => p.id === user?.id);
  };

  const isUserOrganizer = () => {
    return event?.organizer.id === user?.id;
  };

  const canRegisterForEvent = () => {
    if (!event) return false;
    if (isUserRegistered() || isUserOrganizer()) return false;
    if (event.participants.length >= event.capacity) return false;
    return event.status === 'published';
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка события - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 }, textAlign: 'center' }}>
              <CircularProgress size={60} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Загрузка события...
              </Typography>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (error && !event) {
    return (
      <>
        <Head>
          <title>Ошибка загрузки события - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Alert severity="error">
                {error}
              </Alert>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (!event) {
    return null;
  }

  const eventDate = formatEventDate(event.startDate, event.endDate);

  return (
    <>
      <Head>
        <title>{`${event.title} - События - Likes & Love`}</title>
        <meta
          name="description"
          content={event.shortDescription || event.description || `Событие "${event.title}" в приложении знакомств Likes & Love`}
        />
        <meta name="robots" content="index, follow" />
      </Head>

      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                {event.title}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => setReportDialog(true)}>
                  <ReportIcon />
                </IconButton>
                <IconButton>
                  <ShareIcon />
                </IconButton>
                {isUserOrganizer() && (
                  <>
                    <IconButton onClick={() => router.push(`/events/${event.id}/edit`)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => setDeleteDialog(true)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </>
                )}
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Grid container spacing={4}>
              {/* Main Content */}
              <Grid item xs={12} md={8}>
                <Paper elevation={3} sx={{ overflow: 'hidden' }}>
                  {/* Event Cover Image */}
                  <Box sx={{ position: 'relative' }}>
                    <img
                      src={event.media.coverImage}
                      alt={event.title}
                      style={{
                        width: '100%',
                        height: '400px',
                        objectFit: 'cover'
                      }}
                    />

                    {/* Event Type Badge */}
                    <Box sx={{
                      position: 'absolute',
                      top: 16,
                      left: 16,
                      zIndex: 1
                    }}>
                      <Chip
                        label={getEventTypeLabel(event.type)}
                        color="primary"
                        sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                      />
                    </Box>

                    {/* Status Badge */}
                    <Box sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      zIndex: 1
                    }}>
                      <Chip
                        label={event.status === 'published' ? 'Активное' : 'Завершено'}
                        color={event.status === 'published' ? 'success' : 'default'}
                        sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                      />
                    </Box>
                  </Box>

                  <Box sx={{ p: 4 }}>
                    {/* Event Info */}
                    <Box sx={{ mb: 4 }}>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                        <Chip
                          label={event.category}
                          variant="outlined"
                        />
                        {event.visibility === 'private' && (
                          <Chip
                            label="Приватное"
                            color="warning"
                            variant="outlined"
                          />
                        )}
                        {event.ageRestriction && (
                          <Chip
                            label={`${event.ageRestriction.min}+`}
                            variant="outlined"
                          />
                        )}
                      </Box>

                      <Typography variant="h6" gutterBottom>
                        Описание
                      </Typography>
                      <Typography variant="body1" paragraph>
                        {event.description}
                      </Typography>

                      {event.tags.length > 0 && (
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="h6" gutterBottom>
                            Теги
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {event.tags.map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Box>
                      )}
                    </Box>

                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 4 }}>
                      {canRegisterForEvent() && (
                        <Button
                          variant="contained"
                          size="large"
                          onClick={handleRegisterForEvent}
                          disabled={actionLoading === 'register'}
                          startIcon={actionLoading === 'register' ? <CircularProgress size={20} /> : <PersonAddIcon />}
                        >
                          Зарегистрироваться
                        </Button>
                      )}

                      {isUserRegistered() && !isUserOrganizer() && (
                        <Button
                          variant="outlined"
                          color="error"
                          onClick={handleCancelRegistration}
                          disabled={actionLoading === 'cancel'}
                          startIcon={actionLoading === 'cancel' ? <CircularProgress size={20} /> : <LeaveIcon />}
                        >
                          Отменить регистрацию
                        </Button>
                      )}

                      {event.settings.enableChat && (isUserRegistered() || isUserOrganizer()) && (
                        <Button
                          variant="outlined"
                          onClick={() => router.push(`/events/${event.id}/chat`)}
                          startIcon={<ChatIcon />}
                        >
                          Чат события
                        </Button>
                      )}

                      {event.status === 'completed' && (isUserRegistered() || isUserOrganizer()) && (
                        <Button
                          variant="outlined"
                          onClick={() => setRatingDialog(true)}
                          startIcon={<StarIcon />}
                        >
                          Оценить событие
                        </Button>
                      )}

                      <Button
                        variant="outlined"
                        startIcon={<CalendarIcon />}
                      >
                        Добавить в календарь
                      </Button>
                    </Box>
                  </Box>
                </Paper>
              </Grid>

              {/* Sidebar */}
              <Grid item xs={12} md={4}>
                {/* Event Details */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Детали события
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ScheduleIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">
                        {eventDate.date}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {eventDate.time}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">
                        {event.location.name}
                      </Typography>
                      {event.location.address && (
                        <Typography variant="body2" color="text.secondary">
                          {event.location.address}
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PeopleIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      {event.statistics.going} / {event.capacity} участников
                    </Typography>
                  </Box>

                  {event.tickets.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Билеты:
                      </Typography>
                      {event.tickets.map((ticket, index) => (
                        <Typography key={index} variant="body2">
                          {ticket.name}: {ticket.type === 'free' ? 'Бесплатно' : `${ticket.price} ${ticket.currency}`}
                        </Typography>
                      ))}
                    </Box>
                  )}
                </Paper>

                {/* Organizer */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Организатор
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={event.organizer.avatarUrl}
                      sx={{ width: 48, height: 48, mr: 2 }}
                    >
                      {event.organizer.firstName[0]}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {event.organizer.firstName} {event.organizer.lastName}
                        </Typography>
                        {event.organizer.isVerified && (
                          <VerifiedIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {event.organizer.eventsCount} событий
                      </Typography>
                      {event.organizer.rating > 0 && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <StarIcon sx={{ fontSize: 14, color: 'warning.main' }} />
                          <Typography variant="caption">
                            {event.organizer.rating.toFixed(1)}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>

                  {!isUserOrganizer() && (
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<MessageIcon />}
                        onClick={() => router.push(`/chat/new?userId=${event.organizer.id}`)}
                      >
                        Написать
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<PhoneIcon />}
                      >
                        Позвонить
                      </Button>
                    </Box>
                  )}
                </Paper>

                {/* Social Links */}
                {event.socialLinks && Object.keys(event.socialLinks).length > 0 && (
                  <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Ссылки
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {event.socialLinks.website && (
                        <IconButton
                          component="a"
                          href={event.socialLinks.website}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <WebsiteIcon />
                        </IconButton>
                      )}
                      {event.socialLinks.facebook && (
                        <IconButton
                          component="a"
                          href={event.socialLinks.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <FacebookIcon />
                        </IconButton>
                      )}
                      {event.socialLinks.instagram && (
                        <IconButton
                          component="a"
                          href={event.socialLinks.instagram}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <InstagramIcon />
                        </IconButton>
                      )}
                      {event.socialLinks.twitter && (
                        <IconButton
                          component="a"
                          href={event.socialLinks.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <TwitterIcon />
                        </IconButton>
                      )}
                    </Box>
                  </Paper>
                )}

                {/* Participants */}
                <Paper elevation={3} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Участники ({event.statistics.going})
                  </Typography>
                  <List dense>
                    {event.participants.slice(0, 5).map((participant) => (
                      <ListItem key={participant.id} sx={{ px: 0 }}>
                        <ListItemAvatar>
                          <Avatar
                            src={participant.avatarUrl}
                            sx={{ width: 32, height: 32 }}
                          >
                            {participant.firstName[0]}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2">
                                {participant.firstName}
                              </Typography>
                              {participant.verificationStatus.phone && (
                                <VerifiedIcon sx={{ fontSize: 12, color: 'primary.main' }} />
                              )}
                            </Box>
                          }
                          secondary={
                            <Chip
                              label={participant.status === 'going' ? 'Идет' : 'Интересуется'}
                              size="small"
                              color={participant.status === 'going' ? 'success' : 'default'}
                              variant="outlined"
                            />
                          }
                        />
                      </ListItem>
                    ))}
                    {event.participants.length > 5 && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary={
                            <Typography variant="body2" color="text.secondary">
                              И еще {event.participants.length - 5} участников...
                            </Typography>
                          }
                        />
                      </ListItem>
                    )}
                  </List>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default EventDetailsPage;