import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Add as AddIcon,
  Event as EventIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Notifications as NotificationsIcon,
  CalendarToday as CalendarIcon,
  Verified as VerifiedIcon,
  Star as StarIcon,
  Visibility as ViewIcon,
  Share as ShareIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getEvents,
  getMyEvents,
  getUpcomingEvents,
  getFeaturedEvents,
  registerForEvent,
  cancelRegistration
} from '../../src/services/eventsService';
import { 
  Event,
  EventFilters 
} from '../../src/types/events.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`events-tabpanel-${index}`}
      aria-labelledby={`events-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const EventsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [events, setEvents] = useState<Event[]>([]);
  const [myEvents, setMyEvents] = useState<Event[]>([]);
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);
  const [featuredEvents, setFeaturedEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const tabs = [
    { label: 'Все события', key: 'all' },
    { label: 'Рекомендуемые', key: 'featured' },
    { label: 'Мои события', key: 'my' },
    { label: 'Ближайшие', key: 'upcoming' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadData();
  }, [user, router, activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      switch (activeTab) {
        case 0: // All events
          const allEvents = await getEvents();
          setEvents(allEvents.data);
          break;
        case 1: // Featured events
          const featured = await getFeaturedEvents();
          setFeaturedEvents(featured);
          break;
        case 2: // My events
          const myEventsData = await getMyEvents();
          setMyEvents(myEventsData.data);
          break;
        case 3: // Upcoming events
          const upcoming = await getUpcomingEvents();
          setUpcomingEvents(upcoming);
          break;
      }
    } catch (err: any) {
      setError('Ошибка загрузки событий');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, eventItem: Event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedEvent(eventItem);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedEvent(null);
  };

  const handleRegisterForEvent = async (event: Event) => {
    if (!event) return;
    
    try {
      setActionLoading(event.id);
      setError(null);

      await registerForEvent(event.id);
      setSuccess('Вы зарегистрированы на событие');
      loadData(); // Reload data
    } catch (err: any) {
      setError('Ошибка регистрации на событие');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const handleCancelRegistration = async (event: Event) => {
    if (!event) return;
    
    try {
      setActionLoading(event.id);
      setError(null);

      // This would need the registration ID, which we'd get from the event data
      // await cancelRegistration(registrationId);
      setSuccess('Регистрация отменена');
      loadData(); // Reload data
    } catch (err: any) {
      setError('Ошибка отмены регистрации');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const formatEventDate = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();
    
    if (start.toDateString() === end.toDateString()) {
      // Same day event
      return start.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      // Multi-day event
      return `${start.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' })} - ${end.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' })}`;
    }
  };

  const getEventTypeColor = (type: Event['type']) => {
    switch (type) {
      case 'conference':
        return 'primary';
      case 'workshop':
        return 'secondary';
      case 'meetup':
        return 'success';
      case 'party':
        return 'error';
      case 'concert':
        return 'warning';
      case 'sports':
        return 'info';
      default:
        return 'default';
    }
  };

  const getEventTypeLabel = (type: Event['type']) => {
    switch (type) {
      case 'conference':
        return 'Конференция';
      case 'workshop':
        return 'Мастер-класс';
      case 'meetup':
        return 'Митап';
      case 'party':
        return 'Вечеринка';
      case 'concert':
        return 'Концерт';
      case 'sports':
        return 'Спорт';
      case 'cultural':
        return 'Культура';
      case 'networking':
        return 'Нетворкинг';
      case 'educational':
        return 'Образование';
      case 'charity':
        return 'Благотворительность';
      default:
        return type;
    }
  };

  const isUserRegistered = (event: Event) => {
    return event.participants.some(p => p.id === user?.id);
  };

  const isUserOrganizer = (event: Event) => {
    return event.organizer.id === user?.id;
  };

  const getCurrentEvents = () => {
    switch (activeTab) {
      case 0:
        return events;
      case 1:
        return featuredEvents;
      case 2:
        return myEvents;
      case 3:
        return upcomingEvents;
      default:
        return [];
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>События - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите и создайте события в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/events" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
              <Typography variant={isMobile ? "h5" : "h4"}>
                <EventIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                События
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => router.push('/events/notifications')}>
                  <Badge badgeContent={0} color="error">
                    <NotificationsIcon />
                  </Badge>
                </IconButton>
                <IconButton onClick={() => router.push('/calendar')}>
                  <CalendarIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/events?filters=true')}>
                  <FilterIcon />
                </IconButton>
                <IconButton onClick={loadData} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/events/create')}
                  size={isMobile ? "small" : "medium"}
                >
                  Создать событие
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Tabs */}
            <Paper elevation={2} sx={{ mb: 3 }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
              >
                {tabs.map((tab, index) => (
                  <Tab key={index} label={tab.label} />
                ))}
              </Tabs>
            </Paper>

            {/* Content */}
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка событий...
                </Typography>
              </Box>
            ) : getCurrentEvents().length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <EventIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {activeTab === 2 ? 'У вас пока нет событий' : 'События не найдены'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {activeTab === 2 
                    ? 'Создайте свое первое событие или зарегистрируйтесь на существующее'
                    : 'Попробуйте изменить фильтры или создайте новое событие'
                  }
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/events/create')}
                >
                  Создать событие
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {getCurrentEvents().map((event) => (
                    <Grid item xs={12} sm={6} md={4} key={event.id}>
                      <Card 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[8]
                          },
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onClick={() => router.push(`/events/${event.id}`)}
                      >
                        <Box sx={{ position: 'relative' }}>
                          <CardMedia
                            component="img"
                            height="200"
                            image={event.media.coverImage}
                            alt={event.title}
                            sx={{ objectFit: 'cover' }}
                          />
                          
                          {/* Event Type Badge */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            zIndex: 1
                          }}>
                            <Chip
                              label={getEventTypeLabel(event.type)}
                              size="small"
                              color={getEventTypeColor(event.type) as any}
                              sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                            />
                          </Box>

                          {/* Menu Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, event)}
                              sx={{ backgroundColor: 'rgba(0,0,0,0.5)', color: 'white' }}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Box>

                          {/* Featured Badge */}
                          {activeTab === 1 && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<StarIcon />}
                                label="Рекомендуемое"
                                size="small"
                                color="warning"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}
                        </Box>

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" component="h3" gutterBottom>
                            {event.title}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {formatEventDate(event.startDate, event.endDate)}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {event.location.name}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <PeopleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {event.statistics.going} / {event.capacity} участников
                            </Typography>
                          </Box>

                          {event.shortDescription && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {event.shortDescription.length > 100 
                                ? `${event.shortDescription.substring(0, 100)}...`
                                : event.shortDescription
                              }
                            </Typography>
                          )}

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <Avatar
                              src={event.organizer.avatarUrl}
                              sx={{ width: 24, height: 24 }}
                            >
                              {event.organizer.firstName[0]}
                            </Avatar>
                            <Typography variant="caption" color="text.secondary">
                              {event.organizer.firstName} {event.organizer.lastName}
                            </Typography>
                            {event.organizer.isVerified && (
                              <VerifiedIcon sx={{ fontSize: 14, color: 'primary.main' }} />
                            )}
                          </Box>

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {event.tags.slice(0, 3).map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {event.tags.length > 3 && (
                              <Chip
                                label={`+${event.tags.length - 3}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Chip
                              icon={<ViewIcon />}
                              label={event.statistics.views}
                              size="small"
                              variant="outlined"
                            />
                            <Chip
                              icon={<PeopleIcon />}
                              label={event.statistics.interested}
                              size="small"
                              variant="outlined"
                            />
                          </Box>

                          <Box>
                            {event.tickets.length > 0 && event.tickets[0].type === 'free' ? (
                              <Chip label="Бесплатно" size="small" color="success" />
                            ) : event.tickets.length > 0 && (
                              <Chip 
                                label={`от ${event.tickets[0].price} ${event.tickets[0].currency}`} 
                                size="small" 
                                color="warning" 
                              />
                            )}
                          </Box>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => {
            if (selectedEvent) {
              router.push(`/events/${selectedEvent.id}`);
            }
            handleMenuClose();
          }}>
            Подробности
          </MenuItem>
          
          {selectedEvent && !isUserRegistered(selectedEvent) && !isUserOrganizer(selectedEvent) && (
            <MenuItem 
              onClick={() => selectedEvent && handleRegisterForEvent(selectedEvent)}
              disabled={actionLoading === selectedEvent?.id}
            >
              Зарегистрироваться
            </MenuItem>
          )}
          
          {selectedEvent && isUserRegistered(selectedEvent) && !isUserOrganizer(selectedEvent) && (
            <MenuItem 
              onClick={() => selectedEvent && handleCancelRegistration(selectedEvent)}
              disabled={actionLoading === selectedEvent?.id}
            >
              Отменить регистрацию
            </MenuItem>
          )}
          
          {selectedEvent && isUserOrganizer(selectedEvent) && (
            <MenuItem onClick={() => {
              if (selectedEvent) {
                router.push(`/events/${selectedEvent.id}/edit`);
              }
              handleMenuClose();
            }}>
              Редактировать
            </MenuItem>
          )}
          
          <MenuItem onClick={() => {
            if (selectedEvent) {
              // Share event logic
            }
            handleMenuClose();
          }}>
            <ShareIcon sx={{ mr: 1 }} />
            Поделиться
          </MenuItem>
          
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            Пожаловаться
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default EventsPage;
