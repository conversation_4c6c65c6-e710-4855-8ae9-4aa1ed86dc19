import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Divider,
  IconButton,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Payment as PaymentIcon,
  CreditCard as CreditCardIcon,
  AccountBalance as BankIcon,
  Phone as PhoneIcon,
  Security as SecurityIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckIcon,
  Star as StarIcon,
  Lock as LockIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';
import { useSubscription } from '../../../src/contexts/SubscriptionContext';
import { PaymentMethod, CreatePaymentMethodRequest } from '../../../src/types/subscription.types';

const PaymentMethodsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const {
    paymentMethods,
    loading,
    error,
    loadPaymentMethods,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPaymentMethod
  } = useSubscription();

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state for adding/editing payment methods
  const [formData, setFormData] = useState({
    type: 'card' as PaymentMethod['type'],
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: '',
    email: '',
    phone: '',
    isDefault: false,
    billingAddress: {
      country: 'RU',
      city: '',
      state: '',
      postalCode: '',
      addressLine1: '',
      addressLine2: ''
    }
  });

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadPaymentMethods();
  }, [user, router]);

  const handleAddPaymentMethod = async () => {
    try {
      setActionLoading('add');
      
      const request: CreatePaymentMethodRequest = {
        type: formData.type,
        provider: getProviderByType(formData.type),
        token: 'demo_token', // In real implementation, this would come from payment processor
        isDefault: formData.isDefault,
        billingAddress: formData.billingAddress
      };

      await addPaymentMethod(request);
      setSuccess('Способ оплаты успешно добавлен');
      setShowAddDialog(false);
      resetForm();
    } catch (err: any) {
      // Error handled by context
    } finally {
      setActionLoading(null);
    }
  };

  const handleSetDefault = async (methodId: string) => {
    try {
      setActionLoading(methodId);
      await setDefaultPaymentMethod(methodId);
      setSuccess('Способ оплаты установлен по умолчанию');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteMethod = async (methodId: string) => {
    try {
      setActionLoading(methodId);
      await deletePaymentMethod(methodId);
      setSuccess('Способ оплаты удален');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setActionLoading(null);
    }
  };

  const getProviderByType = (type: PaymentMethod['type']): string => {
    switch (type) {
      case 'card':
        return 'stripe';
      case 'paypal':
        return 'paypal';
      case 'apple_pay':
        return 'apple';
      case 'google_pay':
        return 'google';
      case 'bank_transfer':
        return 'bank';
      case 'crypto':
        return 'crypto';
      default:
        return 'unknown';
    }
  };

  const getPaymentMethodIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'card':
        return <CreditCardIcon />;
      case 'paypal':
        return <PaymentIcon />;
      case 'apple_pay':
        return <PhoneIcon />;
      case 'google_pay':
        return <PhoneIcon />;
      case 'bank_transfer':
        return <BankIcon />;
      case 'crypto':
        return <SecurityIcon />;
      default:
        return <PaymentIcon />;
    }
  };

  const getPaymentMethodName = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'card':
        return 'Банковская карта';
      case 'paypal':
        return 'PayPal';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      case 'bank_transfer':
        return 'Банковский перевод';
      case 'crypto':
        return 'Криптовалюта';
      default:
        return 'Неизвестный способ';
    }
  };

  const formatCardNumber = (number: string) => {
    return `**** **** **** ${number}`;
  };

  const resetForm = () => {
    setFormData({
      type: 'card',
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: '',
      email: '',
      phone: '',
      isDefault: false,
      billingAddress: {
        country: 'RU',
        city: '',
        state: '',
        postalCode: '',
        addressLine1: '',
        addressLine2: ''
      }
    });
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Способы оплаты - Likes & Love</title>
        <meta 
          name="description" 
          content="Управление способами оплаты в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <PaymentIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Способы оплаты
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowAddDialog(true)}
              >
                Добавить
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {/* Security Notice */}
            <Alert severity="info" sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LockIcon />
                <Typography variant="body2">
                  Все платежные данные защищены 256-битным шифрованием SSL и соответствуют стандартам PCI DSS
                </Typography>
              </Box>
            </Alert>

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка способов оплаты...
                </Typography>
              </Box>
            ) : paymentMethods.length === 0 ? (
              <Fade in timeout={600}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <PaymentIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Способы оплаты не добавлены
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Добавьте способ оплаты для оформления подписки
                  </Typography>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<AddIcon />}
                    onClick={() => setShowAddDialog(true)}
                  >
                    Добавить способ оплаты
                  </Button>
                </Box>
              </Fade>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {paymentMethods.map((method) => (
                    <Grid item xs={12} key={method.id}>
                      <Card 
                        elevation={method.isDefault ? 4 : 2}
                        sx={{
                          border: method.isDefault ? `2px solid ${theme.palette.primary.main}` : 'none',
                          position: 'relative'
                        }}
                      >
                        {/* Default Badge */}
                        {method.isDefault && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              zIndex: 1
                            }}
                          >
                            <Chip
                              label="По умолчанию"
                              color="primary"
                              size="small"
                              icon={<StarIcon />}
                            />
                          </Box>
                        )}

                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Box sx={{ mr: 2 }}>
                              {getPaymentMethodIcon(method.type)}
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography variant="h6">
                                {getPaymentMethodName(method.type)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {method.type === 'card' && method.details.last4 && (
                                  <>
                                    {formatCardNumber(method.details.last4)}
                                    {method.details.brand && ` • ${method.details.brand.toUpperCase()}`}
                                    {method.details.expiryMonth && method.details.expiryYear && 
                                      ` • ${method.details.expiryMonth}/${method.details.expiryYear}`
                                    }
                                  </>
                                )}
                                {method.type === 'paypal' && method.details.email && (
                                  method.details.email
                                )}
                                {method.type === 'bank_transfer' && method.details.bankName && (
                                  `${method.details.bankName} • ${method.details.accountNumber}`
                                )}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <VerifiedIcon color="success" fontSize="small" />
                              <Typography variant="caption" color="success.main">
                                Подтвержден
                              </Typography>
                            </Box>
                          </Box>

                          {/* Billing Address */}
                          {method.billingAddress && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                Адрес: {method.billingAddress.addressLine1}, {method.billingAddress.city}, {method.billingAddress.country}
                              </Typography>
                            </Box>
                          )}

                          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                            Добавлен: {new Date(method.createdAt).toLocaleDateString('ru-RU')}
                          </Typography>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            {!method.isDefault && (
                              <Button
                                size="small"
                                startIcon={<StarIcon />}
                                onClick={() => handleSetDefault(method.id)}
                                disabled={actionLoading === method.id}
                              >
                                По умолчанию
                              </Button>
                            )}
                            <Button
                              size="small"
                              startIcon={<EditIcon />}
                              onClick={() => setEditingMethod(method)}
                            >
                              Изменить
                            </Button>
                          </Box>

                          <IconButton
                            color="error"
                            onClick={() => handleDeleteMethod(method.id)}
                            disabled={actionLoading === method.id || method.isDefault}
                            title={method.isDefault ? 'Нельзя удалить способ оплаты по умолчанию' : 'Удалить'}
                          >
                            {actionLoading === method.id ? (
                              <CircularProgress size={20} />
                            ) : (
                              <DeleteIcon />
                            )}
                          </IconButton>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}

            {/* Add Payment Method Dialog */}
            <Dialog
              open={showAddDialog}
              onClose={() => setShowAddDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Добавить способ оплаты
              </DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1 }}>
                  {/* Payment Type Selection */}
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Тип оплаты</InputLabel>
                    <Select
                      value={formData.type}
                      onChange={(e) => setFormData({ ...formData, type: e.target.value as PaymentMethod['type'] })}
                      label="Тип оплаты"
                    >
                      <MenuItem value="card">Банковская карта</MenuItem>
                      <MenuItem value="paypal">PayPal</MenuItem>
                      <MenuItem value="apple_pay">Apple Pay</MenuItem>
                      <MenuItem value="google_pay">Google Pay</MenuItem>
                      <MenuItem value="bank_transfer">Банковский перевод</MenuItem>
                    </Select>
                  </FormControl>

                  {/* Card Details */}
                  {formData.type === 'card' && (
                    <Box>
                      <TextField
                        fullWidth
                        label="Номер карты"
                        value={formData.cardNumber}
                        onChange={(e) => setFormData({ ...formData, cardNumber: e.target.value })}
                        placeholder="1234 5678 9012 3456"
                        sx={{ mb: 2 }}
                      />
                      
                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Месяц"
                            value={formData.expiryMonth}
                            onChange={(e) => setFormData({ ...formData, expiryMonth: e.target.value })}
                            placeholder="MM"
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Год"
                            value={formData.expiryYear}
                            onChange={(e) => setFormData({ ...formData, expiryYear: e.target.value })}
                            placeholder="YYYY"
                          />
                        </Grid>
                      </Grid>

                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="CVV"
                            value={formData.cvv}
                            onChange={(e) => setFormData({ ...formData, cvv: e.target.value })}
                            placeholder="123"
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Имя владельца"
                            value={formData.cardholderName}
                            onChange={(e) => setFormData({ ...formData, cardholderName: e.target.value })}
                          />
                        </Grid>
                      </Grid>
                    </Box>
                  )}

                  {/* PayPal Details */}
                  {formData.type === 'paypal' && (
                    <TextField
                      fullWidth
                      label="Email PayPal"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      sx={{ mb: 2 }}
                    />
                  )}

                  {/* Billing Address */}
                  <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                    Адрес для выставления счетов
                  </Typography>
                  
                  <TextField
                    fullWidth
                    label="Адрес"
                    value={formData.billingAddress.addressLine1}
                    onChange={(e) => setFormData({
                      ...formData,
                      billingAddress: { ...formData.billingAddress, addressLine1: e.target.value }
                    })}
                    sx={{ mb: 2 }}
                  />

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Город"
                        value={formData.billingAddress.city}
                        onChange={(e) => setFormData({
                          ...formData,
                          billingAddress: { ...formData.billingAddress, city: e.target.value }
                        })}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Почтовый индекс"
                        value={formData.billingAddress.postalCode}
                        onChange={(e) => setFormData({
                          ...formData,
                          billingAddress: { ...formData.billingAddress, postalCode: e.target.value }
                        })}
                      />
                    </Grid>
                  </Grid>

                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.isDefault}
                        onChange={(e) => setFormData({ ...formData, isDefault: e.target.checked })}
                      />
                    }
                    label="Использовать по умолчанию"
                  />
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowAddDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleAddPaymentMethod}
                  variant="contained"
                  disabled={actionLoading === 'add'}
                  startIcon={actionLoading === 'add' ? <CircularProgress size={20} /> : <AddIcon />}
                >
                  Добавить
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PaymentMethodsPage;
