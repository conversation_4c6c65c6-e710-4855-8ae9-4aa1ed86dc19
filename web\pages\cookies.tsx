import React, { useState } from 'react';
import Head from 'next/head';
import { 
  Box, Typography, Container, Grid, 
  Switch, FormControlLabel, Button, Alert, 
  Accordion, AccordionSummary, AccordionDetails, 
  Chip, List, ListItem, ListItemText
} from '@mui/material';
import { ExpandMore, Cookie } from '@mui/icons-material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaCookie, FaShieldAlt, FaChartLine, FaAd, FaCog } from 'react-icons/fa';

const CookiesPage: React.FC = () => {
  const [cookieSettings, setCookieSettings] = useState({
    necessary: true,
    analytics: true,
    marketing: false,
    personalization: true,
    social: false
  });

  const handleCookieChange = (type: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    if (type === 'necessary') return;
    setCookieSettings(prev => ({
      ...prev,
      [type]: event.target.checked
    }));
  };

  const cookieTypes = [
    {
      id: 'necessary',
      name: 'Необходимые cookies',
      description: 'Обеспечивают базовую функциональность сайта и не могут быть отключены',
      icon: <FaShieldAlt />,
      required: true,
      details: ['Аутентификация пользователя', 'Безопасность сессии', 'Настройки языка'],
      duration: 'Сессия / 1 год'
    },
    {
      id: 'analytics',
      name: 'Аналитические cookies',
      description: 'Помогают понять, как посетители взаимодействуют с сайтом',
      icon: <FaChartLine />,
      required: false,
      details: ['Статистика посещений', 'Популярные страницы', 'Время на сайте'],
      duration: '2 года'
    },
    {
      id: 'marketing',
      name: 'Маркетинговые cookies',
      description: 'Используются для показа релевантной рекламы',
      icon: <FaAd />,
      required: false,
      details: ['Персонализированная реклама', 'Ретаргетинг', 'Отслеживание конверсий'],
      duration: '1 год'
    }
  ];

  const saveSettings = () => {
    localStorage.setItem('cookieSettings', JSON.stringify(cookieSettings));
    alert('Настройки cookies сохранены!');
  };

  return (
    <Layout>
      <Head>
        <title>Политика использования cookies - Likes & Love</title>
        <meta name="description" content="Политика использования cookies на сайте Likes & Love. Управление настройками cookies в соответствии с российским законодательством." />
      </Head>

      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
          color: 'text.primary',
          py: { xs: 8, md: 12 },
          textAlign: 'center'
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ color: 'primary.main', mb: 3, fontSize: '4rem' }}>
            <FaCookie />
          </Box>
          <EnhancedTypography variant="h1" romantic gradient shadow sx={{ mb: 3 }}>
            Политика использования cookies
          </EnhancedTypography>
          <EnhancedTypography variant="h5" readable sx={{ mb: 4, maxWidth: '800px', mx: 'auto' }}>
            Мы используем cookies для улучшения вашего опыта на сайте. 
            Узнайте, какие данные мы собираем и как ими управлять.
          </EnhancedTypography>
          <Alert severity="info" sx={{ maxWidth: 600, mx: 'auto' }}>
            <Typography variant="body2">
              <strong>Важно:</strong> Некоторые cookies необходимы для работы сайта и не могут быть отключены.
            </Typography>
          </Alert>
        </Container>
      </Box>

      {/* Cookie Settings */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
          Управление cookies
        </EnhancedTypography>
        
        <Grid container spacing={4}>
          {cookieTypes.map((type) => (
            <Grid item xs={12} key={type.id}>
              <EnhancedCard romantic hoverable>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', p: 3 }}>
                  <Box sx={{ color: 'primary.main', mr: 3, fontSize: '2rem', mt: 1 }}>
                    {type.icon}
                  </Box>
                  
                  <Box sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <EnhancedTypography variant="h5" romantic sx={{ mb: 1 }}>
                          {type.name}
                          {type.required && (
                            <Chip 
                              label="Обязательные" 
                              color="success" 
                              size="small" 
                              sx={{ ml: 2 }} 
                            />
                          )}
                        </EnhancedTypography>
                        <EnhancedTypography variant="body1" readable>
                          {type.description}
                        </EnhancedTypography>
                      </Box>
                      
                      <FormControlLabel
                        control={
                          <Switch
                            checked={cookieSettings[type.id as keyof typeof cookieSettings]}
                            onChange={handleCookieChange(type.id)}
                            disabled={type.required}
                            color="primary"
                          />
                        }
                        label=""
                        sx={{ ml: 2 }}
                      />
                    </Box>
                    
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMore />}>
                        <Typography variant="body2" color="primary">
                          Подробная информация
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                              Что включает:
                            </Typography>
                            <List dense>
                              {type.details.map((detail, index) => (
                                <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                                  <ListItemText 
                                    primary={`• ${detail}`}
                                    primaryTypographyProps={{ variant: 'body2' }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </Grid>
                          
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                              Срок хранения:
                            </Typography>
                            <Typography variant="body2">
                              {type.duration}
                            </Typography>
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                  </Box>
                </Box>
              </EnhancedCard>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <EnhancedButton
            variant="contained"
            size="large"
            romantic
            large
            onClick={saveSettings}
          >
            Сохранить настройки
          </EnhancedButton>
        </Box>
      </Container>

      {/* Legal Information */}
      <Box sx={{ bgcolor: 'background.default', py: 8 }}>
        <Container maxWidth="lg">
          <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
            Правовые основания
          </EnhancedTypography>
          
          <Grid container spacing={4}>
            {[
              {
                title: 'Федеральный закон "О персональных данных" №152-ФЗ',
                description: 'Регулирует обработку персональных данных в России'
              },
              {
                title: 'Федеральный закон "Об информации" №149-ФЗ',
                description: 'Устанавливает требования к обработке информации'
              }
            ].map((law, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <EnhancedCard romantic hoverable glassmorphism sx={{ height: '100%' }}>
                  <EnhancedTypography variant="h6" romantic sx={{ mb: 2 }}>
                    {law.title}
                  </EnhancedTypography>
                  <EnhancedTypography variant="body2" readable>
                    {law.description}
                  </EnhancedTypography>
                </EnhancedCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Contact Section */}
      <Container maxWidth="md" sx={{ py: 8, textAlign: 'center' }}>
        <EnhancedTypography variant="h3" romantic gradient shadow sx={{ mb: 3 }}>
          Остались вопросы?
        </EnhancedTypography>
        <EnhancedTypography variant="h6" readable sx={{ mb: 4 }}>
          Свяжитесь с нами по вопросам обработки персональных данных и использования cookies
        </EnhancedTypography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <EnhancedButton
            variant="contained"
            size="large"
            romantic
            large
            href="mailto:<EMAIL>"
          >
            <EMAIL>
          </EnhancedButton>
          <EnhancedButton
            variant="outlined"
            size="large"
            href="/help"
          >
            Центр помощи
          </EnhancedButton>
        </Box>
      </Container>
    </Layout>
  );
};

export default CookiesPage;
