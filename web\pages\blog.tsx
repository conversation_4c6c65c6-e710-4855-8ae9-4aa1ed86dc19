import React, { useState } from 'react';
import Head from 'next/head';
import { Box, Typography, Grid, Container, Chip, Avatar, TextField, InputAdornment } from '@mui/material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaSearch, FaHeart, FaCalendar, FaClock, FaComment } from 'react-icons/fa';

const BlogPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Все');

  const categories = ['Все', 'Отношения', 'Психология', 'Знакомства', 'Истории успеха', 'Советы'];

  const blogPosts = [
    {
      id: 1,
      title: 'Как создать идеальный профиль для знакомств: 10 проверенных советов',
      excerpt: 'Узнайте, как составить привлекательный профиль, который поможет найти вашу вторую половинку.',
      author: 'Анна Смирнова',
      authorRole: 'Психолог, эксперт по отношениям',
      authorAvatar: 'https://picsum.photos/50/50?random=1',
      date: '15 декабря 2024',
      readTime: '7 мин',
      category: 'Советы',
      image: 'https://picsum.photos/400/250?random=1',
      comments: 24,
      likes: 156,
      featured: true
    },
    {
      id: 2,
      title: 'История любви: Как Максим и Елена нашли друг друга через Likes & Love',
      excerpt: 'Вдохновляющая история пары, которая познакомилась на нашей платформе и теперь планирует свадьбу.',
      author: 'Редакция Likes & Love',
      authorRole: 'Команда контента',
      authorAvatar: 'https://picsum.photos/50/50?random=2',
      date: '12 декабря 2024',
      readTime: '5 мин',
      category: 'Истории успеха',
      image: 'https://picsum.photos/400/250?random=2',
      comments: 18,
      likes: 89,
      featured: false
    },
    {
      id: 3,
      title: 'Психология первого свидания: как произвести хорошее впечатление',
      excerpt: 'Научные исследования и практические советы о том, как вести себя на первом свидании.',
      author: 'Дмитрий Волков',
      authorRole: 'Клинический психолог',
      authorAvatar: 'https://picsum.photos/50/50?random=3',
      date: '10 декабря 2024',
      readTime: '8 мин',
      category: 'Психология',
      image: 'https://picsum.photos/400/250?random=3',
      comments: 31,
      likes: 203,
      featured: false
    },
    {
      id: 4,
      title: '5 признаков того, что отношения стоит продолжать',
      excerpt: 'Как понять, что ваши отношения развиваются в правильном направлении? Советы от семейных психологов.',
      author: 'Мария Петрова',
      authorRole: 'Семейный психолог',
      authorAvatar: 'https://picsum.photos/50/50?random=4',
      date: '5 декабря 2024',
      readTime: '9 мин',
      category: 'Отношения',
      image: 'https://picsum.photos/400/250?random=4',
      comments: 45,
      likes: 278,
      featured: false
    }
  ];

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'Все' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <Layout>
      <Head>
        <title>Блог Likes & Love - Советы по знакомствам и отношениям</title>
        <meta name="description" content="Экспертные советы по знакомствам, психологии отношений, истории успеха пар. Полезные статьи от специалистов Likes & Love." />
      </Head>

      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          textAlign: 'center'
        }}
      >
        <Container maxWidth="lg">
          <EnhancedTypography variant="h1" romantic gradient shadow sx={{ mb: 3, color: 'white' }}>
            Блог о любви и отношениях
          </EnhancedTypography>
          <EnhancedTypography variant="h5" readable sx={{ mb: 4, opacity: 0.95, maxWidth: '800px', mx: 'auto' }}>
            Экспертные советы, истории успеха и полезные материалы для тех, кто ищет любовь и строит отношения
          </EnhancedTypography>
        </Container>
      </Box>

      {/* Search and Filter Section */}
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ mb: 4 }}>
          <TextField
            fullWidth
            placeholder="Поиск по статьям..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <FaSearch />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
          />
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {categories.map((category) => (
              <Chip
                key={category}
                label={category}
                onClick={() => setSelectedCategory(category)}
                color={selectedCategory === category ? 'primary' : 'default'}
                variant={selectedCategory === category ? 'filled' : 'outlined'}
                sx={{ cursor: 'pointer' }}
              />
            ))}
          </Box>
        </Box>
      </Container>

      {/* Featured Post */}
      {featuredPost && (
        <Container maxWidth="lg" sx={{ mb: 6 }}>
          <EnhancedCard romantic hoverable sx={{ overflow: 'hidden' }}>
            <Grid container>
              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    height: { xs: 250, md: 400 },
                    backgroundImage: `url(${featuredPost.image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    bgcolor: 'grey.300'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Chip label="Рекомендуем" color="primary" size="small" sx={{ mb: 2, alignSelf: 'flex-start' }} />
                  
                  <EnhancedTypography variant="h4" romantic sx={{ mb: 2 }}>
                    {featuredPost.title}
                  </EnhancedTypography>
                  
                  <EnhancedTypography variant="body1" readable sx={{ mb: 3, flexGrow: 1 }}>
                    {featuredPost.excerpt}
                  </EnhancedTypography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Avatar src={featuredPost.authorAvatar} sx={{ width: 40, height: 40, mr: 2 }} />
                    <Box>
                      <Typography variant="subtitle2" fontWeight={600}>
                        {featuredPost.author}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {featuredPost.authorRole}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        <FaCalendar style={{ marginRight: 4 }} />
                        {featuredPost.date}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        <FaClock style={{ marginRight: 4 }} />
                        {featuredPost.readTime}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        <FaHeart style={{ marginRight: 4 }} />
                        {featuredPost.likes}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        <FaComment style={{ marginRight: 4 }} />
                        {featuredPost.comments}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <EnhancedButton
                    variant="contained"
                    romantic
                    large
                    href={`/blog/${featuredPost.id}`}
                  >
                    Читать статью
                  </EnhancedButton>
                </Box>
              </Grid>
            </Grid>
          </EnhancedCard>
        </Container>
      )}

      {/* Regular Posts */}
      <Container maxWidth="lg" sx={{ pb: 8 }}>
        <Grid container spacing={4}>
          {regularPosts.map((post) => (
            <Grid item xs={12} sm={6} md={4} key={post.id}>
              <EnhancedCard romantic hoverable sx={{ height: '100%' }}>
                <Box
                  sx={{
                    height: 200,
                    backgroundImage: `url(${post.image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    bgcolor: 'grey.300'
                  }}
                />
                <Box sx={{ p: 3 }}>
                  <Chip label={post.category} color="secondary" size="small" sx={{ mb: 2 }} />
                  
                  <EnhancedTypography variant="h6" romantic sx={{ mb: 2, minHeight: '3em' }}>
                    {post.title}
                  </EnhancedTypography>
                  
                  <EnhancedTypography variant="body2" readable sx={{ mb: 3, minHeight: '4em' }}>
                    {post.excerpt}
                  </EnhancedTypography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar src={post.authorAvatar} sx={{ width: 32, height: 32, mr: 1.5 }} />
                    <Box>
                      <Typography variant="caption" fontWeight={600}>
                        {post.author}
                      </Typography>
                      <Typography variant="caption" display="block" color="text.secondary">
                        {post.date}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      <FaClock style={{ marginRight: 4 }} />
                      {post.readTime}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        <FaHeart style={{ marginRight: 2 }} />
                        {post.likes}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        <FaComment style={{ marginRight: 2 }} />
                        {post.comments}
                      </Typography>
                    </Box>
                  </Box>
                  
                  <EnhancedButton
                    variant="outlined"
                    romantic
                    fullWidth
                    href={`/blog/${post.id}`}
                  >
                    Читать далее
                  </EnhancedButton>
                </Box>
              </EnhancedCard>
            </Grid>
          ))}
        </Grid>

        {filteredPosts.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <EnhancedTypography variant="h5" sx={{ mb: 2 }}>
              Статьи не найдены
            </EnhancedTypography>
            <EnhancedTypography variant="body1" readable>
              Попробуйте изменить поисковый запрос или выбрать другую категорию
            </EnhancedTypography>
          </Box>
        )}
      </Container>

      {/* Newsletter Section */}
      <Box sx={{ bgcolor: 'background.default', py: 8 }}>
        <Container maxWidth="md" sx={{ textAlign: 'center' }}>
          <EnhancedTypography variant="h3" romantic gradient shadow sx={{ mb: 3 }}>
            Подпишитесь на наш блог
          </EnhancedTypography>
          <EnhancedTypography variant="h6" readable sx={{ mb: 4 }}>
            Получайте новые статьи о любви и отношениях прямо на почту
          </EnhancedTypography>
          <Box sx={{ display: 'flex', gap: 2, maxWidth: 400, mx: 'auto' }}>
            <TextField
              fullWidth
              placeholder="Ваш email"
              variant="outlined"
            />
            <EnhancedButton
              variant="contained"
              romantic
              sx={{ minWidth: 120 }}
            >
              Подписаться
            </EnhancedButton>
          </Box>
        </Container>
      </Box>
    </Layout>
  );
};

export default BlogPage;
