import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip
} from '@mui/material';
import {
  ArrowBack,
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  NotificationImportant as NotificationImportantIcon,
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  People as PeopleIcon,
  Event as EventIcon,
  Security as SecurityIcon,
  Campaign as CampaignIcon,
  Save as SaveIcon,
  PhoneAndroid as PhoneAndroidIcon,
  Computer as ComputerIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для настроек Push-уведомлений
interface PushNotificationSettings {
  enabled: boolean;
  matches: boolean;
  messages: boolean;
  likes: boolean;
  superLikes: boolean;
  events: boolean;
  promotions: boolean;
  security: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
  frequency: 'instant' | 'bundled' | 'daily';
}

// Схема валидации
const pushNotificationSchema = yup.object({
  enabled: yup.boolean(),
  matches: yup.boolean(),
  messages: yup.boolean(),
  likes: yup.boolean(),
  superLikes: yup.boolean(),
  events: yup.boolean(),
  promotions: yup.boolean(),
  security: yup.boolean(),
  soundEnabled: yup.boolean(),
  vibrationEnabled: yup.boolean(),
  quietHoursEnabled: yup.boolean(),
  quietHoursStart: yup.string(),
  quietHoursEnd: yup.string(),
  frequency: yup.string().oneOf(['instant', 'bundled', 'daily'])
});

const PushNotificationsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'default'>('default');

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    watch,
    setValue
  } = useForm<PushNotificationSettings>({
    resolver: yupResolver(pushNotificationSchema),
    defaultValues: {
      enabled: user?.notifications?.push?.enabled ?? true,
      matches: user?.notifications?.push?.matches ?? true,
      messages: user?.notifications?.push?.messages ?? true,
      likes: user?.notifications?.push?.likes ?? true,
      superLikes: user?.notifications?.push?.superLikes ?? true,
      events: user?.notifications?.push?.events ?? false,
      promotions: user?.notifications?.push?.promotions ?? false,
      security: user?.notifications?.push?.security ?? true,
      soundEnabled: user?.notifications?.push?.soundEnabled ?? true,
      vibrationEnabled: user?.notifications?.push?.vibrationEnabled ?? true,
      quietHoursEnabled: user?.notifications?.push?.quietHoursEnabled ?? false,
      quietHoursStart: user?.notifications?.push?.quietHoursStart ?? '22:00',
      quietHoursEnd: user?.notifications?.push?.quietHoursEnd ?? '08:00',
      frequency: user?.notifications?.push?.frequency ?? 'instant'
    }
  });

  const watchedEnabled = watch('enabled');
  const watchedQuietHours = watch('quietHoursEnabled');

  // Проверка разрешений на уведомления
  useEffect(() => {
    if ('Notification' in window) {
      setPermissionStatus(Notification.permission);
    }
  }, []);

  // Категории уведомлений
  const notificationCategories = [
    {
      key: 'matches' as keyof PushNotificationSettings,
      label: 'Новые совпадения',
      description: 'Уведомления о новых взаимных лайках',
      icon: <PeopleIcon />,
      priority: 'high'
    },
    {
      key: 'messages' as keyof PushNotificationSettings,
      label: 'Сообщения',
      description: 'Новые сообщения в чатах',
      icon: <MessageIcon />,
      priority: 'high'
    },
    {
      key: 'likes' as keyof PushNotificationSettings,
      label: 'Лайки',
      description: 'Когда кто-то лайкает ваш профиль',
      icon: <FavoriteIcon />,
      priority: 'medium'
    },
    {
      key: 'superLikes' as keyof PushNotificationSettings,
      label: 'Супер лайки',
      description: 'Уведомления о супер лайках',
      icon: <NotificationImportantIcon />,
      priority: 'high'
    },
    {
      key: 'events' as keyof PushNotificationSettings,
      label: 'События',
      description: 'Приглашения на события и встречи',
      icon: <EventIcon />,
      priority: 'medium'
    },
    {
      key: 'security' as keyof PushNotificationSettings,
      label: 'Безопасность',
      description: 'Важные уведомления о безопасности',
      icon: <SecurityIcon />,
      priority: 'high'
    },
    {
      key: 'promotions' as keyof PushNotificationSettings,
      label: 'Акции и предложения',
      description: 'Специальные предложения и скидки',
      icon: <CampaignIcon />,
      priority: 'low'
    }
  ];

  // Опции частоты уведомлений
  const frequencyOptions = [
    { value: 'instant', label: 'Мгновенно', description: 'Получать уведомления сразу' },
    { value: 'bundled', label: 'Группировать', description: 'Объединять похожие уведомления' },
    { value: 'daily', label: 'Ежедневно', description: 'Сводка один раз в день' }
  ];

  const handleRequestPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      setPermissionStatus(permission);
      
      if (permission === 'granted') {
        setValue('enabled', true);
        setSuccess('Разрешение на уведомления получено');
      } else {
        setValue('enabled', false);
        setError('Разрешение на уведомления отклонено');
      }
    }
  };

  const handleSaveSettings = async (data: PushNotificationSettings) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для сохранения настроек Push-уведомлений
      // await updatePushNotificationSettings(data);

      // Обновляем профиль пользователя
      await updateProfile({
        notifications: {
          ...user?.notifications,
          push: data
        }
      });

      setSuccess('Настройки Push-уведомлений сохранены');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/settings/notifications');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  if (!user) {
    return (
      <Layout title="Push-уведомления">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Push-уведомления - Likes & Love</title>
        <meta name="description" content="Настройки Push-уведомлений в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Push-уведомления">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/notifications" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/notifications'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <NotificationsIcon fontSize="small" />
                Уведомления
              </Link>
              <Typography color="text.primary">Push</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <NotificationsIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Push-уведомления
                </Typography>
                {permissionStatus === 'granted' ? (
                  <Chip icon={<NotificationsActiveIcon />} label="Разрешены" color="success" />
                ) : (
                  <Chip icon={<NotificationsOffIcon />} label="Отключены" color="error" />
                )}
              </Box>
              <Typography variant="h6" color="text.secondary">
                Настройте уведомления для мобильного устройства
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Разрешение на уведомления */}
            {permissionStatus !== 'granted' && (
              <Alert severity="warning" sx={{ mb: 4 }}>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Для получения Push-уведомлений необходимо разрешить их в браузере
                </Typography>
                <Button
                  onClick={handleRequestPermission}
                  variant="contained"
                  size="small"
                  startIcon={<NotificationsIcon />}
                >
                  Разрешить уведомления
                </Button>
              </Alert>
            )}

            <form onSubmit={handleSubmit(handleSaveSettings)}>
              <Grid container spacing={3}>
                {/* Основные настройки */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Основные настройки
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <NotificationsIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Push-уведомления"
                            secondary="Включить или отключить все Push-уведомления"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="enabled"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                  disabled={permissionStatus !== 'granted'}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Категории уведомлений */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Типы уведомлений
                      </Typography>

                      <List>
                        {notificationCategories.map((category, index) => (
                          <React.Fragment key={category.key}>
                            <ListItem>
                              <ListItemIcon>
                                {category.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {category.label}
                                    <Chip 
                                      label={category.priority} 
                                      size="small" 
                                      color={getPriorityColor(category.priority) as any}
                                      variant="outlined"
                                    />
                                  </Box>
                                }
                                secondary={category.description}
                              />
                              <ListItemSecondaryAction>
                                <Controller
                                  name={category.key}
                                  control={control}
                                  render={({ field }) => (
                                    <Switch
                                      checked={field.value}
                                      onChange={field.onChange}
                                      disabled={!watchedEnabled}
                                    />
                                  )}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < notificationCategories.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Настройки поведения */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Поведение уведомлений
                      </Typography>

                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="frequency"
                            control={control}
                            render={({ field }) => (
                              <FormControl fullWidth>
                                <InputLabel>Частота уведомлений</InputLabel>
                                <Select {...field} label="Частота уведомлений" disabled={!watchedEnabled}>
                                  {frequencyOptions.map((option) => (
                                    <MenuItem key={option.value} value={option.value}>
                                      <Box>
                                        <Typography variant="body1">{option.label}</Typography>
                                        <Typography variant="caption" color="text.secondary">
                                          {option.description}
                                        </Typography>
                                      </Box>
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            )}
                          />
                        </Grid>
                      </Grid>

                      <List sx={{ mt: 2 }}>
                        <ListItem>
                          <ListItemIcon>
                            <PhoneAndroidIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Звук уведомлений"
                            secondary="Воспроизводить звук при получении уведомления"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="soundEnabled"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                  disabled={!watchedEnabled}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        <Divider />
                        <ListItem>
                          <ListItemIcon>
                            <PhoneAndroidIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Вибрация"
                            secondary="Вибрировать при получении уведомления"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="vibrationEnabled"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                  disabled={!watchedEnabled}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Тихие часы */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Тихие часы
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <ScheduleIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Включить тихие часы"
                            secondary="Не получать уведомления в определенное время"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="quietHoursEnabled"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                  disabled={!watchedEnabled}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>

                      {watchedQuietHours && (
                        <Grid container spacing={2} sx={{ mt: 2 }}>
                          <Grid item xs={6}>
                            <Controller
                              name="quietHoursStart"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth>
                                  <InputLabel>Начало</InputLabel>
                                  <Select {...field} label="Начало">
                                    {Array.from({ length: 24 }, (_, i) => {
                                      const hour = i.toString().padStart(2, '0');
                                      return (
                                        <MenuItem key={`${hour}:00`} value={`${hour}:00`}>
                                          {hour}:00
                                        </MenuItem>
                                      );
                                    })}
                                  </Select>
                                </FormControl>
                              )}
                            />
                          </Grid>
                          <Grid item xs={6}>
                            <Controller
                              name="quietHoursEnd"
                              control={control}
                              render={({ field }) => (
                                <FormControl fullWidth>
                                  <InputLabel>Конец</InputLabel>
                                  <Select {...field} label="Конец">
                                    {Array.from({ length: 24 }, (_, i) => {
                                      const hour = i.toString().padStart(2, '0');
                                      return (
                                        <MenuItem key={`${hour}:00`} value={`${hour}:00`}>
                                          {hour}:00
                                        </MenuItem>
                                      );
                                    })}
                                  </Select>
                                </FormControl>
                              )}
                            />
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Кнопки сохранения */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button onClick={handleBack} disabled={loading}>
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading || !isDirty}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Сохранение...' : 'Сохранить'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PushNotificationsPage;
