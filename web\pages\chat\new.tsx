import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  TextField,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Alert,
  CircularProgress,
  Checkbox,
  InputAdornment,
  Chip,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Search as SearchIcon,
  Message as MessageIcon,
  Verified as VerifiedIcon,
  Group as GroupIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  createConversation,
  searchConversations
} from '../../src/services/chatService';
import { getMatches } from '../../src/services/likesService';
import { 
  CreateConversationRequest,
  ChatUser
} from '../../src/types/chat.types';
import { Match } from '../../src/types/likes.types';

const NewChatPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [matches, setMatches] = useState<Match[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<ChatUser[]>([]);
  const [chatType, setChatType] = useState<'private' | 'group'>('private');
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadMatches();
  }, [user, router]);

  const loadMatches = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getMatches({
        page: 1,
        limit: 100,
        status: 'active'
      });
      
      setMatches(response.data);
    } catch (err: any) {
      setError('Ошибка загрузки совпадений');
    } finally {
      setLoading(false);
    }
  };

  const handleUserSelect = (match: Match) => {
    const otherUser = match.user1Id === user?.id ? match.user2 : match.user1;
    const chatUser: ChatUser = {
      id: otherUser.id,
      firstName: otherUser.firstName,
      lastName: otherUser.lastName,
      avatarUrl: otherUser.avatarUrl,
      isOnline: otherUser.isOnline,
      lastActiveAt: otherUser.lastActiveAt,
      verificationStatus: otherUser.verificationStatus
    };

    if (chatType === 'private') {
      setSelectedUsers([chatUser]);
    } else {
      const isSelected = selectedUsers.some(u => u.id === chatUser.id);
      if (isSelected) {
        setSelectedUsers(prev => prev.filter(u => u.id !== chatUser.id));
      } else {
        setSelectedUsers(prev => [...prev, chatUser]);
      }
    }
  };

  const handleCreateChat = async () => {
    if (selectedUsers.length === 0) {
      setError('Выберите пользователей для чата');
      return;
    }

    if (chatType === 'group' && !groupName.trim()) {
      setError('Введите название группы');
      return;
    }

    try {
      setCreating(true);
      setError(null);

      const request: CreateConversationRequest = {
        participantIds: selectedUsers.map(u => u.id),
        type: chatType,
        groupName: chatType === 'group' ? groupName.trim() : undefined,
        groupDescription: chatType === 'group' ? groupDescription.trim() : undefined
      };

      const result = await createConversation(request);
      
      if (result.success && result.conversation) {
        setSuccess('Чат создан успешно');
        setTimeout(() => {
          router.push(`/chat/${result.conversation!.id}`);
        }, 1000);
      } else {
        setError(result.error || 'Ошибка создания чата');
      }
    } catch (err: any) {
      setError('Ошибка создания чата');
    } finally {
      setCreating(false);
    }
  };

  const filteredMatches = matches.filter(match => {
    if (!searchQuery) return true;
    
    const otherUser = match.user1Id === user?.id ? match.user2 : match.user1;
    const searchLower = searchQuery.toLowerCase();
    
    return (
      otherUser.firstName.toLowerCase().includes(searchLower) ||
      otherUser.lastName.toLowerCase().includes(searchLower)
    );
  });

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Новый чат - Likes & Love</title>
        <meta 
          name="description" 
          content="Создайте новый чат в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                <Button
                  startIcon={<ArrowBack />}
                  onClick={() => router.back()}
                  sx={{ mr: 2 }}
                >
                  Назад
                </Button>
                <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                  <MessageIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                  Новый чат
                </Typography>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {success}
                </Alert>
              )}

              {/* Chat Type Selection */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Тип чата
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant={chatType === 'private' ? 'contained' : 'outlined'}
                    startIcon={<PersonIcon />}
                    onClick={() => {
                      setChatType('private');
                      setSelectedUsers([]);
                    }}
                  >
                    Личный чат
                  </Button>
                  <Button
                    variant={chatType === 'group' ? 'contained' : 'outlined'}
                    startIcon={<GroupIcon />}
                    onClick={() => {
                      setChatType('group');
                      setSelectedUsers([]);
                    }}
                  >
                    Групповой чат
                  </Button>
                </Box>
              </Box>

              {/* Group Settings */}
              {chatType === 'group' && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Настройки группы
                  </Typography>
                  <TextField
                    fullWidth
                    label="Название группы"
                    value={groupName}
                    onChange={(e) => setGroupName(e.target.value)}
                    placeholder="Введите название группы"
                    sx={{ mb: 2 }}
                  />
                  <TextField
                    fullWidth
                    label="Описание группы (необязательно)"
                    value={groupDescription}
                    onChange={(e) => setGroupDescription(e.target.value)}
                    placeholder="Введите описание группы"
                    multiline
                    rows={3}
                  />
                </Box>
              )}

              {/* Selected Users */}
              {selectedUsers.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Выбранные пользователи ({selectedUsers.length})
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedUsers.map((user) => (
                      <Chip
                        key={user.id}
                        avatar={<Avatar src={user.avatarUrl}>{user.firstName[0]}</Avatar>}
                        label={`${user.firstName} ${user.lastName}`}
                        onDelete={() => setSelectedUsers(prev => prev.filter(u => u.id !== user.id))}
                        color="primary"
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Search */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  placeholder="Поиск пользователей..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>

              {/* User List */}
              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка пользователей...
                  </Typography>
                </Box>
              ) : filteredMatches.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <MessageIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    {searchQuery ? 'Пользователи не найдены' : 'Нет доступных пользователей'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {searchQuery 
                      ? 'Попробуйте изменить поисковый запрос'
                      : 'Сначала найдите совпадения, чтобы начать общение'
                    }
                  </Typography>
                  {!searchQuery && (
                    <Button
                      variant="contained"
                      onClick={() => router.push('/discover')}
                    >
                      Найти людей
                    </Button>
                  )}
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Выберите пользователей
                    </Typography>
                    <List>
                      {filteredMatches.map((match) => {
                        const otherUser = match.user1Id === user.id ? match.user2 : match.user1;
                        const isSelected = selectedUsers.some(u => u.id === otherUser.id);
                        
                        return (
                          <ListItem
                            key={match.id}
                            button
                            onClick={() => handleUserSelect(match)}
                            sx={{
                              borderRadius: 2,
                              mb: 1,
                              border: 1,
                              borderColor: isSelected ? 'primary.main' : 'divider',
                              backgroundColor: isSelected ? 'primary.light' : 'transparent',
                              '&:hover': {
                                backgroundColor: isSelected ? 'primary.light' : 'action.hover'
                              }
                            }}
                          >
                            {chatType === 'group' && (
                              <Checkbox
                                checked={isSelected}
                                onChange={() => handleUserSelect(match)}
                                sx={{ mr: 1 }}
                              />
                            )}
                            
                            <ListItemAvatar>
                              <Avatar
                                src={otherUser.avatarUrl}
                                sx={{ width: 56, height: 56 }}
                              >
                                {otherUser.firstName[0]}
                              </Avatar>
                            </ListItemAvatar>
                            
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="subtitle1">
                                    {otherUser.firstName} {otherUser.lastName}
                                  </Typography>
                                  {otherUser.verificationStatus.phone && (
                                    <VerifiedIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                                  )}
                                </Box>
                              }
                              secondary={
                                <Typography variant="body2" color="text.secondary">
                                  {otherUser.isOnline ? 'Онлайн' : 'Был(а) в сети недавно'}
                                  {match.compatibilityScore && ` • ${match.compatibilityScore}% совместимость`}
                                </Typography>
                              }
                            />
                          </ListItem>
                        );
                      })}
                    </List>
                  </Box>
                </Fade>
              )}

              {/* Create Button */}
              {selectedUsers.length > 0 && (
                <Box sx={{ mt: 4, textAlign: 'center' }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleCreateChat}
                    disabled={creating}
                    startIcon={creating ? <CircularProgress size={20} /> : <MessageIcon />}
                  >
                    {creating ? 'Создание...' : 
                     chatType === 'private' ? 'Начать чат' : 
                     `Создать группу (${selectedUsers.length} участников)`}
                  </Button>
                </Box>
              )}
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default NewChatPage;
