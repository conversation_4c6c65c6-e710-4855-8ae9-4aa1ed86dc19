import React, { useState } from 'react';
import Head from 'next/head';
import {
  Box,
  Container,
  Grid,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper
} from '@mui/material';
import {
  ExpandMore,
  Search,
  Help,
  Security,
  Payment,
  Person,
  Chat,
  Favorite,
  Settings,
  Phone,
  Email,
  QuestionAnswer
} from '@mui/icons-material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaQuestionCircle, FaHeart, FaShieldAlt, FaHeadset } from 'react-icons/fa';

const HelpPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'Все вопросы', icon: <Help /> },
    { id: 'account', name: 'Аккаунт', icon: <Person /> },
    { id: 'matching', name: 'Знакомства', icon: <Favorite /> },
    { id: 'chat', name: 'Сообщения', icon: <Chat /> },
    { id: 'payment', name: 'Оплата', icon: <Payment /> },
    { id: 'safety', name: 'Безопасность', icon: <Security /> },
    { id: 'settings', name: 'Настройки', icon: <Settings /> }
  ];

  const faqItems = [
    {
      category: 'account',
      question: 'Как создать привлекательный профиль?',
      answer: 'Добавьте качественные фотографии, напишите интересное описание о себе, укажите свои увлечения и интересы. Будьте честными и позитивными.'
    },
    {
      category: 'account',
      question: 'Как верифицировать профиль?',
      answer: 'Перейдите в настройки профиля и загрузите селфи с документом. Верификация занимает до 24 часов и повышает доверие к вашему профилю.'
    },
    {
      category: 'matching',
      question: 'Как работает алгоритм подбора?',
      answer: 'Мы учитываем ваши предпочтения, интересы, местоположение и активность. Чем больше информации в профиле, тем точнее подбор.'
    },
    {
      category: 'matching',
      question: 'Что делать, если закончились анкеты?',
      answer: 'Расширьте критерии поиска в настройках, увеличьте радиус поиска или попробуйте позже - новые пользователи регистрируются каждый день.'
    },
    {
      category: 'chat',
      question: 'Как начать разговор?',
      answer: 'Обратите внимание на интересы собеседника, прокомментируйте фото или задайте вопрос о хобби. Избегайте банальных "Привет, как дела?"'
    },
    {
      category: 'chat',
      question: 'Почему не приходят уведомления о сообщениях?',
      answer: 'Проверьте настройки уведомлений в приложении и на устройстве. Убедитесь, что приложение не заблокировано антивирусом.'
    },
    {
      category: 'payment',
      question: 'Что включает Premium подписка?',
      answer: 'Безлимитные лайки, возможность видеть кто лайкнул вас, суперлайки, возврат последнего действия, приоритет в показе анкет.'
    },
    {
      category: 'payment',
      question: 'Как отменить подписку?',
      answer: 'Зайдите в настройки аккаунта, выберите "Подписка" и нажмите "Отменить". Подписка будет активна до конца оплаченного периода.'
    },
    {
      category: 'safety',
      question: 'Как пожаловаться на пользователя?',
      answer: 'Нажмите на три точки в профиле пользователя и выберите "Пожаловаться". Опишите причину жалобы - мы рассмотрим её в течение 24 часов.'
    },
    {
      category: 'safety',
      question: 'Как заблокировать пользователя?',
      answer: 'В профиле пользователя нажмите "Заблокировать". Заблокированный пользователь не сможет видеть ваш профиль и писать вам сообщения.'
    },
    {
      category: 'settings',
      question: 'Как изменить настройки приватности?',
      answer: 'Перейдите в Настройки → Приватность. Здесь можно настроить видимость профиля, показ активности и другие параметры конфиденциальности.'
    },
    {
      category: 'settings',
      question: 'Как удалить аккаунт?',
      answer: 'В настройках выберите "Удалить аккаунт". Внимание: это действие необратимо, все данные будут удалены безвозвратно.'
    }
  ];

  const filteredFAQ = faqItems.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const contactMethods = [
    {
      title: 'Онлайн-чат',
      description: 'Быстрая помощь в режиме реального времени',
      icon: <Chat />,
      action: 'Начать чат',
      available: '24/7'
    },
    {
      title: 'Email поддержка',
      description: 'Подробный ответ на сложные вопросы',
      icon: <Email />,
      action: '<EMAIL>',
      available: 'Ответ в течение 24 часов'
    },
    {
      title: 'Телефон',
      description: 'Срочные вопросы и техническая поддержка',
      icon: <Phone />,
      action: '+7 (495) 123-45-67',
      available: 'Пн-Пт 9:00-18:00'
    }
  ];

  return (
    <Layout>
      <Head>
        <title>Центр помощи - Likes & Love</title>
        <meta name="description" content="Ответы на часто задаваемые вопросы, руководства и поддержка пользователей Likes & Love" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <EnhancedTypography variant="h3" romantic gradient shadow sx={{ mb: 2 }}>
            🆘 Центр помощи
          </EnhancedTypography>
          <EnhancedTypography variant="h6" readable sx={{ mb: 3 }}>
            Найдите ответы на ваши вопросы или свяжитесь с нашей службой поддержки
          </EnhancedTypography>
          
          <TextField
            fullWidth
            placeholder="Поиск по вопросам..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ maxWidth: 600, mx: 'auto' }}
          />
        </Box>

        {/* Categories */}
        <Box sx={{ mb: 4, display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'center' }}>
          {categories.map((category) => (
            <Chip
              key={category.id}
              label={category.name}
              icon={category.icon}
              onClick={() => setSelectedCategory(category.id)}
              color={selectedCategory === category.id ? 'primary' : 'default'}
              variant={selectedCategory === category.id ? 'filled' : 'outlined'}
              sx={{ cursor: 'pointer' }}
            />
          ))}
        </Box>

        <Grid container spacing={4}>
          {/* FAQ Section */}
          <Grid item xs={12} md={8}>
            <EnhancedTypography variant="h4" romantic sx={{ mb: 3 }}>
              Часто задаваемые вопросы
            </EnhancedTypography>
            
            {filteredFAQ.length > 0 ? (
              filteredFAQ.map((item, index) => (
                <Accordion key={index} sx={{ mb: 1 }}>
                  <AccordionSummary expandIcon={<ExpandMore />}>
                    <Typography variant="h6" sx={{ fontWeight: 500 }}>
                      {item.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body1" readable>
                      {item.answer}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              ))
            ) : (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <FaQuestionCircle style={{ fontSize: '3rem', color: '#ccc', marginBottom: '1rem' }} />
                <Typography variant="h6" gutterBottom>
                  Вопросы не найдены
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Попробуйте изменить поисковый запрос или выбрать другую категорию
                </Typography>
              </Paper>
            )}
          </Grid>

          {/* Contact Section */}
          <Grid item xs={12} md={4}>
            <EnhancedTypography variant="h4" romantic sx={{ mb: 3 }}>
              Связаться с нами
            </EnhancedTypography>
            
            {contactMethods.map((method, index) => (
              <EnhancedCard key={index} romantic hoverable sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', p: 2 }}>
                  <Box sx={{ color: 'primary.main', mr: 2, mt: 0.5 }}>
                    {method.icon}
                  </Box>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" sx={{ mb: 1 }}>
                      {method.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {method.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 2 }}>
                      {method.available}
                    </Typography>
                    <EnhancedButton
                      variant="outlined"
                      romantic
                      size="small"
                      fullWidth
                      href={method.action.includes('@') ? `mailto:${method.action}` : 
                            method.action.includes('+') ? `tel:${method.action}` : '#'}
                    >
                      {method.action}
                    </EnhancedButton>
                  </Box>
                </Box>
              </EnhancedCard>
            ))}

            {/* Quick Links */}
            <EnhancedCard romantic sx={{ mt: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, p: 2, pb: 0 }}>
                Полезные ссылки
              </Typography>
              <List dense>
                <ListItem button component="a" href="/safety">
                  <ListItemIcon>
                    <Security color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Безопасность" />
                </ListItem>
                <ListItem button component="a" href="/privacy">
                  <ListItemIcon>
                    <Person color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Политика конфиденциальности" />
                </ListItem>
                <ListItem button component="a" href="/terms">
                  <ListItemIcon>
                    <QuestionAnswer color="primary" />
                  </ListItemIcon>
                  <ListItemText primary="Пользовательское соглашение" />
                </ListItem>
              </List>
            </EnhancedCard>
          </Grid>
        </Grid>

        {/* Bottom CTA */}
        <Box sx={{ textAlign: 'center', mt: 6, p: 4, bgcolor: 'background.paper', borderRadius: 2 }}>
          <FaHeadset style={{ fontSize: '3rem', color: '#ff6b9d', marginBottom: '1rem' }} />
          <EnhancedTypography variant="h5" romantic sx={{ mb: 2 }}>
            Не нашли ответ на свой вопрос?
          </EnhancedTypography>
          <EnhancedTypography variant="body1" readable sx={{ mb: 3 }}>
            Наша служба поддержки готова помочь вам 24/7
          </EnhancedTypography>
          <EnhancedButton
            variant="contained"
            size="large"
            romantic
            large
            href="mailto:<EMAIL>"
          >
            Написать в поддержку
          </EnhancedButton>
        </Box>
      </Container>
    </Layout>
  );
};

export default HelpPage;
