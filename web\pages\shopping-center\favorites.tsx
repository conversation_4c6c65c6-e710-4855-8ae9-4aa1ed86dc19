import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Skeleton,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Star as StarIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Event as EventIcon,
  LocalOffer as DiscountIcon,
  Upgrade as UpgradeIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../src/providers/AuthProvider';
import { useSubscription } from '../../src/providers/SubscriptionProvider';
import Layout from '../../components/Layout/Layout';
import { FeatureLock } from '../../components/Subscription/FeatureLock';
import { shoppingCenterService } from '../../src/services/shoppingCenterService';
import { ShoppingCenter, FavoriteShoppingCenter } from '../../src/types/shoppingCenter.types';

interface FavoriteShoppingCentersPageProps {
  initialFavorites: FavoriteShoppingCenter[];
}

const FavoriteShoppingCentersPage: React.FC<FavoriteShoppingCentersPageProps> = ({
  initialFavorites
}) => {
  const { t } = useTranslation('common');
  const { user, isLoading: authLoading } = useAuth();
  const { subscription, isLoading: subLoading } = useSubscription();
  const router = useRouter();

  const [favorites, setFavorites] = useState<FavoriteShoppingCenter[]>(initialFavorites);
  const [isLoading, setIsLoading] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; centerId?: string }>({
    open: false
  });
  const [upgradeDialog, setUpgradeDialog] = useState(false);

  // Определяем лимиты по подписке
  const getLimits = () => {
    if (!subscription) return { max: 1, current: favorites.length };
    
    switch (subscription.plan) {
      case 'premium':
        return { max: 5, current: favorites.length };
      case 'vip':
        return { max: 10, current: favorites.length };
      default:
        return { max: 1, current: favorites.length };
    }
  };

  const limits = getLimits();
  const canAddMore = limits.current < limits.max;

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login');
    }
  }, [user, authLoading, router]);

  const handleRemoveFavorite = async (centerId: string) => {
    try {
      setIsLoading(true);
      await shoppingCenterService.removeFavorite(centerId);
      setFavorites(prev => prev.filter(fav => fav.shoppingCenter.id !== centerId));
      setDeleteDialog({ open: false });
    } catch (error) {
      console.error('Error removing favorite:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddFavorite = () => {
    if (!canAddMore) {
      setUpgradeDialog(true);
      return;
    }
    router.push('/shopping-centers?action=add-favorite');
  };

  const handleUpgrade = () => {
    router.push('/subscription/shopping-centers');
  };

  if (authLoading || subLoading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Grid container spacing={3}>
            {[...Array(6)].map((_, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card>
                  <Skeleton variant="rectangular" height={200} />
                  <CardContent>
                    <Skeleton variant="text" height={32} />
                    <Skeleton variant="text" height={24} />
                    <Skeleton variant="text" height={20} />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Layout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Мои любимые ТЦ - Likes Love</title>
        <meta 
          name="description" 
          content="Управляйте списком ваших любимых торговых центров. Получайте уведомления о событиях и скидках." 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          {/* Header */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Мои любимые ТЦ
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              Управляйте списком ваших любимых торговых центров
            </Typography>
            
            {/* Лимиты */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
              <Chip
                label={`${limits.current} из ${limits.max} ТЦ`}
                color={limits.current >= limits.max ? 'error' : 'primary'}
                variant="outlined"
              />
              {subscription?.plan !== 'vip' && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<UpgradeIcon />}
                  onClick={handleUpgrade}
                >
                  Увеличить лимит
                </Button>
              )}
            </Box>
          </Box>

          {/* Add Button */}
          <Box sx={{ mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddFavorite}
              disabled={!canAddMore}
              size="large"
            >
              Добавить ТЦ
            </Button>
            {!canAddMore && (
              <Typography variant="caption" color="error" sx={{ ml: 2 }}>
                Достигнут лимит для вашего плана
              </Typography>
            )}
          </Box>

          {/* Favorites Grid */}
          {favorites.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" gutterBottom>
                У вас пока нет любимых ТЦ
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Добавьте торговые центры, которые вы часто посещаете
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddFavorite}
                sx={{ mt: 2 }}
              >
                Добавить первый ТЦ
              </Button>
            </Box>
          ) : (
            <Grid container spacing={3}>
              <AnimatePresence>
                {favorites.map((favorite) => (
                  <Grid item xs={12} sm={6} md={4} key={favorite.shoppingCenter.id}>
                    <motion.div
                      layout
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card 
                        sx={{ 
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          position: 'relative',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 4,
                          },
                          transition: 'all 0.2s ease-in-out',
                        }}
                      >
                        {/* Remove Button */}
                        <IconButton
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            bgcolor: 'background.paper',
                            zIndex: 1,
                            '&:hover': { bgcolor: 'error.light', color: 'white' }
                          }}
                          onClick={() => setDeleteDialog({ 
                            open: true, 
                            centerId: favorite.shoppingCenter.id 
                          })}
                        >
                          <DeleteIcon />
                        </IconButton>

                        <CardMedia
                          component="img"
                          height="200"
                          image={favorite.shoppingCenter.image || '/images/shopping-center-placeholder.jpg'}
                          alt={favorite.shoppingCenter.name}
                          sx={{ cursor: 'pointer' }}
                          onClick={() => router.push(`/shopping-center/${favorite.shoppingCenter.slug}`)}
                        />

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" gutterBottom>
                            {favorite.shoppingCenter.name}
                          </Typography>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" color="action" />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                              {favorite.shoppingCenter.address}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <StarIcon fontSize="small" color="warning" />
                            <Typography variant="body2" sx={{ ml: 0.5 }}>
                              {favorite.shoppingCenter.rating}
                            </Typography>
                          </Box>

                          {/* Stats */}
                          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                            <Tooltip title="Пользователи">
                              <Badge badgeContent={favorite.shoppingCenter.usersCount} color="primary">
                                <PeopleIcon fontSize="small" />
                              </Badge>
                            </Tooltip>
                            <Tooltip title="События">
                              <Badge badgeContent={favorite.shoppingCenter.eventsCount} color="secondary">
                                <EventIcon fontSize="small" />
                              </Badge>
                            </Tooltip>
                            <Tooltip title="Скидки">
                              <Badge badgeContent={favorite.shoppingCenter.discountsCount} color="error">
                                <DiscountIcon fontSize="small" />
                              </Badge>
                            </Tooltip>
                          </Box>

                          <Typography variant="caption" color="text.secondary">
                            Добавлен: {new Date(favorite.addedAt).toLocaleDateString('ru-RU')}
                          </Typography>
                        </CardContent>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </AnimatePresence>
            </Grid>
          )}

          {/* Delete Confirmation Dialog */}
          <Dialog
            open={deleteDialog.open}
            onClose={() => setDeleteDialog({ open: false })}
          >
            <DialogTitle>Удалить из избранного?</DialogTitle>
            <DialogContent>
              <Typography>
                Вы уверены, что хотите удалить этот торговый центр из списка любимых?
                Вы перестанете получать уведомления о событиях и скидках.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDeleteDialog({ open: false })}>
                Отмена
              </Button>
              <Button
                onClick={() => deleteDialog.centerId && handleRemoveFavorite(deleteDialog.centerId)}
                color="error"
                disabled={isLoading}
              >
                Удалить
              </Button>
            </DialogActions>
          </Dialog>

          {/* Upgrade Dialog */}
          <Dialog
            open={upgradeDialog}
            onClose={() => setUpgradeDialog(false)}
          >
            <DialogTitle>Увеличьте лимит любимых ТЦ</DialogTitle>
            <DialogContent>
              <Typography gutterBottom>
                Вы достигли лимита для вашего текущего плана.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Premium: до 5 ТЦ (299₽/мес)
                • VIP: до 10 ТЦ (599₽/мес)
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setUpgradeDialog(false)}>
                Отмена
              </Button>
              <Button
                onClick={handleUpgrade}
                variant="contained"
              >
                Обновить план
              </Button>
            </DialogActions>
          </Dialog>
        </Container>
      </Layout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ req, locale }) => {
  try {
    // В реальном приложении здесь будет получение данных с сервера
    const initialFavorites: FavoriteShoppingCenter[] = [];

    return {
      props: {
        initialFavorites,
        ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
      },
    };
  } catch (error) {
    return {
      props: {
        initialFavorites: [],
        ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
      },
    };
  }
};

export default FavoriteShoppingCentersPage;
