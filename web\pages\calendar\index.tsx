import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  CalendarToday as CalendarIcon,
  Event as EventIcon,
  People as MeetingIcon,
  Add as AddIcon,
  Today as TodayIcon,
  NavigateBefore as PrevIcon,
  NavigateNext as NextIcon,
  ViewWeek as WeekIcon,
  ViewDay as DayIcon,
  ViewModule as MonthIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getMyMeetings,
  getUpcomingMeetings
} from '../../src/services/meetingsService';
import { 
  getMyEvents,
  getUpcomingEvents
} from '../../src/services/eventsService';
import { 
  Meeting 
} from '../../src/types/meetings.types';
import { 
  Event 
} from '../../src/types/events.types';

interface CalendarItem {
  id: string;
  title: string;
  type: 'meeting' | 'event';
  startDate: string;
  endDate: string;
  location: string;
  participants?: number;
  status: string;
  data: Meeting | Event;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`calendar-tabpanel-${index}`}
      aria-labelledby={`calendar-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const CalendarPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');

  const tabs = [
    { label: 'Все', key: 'all' },
    { label: 'Встречи', key: 'meetings' },
    { label: 'События', key: 'events' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadData();
  }, [user, router]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [meetingsData, eventsData] = await Promise.all([
        getMyMeetings('all'),
        getMyEvents('all')
      ]);
      
      setMeetings(meetingsData.data);
      setEvents(eventsData.data);
    } catch (err: any) {
      setError('Ошибка загрузки календаря');
    } finally {
      setLoading(false);
    }
  };

  const getAllCalendarItems = (): CalendarItem[] => {
    const items: CalendarItem[] = [];
    
    // Add meetings
    meetings.forEach(meeting => {
      items.push({
        id: meeting.id,
        title: meeting.title,
        type: 'meeting',
        startDate: meeting.scheduledAt,
        endDate: new Date(new Date(meeting.scheduledAt).getTime() + meeting.duration * 60000).toISOString(),
        location: meeting.location.name,
        participants: meeting.participants.length,
        status: meeting.status,
        data: meeting
      });
    });
    
    // Add events
    events.forEach(event => {
      items.push({
        id: event.id,
        title: event.title,
        type: 'event',
        startDate: event.startDate,
        endDate: event.endDate,
        location: event.location.name,
        participants: event.statistics.going,
        status: event.status,
        data: event
      });
    });
    
    return items.sort((a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime());
  };

  const getFilteredItems = () => {
    const allItems = getAllCalendarItems();
    
    switch (activeTab) {
      case 1: // Meetings only
        return allItems.filter(item => item.type === 'meeting');
      case 2: // Events only
        return allItems.filter(item => item.type === 'event');
      default: // All
        return allItems;
    }
  };

  const getTodayItems = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return getFilteredItems().filter(item => {
      const itemDate = new Date(item.startDate);
      return itemDate >= today && itemDate < tomorrow;
    });
  };

  const getUpcomingItems = () => {
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    return getFilteredItems().filter(item => {
      const itemDate = new Date(item.startDate);
      return itemDate > now && itemDate <= nextWeek;
    });
  };

  const formatItemTime = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start.toDateString() === end.toDateString()) {
      return `${start.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })} - ${end.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return `${start.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' })} - ${end.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' })}`;
    }
  };

  const formatItemDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Сегодня';
    if (diffInDays === 1) return 'Завтра';
    if (diffInDays === -1) return 'Вчера';
    if (diffInDays > 1 && diffInDays <= 7) return `Через ${diffInDays} дн.`;
    if (diffInDays < -1 && diffInDays >= -7) return `${Math.abs(diffInDays)} дн. назад`;
    
    return date.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' });
  };

  const getItemTypeColor = (type: 'meeting' | 'event') => {
    return type === 'meeting' ? 'primary' : 'secondary';
  };

  const getItemTypeIcon = (type: 'meeting' | 'event') => {
    return type === 'meeting' ? <MeetingIcon /> : <EventIcon />;
  };

  const handleItemClick = (item: CalendarItem) => {
    if (item.type === 'meeting') {
      router.push(`/meetings/${item.id}`);
    } else {
      router.push(`/events/${item.id}`);
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    
    switch (viewMode) {
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
    }
    
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  if (!user) {
    return null;
  }

  const todayItems = getTodayItems();
  const upcomingItems = getUpcomingItems();

  return (
    <>
      <Head>
        <title>Календарь - Likes & Love</title>
        <meta 
          name="description" 
          content="Календарь встреч и событий в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/calendar" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
              <Typography variant={isMobile ? "h5" : "h4"}>
                <CalendarIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Календарь
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={loadData} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/meetings/create')}
                  size={isMobile ? "small" : "medium"}
                >
                  Встреча
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/events/create')}
                  size={isMobile ? "small" : "medium"}
                >
                  Событие
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {/* Calendar Controls */}
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <IconButton onClick={() => navigateDate('prev')}>
                    <PrevIcon />
                  </IconButton>
                  <Button onClick={goToToday} startIcon={<TodayIcon />}>
                    Сегодня
                  </Button>
                  <IconButton onClick={() => navigateDate('next')}>
                    <NextIcon />
                  </IconButton>
                  <Typography variant="h6" sx={{ ml: 2 }}>
                    {currentDate.toLocaleDateString('ru-RU', { 
                      year: 'numeric', 
                      month: 'long',
                      ...(viewMode === 'day' && { day: 'numeric' })
                    })}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <IconButton 
                    onClick={() => setViewMode('day')}
                    color={viewMode === 'day' ? 'primary' : 'default'}
                  >
                    <DayIcon />
                  </IconButton>
                  <IconButton 
                    onClick={() => setViewMode('week')}
                    color={viewMode === 'week' ? 'primary' : 'default'}
                  >
                    <WeekIcon />
                  </IconButton>
                  <IconButton 
                    onClick={() => setViewMode('month')}
                    color={viewMode === 'month' ? 'primary' : 'default'}
                  >
                    <MonthIcon />
                  </IconButton>
                </Box>
              </Box>
            </Paper>

            {/* Tabs */}
            <Paper elevation={2} sx={{ mb: 3 }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
              >
                {tabs.map((tab, index) => (
                  <Tab key={index} label={tab.label} />
                ))}
              </Tabs>
            </Paper>

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка календаря...
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {/* Today's Items */}
                <Grid item xs={12} md={6}>
                  <Card elevation={3}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        <TodayIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Сегодня ({todayItems.length})
                      </Typography>
                      
                      {todayItems.length === 0 ? (
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4, textAlign: 'center' }}>
                          На сегодня ничего не запланировано
                        </Typography>
                      ) : (
                        <List dense>
                          {todayItems.map((item) => (
                            <ListItem
                              key={item.id}
                              button
                              onClick={() => handleItemClick(item)}
                              sx={{ borderRadius: 1, mb: 1 }}
                            >
                              <ListItemAvatar>
                                <Avatar color={getItemTypeColor(item.type) as any}>
                                  {getItemTypeIcon(item.type)}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Typography variant="subtitle2">
                                      {item.title}
                                    </Typography>
                                    <Chip
                                      label={item.type === 'meeting' ? 'Встреча' : 'Событие'}
                                      size="small"
                                      color={getItemTypeColor(item.type) as any}
                                      variant="outlined"
                                    />
                                  </Box>
                                }
                                secondary={
                                  <Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                                      <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption">
                                        {formatItemTime(item.startDate, item.endDate)}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <LocationIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption">
                                        {item.location}
                                      </Typography>
                                    </Box>
                                  </Box>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Upcoming Items */}
                <Grid item xs={12} md={6}>
                  <Card elevation={3}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        <ScheduleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Ближайшие ({upcomingItems.length})
                      </Typography>
                      
                      {upcomingItems.length === 0 ? (
                        <Typography variant="body2" color="text.secondary" sx={{ py: 4, textAlign: 'center' }}>
                          Нет запланированных мероприятий на ближайшую неделю
                        </Typography>
                      ) : (
                        <List dense>
                          {upcomingItems.slice(0, 5).map((item) => (
                            <ListItem
                              key={item.id}
                              button
                              onClick={() => handleItemClick(item)}
                              sx={{ borderRadius: 1, mb: 1 }}
                            >
                              <ListItemAvatar>
                                <Avatar color={getItemTypeColor(item.type) as any}>
                                  {getItemTypeIcon(item.type)}
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <Typography variant="subtitle2">
                                      {item.title}
                                    </Typography>
                                    <Chip
                                      label={formatItemDate(item.startDate)}
                                      size="small"
                                      variant="outlined"
                                    />
                                  </Box>
                                }
                                secondary={
                                  <Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 0.5 }}>
                                      <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption">
                                        {formatItemTime(item.startDate, item.endDate)}
                                      </Typography>
                                    </Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <LocationIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption">
                                        {item.location}
                                      </Typography>
                                    </Box>
                                  </Box>
                                }
                              />
                            </ListItem>
                          ))}
                          {upcomingItems.length > 5 && (
                            <ListItem>
                              <ListItemText
                                primary={
                                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                                    И еще {upcomingItems.length - 5} мероприятий...
                                  </Typography>
                                }
                              />
                            </ListItem>
                          )}
                        </List>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CalendarPage;
