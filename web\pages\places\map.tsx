import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Rating,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Map as MapIcon,
  LocationOn as LocationIcon,
  MyLocation as MyLocationIcon,
  FilterList as FilterIcon,
  Layers as LayersIcon,
  Fullscreen as FullscreenIcon,
  Close as CloseIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Directions as DirectionsIcon,
  Phone as PhoneIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getPlaces,
  getNearbyPlaces,
  getPlaceCategories,
  addToFavorites,
  removeFromFavorites,
  getUserLocation
} from '../../src/services/placesService';
import { 
  Place,
  PlaceCategory,
  PlaceFilters 
} from '../../src/types/places.types';

const PlacesMapPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { category, placeId, favorites, nearby } = router.query;

  const [places, setPlaces] = useState<Place[]>([]);
  const [categories, setCategories] = useState<PlaceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [mapCenter, setMapCenter] = useState<{ latitude: number; longitude: number }>({
    latitude: 55.7558, // Moscow default
    longitude: 37.6176
  });
  const [mapZoom, setMapZoom] = useState(12);
  const [showFilters, setShowFilters] = useState(false);
  const [showPlacesList, setShowPlacesList] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Filter states
  const [selectedCategory, setSelectedCategory] = useState<string>(category as string || '');
  const [radius, setRadius] = useState(10); // km
  const [priceFilter, setPriceFilter] = useState<Place['priceRange'] | ''>('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInitialData();
  }, [user, router]);

  useEffect(() => {
    loadPlaces();
  }, [selectedCategory, radius, priceFilter, userLocation]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load categories
      const categoriesData = await getPlaceCategories();
      setCategories(categoriesData);
      
      // Get user location
      try {
        const location = await getUserLocation();
        setUserLocation(location);
        setMapCenter(location);
        setSuccess('Местоположение определено');
      } catch (err) {
        console.warn('Could not get user location:', err);
      }
      
      // Load places
      await loadPlaces();
    } catch (err: any) {
      setError('Ошибка загрузки карты');
    } finally {
      setLoading(false);
    }
  };

  const loadPlaces = async () => {
    try {
      setError(null);
      
      const filters: PlaceFilters = {
        category: selectedCategory ? [selectedCategory] : undefined,
        priceRange: priceFilter ? [priceFilter] : undefined,
        distance: userLocation ? {
          radius,
          unit: 'km',
          center: userLocation
        } : undefined
      };

      let placesData: Place[] = [];

      if (favorites === 'true') {
        // Load favorite places - would need getFavoritePlaces
        // placesData = await getFavoritePlaces();
      } else if (nearby === 'true' && userLocation) {
        placesData = await getNearbyPlaces(
          userLocation.latitude,
          userLocation.longitude,
          radius,
          selectedCategory || undefined
        );
      } else {
        const result = await getPlaces(filters);
        placesData = result.data;
      }
      
      setPlaces(placesData);
      
      // If specific place requested, select it
      if (placeId && typeof placeId === 'string') {
        const place = placesData.find(p => p.id === placeId);
        if (place) {
          setSelectedPlace(place);
          setMapCenter({
            latitude: place.location.latitude,
            longitude: place.location.longitude
          });
          setMapZoom(15);
        }
      }
    } catch (err: any) {
      setError('Ошибка загрузки мест');
    }
  };

  const handleToggleFavorite = async (place: Place) => {
    try {
      setActionLoading(place.id);
      setError(null);

      if (place.userInteraction?.isFavorite) {
        await removeFromFavorites(place.id);
        setSuccess('Место удалено из избранного');
      } else {
        await addToFavorites(place.id);
        setSuccess('Место добавлено в избранное');
      }
      
      // Update local state
      setPlaces(places.map(p => 
        p.id === place.id 
          ? { 
              ...p, 
              userInteraction: { 
                ...p.userInteraction, 
                isFavorite: !p.userInteraction?.isFavorite 
              } 
            }
          : p
      ));
      
      if (selectedPlace?.id === place.id) {
        setSelectedPlace({
          ...selectedPlace,
          userInteraction: {
            ...selectedPlace.userInteraction,
            isFavorite: !selectedPlace.userInteraction?.isFavorite
          }
        });
      }
    } catch (err: any) {
      setError('Ошибка обновления избранного');
    } finally {
      setActionLoading(null);
    }
  };

  const handleGetDirections = (place: Place) => {
    const destination = `${place.location.latitude},${place.location.longitude}`;
    const url = `https://maps.google.com/maps?daddr=${destination}`;
    window.open(url, '_blank');
  };

  const formatPriceRange = (priceRange: Place['priceRange']) => {
    switch (priceRange) {
      case '$':
        return 'Бюджетно';
      case '$$':
        return 'Умеренно';
      case '$$$':
        return 'Дорого';
      case '$$$$':
        return 'Очень дорого';
      default:
        return priceRange;
    }
  };

  const formatDistance = (distance?: Place['distance']) => {
    if (!distance) return null;
    
    if (distance.value < 1) {
      return `${Math.round(distance.value * 1000)} м`;
    }
    return `${distance.value.toFixed(1)} км`;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Карта мест - Likes & Love</title>
        <meta 
          name="description" 
          content="Интерактивная карта мест для встреч в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="xl" sx={{ height: '100vh', p: 0 }}>
          {/* Header */}
          <Paper 
            elevation={3} 
            sx={{ 
              position: 'absolute', 
              top: 16, 
              left: 16, 
              right: 16, 
              zIndex: 1000,
              p: 2,
              display: 'flex',
              alignItems: 'center',
              gap: 2
            }}
          >
            <Button
              startIcon={<ArrowBack />}
              onClick={() => router.back()}
              size="small"
            >
              Назад
            </Button>
            
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              <MapIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Карта мест ({places.length})
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton 
                onClick={() => setShowFilters(!showFilters)}
                color={showFilters ? 'primary' : 'default'}
                size="small"
              >
                <FilterIcon />
              </IconButton>
              
              <IconButton 
                onClick={() => setShowPlacesList(!showPlacesList)}
                color={showPlacesList ? 'primary' : 'default'}
                size="small"
              >
                <LayersIcon />
              </IconButton>
              
              <IconButton 
                onClick={loadInitialData}
                disabled={loading}
                size="small"
              >
                <MyLocationIcon />
              </IconButton>
            </Box>
          </Paper>

          {/* Error/Success Alerts */}
          {error && (
            <Alert 
              severity="error" 
              sx={{ 
                position: 'absolute', 
                top: 80, 
                left: 16, 
                right: 16, 
                zIndex: 1000 
              }}
              onClose={() => setError(null)}
            >
              {error}
            </Alert>
          )}

          {success && (
            <Alert 
              severity="success" 
              sx={{ 
                position: 'absolute', 
                top: 80, 
                left: 16, 
                right: 16, 
                zIndex: 1000 
              }}
              onClose={() => setSuccess(null)}
            >
              {success}
            </Alert>
          )}

          {/* Map Container */}
          <Box 
            sx={{ 
              width: '100%', 
              height: '100vh', 
              backgroundColor: 'grey.200',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative'
            }}
          >
            {loading ? (
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка карты...
                </Typography>
              </Box>
            ) : (
              <Box sx={{ 
                width: '100%', 
                height: '100%', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                backgroundColor: 'grey.100'
              }}>
                <Typography variant="h6" color="text.secondary">
                  Здесь будет интерактивная карта с местами
                </Typography>
                {/* Here would be the actual map component (Google Maps, Yandex Maps, etc.) */}
              </Box>
            )}
          </Box>

          {/* Filters Drawer */}
          <Drawer
            anchor="left"
            open={showFilters}
            onClose={() => setShowFilters(false)}
            sx={{ zIndex: 1200 }}
          >
            <Box sx={{ width: 300, p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" sx={{ flexGrow: 1 }}>
                  Фильтры
                </Typography>
                <IconButton onClick={() => setShowFilters(false)}>
                  <CloseIcon />
                </IconButton>
              </Box>

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Категория</InputLabel>
                <Select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  label="Категория"
                >
                  <MenuItem value="">Все категории</MenuItem>
                  {categories.map((cat) => (
                    <MenuItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Ценовая категория</InputLabel>
                <Select
                  value={priceFilter}
                  onChange={(e) => setPriceFilter(e.target.value as Place['priceRange'] | '')}
                  label="Ценовая категория"
                >
                  <MenuItem value="">Любая</MenuItem>
                  <MenuItem value="$">$ - Бюджетно</MenuItem>
                  <MenuItem value="$$">$$ - Умеренно</MenuItem>
                  <MenuItem value="$$$">$$$ - Дорого</MenuItem>
                  <MenuItem value="$$$$">$$$$ - Очень дорого</MenuItem>
                </Select>
              </FormControl>

              {userLocation && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" gutterBottom>
                    Радиус поиска: {radius} км
                  </Typography>
                  <Slider
                    value={radius}
                    onChange={(_, value) => setRadius(value as number)}
                    min={1}
                    max={50}
                    step={1}
                    marks={[
                      { value: 1, label: '1км' },
                      { value: 10, label: '10км' },
                      { value: 25, label: '25км' },
                      { value: 50, label: '50км' }
                    ]}
                    valueLabelDisplay="auto"
                  />
                </Box>
              )}

              <Button
                variant="outlined"
                fullWidth
                onClick={() => {
                  setSelectedCategory('');
                  setPriceFilter('');
                  setRadius(10);
                }}
              >
                Сбросить фильтры
              </Button>
            </Box>
          </Drawer>

          {/* Places List Drawer */}
          <Drawer
            anchor="right"
            open={showPlacesList}
            onClose={() => setShowPlacesList(false)}
            sx={{ zIndex: 1200 }}
          >
            <Box sx={{ width: 350, p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ flexGrow: 1 }}>
                  Места на карте ({places.length})
                </Typography>
                <IconButton onClick={() => setShowPlacesList(false)}>
                  <CloseIcon />
                </IconButton>
              </Box>

              <List sx={{ maxHeight: 'calc(100vh - 120px)', overflow: 'auto' }}>
                {places.map((place) => (
                  <ListItem
                    key={place.id}
                    button
                    onClick={() => {
                      setSelectedPlace(place);
                      setMapCenter({
                        latitude: place.location.latitude,
                        longitude: place.location.longitude
                      });
                      setMapZoom(15);
                    }}
                    sx={{ 
                      mb: 1, 
                      borderRadius: 1,
                      backgroundColor: selectedPlace?.id === place.id ? 'action.selected' : 'transparent'
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar src={place.photos[0]?.url}>
                        <LocationIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={place.name}
                      secondary={
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Rating value={place.rating.average} size="small" readOnly />
                            <Typography variant="caption">
                              {place.rating.average.toFixed(1)}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {place.location.district || place.location.city}
                          </Typography>
                          {place.distance && (
                            <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                              • {formatDistance(place.distance)}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </Drawer>

          {/* Selected Place Card */}
          {selectedPlace && (
            <Card 
              sx={{ 
                position: 'absolute', 
                bottom: 16, 
                left: 16, 
                right: 16, 
                zIndex: 1000,
                maxWidth: 400,
                mx: 'auto'
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  <Avatar 
                    src={selectedPlace.photos[0]?.url}
                    sx={{ width: 60, height: 60 }}
                  >
                    <LocationIcon />
                  </Avatar>
                  
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" gutterBottom>
                      {selectedPlace.name}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Rating value={selectedPlace.rating.average} size="small" readOnly />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        {selectedPlace.rating.average.toFixed(1)} ({selectedPlace.rating.count})
                      </Typography>
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {selectedPlace.location.address}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip 
                        label={selectedPlace.category.name} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                      <Chip 
                        label={formatPriceRange(selectedPlace.priceRange)} 
                        size="small" 
                        variant="outlined" 
                      />
                      {selectedPlace.distance && (
                        <Chip 
                          label={formatDistance(selectedPlace.distance)} 
                          size="small" 
                          color="secondary" 
                          variant="outlined" 
                        />
                      )}
                    </Box>
                  </Box>
                  
                  <IconButton 
                    onClick={() => setSelectedPlace(null)}
                    size="small"
                  >
                    <CloseIcon />
                  </IconButton>
                </Box>
              </CardContent>
              
              <CardActions sx={{ justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    onClick={() => router.push(`/places/${selectedPlace.id}`)}
                  >
                    Подробнее
                  </Button>
                  
                  <IconButton
                    size="small"
                    onClick={() => handleToggleFavorite(selectedPlace)}
                    disabled={actionLoading === selectedPlace.id}
                    color={selectedPlace.userInteraction?.isFavorite ? 'error' : 'default'}
                  >
                    {selectedPlace.userInteraction?.isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                  </IconButton>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {selectedPlace.contact.phone && (
                    <IconButton
                      size="small"
                      onClick={() => window.open(`tel:${selectedPlace.contact.phone}`)}
                    >
                      <PhoneIcon />
                    </IconButton>
                  )}
                  
                  <IconButton
                    size="small"
                    onClick={() => handleGetDirections(selectedPlace)}
                  >
                    <DirectionsIcon />
                  </IconButton>
                </Box>
              </CardActions>
            </Card>
          )}
        </Container>
      </Layout>
    </>
  );
};

export default PlacesMapPage;
