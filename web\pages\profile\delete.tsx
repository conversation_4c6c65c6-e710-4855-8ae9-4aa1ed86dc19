import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Checkbox,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  Divider,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  DeleteForever as DeleteIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  deleteProfile, 
  cancelProfileDeletion 
} from '../../src/services/profileService';
import { ProfileDeletionRequest } from '../../src/types/profile.types';

const deleteSchema = yup.object({
  reason: yup.string().oneOf(['found_someone', 'not_working', 'privacy', 'too_busy', 'other']).required('Выберите причину'),
  customReason: yup.string().when('reason', {
    is: 'other',
    then: (schema) => schema.required('Укажите причину').min(10, 'Минимум 10 символов'),
    otherwise: (schema) => schema.notRequired()
  }),
  feedback: yup.string().max(500, 'Максимум 500 символов'),
  deleteImmediately: yup.boolean(),
  confirmDeletion: yup.boolean().oneOf([true], 'Подтвердите удаление профиля')
});

const ProfileDeletePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [scheduledDeletionDate, setScheduledDeletionDate] = useState<string | null>(null);

  const { control, handleSubmit, watch, formState: { errors, isValid } } = useForm<ProfileDeletionRequest & { confirmDeletion: boolean }>({
    resolver: yupResolver(deleteSchema),
    defaultValues: {
      reason: 'found_someone',
      customReason: '',
      feedback: '',
      deleteImmediately: false,
      confirmDeletion: false
    },
    mode: 'onChange'
  });

  const watchedReason = watch('reason');
  const watchedDeleteImmediately = watch('deleteImmediately');

  const reasonOptions = [
    { 
      value: 'found_someone', 
      label: 'Нашел(а) кого-то', 
      description: 'Встретил(а) подходящего человека' 
    },
    { 
      value: 'not_working', 
      label: 'Приложение не работает', 
      description: 'Не получается найти подходящих людей' 
    },
    { 
      value: 'privacy', 
      label: 'Вопросы приватности', 
      description: 'Беспокоюсь о конфиденциальности данных' 
    },
    { 
      value: 'too_busy', 
      label: 'Слишком занят(а)', 
      description: 'Нет времени на знакомства' 
    },
    { 
      value: 'other', 
      label: 'Другая причина', 
      description: 'Укажите свою причину' 
    }
  ];

  const steps = [
    'Причина удаления',
    'Дополнительная информация',
    'Подтверждение'
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
    }
  }, [user, router]);

  const onSubmit = async (data: ProfileDeletionRequest & { confirmDeletion: boolean }) => {
    try {
      setLoading(true);
      setError(null);

      const { confirmDeletion, ...deletionData } = data;
      const result = await deleteProfile(deletionData);
      
      if (result.success) {
        setSuccess(result.message);
        setScheduledDeletionDate(result.scheduledDeletionDate || null);
        
        if (data.deleteImmediately) {
          // Немедленное удаление - выходим из системы
          setTimeout(async () => {
            await logout();
            router.push('/');
          }, 3000);
        }
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка удаления профиля');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelDeletion = async () => {
    try {
      setLoading(true);
      await cancelProfileDeletion();
      setSuccess('Удаление профиля отменено');
      setTimeout(() => {
        router.push('/profile');
      }, 2000);
    } catch (err: any) {
      setError('Ошибка отмены удаления');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <FormControl component="fieldset" fullWidth>
              <FormLabel component="legend" sx={{ mb: 2 }}>
                Почему вы хотите удалить профиль?
              </FormLabel>
              <Controller
                name="reason"
                control={control}
                render={({ field }) => (
                  <RadioGroup {...field}>
                    {reasonOptions.map((option) => (
                      <FormControlLabel
                        key={option.value}
                        value={option.value}
                        control={<Radio />}
                        label={
                          <Box>
                            <Typography variant="body2">{option.label}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {option.description}
                            </Typography>
                          </Box>
                        }
                        sx={{ mb: 1 }}
                      />
                    ))}
                  </RadioGroup>
                )}
              />
              {errors.reason && (
                <Typography variant="caption" color="error">
                  {errors.reason.message}
                </Typography>
              )}
            </FormControl>

            {watchedReason === 'other' && (
              <Controller
                name="customReason"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Опишите причину"
                    multiline
                    rows={3}
                    placeholder="Расскажите, почему вы хотите удалить профиль"
                    error={!!errors.customReason}
                    helperText={errors.customReason?.message}
                    sx={{ mt: 3 }}
                  />
                )}
              />
            )}
          </Box>
        );

      case 1:
        return (
          <Box>
            <Controller
              name="feedback"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Отзыв о приложении (необязательно)"
                  multiline
                  rows={4}
                  placeholder="Поделитесь своим мнением о приложении. Это поможет нам стать лучше."
                  error={!!errors.feedback}
                  helperText={errors.feedback?.message || `${field.value?.length || 0}/500 символов`}
                  sx={{ mb: 3 }}
                />
              )}
            />

            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Варианты удаления
                </Typography>
                
                <Controller
                  name="deleteImmediately"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={<Checkbox {...field} checked={field.value} />}
                      label={
                        <Box>
                          <Typography variant="body2">
                            Удалить профиль немедленно
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Если не отмечено, профиль будет удален через 30 дней. 
                            В течение этого времени вы можете восстановить его.
                          </Typography>
                        </Box>
                      }
                    />
                  )}
                />

                {watchedDeleteImmediately && (
                  <Alert severity="warning" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      ⚠️ При немедленном удалении восстановление профиля будет невозможно!
                    </Typography>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Внимание! Удаление профиля
              </Typography>
              <Typography variant="body2">
                При удалении профиля будут безвозвратно удалены:
              </Typography>
              <Box component="ul" sx={{ mt: 1, mb: 0 }}>
                <li>Все ваши фотографии</li>
                <li>История сообщений</li>
                <li>Список лайков и совпадений</li>
                <li>Настройки и предпочтения</li>
                <li>История активности</li>
              </Box>
            </Alert>

            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Сводка удаления
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Причина:</strong> {reasonOptions.find(opt => opt.value === watchedReason)?.label}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Тип удаления:</strong> {watchedDeleteImmediately ? 'Немедленное' : 'Отложенное (30 дней)'}
                </Typography>
                {watchedDeleteImmediately && (
                  <Typography variant="body2" color="error">
                    <strong>Восстановление:</strong> Невозможно
                  </Typography>
                )}
              </CardContent>
            </Card>

            <Controller
              name="confirmDeletion"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={<Checkbox {...field} checked={field.value} />}
                  label={
                    <Typography variant="body2">
                      Я понимаю последствия и хочу удалить свой профиль
                    </Typography>
                  }
                />
              )}
            />
            {errors.confirmDeletion && (
              <Typography variant="caption" color="error" display="block" sx={{ mt: 1 }}>
                {errors.confirmDeletion.message}
              </Typography>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  if (!user) {
    return null;
  }

  if (success && scheduledDeletionDate) {
    return (
      <>
        <Head>
          <title>Профиль запланирован к удалению - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="sm">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
                <ScheduleIcon sx={{ fontSize: 80, color: 'warning.main', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Профиль запланирован к удалению
                </Typography>
                <Typography variant="body1" sx={{ mb: 3 }}>
                  Ваш профиль будет удален {new Date(scheduledDeletionDate).toLocaleDateString('ru-RU')}
                </Typography>
                <Alert severity="info" sx={{ mb: 3 }}>
                  До этой даты вы можете отменить удаление и восстановить профиль
                </Alert>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="contained"
                    onClick={handleCancelDeletion}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <CancelIcon />}
                  >
                    Отменить удаление
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/profile')}
                  >
                    Вернуться к профилю
                  </Button>
                </Box>
              </Paper>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (success && watchedDeleteImmediately) {
    return (
      <>
        <Head>
          <title>Профиль удален - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="sm">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
                <CheckIcon sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
                <Typography variant="h5" gutterBottom>
                  Профиль удален
                </Typography>
                <Typography variant="body1" sx={{ mb: 3 }}>
                  Ваш профиль был успешно удален. Спасибо за использование нашего приложения!
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Вы будете перенаправлены на главную страницу через несколько секунд...
                </Typography>
              </Paper>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Удаление профиля - Likes & Love</title>
        <meta 
          name="description" 
          content="Удалите ваш профиль из приложения знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                <Button
                  startIcon={<ArrowBack />}
                  onClick={() => router.back()}
                  sx={{ mr: 2 }}
                >
                  Назад
                </Button>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <DeleteIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'error.main' }} />
                    Удаление профиля
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Мы сожалеем, что вы решили покинуть нас
                  </Typography>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Fade in timeout={600}>
                <Box>
                  {/* Stepper */}
                  <Stepper activeStep={activeStep} orientation={isMobile ? "vertical" : "horizontal"} sx={{ mb: 4 }}>
                    {steps.map((label) => (
                      <Step key={label}>
                        <StepLabel>{label}</StepLabel>
                      </Step>
                    ))}
                  </Stepper>

                  {/* Form */}
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <Box sx={{ mb: 4 }}>
                      {renderStepContent(activeStep)}
                    </Box>

                    {/* Navigation buttons */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Button
                        disabled={activeStep === 0}
                        onClick={handleBack}
                      >
                        Назад
                      </Button>
                      
                      {activeStep === steps.length - 1 ? (
                        <Button
                          type="submit"
                          variant="contained"
                          color="error"
                          disabled={!isValid || loading}
                          startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
                        >
                          {loading ? 'Удаление...' : 'Удалить профиль'}
                        </Button>
                      ) : (
                        <Button
                          variant="contained"
                          onClick={handleNext}
                        >
                          Далее
                        </Button>
                      )}
                    </Box>
                  </form>
                </Box>
              </Fade>
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ProfileDeletePage;
