import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip
} from '@mui/material';
import {
  Edit,
  Delete,
  Email,
  Phone,
  Lock,
  Language,
  AccountCircle,
  Warning,
  CheckCircle,
  Add
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import axios from 'axios';

interface AccountSettings {
  email: string;
  phone: string;
  username: string;
  language: string;
  timezone: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  loginNotifications: boolean;
  marketingEmails: boolean;
}

const AccountSettingsPage: React.FC = () => {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const [settings, setSettings] = useState<AccountSettings>({
    email: '',
    phone: '',
    username: '',
    language: 'ru',
    timezone: 'Europe/Moscow',
    emailVerified: false,
    phoneVerified: false,
    twoFactorEnabled: false,
    loginNotifications: true,
    marketingEmails: false
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Диалоги
  const [changeEmailDialog, setChangeEmailDialog] = useState(false);
  const [changePhoneDialog, setChangePhoneDialog] = useState(false);
  const [changePasswordDialog, setChangePasswordDialog] = useState(false);
  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);
  
  // Формы
  const [newEmail, setNewEmail] = useState('');
  const [newPhone, setNewPhone] = useState('');
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await axios.get('/api/settings/account');
      setSettings(response.data);
    } catch (err) {
      setError('Ошибка загрузки настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      await axios.put('/api/settings/account', {
        username: settings.username,
        language: settings.language,
        timezone: settings.timezone,
        loginNotifications: settings.loginNotifications,
        marketingEmails: settings.marketingEmails
      });
      setSuccess('Настройки сохранены');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка сохранения настроек');
    } finally {
      setSaving(false);
    }
  };

  const handleChangeEmail = async () => {
    if (!newEmail) return;
    
    setSaving(true);
    try {
      await axios.post('/api/settings/change-email', { email: newEmail });
      setSuccess('На новый email отправлено письмо для подтверждения');
      setChangeEmailDialog(false);
      setNewEmail('');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка изменения email');
    } finally {
      setSaving(false);
    }
  };

  const handleChangePhone = async () => {
    if (!newPhone) return;
    
    setSaving(true);
    try {
      await axios.post('/api/settings/change-phone', { phone: newPhone });
      setSuccess('На новый номер отправлен код подтверждения');
      setChangePhoneDialog(false);
      router.push('/auth/verify-phone?phone=' + encodeURIComponent(newPhone));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка изменения телефона');
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setError('Пароли не совпадают');
      return;
    }
    
    setSaving(true);
    try {
      await axios.post('/api/settings/change-password', {
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });
      setSuccess('Пароль успешно изменен');
      setChangePasswordDialog(false);
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка изменения пароля');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteAccount = async () => {
    setSaving(true);
    try {
      await axios.delete('/api/settings/account');
      // Выход и перенаправление
      router.push('/auth/login');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка удаления аккаунта');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle2FA = async () => {
    if (!settings.twoFactorEnabled) {
      router.push('/auth/2fa-setup');
    } else {
      try {
        await axios.post('/api/settings/disable-2fa');
        setSettings({ ...settings, twoFactorEnabled: false });
        setSuccess('Двухфакторная аутентификация отключена');
      } catch (err) {
        setError('Ошибка отключения 2FA');
      }
    }
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout title="Настройки аккаунта">
      <Container maxWidth="md">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Typography variant="h4" gutterBottom>
              Настройки аккаунта
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
                {success}
              </Alert>
            )}

            {/* Основная информация */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Основная информация
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Email"
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {settings.email}
                        {settings.emailVerified ? (
                          <Chip
                            icon={<CheckCircle />}
                            label="Подтвержден"
                            size="small"
                            color="success"
                          />
                        ) : (
                          <Chip
                            icon={<Warning />}
                            label="Не подтвержден"
                            size="small"
                            color="warning"
                          />
                        )}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setChangeEmailDialog(true)}
                    >
                      Изменить
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemText
                    primary="Телефон"
                    secondary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {settings.phone || 'Не указан'}
                        {settings.phone && settings.phoneVerified ? (
                          <Chip
                            icon={<CheckCircle />}
                            label="Подтвержден"
                            size="small"
                            color="success"
                          />
                        ) : settings.phone ? (
                          <Chip
                            icon={<Warning />}
                            label="Не подтвержден"
                            size="small"
                            color="warning"
                          />
                        ) : null}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setChangePhoneDialog(true)}
                    >
                      {settings.phone ? 'Изменить' : 'Добавить'}
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemText
                    primary="Имя пользователя"
                    secondary={settings.username}
                  />
                </ListItem>
              </List>

              <Grid container spacing={2} sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Имя пользователя"
                    value={settings.username}
                    onChange={(e) => setSettings({ ...settings, username: e.target.value })}
                    helperText="Используется в URL вашего профиля"
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Региональные настройки */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Региональные настройки
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Язык</InputLabel>
                    <Select
                      value={settings.language}
                      onChange={(e) => setSettings({ ...settings, language: e.target.value })}
                      label="Язык"
                    >
                      <MenuItem value="ru">Русский</MenuItem>
                      <MenuItem value="en">English</MenuItem>
                      <MenuItem value="es">Español</MenuItem>
                      <MenuItem value="fr">Français</MenuItem>
                      <MenuItem value="de">Deutsch</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Часовой пояс</InputLabel>
                    <Select
                      value={settings.timezone}
                      onChange={(e) => setSettings({ ...settings, timezone: e.target.value })}
                      label="Часовой пояс"
                    >
                      <MenuItem value="Europe/Moscow">Москва (UTC+3)</MenuItem>
                      <MenuItem value="Europe/London">Лондон (UTC+0)</MenuItem>
                      <MenuItem value="America/New_York">Нью-Йорк (UTC-5)</MenuItem>
                      <MenuItem value="Asia/Tokyo">Токио (UTC+9)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>

            {/* Безопасность */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Безопасность
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Пароль"
                    secondary="Последнее изменение: 30 дней назад"
                  />
                  <ListItemSecondaryAction>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setChangePasswordDialog(true)}
                    >
                      Изменить
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemText
                    primary="Двухфакторная аутентификация"
                    secondary={settings.twoFactorEnabled ? 'Включена' : 'Отключена'}
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.twoFactorEnabled}
                      onChange={handleToggle2FA}
                    />
                  </ListItemSecondaryAction>
                </ListItem>

                <ListItem>
                  <ListItemText
                    primary="Уведомления о входе"
                    secondary="Получать уведомления о входе с новых устройств"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.loginNotifications}
                      onChange={(e) => setSettings({ ...settings, loginNotifications: e.target.checked })}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </Box>

            {/* Уведомления */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Email-уведомления
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.marketingEmails}
                    onChange={(e) => setSettings({ ...settings, marketingEmails: e.target.checked })}
                  />
                }
                label="Получать новости и специальные предложения"
              />
            </Box>

            {/* Кнопки действий */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                variant="outlined"
                color="error"
                onClick={() => setDeleteAccountDialog(true)}
              >
                Удалить аккаунт
              </Button>
              
              <Button
                variant="contained"
                onClick={handleSaveSettings}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Сохранение...
                  </>
                ) : (
                  'Сохранить изменения'
                )}
              </Button>
            </Box>
          </Paper>
        </Box>
      </Container>

      {/* Диалог изменения email */}
      <Dialog open={changeEmailDialog} onClose={() => setChangeEmailDialog(false)}>
        <DialogTitle>Изменить email</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Новый email"
            type="email"
            value={newEmail}
            onChange={(e) => setNewEmail(e.target.value)}
            margin="normal"
            autoFocus
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChangeEmailDialog(false)}>Отмена</Button>
          <Button onClick={handleChangeEmail} variant="contained" disabled={saving}>
            Изменить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог изменения телефона */}
      <Dialog open={changePhoneDialog} onClose={() => setChangePhoneDialog(false)}>
        <DialogTitle>Изменить телефон</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Новый телефон"
            type="tel"
            value={newPhone}
            onChange={(e) => setNewPhone(e.target.value)}
            margin="normal"
            autoFocus
            placeholder="+7 (999) 123-45-67"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChangePhoneDialog(false)}>Отмена</Button>
          <Button onClick={handleChangePhone} variant="contained" disabled={saving}>
            Изменить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог изменения пароля */}
      <Dialog open={changePasswordDialog} onClose={() => setChangePasswordDialog(false)}>
        <DialogTitle>Изменить пароль</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Текущий пароль"
            type="password"
            value={passwordForm.currentPassword}
            onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
            margin="normal"
            autoFocus
          />
          <TextField
            fullWidth
            label="Новый пароль"
            type="password"
            value={passwordForm.newPassword}
            onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Подтвердите новый пароль"
            type="password"
            value={passwordForm.confirmPassword}
            onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChangePasswordDialog(false)}>Отмена</Button>
          <Button onClick={handleChangePassword} variant="contained" disabled={saving}>
            Изменить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог удаления аккаунта */}
      <Dialog open={deleteAccountDialog} onClose={() => setDeleteAccountDialog(false)}>
        <DialogTitle>Удалить аккаунт</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Это действие необратимо! Все ваши данные будут удалены.
          </Alert>
          <Typography>
            Вы уверены, что хотите удалить свой аккаунт? Это приведет к:
          </Typography>
          <ul>
            <li>Удалению всех ваших данных и фотографий</li>
            <li>Удалению всех сообщений и совпадений</li>
            <li>Отмене всех активных подписок</li>
          </ul>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteAccountDialog(false)}>Отмена</Button>
          <Button onClick={handleDeleteAccount} color="error" variant="contained" disabled={saving}>
            Удалить аккаунт
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default AccountSettingsPage;
