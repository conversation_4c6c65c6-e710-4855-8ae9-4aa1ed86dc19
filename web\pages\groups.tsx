import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { GetStaticProps } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Avatar,
  Chip,
  IconButton,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Skeleton,
  Alert,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Add as AddIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
  Public as PublicIcon,
  Lock as PrivateIcon,
  Chat as ChatIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import Layout from '../components/Layout/Layout';
import { useAuth } from '../src/providers/AuthProvider';
import { getGroups, createGroupChat, getGroupInfo } from '../src/services/chatService';
import { GroupChatInfo } from '../src/types/chat.types';

const GroupsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [groups, setGroups] = useState<GroupChatInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newGroupData, setNewGroupData] = useState({
    name: '',
    description: '',
    isPublic: false,
    maxParticipants: 50
  });

  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = async () => {
    try {
      setLoading(true);
      setError(null);
      const groupsData = await getGroups();
      setGroups(groupsData);
    } catch (err) {
      setError('Не удалось загрузить группы');
      console.error('Error loading groups:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGroup = async () => {
    try {
      if (!newGroupData.name.trim()) {
        setError('Введите название группы');
        return;
      }

      await createGroupChat(
        newGroupData.name,
        newGroupData.description,
        [] // Пустой список участников - создатель добавится автоматически
      );

      setCreateDialogOpen(false);
      setNewGroupData({
        name: '',
        description: '',
        isPublic: false,
        maxParticipants: 50
      });
      
      // Перезагружаем список групп
      await loadGroups();
    } catch (err) {
      setError('Не удалось создать группу');
      console.error('Error creating group:', err);
    }
  };

  const handleGroupClick = (groupId: string) => {
    router.push(`/chat/${groupId}`);
  };

  const renderGroupCard = (group: GroupChatInfo) => (
    <Grid item xs={12} sm={6} md={4} key={group.id}>
      <Card 
        sx={{ 
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          '&:hover': {
            boxShadow: theme.shadows[4]
          }
        }}
        onClick={() => handleGroupClick(group.id)}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <Avatar
              src={group.avatarUrl}
              sx={{ 
                width: 56, 
                height: 56, 
                mr: 2,
                bgcolor: theme.palette.primary.main
              }}
            >
              <GroupIcon />
            </Avatar>
            <Box flexGrow={1}>
              <Typography variant="h6" component="h3" noWrap>
                {group.name}
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Chip
                  icon={group.settings.isPublic ? <PublicIcon /> : <PrivateIcon />}
                  label={group.settings.isPublic ? 'Публичная' : 'Приватная'}
                  size="small"
                  color={group.settings.isPublic ? 'success' : 'default'}
                />
                <Chip
                  icon={<PersonIcon />}
                  label={group.participantCount}
                  size="small"
                  variant="outlined"
                />
              </Box>
            </Box>
            <IconButton size="small">
              <MoreVertIcon />
            </IconButton>
          </Box>
          
          {group.description && (
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}
            >
              {group.description}
            </Typography>
          )}
        </CardContent>
        
        <CardActions>
          <Button 
            size="small" 
            startIcon={<ChatIcon />}
            onClick={(e) => {
              e.stopPropagation();
              handleGroupClick(group.id);
            }}
          >
            Открыть чат
          </Button>
          <Button 
            size="small" 
            startIcon={<SettingsIcon />}
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Открыть настройки группы
            }}
          >
            Настройки
          </Button>
        </CardActions>
      </Card>
    </Grid>
  );

  const renderSkeletonCard = () => (
    <Grid item xs={12} sm={6} md={4}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <Skeleton variant="circular" width={56} height={56} sx={{ mr: 2 }} />
            <Box flexGrow={1}>
              <Skeleton variant="text" width="60%" height={32} />
              <Skeleton variant="text" width="40%" height={24} />
            </Box>
          </Box>
          <Skeleton variant="text" width="100%" />
          <Skeleton variant="text" width="80%" />
        </CardContent>
        <CardActions>
          <Skeleton variant="rectangular" width={100} height={32} />
          <Skeleton variant="rectangular" width={100} height={32} />
        </CardActions>
      </Card>
    </Grid>
  );

  if (!user) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Alert severity="warning">
            Войдите в систему, чтобы просматривать группы
          </Alert>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Группы
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Присоединяйтесь к группам по интересам или создайте свою
            </Typography>
          </Box>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            sx={{ display: { xs: 'none', sm: 'flex' } }}
          >
            Создать группу
          </Button>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Groups Grid */}
        <Grid container spacing={3}>
          {loading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, index) => (
              <React.Fragment key={index}>
                {renderSkeletonCard()}
              </React.Fragment>
            ))
          ) : groups.length > 0 ? (
            // Groups list
            groups.map(renderGroupCard)
          ) : (
            // Empty state
            <Grid item xs={12}>
              <Box 
                display="flex" 
                flexDirection="column" 
                alignItems="center" 
                py={8}
                textAlign="center"
              >
                <GroupIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Пока нет групп
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Создайте первую группу или присоединитесь к существующей
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setCreateDialogOpen(true)}
                >
                  Создать группу
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>

        {/* Mobile FAB */}
        {isMobile && (
          <Fab
            color="primary"
            sx={{ position: 'fixed', bottom: 16, right: 16 }}
            onClick={() => setCreateDialogOpen(true)}
          >
            <AddIcon />
          </Fab>
        )}

        {/* Create Group Dialog */}
        <Dialog 
          open={createDialogOpen} 
          onClose={() => setCreateDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Создать новую группу</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 1 }}>
              <TextField
                fullWidth
                label="Название группы"
                value={newGroupData.name}
                onChange={(e) => setNewGroupData(prev => ({ ...prev, name: e.target.value }))}
                margin="normal"
                required
              />
              
              <TextField
                fullWidth
                label="Описание"
                value={newGroupData.description}
                onChange={(e) => setNewGroupData(prev => ({ ...prev, description: e.target.value }))}
                margin="normal"
                multiline
                rows={3}
              />
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Максимум участников</InputLabel>
                <Select
                  value={newGroupData.maxParticipants}
                  onChange={(e) => setNewGroupData(prev => ({ ...prev, maxParticipants: Number(e.target.value) }))}
                  label="Максимум участников"
                >
                  <MenuItem value={10}>10</MenuItem>
                  <MenuItem value={25}>25</MenuItem>
                  <MenuItem value={50}>50</MenuItem>
                  <MenuItem value={100}>100</MenuItem>
                  <MenuItem value={250}>250</MenuItem>
                </Select>
              </FormControl>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={newGroupData.isPublic}
                    onChange={(e) => setNewGroupData(prev => ({ ...prev, isPublic: e.target.checked }))}
                  />
                }
                label="Публичная группа"
                sx={{ mt: 2 }}
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>
              Отмена
            </Button>
            <Button 
              onClick={handleCreateGroup}
              variant="contained"
              disabled={!newGroupData.name.trim()}
            >
              Создать
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
    },
  };
};

export default GroupsPage;
