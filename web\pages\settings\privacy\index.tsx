import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormLabel
} from '@mui/material';
import {
  ArrowBack,
  Privacy as PrivacyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  LocationOn as LocationIcon,
  Message as MessageIcon,
  Search as SearchIcon,
  Screenshot as ScreenshotIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Типы для настроек приватности
interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showOnlineStatus: boolean;
  showLastSeen: boolean;
  showAge: boolean;
  showLocation: boolean;
  showDistance: boolean;
  allowMessagesFrom: 'everyone' | 'matches' | 'premium';
  allowProfileViews: 'everyone' | 'matches' | 'premium';
  hideFromSearch: boolean;
  incognitoMode: boolean;
  blockScreenshots: boolean;
  dataProcessingConsent: boolean;
  marketingConsent: boolean;
  analyticsConsent: boolean;
}

// Схема валидации
const privacySchema = yup.object({
  profileVisibility: yup.string().oneOf(['public', 'friends', 'private']).required(),
  allowMessagesFrom: yup.string().oneOf(['everyone', 'matches', 'premium']).required(),
  allowProfileViews: yup.string().oneOf(['everyone', 'matches', 'premium']).required(),
});

const PrivacySettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch
  } = useForm<PrivacySettings>({
    resolver: yupResolver(privacySchema),
    defaultValues: {
      profileVisibility: 'public',
      showOnlineStatus: true,
      showLastSeen: true,
      showAge: true,
      showLocation: true,
      showDistance: true,
      allowMessagesFrom: 'everyone',
      allowProfileViews: 'everyone',
      hideFromSearch: false,
      incognitoMode: false,
      blockScreenshots: false,
      dataProcessingConsent: true,
      marketingConsent: false,
      analyticsConsent: true
    }
  });

  useEffect(() => {
    if (user?.settings?.privacy) {
      reset(user.settings.privacy);
    }
  }, [user, reset]);

  const handleSave = async (data: PrivacySettings) => {
    try {
      setLoading(true);
      setError(null);
      
      await updateProfile({
        settings: {
          privacy: data
        }
      });

      setSuccess('Настройки приватности успешно обновлены');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset();
    setError(null);
  };

  if (!user) {
    return (
      <Layout title="Настройки приватности">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Настройки приватности - Likes & Love</title>
        <meta name="description" content="Управление настройками приватности в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Настройки приватности">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton 
                onClick={() => router.back()} 
                sx={{ display: { xs: 'flex', md: 'none' } }}
              >
                <ArrowBack />
              </IconButton>
              <PrivacyIcon color="primary" sx={{ fontSize: 32 }} />
              <Typography variant="h4" component="h1" fontWeight="bold">
                Настройки приватности
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <form onSubmit={handleSubmit(handleSave)}>
              <Grid container spacing={3}>
                {/* Видимость профиля */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Видимость профиля
                      </Typography>

                      <Controller
                        name="profileVisibility"
                        control={control}
                        render={({ field }) => (
                          <FormControl component="fieldset">
                            <FormLabel component="legend">Кто может видеть мой профиль</FormLabel>
                            <RadioGroup {...field} sx={{ mt: 1 }}>
                              <FormControlLabel
                                value="public"
                                control={<Radio />}
                                label={
                                  <Box>
                                    <Typography variant="body1">Все пользователи</Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Ваш профиль будет виден всем пользователям приложения
                                    </Typography>
                                  </Box>
                                }
                              />
                              <FormControlLabel
                                value="friends"
                                control={<Radio />}
                                label={
                                  <Box>
                                    <Typography variant="body1">Только совпадения</Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Только пользователи с взаимными лайками могут видеть ваш профиль
                                    </Typography>
                                  </Box>
                                }
                              />
                              <FormControlLabel
                                value="private"
                                control={<Radio />}
                                label={
                                  <Box>
                                    <Typography variant="body1">Приватный</Typography>
                                    <Typography variant="body2" color="text.secondary">
                                      Ваш профиль скрыт от всех пользователей
                                    </Typography>
                                  </Box>
                                }
                              />
                            </RadioGroup>
                          </FormControl>
                        )}
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* Информация о статусе */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Информация о статусе
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <VisibilityIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Показывать статус онлайн"
                            secondary="Другие пользователи увидят, что вы сейчас в сети"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="showOnlineStatus"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <VisibilityIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Показывать время последней активности"
                            secondary="Когда вы были в сети в последний раз"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="showLastSeen"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <InfoIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Показывать возраст"
                            secondary="Ваш возраст будет виден в профиле"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="showAge"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Геолокация */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Геолокация
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <LocationIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Показывать местоположение"
                            secondary="Ваш город будет виден в профиле"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="showLocation"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <LocationIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Показывать расстояние"
                            secondary="Расстояние до вас будет видно другим пользователям"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="showDistance"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Взаимодействие */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Взаимодействие
                      </Typography>

                      <Box sx={{ mb: 3 }}>
                        <Controller
                          name="allowMessagesFrom"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel>Кто может отправлять сообщения</InputLabel>
                              <Select {...field} label="Кто может отправлять сообщения">
                                <MenuItem value="everyone">Все пользователи</MenuItem>
                                <MenuItem value="matches">Только совпадения</MenuItem>
                                <MenuItem value="premium">Только Premium пользователи</MenuItem>
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Box>

                      <Box sx={{ mb: 3 }}>
                        <Controller
                          name="allowProfileViews"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth>
                              <InputLabel>Кто может просматривать профиль</InputLabel>
                              <Select {...field} label="Кто может просматривать профиль">
                                <MenuItem value="everyone">Все пользователи</MenuItem>
                                <MenuItem value="matches">Только совпадения</MenuItem>
                                <MenuItem value="premium">Только Premium пользователи</MenuItem>
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Box>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <SearchIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Скрыть из поиска"
                            secondary="Ваш профиль не будет появляться в результатах поиска"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="hideFromSearch"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <VisibilityOffIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Режим инкогнито"
                            secondary="Просматривайте профили анонимно"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="incognitoMode"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Безопасность */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Безопасность
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <ScreenshotIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Блокировать скриншоты"
                            secondary="Запретить создание скриншотов ваших фото"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="blockScreenshots"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Согласие на обработку данных */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Согласие на обработку данных
                      </Typography>

                      <List>
                        <ListItem>
                          <ListItemIcon>
                            <SecurityIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Обработка персональных данных"
                            secondary="Согласие на обработку данных для работы приложения"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="dataProcessingConsent"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                  disabled
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <MessageIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Маркетинговые сообщения"
                            secondary="Получать информацию о новых функциях и акциях"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="marketingConsent"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>

                        <Divider />

                        <ListItem>
                          <ListItemIcon>
                            <AnalyticsIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Аналитика и улучшение сервиса"
                            secondary="Помочь нам улучшить приложение"
                          />
                          <ListItemSecondaryAction>
                            <Controller
                              name="analyticsConsent"
                              control={control}
                              render={({ field }) => (
                                <Switch
                                  checked={field.value}
                                  onChange={field.onChange}
                                />
                              )}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Кнопки сохранения */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      onClick={handleCancel}
                      startIcon={<CancelIcon />}
                      disabled={loading}
                    >
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                      disabled={loading || !isDirty}
                    >
                      Сохранить
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PrivacySettingsPage;
