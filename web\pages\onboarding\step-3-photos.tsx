import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Stack,
  Grid,
  Card,
  CardMedia,
  CardActions,
  IconButton,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Fade,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  PhotoCamera,
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  CloudUpload,
  DragIndicator
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import Layout from '../../components/Layout/Layout';
import OnboardingProgress from '../../components/Onboarding/OnboardingProgress';
import { useAuth } from '../../src/providers/AuthProvider';
import { useOnboarding } from '../../src/contexts/OnboardingContext';
import { uploadPhoto, deletePhoto, setMainPhoto, reorderPhotos } from '../../src/services/onboardingService';
import { PhotoUpload } from '../../src/types/onboarding.types';

const PhotosPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    data, 
    steps, 
    currentStep, 
    updatePhotos, 
    nextStep, 
    previousStep,
    saveProgress,
    loading: onboardingLoading 
  } = useOnboarding();

  const [photos, setPhotos] = useState<PhotoUpload[]>(data.photos.photos || []);
  const [mainPhotoId, setMainPhotoId] = useState<string | undefined>(data.photos.mainPhotoId);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [photoToDelete, setPhotoToDelete] = useState<string | null>(null);

  const maxPhotos = 9;
  const minPhotos = 2;
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
    }
  }, [user, router]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (photos.length + acceptedFiles.length > maxPhotos) {
      setError(`Максимум ${maxPhotos} фотографий`);
      return;
    }

    setUploading(true);
    setError(null);

    for (const file of acceptedFiles) {
      if (file.size > maxFileSize) {
        setError(`Файл ${file.name} слишком большой. Максимум 10MB`);
        continue;
      }

      try {
        const tempId = `temp-${Date.now()}-${Math.random()}`;
        const tempPhoto: PhotoUpload = {
          id: tempId,
          file,
          preview: URL.createObjectURL(file),
          isMain: photos.length === 0 && !mainPhotoId,
          order: photos.length,
          uploadProgress: 0,
          isUploaded: false
        };

        setPhotos(prev => [...prev, tempPhoto]);

        // Загружаем фото на сервер
        const result = await uploadPhoto(file, tempPhoto.isMain, tempPhoto.order);
        
        if (result.success && result.photo) {
          setPhotos(prev => prev.map(p => 
            p.id === tempId 
              ? { 
                  ...p, 
                  id: result.photo!.id, 
                  url: result.photo!.url,
                  isUploaded: true,
                  uploadProgress: 100
                }
              : p
          ));

          if (tempPhoto.isMain) {
            setMainPhotoId(result.photo.id);
          }
        } else {
          setPhotos(prev => prev.filter(p => p.id !== tempId));
          setError(result.message);
        }
      } catch (err: any) {
        setPhotos(prev => prev.filter(p => p.id !== tempId));
        setError('Ошибка загрузки фотографии');
      }
    }

    setUploading(false);
  }, [photos, mainPhotoId]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    multiple: true,
    disabled: uploading || photos.length >= maxPhotos
  });

  const handleDeletePhoto = async (photoId: string) => {
    try {
      const result = await deletePhoto(photoId);
      if (result.success) {
        setPhotos(prev => prev.filter(p => p.id !== photoId));
        
        if (mainPhotoId === photoId) {
          const remainingPhotos = photos.filter(p => p.id !== photoId);
          if (remainingPhotos.length > 0) {
            const newMainId = remainingPhotos[0].id;
            setMainPhotoId(newMainId);
            await setMainPhoto(newMainId);
          } else {
            setMainPhotoId(undefined);
          }
        }
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка удаления фотографии');
    }
    
    setDeleteDialogOpen(false);
    setPhotoToDelete(null);
  };

  const handleSetMainPhoto = async (photoId: string) => {
    try {
      const result = await setMainPhoto(photoId);
      if (result.success) {
        setMainPhotoId(photoId);
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка установки главной фотографии');
    }
  };

  const handleNext = async () => {
    if (photos.length < minPhotos) {
      setError(`Загрузите минимум ${minPhotos} фотографии`);
      return;
    }

    if (!mainPhotoId) {
      setError('Выберите главную фотографию');
      return;
    }

    try {
      setError(null);
      updatePhotos({
        photos,
        mainPhotoId
      });
      
      await saveProgress();
      nextStep();
    } catch (err: any) {
      setError('Ошибка сохранения фотографий');
    }
  };

  const handleBack = () => {
    updatePhotos({
      photos,
      mainPhotoId
    });
    previousStep();
  };

  const canProceed = photos.length >= minPhotos && mainPhotoId;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Загрузите фотографии - Likes & Love</title>
        <meta 
          name="description" 
          content="Добавьте ваши лучшие фотографии для привлекательного профиля в приложении знакомств" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <OnboardingProgress
              steps={steps}
              currentStep={currentStep}
              variant={isMobile ? 'minimal' : 'horizontal'}
              showLabels={!isMobile}
            />

            <Fade in timeout={600}>
              <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <PhotoCamera sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    Добавьте ваши фотографии
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                    Загрузите от {minPhotos} до {maxPhotos} фотографий. Первая фотография будет главной.
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Поддерживаются форматы: JPEG, PNG, WebP (до 10MB)
                  </Typography>
                </Box>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                {/* Progress indicator */}
                <Box sx={{ mb: 3, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Загружено: {photos.length} из {maxPhotos}
                  </Typography>
                  <Box sx={{ 
                    width: '100%', 
                    height: 4, 
                    backgroundColor: theme.palette.grey[200], 
                    borderRadius: 2,
                    mt: 1,
                    overflow: 'hidden'
                  }}>
                    <Box sx={{
                      width: `${(photos.length / maxPhotos) * 100}%`,
                      height: '100%',
                      backgroundColor: photos.length >= minPhotos 
                        ? theme.palette.success.main 
                        : theme.palette.primary.main,
                      transition: 'all 0.3s ease'
                    }} />
                  </Box>
                </Box>

                {/* Upload area */}
                {photos.length < maxPhotos && (
                  <Box
                    {...getRootProps()}
                    sx={{
                      border: `2px dashed ${isDragActive ? theme.palette.primary.main : theme.palette.grey[300]}`,
                      borderRadius: 2,
                      p: 4,
                      textAlign: 'center',
                      cursor: 'pointer',
                      backgroundColor: isDragActive ? theme.palette.primary.light + '10' : 'transparent',
                      transition: 'all 0.2s ease',
                      mb: 4,
                      '&:hover': {
                        borderColor: theme.palette.primary.main,
                        backgroundColor: theme.palette.primary.light + '05'
                      }
                    }}
                  >
                    <input {...getInputProps()} />
                    <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      {isDragActive ? 'Отпустите файлы здесь' : 'Перетащите фото или нажмите для выбора'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Можно выбрать несколько файлов одновременно
                    </Typography>
                    {uploading && (
                      <Box sx={{ mt: 2 }}>
                        <CircularProgress size={24} />
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          Загрузка...
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}

                {/* Photos grid */}
                {photos.length > 0 && (
                  <Grid container spacing={2} sx={{ mb: 4 }}>
                    {photos.map((photo, index) => (
                      <Grid item xs={6} sm={4} md={3} key={photo.id}>
                        <Card sx={{ position: 'relative' }}>
                          <CardMedia
                            component="img"
                            height="200"
                            image={photo.url || photo.preview}
                            alt={`Фото ${index + 1}`}
                            sx={{
                              objectFit: 'cover',
                              filter: photo.isUploaded ? 'none' : 'grayscale(50%)'
                            }}
                          />
                          
                          {/* Main photo indicator */}
                          <IconButton
                            sx={{
                              position: 'absolute',
                              top: 8,
                              left: 8,
                              backgroundColor: 'rgba(0,0,0,0.5)',
                              color: photo.id === mainPhotoId ? 'gold' : 'white',
                              '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.7)'
                              }
                            }}
                            onClick={() => handleSetMainPhoto(photo.id)}
                            disabled={!photo.isUploaded}
                          >
                            {photo.id === mainPhotoId ? <StarIcon /> : <StarBorderIcon />}
                          </IconButton>

                          {/* Upload progress */}
                          {!photo.isUploaded && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 0,
                              left: 0,
                              right: 0,
                              backgroundColor: 'rgba(0,0,0,0.7)',
                              color: 'white',
                              p: 1,
                              textAlign: 'center'
                            }}>
                              <CircularProgress size={20} sx={{ color: 'white' }} />
                              <Typography variant="caption" sx={{ ml: 1 }}>
                                Загрузка...
                              </Typography>
                            </Box>
                          )}

                          <CardActions sx={{ justifyContent: 'space-between', p: 1 }}>
                            <Typography variant="caption" color="text.secondary">
                              {photo.id === mainPhotoId ? 'Главная' : `Фото ${index + 1}`}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => {
                                setPhotoToDelete(photo.id);
                                setDeleteDialogOpen(true);
                              }}
                              disabled={!photo.isUploaded}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                )}

                {/* Tips */}
                <Box sx={{ 
                  backgroundColor: theme.palette.info.light + '10',
                  borderRadius: 2,
                  p: 3,
                  mb: 4
                }}>
                  <Typography variant="h6" gutterBottom>
                    💡 Советы для лучших фотографий:
                  </Typography>
                  <Box component="ul" sx={{ m: 0, pl: 2 }}>
                    <li>Используйте качественные фото с хорошим освещением</li>
                    <li>Покажите себя в полный рост и крупным планом</li>
                    <li>Добавьте фото с вашими увлечениями</li>
                    <li>Избегайте групповых фото как главную</li>
                    <li>Улыбайтесь и будьте естественными</li>
                  </Box>
                </Box>

                {/* Navigation */}
                <Stack 
                  direction="row" 
                  justifyContent="space-between" 
                  alignItems="center"
                  sx={{ mt: 4 }}
                >
                  <Button
                    variant="outlined"
                    onClick={handleBack}
                    startIcon={<ArrowBack />}
                    disabled={onboardingLoading || uploading}
                  >
                    Назад
                  </Button>

                  <Button
                    variant="contained"
                    onClick={handleNext}
                    endIcon={<ArrowForward />}
                    disabled={!canProceed || onboardingLoading || uploading}
                    sx={{
                      background: canProceed 
                        ? `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                        : undefined
                    }}
                  >
                    {onboardingLoading ? <CircularProgress size={20} /> : 'Продолжить'}
                  </Button>
                </Stack>
              </Paper>
            </Fade>
          </Box>
        </Container>

        {/* Delete confirmation dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Удалить фотографию?</DialogTitle>
          <DialogContent>
            <Typography>
              Вы уверены, что хотите удалить эту фотографию? Это действие нельзя отменить.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              Отмена
            </Button>
            <Button 
              onClick={() => photoToDelete && handleDeletePhoto(photoToDelete)}
              color="error"
              variant="contained"
            >
              Удалить
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default PhotosPage;
