import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardMedia,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Grid,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tabs,
  Tab
} from '@mui/material';
import {
  ArrowBack,
  Favorite as FavoriteIcon,
  Reply as ReplyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Send as SendIcon,
  CardGiftcard as GiftIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

// Типы для полученных подарков
interface ReceivedGift {
  id: string;
  gift: {
    id: string;
    name: string;
    description: string;
    image: string;
    price: number;
  };
  sender: {
    id: string;
    name: string;
    avatar: string;
  } | null; // null для анонимных подарков
  message: string;
  isAnonymous: boolean;
  isViewed: boolean;
  receivedAt: string;
  isLiked: boolean;
  reply?: string;
}

interface ReplyForm {
  message: string;
}

// Схема валидации для ответа
const replySchema = yup.object({
  message: yup.string().required('Введите сообщение').max(200, 'Максимум 200 символов')
});

const ReceivedGiftsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [gifts, setGifts] = useState<ReceivedGift[]>([]);
  const [selectedGift, setSelectedGift] = useState<ReceivedGift | null>(null);
  const [replyDialogOpen, setReplyDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ReplyForm>({
    resolver: yupResolver(replySchema),
    defaultValues: {
      message: ''
    }
  });

  const tabs = [
    { label: 'Все', value: 'all' },
    { label: 'Новые', value: 'new' },
    { label: 'Просмотренные', value: 'viewed' }
  ];

  // Загрузка подарков при монтировании компонента
  useEffect(() => {
    loadReceivedGifts();
  }, []);

  const loadReceivedGifts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения полученных подарков
      // const response = await getReceivedGifts();
      
      // Мок данные
      const mockGifts: ReceivedGift[] = [
        {
          id: 'received1',
          gift: {
            id: 'gift1',
            name: 'Букет роз',
            description: 'Красивый букет красных роз',
            image: '/gifts/roses.jpg',
            price: 500
          },
          sender: {
            id: 'user1',
            name: 'Анна Петрова',
            avatar: '/avatars/anna.jpg'
          },
          message: 'Спасибо за прекрасный вечер! 🌹',
          isAnonymous: false,
          isViewed: false,
          receivedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isLiked: false
        },
        {
          id: 'received2',
          gift: {
            id: 'gift2',
            name: 'Коробка конфет',
            description: 'Элитные шоколадные конфеты',
            image: '/gifts/chocolates.jpg',
            price: 300
          },
          sender: null, // Анонимный подарок
          message: 'От тайного поклонника 😊',
          isAnonymous: true,
          isViewed: true,
          receivedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          isLiked: true,
          reply: 'Спасибо за сладкий сюрприз!'
        },
        {
          id: 'received3',
          gift: {
            id: 'gift3',
            name: 'Кофе премиум',
            description: 'Ароматный кофе высшего качества',
            image: '/gifts/coffee.jpg',
            price: 200
          },
          sender: {
            id: 'user2',
            name: 'Михаил Иванов',
            avatar: '/avatars/mikhail.jpg'
          },
          message: 'Доброе утро! Надеюсь, этот кофе поднимет настроение ☕',
          isAnonymous: false,
          isViewed: true,
          receivedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          isLiked: false
        }
      ];

      setGifts(mockGifts);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке подарков');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleGiftClick = async (gift: ReceivedGift) => {
    // Отмечаем подарок как просмотренный
    if (!gift.isViewed) {
      try {
        // Здесь будет вызов API для отметки просмотра
        // await markGiftAsViewed(gift.id);
        
        setGifts(prev => prev.map(g => 
          g.id === gift.id ? { ...g, isViewed: true } : g
        ));
      } catch (err) {
        console.error('Ошибка при отметке просмотра:', err);
      }
    }

    setSelectedGift(gift);
  };

  const handleLikeGift = async (giftId: string) => {
    try {
      // Здесь будет вызов API для лайка подарка
      // await likeReceivedGift(giftId);
      
      setGifts(prev => prev.map(gift => 
        gift.id === giftId 
          ? { ...gift, isLiked: !gift.isLiked }
          : gift
      ));
    } catch (err: any) {
      setError(err.message || 'Ошибка при лайке подарка');
    }
  };

  const handleReplyToGift = (gift: ReceivedGift) => {
    setSelectedGift(gift);
    setReplyDialogOpen(true);
  };

  const handleSendReply = async (data: ReplyForm) => {
    if (!selectedGift) return;

    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для отправки ответа
      // await replyToGift(selectedGift.id, data.message);

      setGifts(prev => prev.map(gift => 
        gift.id === selectedGift.id 
          ? { ...gift, reply: data.message }
          : gift
      ));

      setSuccess('Ответ отправлен!');
      setReplyDialogOpen(false);
      setSelectedGift(null);
      reset();
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке ответа');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredGifts = () => {
    switch (tabs[activeTab].value) {
      case 'new':
        return gifts.filter(gift => !gift.isViewed);
      case 'viewed':
        return gifts.filter(gift => gift.isViewed);
      default:
        return gifts;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ч назад`;
    return `${Math.floor(diffInMinutes / 1440)} дн назад`;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(price);
  };

  const handleBack = () => {
    router.push('/gifts');
  };

  if (!user) {
    return (
      <Layout title="Полученные подарки">
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  const filteredGifts = getFilteredGifts();

  return (
    <>
      <Head>
        <title>Полученные подарки - Likes & Love</title>
        <meta name="description" content="Просматривайте полученные виртуальные подарки в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Полученные подарки">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Полученные подарки
                </Typography>
                <Chip 
                  label={`${gifts.length} ${gifts.length === 1 ? 'подарок' : 'подарков'}`} 
                  color="primary" 
                />
              </Box>
              <Typography variant="h6" color="text.secondary">
                Подарки, которые вы получили от других пользователей
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Фильтры */}
            <Box sx={{ mb: 4 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{ borderBottom: 1, borderColor: 'divider' }}
              >
                {tabs.map((tab, index) => (
                  <Tab
                    key={tab.value}
                    label={`${tab.label} ${tab.value === 'new' ? `(${gifts.filter(g => !g.isViewed).length})` : 
                           tab.value === 'viewed' ? `(${gifts.filter(g => g.isViewed).length})` : 
                           `(${gifts.length})`}`}
                  />
                ))}
              </Tabs>
            </Box>

            {/* Список подарков */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {filteredGifts.map((receivedGift) => (
                  <Grid item xs={12} sm={6} md={4} key={receivedGift.id}>
                    <Card 
                      sx={{ 
                        height: '100%',
                        cursor: 'pointer',
                        position: 'relative',
                        border: !receivedGift.isViewed ? 2 : 1,
                        borderColor: !receivedGift.isViewed ? 'primary.main' : 'divider',
                        '&:hover': {
                          transform: 'scale(1.02)',
                          transition: 'transform 0.2s',
                          boxShadow: theme.shadows[8]
                        }
                      }}
                      onClick={() => handleGiftClick(receivedGift)}
                    >
                      <CardMedia
                        component="img"
                        height="200"
                        image={receivedGift.gift.image}
                        alt={receivedGift.gift.name}
                        sx={{ objectFit: 'cover' }}
                      />
                      
                      {/* Статус просмотра */}
                      {!receivedGift.isViewed && (
                        <Chip 
                          label="Новый" 
                          size="small" 
                          color="primary"
                          sx={{ position: 'absolute', top: 8, right: 8 }}
                        />
                      )}

                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                          {receivedGift.gift.name}
                        </Typography>
                        
                        {/* Отправитель */}
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                          {receivedGift.isAnonymous ? (
                            <>
                              <Avatar sx={{ width: 24, height: 24, bgcolor: 'grey.400' }}>
                                ?
                              </Avatar>
                              <Typography variant="body2" color="text.secondary">
                                Анонимный отправитель
                              </Typography>
                            </>
                          ) : (
                            <>
                              <Avatar 
                                src={receivedGift.sender?.avatar} 
                                sx={{ width: 24, height: 24 }}
                              />
                              <Typography variant="body2" color="text.secondary">
                                от {receivedGift.sender?.name}
                              </Typography>
                            </>
                          )}
                        </Box>

                        {/* Сообщение */}
                        {receivedGift.message && (
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              mb: 2,
                              fontStyle: 'italic',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}
                          >
                            "{receivedGift.message}"
                          </Typography>
                        )}

                        {/* Время и цена */}
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="caption" color="text.secondary">
                            {formatTimeAgo(receivedGift.receivedAt)}
                          </Typography>
                          <Typography variant="body2" color="primary" fontWeight="bold">
                            {formatPrice(receivedGift.gift.price)}
                          </Typography>
                        </Box>

                        {/* Действия */}
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'space-between' }}>
                          <IconButton
                            onClick={(e) => {
                              e.stopPropagation();
                              handleLikeGift(receivedGift.id);
                            }}
                            color={receivedGift.isLiked ? 'error' : 'default'}
                            size="small"
                          >
                            <FavoriteIcon />
                          </IconButton>
                          
                          {!receivedGift.isAnonymous && !receivedGift.reply && (
                            <Button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleReplyToGift(receivedGift);
                              }}
                              size="small"
                              startIcon={<ReplyIcon />}
                            >
                              Ответить
                            </Button>
                          )}

                          {receivedGift.reply && (
                            <Chip label="Отвечено" size="small" color="success" />
                          )}
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            {/* Пустое состояние */}
            {!loading && filteredGifts.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <GiftIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                  {activeTab === 1 ? 'Нет новых подарков' : 
                   activeTab === 2 ? 'Нет просмотренных подарков' : 
                   'У вас пока нет подарков'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {activeTab === 0 && 'Когда кто-то отправит вам подарок, он появится здесь'}
                </Typography>
                {activeTab === 0 && (
                  <Button
                    variant="contained"
                    onClick={() => router.push('/gifts')}
                    startIcon={<GiftIcon />}
                  >
                    Посмотреть магазин подарков
                  </Button>
                )}
              </Box>
            )}

            {/* Диалог ответа на подарок */}
            <Dialog
              open={replyDialogOpen}
              onClose={() => setReplyDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Ответить на подарок
              </DialogTitle>
              <DialogContent>
                {selectedGift && (
                  <Box sx={{ pt: 2 }}>
                    <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                      <img 
                        src={selectedGift.gift.image} 
                        alt={selectedGift.gift.name}
                        style={{ width: 60, height: 60, borderRadius: 8 }}
                      />
                      <Box>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {selectedGift.gift.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          от {selectedGift.sender?.name}
                        </Typography>
                      </Box>
                    </Box>

                    <form onSubmit={handleSubmit(handleSendReply)}>
                      <Controller
                        name="message"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Ваш ответ"
                            multiline
                            rows={3}
                            fullWidth
                            error={!!errors.message}
                            helperText={errors.message?.message || `${field.value?.length || 0}/200`}
                            placeholder="Спасибо за подарок!"
                          />
                        )}
                      />
                    </form>
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setReplyDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleSubmit(handleSendReply)}
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                >
                  {loading ? 'Отправка...' : 'Отправить'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ReceivedGiftsPage;
