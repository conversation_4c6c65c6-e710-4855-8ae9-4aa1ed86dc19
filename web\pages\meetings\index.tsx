import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Add as AddIcon,
  Event as EventIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Notifications as NotificationsIcon,
  CalendarToday as CalendarIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getMeetings,
  getMyMeetings,
  getUpcomingMeetings,
  joinMeeting,
  leaveMeeting
} from '../../src/services/meetingsService';
import { 
  Meeting,
  MeetingFilters 
} from '../../src/types/meetings.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`meetings-tabpanel-${index}`}
      aria-labelledby={`meetings-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const MeetingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [myMeetings, setMyMeetings] = useState<Meeting[]>([]);
  const [upcomingMeetings, setUpcomingMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const tabs = [
    { label: 'Все встречи', key: 'all' },
    { label: 'Мои встречи', key: 'my' },
    { label: 'Ближайшие', key: 'upcoming' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadData();
  }, [user, router, activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      switch (activeTab) {
        case 0: // All meetings
          const allMeetings = await getMeetings();
          setMeetings(allMeetings.data);
          break;
        case 1: // My meetings
          const myMeetingsData = await getMyMeetings();
          setMyMeetings(myMeetingsData.data);
          break;
        case 2: // Upcoming meetings
          const upcoming = await getUpcomingMeetings();
          setUpcomingMeetings(upcoming);
          break;
      }
    } catch (err: any) {
      setError('Ошибка загрузки встреч');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, meeting: Meeting) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedMeeting(meeting);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedMeeting(null);
  };

  const handleJoinMeeting = async (meeting: Meeting) => {
    if (!meeting) return;
    
    try {
      setActionLoading(meeting.id);
      setError(null);

      await joinMeeting(meeting.id);
      setSuccess('Вы присоединились к встрече');
      loadData(); // Reload data
    } catch (err: any) {
      setError('Ошибка присоединения к встрече');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const handleLeaveMeeting = async (meeting: Meeting) => {
    if (!meeting) return;
    
    try {
      setActionLoading(meeting.id);
      setError(null);

      await leaveMeeting(meeting.id);
      setSuccess('Вы покинули встречу');
      loadData(); // Reload data
    } catch (err: any) {
      setError('Ошибка выхода из встречи');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const formatMeetingDate = (dateString: string) => {
    const meetingDate = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((meetingDate.getTime() - now.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 0) {
      return 'Прошедшая';
    } else if (diffInHours < 24) {
      return `Через ${diffInHours} ч.`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) return 'Завтра';
      return `Через ${diffInDays} дн.`;
    }
  };

  const formatMeetingTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMeetingTypeColor = (type: Meeting['type']) => {
    switch (type) {
      case 'date':
        return 'error';
      case 'group_meeting':
        return 'primary';
      case 'activity':
        return 'success';
      case 'casual':
        return 'warning';
      case 'business':
        return 'info';
      default:
        return 'default';
    }
  };

  const getMeetingTypeLabel = (type: Meeting['type']) => {
    switch (type) {
      case 'date':
        return 'Свидание';
      case 'group_meeting':
        return 'Групповая встреча';
      case 'activity':
        return 'Активность';
      case 'casual':
        return 'Неформальная';
      case 'business':
        return 'Деловая';
      default:
        return type;
    }
  };

  const isUserParticipant = (meeting: Meeting) => {
    return meeting.participants.some(p => p.id === user?.id);
  };

  const isUserOrganizer = (meeting: Meeting) => {
    return meeting.organizer.id === user?.id;
  };

  const getCurrentMeetings = () => {
    switch (activeTab) {
      case 0:
        return meetings;
      case 1:
        return myMeetings;
      case 2:
        return upcomingMeetings;
      default:
        return [];
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Встречи - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите и создайте встречи в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/meetings" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
              <Typography variant={isMobile ? "h5" : "h4"}>
                <EventIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Встречи
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => router.push('/meetings/requests')}>
                  <Badge badgeContent={0} color="error">
                    <NotificationsIcon />
                  </Badge>
                </IconButton>
                <IconButton onClick={() => router.push('/calendar')}>
                  <CalendarIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/meetings?filters=true')}>
                  <FilterIcon />
                </IconButton>
                <IconButton onClick={loadData} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/meetings/create')}
                  size={isMobile ? "small" : "medium"}
                >
                  Создать встречу
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Tabs */}
            <Paper elevation={2} sx={{ mb: 3 }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
              >
                {tabs.map((tab, index) => (
                  <Tab key={index} label={tab.label} />
                ))}
              </Tabs>
            </Paper>

            {/* Content */}
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка встреч...
                </Typography>
              </Box>
            ) : getCurrentMeetings().length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <EventIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {activeTab === 1 ? 'У вас пока нет встреч' : 'Встречи не найдены'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {activeTab === 1 
                    ? 'Создайте свою первую встречу или присоединитесь к существующей'
                    : 'Попробуйте изменить фильтры или создайте новую встречу'
                  }
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/meetings/create')}
                >
                  Создать встречу
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {getCurrentMeetings().map((meeting) => (
                    <Grid item xs={12} sm={6} md={4} key={meeting.id}>
                      <Card 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[8]
                          },
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onClick={() => router.push(`/meetings/${meeting.id}`)}
                      >
                        {meeting.photos.length > 0 && (
                          <CardMedia
                            component="img"
                            height="200"
                            image={meeting.photos[0]}
                            alt={meeting.title}
                            sx={{ objectFit: 'cover' }}
                          />
                        )}

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                            <Typography variant="h6" component="h3" sx={{ flexGrow: 1, mr: 1 }}>
                              {meeting.title}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, meeting)}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Box>

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                            <Chip
                              label={getMeetingTypeLabel(meeting.type)}
                              size="small"
                              color={getMeetingTypeColor(meeting.type) as any}
                              variant="outlined"
                            />
                            <Chip
                              label={formatMeetingDate(meeting.scheduledAt)}
                              size="small"
                              color="primary"
                            />
                            {meeting.privacy === 'private' && (
                              <Chip
                                label="Приватная"
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {formatMeetingTime(meeting.scheduledAt)}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {meeting.location.name}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <PeopleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {meeting.participants.length}
                              {meeting.maxParticipants && ` / ${meeting.maxParticipants}`} участников
                            </Typography>
                          </Box>

                          {meeting.description && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {meeting.description.length > 100 
                                ? `${meeting.description.substring(0, 100)}...`
                                : meeting.description
                              }
                            </Typography>
                          )}

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar
                              src={meeting.organizer.avatarUrl}
                              sx={{ width: 24, height: 24 }}
                            >
                              {meeting.organizer.firstName[0]}
                            </Avatar>
                            <Typography variant="caption" color="text.secondary">
                              Организатор: {meeting.organizer.firstName}
                            </Typography>
                            {meeting.organizer.verificationStatus.phone && (
                              <VerifiedIcon sx={{ fontSize: 14, color: 'primary.main' }} />
                            )}
                          </Box>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            {meeting.tags.slice(0, 2).map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {meeting.tags.length > 2 && (
                              <Chip
                                label={`+${meeting.tags.length - 2}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>

                          <Box>
                            {meeting.cost?.type === 'free' ? (
                              <Chip label="Бесплатно" size="small" color="success" />
                            ) : meeting.cost?.amount && (
                              <Chip 
                                label={`${meeting.cost.amount} ${meeting.cost.currency}`} 
                                size="small" 
                                color="warning" 
                              />
                            )}
                          </Box>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => {
            if (selectedMeeting) {
              router.push(`/meetings/${selectedMeeting.id}`);
            }
            handleMenuClose();
          }}>
            Подробности
          </MenuItem>
          
          {selectedMeeting && !isUserParticipant(selectedMeeting) && !isUserOrganizer(selectedMeeting) && (
            <MenuItem 
              onClick={() => selectedMeeting && handleJoinMeeting(selectedMeeting)}
              disabled={actionLoading === selectedMeeting?.id}
            >
              Присоединиться
            </MenuItem>
          )}
          
          {selectedMeeting && isUserParticipant(selectedMeeting) && !isUserOrganizer(selectedMeeting) && (
            <MenuItem 
              onClick={() => selectedMeeting && handleLeaveMeeting(selectedMeeting)}
              disabled={actionLoading === selectedMeeting?.id}
            >
              Покинуть встречу
            </MenuItem>
          )}
          
          {selectedMeeting && isUserOrganizer(selectedMeeting) && (
            <MenuItem onClick={() => {
              if (selectedMeeting) {
                router.push(`/meetings/${selectedMeeting.id}/edit`);
              }
              handleMenuClose();
            }}>
              Редактировать
            </MenuItem>
          )}
          
          <MenuItem onClick={() => {
            if (selectedMeeting) {
              // Share meeting logic
            }
            handleMenuClose();
          }}>
            Поделиться
          </MenuItem>
          
          <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
            Пожаловаться
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default MeetingsPage;
