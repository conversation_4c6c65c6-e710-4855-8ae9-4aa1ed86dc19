import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  TextField, 
  Button, 
  Typography, 
  Box, 
  Link,
  Divider,
  Alert,
  InputAdornment,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useAuth } from '../../src/providers/AuthProvider';
import SocialAuthButtons from '../../components/Auth/SocialAuthButtons';
import Layout from '../../components/Layout/Layout';

const steps = ['Основная информация', 'Создание пароля', 'Подтверждение'];

const RegisterPage: React.FC = () => {
  const router = useRouter();
  const { register } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'agreeToTerms' ? checked : value
    });
  };

  const handleNext = () => {
    if (validateStep()) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateStep = () => {
    setError('');
    
    switch (activeStep) {
      case 0:
        if (!formData.email || !formData.firstName || !formData.lastName) {
          setError('Заполните все обязательные поля');
          return false;
        }
        if (!/\S+@\S+\.\S+/.test(formData.email)) {
          setError('Введите корректный email');
          return false;
        }
        break;
      case 1:
        if (!formData.password || !formData.confirmPassword) {
          setError('Введите пароль');
          return false;
        }
        if (formData.password.length < 8) {
          setError('Пароль должен содержать минимум 8 символов');
          return false;
        }
        if (formData.password !== formData.confirmPassword) {
          setError('Пароли не совпадают');
          return false;
        }
        break;
      case 2:
        if (!formData.agreeToTerms) {
          setError('Необходимо принять условия использования');
          return false;
        }
        break;
    }
    
    return true;
  };

  const handleSubmit = async () => {
    if (!validateStep()) return;
    
    setLoading(true);
    try {
      await register({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName
      });
      router.push('/profile/edit');
    } catch (err: any) {
      setError(err.message || 'Ошибка регистрации');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <>
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              margin="normal"
              required
              autoComplete="email"
            />
            <TextField
              fullWidth
              label="Телефон"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              margin="normal"
              autoComplete="tel"
              helperText="Необязательно"
            />
            <TextField
              fullWidth
              label="Имя"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              margin="normal"
              required
              autoComplete="given-name"
            />
            <TextField
              fullWidth
              label="Фамилия"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              margin="normal"
              required
              autoComplete="family-name"
            />
          </>
        );
      
      case 1:
        return (
          <>
            <TextField
              fullWidth
              label="Пароль"
              name="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleChange}
              margin="normal"
              required
              autoComplete="new-password"
              helperText="Минимум 8 символов"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              fullWidth
              label="Подтвердите пароль"
              name="confirmPassword"
              type={showPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleChange}
              margin="normal"
              required
              autoComplete="new-password"
            />
          </>
        );
      
      case 2:
        return (
          <>
            <Box sx={{ mt: 2, mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Подтверждение регистрации
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Email: {formData.email}
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Имя: {formData.firstName} {formData.lastName}
              </Typography>
            </Box>
            
            <FormControlLabel
              control={
                <Checkbox
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleChange}
                />
              }
              label={
                <Typography variant="body2">
                  Я принимаю{' '}
                  <Link href="/terms" target="_blank">
                    условия использования
                  </Link>{' '}
                  и{' '}
                  <Link href="/privacy" target="_blank">
                    политику конфиденциальности
                  </Link>
                </Typography>
              }
            />
          </>
        );
      
      default:
        return null;
    }
  };

  return (
    <Layout>
      <Container maxWidth="sm">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Typography variant="h4" align="center" gutterBottom>
              Регистрация
            </Typography>
            
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
            
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {renderStepContent()}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                disabled={activeStep === 0}
                onClick={handleBack}
              >
                Назад
              </Button>
              
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={loading}
                >
                  {loading ? 'Регистрация...' : 'Зарегистрироваться'}
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                >
                  Далее
                </Button>
              )}
            </Box>

            {activeStep === 0 && (
              <>
                <Divider sx={{ my: 3 }}>или</Divider>
                <SocialAuthButtons />
              </>
            )}

            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Typography variant="body2">
                Уже есть аккаунт?{' '}
                <Link href="/auth/login" variant="body2">
                  Войти
                </Link>
              </Typography>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default RegisterPage;
