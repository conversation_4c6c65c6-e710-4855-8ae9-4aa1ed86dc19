import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Switch,
  FormControlLabel,
  FormGroup,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  useTheme,
  useMediaQuery,
  Fade,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ArrowBack,
  Privacy as PrivacyIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Shield as ShieldIcon,
  Block as BlockIcon,
  Report as ReportIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSettings } from '../../src/contexts/SettingsContext';

const PrivacySettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    settings, 
    loading, 
    error, 
    updatePrivacySettings,
    loadSettings 
  } = useSettings();

  const [localSettings, setLocalSettings] = useState<any>(null);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showInfoDialog, setShowInfoDialog] = useState(false);
  const [infoDialogContent, setInfoDialogContent] = useState<{ title: string; content: string } | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    
    if (settings?.privacy) {
      setLocalSettings(settings.privacy);
    }
  }, [user, router, settings]);

  const handleSettingChange = (key: string, value: boolean) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    if (!localSettings) return;
    
    try {
      setSaving(true);
      await updatePrivacySettings({ privacy: localSettings });
      setSuccess('Настройки приватности обновлены');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (settings?.privacy) {
      setLocalSettings(settings.privacy);
    }
  };

  const showInfo = (title: string, content: string) => {
    setInfoDialogContent({ title, content });
    setShowInfoDialog(true);
  };

  const privacySettings = [
    {
      category: 'Видимость профиля',
      icon: <VisibilityIcon color="primary" />,
      settings: [
        {
          key: 'profileVisibility',
          title: 'Видимость профиля',
          description: 'Кто может видеть ваш профиль',
          type: 'select',
          options: [
            { value: 'public', label: 'Всем пользователям' },
            { value: 'friends', label: 'Только совпадениям' },
            { value: 'private', label: 'Никому' }
          ],
          info: 'Управляет тем, кто может найти и просматривать ваш профиль в приложении'
        },
        {
          key: 'showOnlineStatus',
          title: 'Показывать статус "В сети"',
          description: 'Другие пользователи видят, когда вы онлайн',
          type: 'switch',
          info: 'Когда включено, другие пользователи могут видеть зеленую точку рядом с вашим именем'
        },
        {
          key: 'showLastSeen',
          title: 'Показывать время последнего посещения',
          description: 'Когда вы были в сети в последний раз',
          type: 'switch',
          info: 'Отображает информацию о том, когда вы последний раз заходили в приложение'
        },
        {
          key: 'showAge',
          title: 'Показывать возраст',
          description: 'Отображать ваш возраст в профиле',
          type: 'switch',
          info: 'Ваш возраст будет виден другим пользователям в профиле'
        }
      ]
    },
    {
      category: 'Местоположение',
      icon: <LocationIcon color="primary" />,
      settings: [
        {
          key: 'showLocation',
          title: 'Показывать местоположение',
          description: 'Отображать ваше местоположение в профиле',
          type: 'switch',
          info: 'Другие пользователи смогут видеть ваш город или район'
        },
        {
          key: 'showDistance',
          title: 'Показывать расстояние',
          description: 'Отображать расстояние до других пользователей',
          type: 'switch',
          info: 'В профилях других пользователей будет показано расстояние до них'
        }
      ]
    },
    {
      category: 'Сообщения и взаимодействия',
      icon: <PersonIcon color="primary" />,
      settings: [
        {
          key: 'allowMessagesFrom',
          title: 'Кто может писать сообщения',
          description: 'Ограничить возможность отправки сообщений',
          type: 'select',
          options: [
            { value: 'everyone', label: 'Все пользователи' },
            { value: 'matches', label: 'Только совпадения' },
            { value: 'premium', label: 'Только Premium пользователи' }
          ],
          info: 'Контролирует, кто может отправлять вам первое сообщение'
        },
        {
          key: 'allowProfileViews',
          title: 'Кто может просматривать профиль',
          description: 'Ограничить просмотр полного профиля',
          type: 'select',
          options: [
            { value: 'everyone', label: 'Все пользователи' },
            { value: 'matches', label: 'Только совпадения' },
            { value: 'premium', label: 'Только Premium пользователи' }
          ],
          info: 'Определяет, кто может видеть все ваши фотографии и полную информацию'
        }
      ]
    },
    {
      category: 'Безопасность и конфиденциальность',
      icon: <ShieldIcon color="primary" />,
      settings: [
        {
          key: 'incognitoMode',
          title: 'Режим инкогнито',
          description: 'Скрыть ваш профиль от поиска',
          type: 'switch',
          info: 'В режиме инкогнито ваш профиль не будет показываться в поиске других пользователей'
        },
        {
          key: 'hideFromSearch',
          title: 'Скрыть из поиска',
          description: 'Не показывать в результатах поиска',
          type: 'switch',
          info: 'Ваш профиль не будет появляться в результатах поиска по имени или фильтрам'
        },
        {
          key: 'blockScreenshots',
          title: 'Блокировать скриншоты',
          description: 'Запретить создание скриншотов профиля',
          type: 'switch',
          info: 'Попытка сделать скриншот вашего профиля будет заблокирована (работает не на всех устройствах)'
        }
      ]
    },
    {
      category: 'Согласие на обработку данных',
      icon: <SecurityIcon color="primary" />,
      settings: [
        {
          key: 'dataProcessingConsent',
          title: 'Обработка персональных данных',
          description: 'Согласие на обработку ваших данных',
          type: 'switch',
          info: 'Необходимо для работы основных функций приложения',
          required: true
        },
        {
          key: 'marketingConsent',
          title: 'Маркетинговые материалы',
          description: 'Получать рекламные предложения',
          type: 'switch',
          info: 'Согласие на получение персонализированной рекламы и предложений'
        },
        {
          key: 'analyticsConsent',
          title: 'Аналитика использования',
          description: 'Сбор данных для улучшения сервиса',
          type: 'switch',
          info: 'Помогает нам улучшать приложение на основе анализа использования'
        }
      ]
    }
  ];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Настройки приватности - Likes & Love</title>
        <meta 
          name="description" 
          content="Настройки приватности и безопасности в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <PrivacyIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Настройки приватности
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleReset}
                  disabled={loading || saving}
                >
                  Сбросить
                </Button>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                  disabled={loading || saving || !localSettings}
                >
                  {saving ? 'Сохранение...' : 'Сохранить'}
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка настроек приватности...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* Privacy Warning */}
                  <Alert severity="info" sx={{ mb: 4 }}>
                    <Typography variant="body2">
                      <strong>Важно:</strong> Настройки приватности влияют на то, как другие пользователи 
                      видят ваш профиль и взаимодействуют с вами. Рекомендуем внимательно изучить каждую опцию.
                    </Typography>
                  </Alert>

                  {/* Privacy Settings */}
                  {privacySettings.map((category, categoryIndex) => (
                    <Accordion key={category.category} defaultExpanded={categoryIndex === 0}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          {category.icon}
                          <Typography variant="h6">{category.category}</Typography>
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <List>
                          {category.settings.map((setting, settingIndex) => (
                            <React.Fragment key={setting.key}>
                              <ListItem sx={{ px: 0, py: 2 }}>
                                <ListItemText
                                  primary={
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <Typography variant="body1">
                                        {setting.title}
                                      </Typography>
                                      {setting.required && (
                                        <Chip label="Обязательно" size="small" color="error" />
                                      )}
                                      <Button
                                        size="small"
                                        onClick={() => showInfo(setting.title, setting.info)}
                                        sx={{ minWidth: 'auto', p: 0.5 }}
                                      >
                                        <InfoIcon fontSize="small" />
                                      </Button>
                                    </Box>
                                  }
                                  secondary={setting.description}
                                />
                                <ListItemSecondaryAction>
                                  {setting.type === 'switch' ? (
                                    <Switch
                                      checked={localSettings?.[setting.key] || false}
                                      onChange={(e) => handleSettingChange(setting.key, e.target.checked)}
                                      disabled={saving}
                                    />
                                  ) : (
                                    <TextField
                                      select
                                      size="small"
                                      value={localSettings?.[setting.key] || setting.options?.[0]?.value}
                                      onChange={(e) => handleSettingChange(setting.key, e.target.value)}
                                      disabled={saving}
                                      sx={{ minWidth: 150 }}
                                    >
                                      {setting.options?.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                          {option.label}
                                        </MenuItem>
                                      ))}
                                    </TextField>
                                  )}
                                </ListItemSecondaryAction>
                              </ListItem>
                              {settingIndex < category.settings.length - 1 && <Divider />}
                            </React.Fragment>
                          ))}
                        </List>
                      </AccordionDetails>
                    </Accordion>
                  ))}

                  {/* Quick Actions */}
                  <Paper elevation={2} sx={{ p: 3, mt: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Быстрые действия
                    </Typography>

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<BlockIcon />}
                        onClick={() => router.push('/settings/blocked')}
                        fullWidth={isMobile}
                      >
                        Заблокированные пользователи
                      </Button>

                      <Button
                        variant="outlined"
                        startIcon={<ReportIcon />}
                        onClick={() => router.push('/settings/reports')}
                        fullWidth={isMobile}
                      >
                        Мои жалобы
                      </Button>

                      <Button
                        variant="outlined"
                        startIcon={<InfoIcon />}
                        onClick={() => router.push('/privacy-policy')}
                        fullWidth={isMobile}
                      >
                        Политика конфиденциальности
                      </Button>
                    </Box>
                  </Paper>

                  {/* Privacy Tips */}
                  <Card sx={{ mt: 4, backgroundColor: 'background.default' }}>
                    <CardHeader
                      avatar={<ShieldIcon color="primary" />}
                      title="Советы по безопасности"
                      titleTypographyProps={{ variant: 'h6' }}
                    />
                    <CardContent>
                      <List dense>
                        <ListItem>
                          <ListItemIcon>
                            <WarningIcon color="warning" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Не делитесь личной информацией"
                            secondary="Избегайте указания номера телефона, адреса или места работы в профиле"
                          />
                        </ListItem>

                        <ListItem>
                          <ListItemIcon>
                            <SecurityIcon color="primary" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Используйте режим инкогнито"
                            secondary="Если хотите просматривать профили незаметно"
                          />
                        </ListItem>

                        <ListItem>
                          <ListItemIcon>
                            <BlockIcon color="error" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary="Блокируйте подозрительных пользователей"
                            secondary="Не стесняйтесь блокировать тех, кто ведет себя неподобающе"
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Box>
              </Fade>
            )}

            {/* Info Dialog */}
            <Dialog
              open={showInfoDialog}
              onClose={() => setShowInfoDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                {infoDialogContent?.title}
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1">
                  {infoDialogContent?.content}
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowInfoDialog(false)}>
                  Понятно
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PrivacySettingsPage;
