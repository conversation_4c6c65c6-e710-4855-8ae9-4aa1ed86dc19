import React from 'react';
import Head from 'next/head';
import { Box, Typography, Grid, Container, Chip, Avatar } from '@mui/material';
import Layout from '../components/Layout/Layout';
import { EnhancedCard, EnhancedTypography, EnhancedButton } from '../components/UI';
import { FaNewspaper, FaDownload, FaCalendar } from 'react-icons/fa';

const PressPage: React.FC = () => {
  const pressReleases = [
    {
      date: '15 декабря 2024',
      title: 'Likes & Love достигает 1 миллиона пользователей в России',
      excerpt: 'Платформа знакомств Likes & Love объявляет о достижении важной вехи - 1 миллион зарегистрированных пользователей в России.',
      category: 'Достижения',
      readTime: '3 мин'
    },
    {
      date: '28 ноября 2024',
      title: 'Запуск новых функций безопасности и верификации',
      excerpt: 'Компания представляет революционную систему верификации пользователей с использованием ИИ для повышения безопасности знакомств.',
      category: 'Продукт',
      readTime: '4 мин'
    },
    {
      date: '10 ноября 2024',
      title: 'Likes & Love получает инвестиции в размере $5 млн',
      excerpt: 'Российская платформа знакомств привлекает серию A инвестиций для расширения на международные рынки.',
      category: 'Инвестиции',
      readTime: '5 мин'
    }
  ];

  const mediaKit = [
    {
      title: 'Логотипы и брендинг',
      description: 'Официальные логотипы в различных форматах и цветовых схемах',
      fileSize: '2.5 MB',
      format: 'ZIP (PNG, SVG, AI)'
    },
    {
      title: 'Скриншоты приложения',
      description: 'Высококачественные скриншоты мобильного приложения и веб-версии',
      fileSize: '8.1 MB',
      format: 'ZIP (PNG, JPG)'
    },
    {
      title: 'Фотографии команды',
      description: 'Профессиональные фотографии основателей и ключевых сотрудников',
      fileSize: '12.3 MB',
      format: 'ZIP (JPG)'
    }
  ];

  const stats = [
    { number: '1M+', label: 'Активных пользователей' },
    { number: '50K+', label: 'Успешных пар' },
    { number: '15', label: 'Стран присутствия' },
    { number: '4.8', label: 'Рейтинг в App Store' }
  ];

  const teamMembers = [
    {
      name: 'Алексей Петров',
      position: 'CEO и основатель',
      bio: 'Серийный предприниматель с 10-летним опытом в IT. Ранее основал две успешные технологические компании.',
      avatar: '/images/team/ceo.jpg'
    },
    {
      name: 'Мария Сидорова',
      position: 'CTO',
      bio: 'Эксперт в области машинного обучения и больших данных. Бывший ведущий разработчик в Яндексе.',
      avatar: '/images/team/cto.jpg'
    }
  ];

  return (
    <Layout>
      <Head>
        <title>Пресс-центр Likes & Love - Новости и медиа-материалы</title>
        <meta name="description" content="Пресс-релизы, новости, медиа-кит и контакты для прессы. Актуальная информация о Likes & Love для журналистов и СМИ." />
      </Head>

      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          textAlign: 'center'
        }}
      >
        <Container maxWidth="lg">
          <EnhancedTypography variant="h1" romantic gradient shadow sx={{ mb: 3, color: 'white' }}>
            Пресс-центр Likes & Love
          </EnhancedTypography>
          <EnhancedTypography variant="h5" readable sx={{ mb: 4, opacity: 0.95, maxWidth: '800px', mx: 'auto' }}>
            Актуальные новости, пресс-релизы и медиа-материалы о ведущей российской платформе знакомств
          </EnhancedTypography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <EnhancedButton
              variant="contained"
              size="large"
              href="#press-releases"
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
              }}
            >
              Пресс-релизы
            </EnhancedButton>
            <EnhancedButton
              variant="outlined"
              size="large"
              href="#media-kit"
              sx={{
                borderColor: 'rgba(255,255,255,0.5)',
                color: 'white',
                '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }
              }}
            >
              Медиа-кит
            </EnhancedButton>
          </Box>
        </Container>
      </Box>

      {/* Stats Section */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          {stats.map((stat, index) => (
            <Grid item xs={6} md={3} key={index}>
              <Box sx={{ textAlign: 'center' }}>
                <EnhancedTypography variant="h2" romantic gradient sx={{ mb: 1 }}>
                  {stat.number}
                </EnhancedTypography>
                <EnhancedTypography variant="body1" readable color="text.secondary">
                  {stat.label}
                </EnhancedTypography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Press Releases Section */}
      <Box sx={{ bgcolor: 'background.default', py: 8 }} id="press-releases">
        <Container maxWidth="lg">
          <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
            Последние новости
          </EnhancedTypography>
          
          <Grid container spacing={4}>
            {pressReleases.map((release, index) => (
              <Grid item xs={12} md={6} key={index}>
                <EnhancedCard romantic hoverable sx={{ height: '100%' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Chip label={release.category} color="primary" size="small" />
                    <Typography variant="caption" color="text.secondary">
                      <FaCalendar style={{ marginRight: 4 }} />
                      {release.date}
                    </Typography>
                  </Box>
                  
                  <EnhancedTypography variant="h5" romantic sx={{ mb: 2 }}>
                    {release.title}
                  </EnhancedTypography>
                  
                  <EnhancedTypography variant="body1" readable sx={{ mb: 3 }}>
                    {release.excerpt}
                  </EnhancedTypography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      Время чтения: {release.readTime}
                    </Typography>
                    <EnhancedButton
                      variant="outlined"
                      romantic
                      href={`mailto:<EMAIL>?subject=Запрос на полный пресс-релиз: ${release.title}`}
                    >
                      Читать полностью
                    </EnhancedButton>
                  </Box>
                </EnhancedCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Media Kit Section */}
      <Container maxWidth="lg" sx={{ py: 8 }} id="media-kit">
        <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
          Медиа-кит для прессы
        </EnhancedTypography>
        
        <Grid container spacing={4}>
          {mediaKit.map((item, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <EnhancedCard romantic hoverable glassmorphism sx={{ textAlign: 'center', height: '100%' }}>
                <Box sx={{ color: 'primary.main', mb: 2, fontSize: '2rem' }}>
                  <FaDownload />
                </Box>
                <EnhancedTypography variant="h6" romantic sx={{ mb: 2 }}>
                  {item.title}
                </EnhancedTypography>
                <EnhancedTypography variant="body2" readable sx={{ mb: 2 }}>
                  {item.description}
                </EnhancedTypography>
                <Box sx={{ mb: 3 }}>
                  <Chip label={item.format} size="small" sx={{ mb: 1 }} />
                  <Typography variant="caption" display="block" color="text.secondary">
                    {item.fileSize}
                  </Typography>
                </Box>
                <EnhancedButton
                  variant="contained"
                  romantic
                  fullWidth
                  href={`mailto:<EMAIL>?subject=Запрос медиа-материалов: ${item.title}`}
                >
                  Скачать
                </EnhancedButton>
              </EnhancedCard>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Team Section */}
      <Box sx={{ bgcolor: 'background.default', py: 8 }}>
        <Container maxWidth="lg">
          <EnhancedTypography variant="h2" textAlign="center" romantic gradient shadow sx={{ mb: 6 }}>
            Ключевые лица компании
          </EnhancedTypography>
          
          <Grid container spacing={4}>
            {teamMembers.map((member, index) => (
              <Grid item xs={12} md={6} key={index}>
                <EnhancedCard romantic hoverable sx={{ textAlign: 'center', height: '100%' }}>
                  <Avatar
                    src={member.avatar}
                    alt={member.name}
                    sx={{ width: 120, height: 120, mx: 'auto', mb: 3 }}
                  />
                  <EnhancedTypography variant="h5" romantic sx={{ mb: 1 }}>
                    {member.name}
                  </EnhancedTypography>
                  <Typography variant="subtitle1" color="primary" sx={{ mb: 2, fontWeight: 600 }}>
                    {member.position}
                  </Typography>
                  <EnhancedTypography variant="body2" readable>
                    {member.bio}
                  </EnhancedTypography>
                </EnhancedCard>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Contact Section */}
      <Container maxWidth="lg" sx={{ py: 8, textAlign: 'center' }}>
        <EnhancedTypography variant="h2" romantic gradient shadow sx={{ mb: 4 }}>
          Контакты для прессы
        </EnhancedTypography>
        <EnhancedTypography variant="h5" readable sx={{ mb: 4, maxWidth: '600px', mx: 'auto' }}>
          Для получения дополнительной информации, интервью или медиа-материалов обращайтесь к нашей пресс-службе
        </EnhancedTypography>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
          <EnhancedButton
            variant="contained"
            size="large"
            romantic
            large
            href="mailto:<EMAIL>"
          >
            <EMAIL>
          </EnhancedButton>
          <EnhancedButton
            variant="outlined"
            size="large"
            href="tel:+74951234567"
          >
            +7 (495) 123-45-67
          </EnhancedButton>
        </Box>
      </Container>
    </Layout>
  );
};

export default PressPage;
