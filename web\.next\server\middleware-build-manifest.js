globalThis.__BUILD_MANIFEST = {
  "polyfillFiles": [
    "static/chunks/polyfills.js"
  ],
  "devFiles": [
    "static/chunks/react-refresh.js"
  ],
  "ampDevFiles": [],
  "lowPriorityFiles": [],
  "rootMainFiles": [],
  "rootMainFilesTree": {},
  "pages": {
    "/": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/index.js"
    ],
    "/_app": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/_app.js"
    ],
    "/_error": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/_error.js"
    ],
    "/auth/login": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/auth/login.js"
    ],
    "/calls": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/calls.js"
    ],
    "/discover": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/discover.js"
    ],
    "/events": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/events.js"
    ],
    "/likes": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/likes.js"
    ],
    "/messages": [
      "static/chunks/webpack.js",
      "static/chunks/main.js",
      "static/chunks/pages/messages.js"
    ]
  },
  "ampFirstPages": []
};
globalThis.__BUILD_MANIFEST.lowPriorityFiles = [
"/static/" + process.env.__NEXT_BUILD_ID + "/_buildManifest.js",
,"/static/" + process.env.__NEXT_BUILD_ID + "/_ssgManifest.js",

];