import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Stack,
  Grid,
  Card,
  CardContent,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Slider,
  Chip,
  Switch,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Fade,
  Divider
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Tune as TuneIcon,
  Favorite,
  LocationOn,
  Person,
  Block
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import OnboardingProgress from '../../components/Onboarding/OnboardingProgress';
import { useAuth } from '../../src/providers/AuthProvider';
import { useOnboarding } from '../../src/contexts/OnboardingContext';
import { MatchPreferences } from '../../src/types/onboarding.types';

const schema = yup.object({
  ageRange: yup.array().of(yup.number()).min(2).required('Укажите возрастной диапазон'),
  maxDistance: yup.number().min(1).max(100).required('Укажите максимальное расстояние'),
  genderPreference: yup.string().oneOf(['male', 'female', 'both']).required('Выберите предпочтение по полу'),
  relationshipGoals: yup.string().oneOf(['casual', 'serious', 'friendship', 'networking']).required('Выберите цель знакомства'),
  dealBreakers: yup.object({
    smoking: yup.boolean(),
    drinking: yup.boolean(),
    hasChildren: yup.boolean(),
    wantsChildren: yup.boolean()
  }),
  importantTraits: yup.array().of(yup.string()).max(5, 'Максимум 5 важных качеств')
});

const PreferencesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    data, 
    steps, 
    currentStep, 
    updatePreferences, 
    nextStep, 
    previousStep,
    saveProgress,
    loading: onboardingLoading 
  } = useOnboarding();

  const [error, setError] = useState<string | null>(null);

  const { control, handleSubmit, watch, setValue, formState: { errors, isValid } } = useForm<MatchPreferences>({
    resolver: yupResolver(schema),
    defaultValues: {
      ageRange: data.preferences.ageRange || [18, 35],
      maxDistance: data.preferences.maxDistance || 25,
      genderPreference: data.preferences.genderPreference || 'both',
      relationshipGoals: data.preferences.relationshipGoals || 'serious',
      dealBreakers: {
        smoking: data.preferences.dealBreakers?.smoking || false,
        drinking: data.preferences.dealBreakers?.drinking || false,
        hasChildren: data.preferences.dealBreakers?.hasChildren || false,
        wantsChildren: data.preferences.dealBreakers?.wantsChildren || false
      },
      importantTraits: data.preferences.importantTraits || []
    },
    mode: 'onChange'
  });

  const watchedValues = watch();

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
    }
  }, [user, router]);

  const relationshipGoalsOptions = [
    { value: 'serious', label: 'Серьезные отношения', icon: '💕', description: 'Ищу долгосрочные отношения' },
    { value: 'casual', label: 'Легкое общение', icon: '😊', description: 'Без обязательств, просто хорошо провести время' },
    { value: 'friendship', label: 'Дружба', icon: '🤝', description: 'Ищу новых друзей и интересное общение' },
    { value: 'networking', label: 'Нетворкинг', icon: '💼', description: 'Профессиональные и деловые знакомства' }
  ];

  const genderOptions = [
    { value: 'male', label: 'Мужчины', icon: '👨' },
    { value: 'female', label: 'Женщины', icon: '👩' },
    { value: 'both', label: 'Все', icon: '👥' }
  ];

  const importantTraitsOptions = [
    'Чувство юмора', 'Честность', 'Доброта', 'Амбициозность', 'Интеллект',
    'Спортивность', 'Творческость', 'Надежность', 'Романтичность', 'Общительность',
    'Самостоятельность', 'Семейность', 'Путешествия', 'Образованность', 'Щедрость'
  ];

  const handleTraitToggle = (trait: string) => {
    const currentTraits = watchedValues.importantTraits || [];
    if (currentTraits.includes(trait)) {
      setValue('importantTraits', currentTraits.filter(t => t !== trait));
    } else if (currentTraits.length < 5) {
      setValue('importantTraits', [...currentTraits, trait]);
    }
  };

  const onSubmit = async (formData: MatchPreferences) => {
    try {
      setError(null);
      updatePreferences(formData);
      await saveProgress();
      nextStep();
    } catch (err: any) {
      setError('Ошибка сохранения предпочтений');
    }
  };

  const handleBack = () => {
    updatePreferences(watchedValues);
    previousStep();
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Настройте предпочтения - Likes & Love</title>
        <meta 
          name="description" 
          content="Настройте ваши предпочтения для поиска идеального партнера в приложении знакомств" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <OnboardingProgress
              steps={steps}
              currentStep={currentStep}
              variant={isMobile ? 'minimal' : 'horizontal'}
              showLabels={!isMobile}
            />

            <Fade in timeout={600}>
              <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <TuneIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    Настройте ваши предпочтения
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    Расскажите нам, кого вы ищете, чтобы мы могли предложить лучшие совпадения
                  </Typography>
                </Box>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <form onSubmit={handleSubmit(onSubmit)}>
                  <Grid container spacing={4}>
                    {/* Цель знакомства */}
                    <Grid item xs={12}>
                      <Card sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Favorite sx={{ mr: 2, color: 'primary.main' }} />
                          <Typography variant="h6">
                            Цель знакомства
                          </Typography>
                        </Box>
                        
                        <Controller
                          name="relationshipGoals"
                          control={control}
                          render={({ field }) => (
                            <RadioGroup {...field} row={!isMobile}>
                              <Grid container spacing={2}>
                                {relationshipGoalsOptions.map((option) => (
                                  <Grid item xs={12} sm={6} md={3} key={option.value}>
                                    <Card 
                                      variant="outlined"
                                      sx={{
                                        cursor: 'pointer',
                                        border: field.value === option.value 
                                          ? `2px solid ${theme.palette.primary.main}`
                                          : `1px solid ${theme.palette.divider}`,
                                        '&:hover': {
                                          borderColor: theme.palette.primary.main
                                        }
                                      }}
                                      onClick={() => field.onChange(option.value)}
                                    >
                                      <CardContent sx={{ textAlign: 'center', p: 2 }}>
                                        <Typography variant="h4" sx={{ mb: 1 }}>
                                          {option.icon}
                                        </Typography>
                                        <Typography variant="subtitle2" gutterBottom>
                                          {option.label}
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                          {option.description}
                                        </Typography>
                                        <FormControlLabel
                                          value={option.value}
                                          control={<Radio />}
                                          label=""
                                          sx={{ display: 'none' }}
                                        />
                                      </CardContent>
                                    </Card>
                                  </Grid>
                                ))}
                              </Grid>
                            </RadioGroup>
                          )}
                        />
                        {errors.relationshipGoals && (
                          <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                            {errors.relationshipGoals.message}
                          </Typography>
                        )}
                      </Card>
                    </Grid>

                    {/* Предпочтения по полу */}
                    <Grid item xs={12} md={6}>
                      <Card sx={{ p: 3, height: '100%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Person sx={{ mr: 2, color: 'primary.main' }} />
                          <Typography variant="h6">
                            Кого ищете
                          </Typography>
                        </Box>
                        
                        <Controller
                          name="genderPreference"
                          control={control}
                          render={({ field }) => (
                            <Stack spacing={2}>
                              {genderOptions.map((option) => (
                                <Card
                                  key={option.value}
                                  variant="outlined"
                                  sx={{
                                    cursor: 'pointer',
                                    border: field.value === option.value 
                                      ? `2px solid ${theme.palette.primary.main}`
                                      : `1px solid ${theme.palette.divider}`,
                                    '&:hover': {
                                      borderColor: theme.palette.primary.main
                                    }
                                  }}
                                  onClick={() => field.onChange(option.value)}
                                >
                                  <CardContent sx={{ display: 'flex', alignItems: 'center', p: 2 }}>
                                    <Typography variant="h6" sx={{ mr: 2 }}>
                                      {option.icon}
                                    </Typography>
                                    <Typography variant="body1">
                                      {option.label}
                                    </Typography>
                                  </CardContent>
                                </Card>
                              ))}
                            </Stack>
                          )}
                        />
                      </Card>
                    </Grid>

                    {/* Возраст и расстояние */}
                    <Grid item xs={12} md={6}>
                      <Card sx={{ p: 3, height: '100%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <LocationOn sx={{ mr: 2, color: 'primary.main' }} />
                          <Typography variant="h6">
                            Параметры поиска
                          </Typography>
                        </Box>
                        
                        <Stack spacing={4}>
                          {/* Возрастной диапазон */}
                          <Box>
                            <Typography variant="body2" gutterBottom>
                              Возраст: {watchedValues.ageRange?.[0]} - {watchedValues.ageRange?.[1]} лет
                            </Typography>
                            <Controller
                              name="ageRange"
                              control={control}
                              render={({ field }) => (
                                <Slider
                                  {...field}
                                  valueLabelDisplay="auto"
                                  min={18}
                                  max={80}
                                  marks={[
                                    { value: 18, label: '18' },
                                    { value: 30, label: '30' },
                                    { value: 50, label: '50' },
                                    { value: 80, label: '80' }
                                  ]}
                                />
                              )}
                            />
                          </Box>

                          {/* Максимальное расстояние */}
                          <Box>
                            <Typography variant="body2" gutterBottom>
                              Максимальное расстояние: {watchedValues.maxDistance} км
                            </Typography>
                            <Controller
                              name="maxDistance"
                              control={control}
                              render={({ field }) => (
                                <Slider
                                  {...field}
                                  valueLabelDisplay="auto"
                                  min={1}
                                  max={100}
                                  marks={[
                                    { value: 1, label: '1км' },
                                    { value: 25, label: '25км' },
                                    { value: 50, label: '50км' },
                                    { value: 100, label: '100км' }
                                  ]}
                                />
                              )}
                            />
                          </Box>
                        </Stack>
                      </Card>
                    </Grid>

                    {/* Важные качества */}
                    <Grid item xs={12}>
                      <Card sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Важные качества (выберите до 5)
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                          Выбрано: {watchedValues.importantTraits?.length || 0} из 5
                        </Typography>
                        
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {importantTraitsOptions.map((trait) => (
                            <Chip
                              key={trait}
                              label={trait}
                              onClick={() => handleTraitToggle(trait)}
                              color={watchedValues.importantTraits?.includes(trait) ? "primary" : "default"}
                              variant={watchedValues.importantTraits?.includes(trait) ? "filled" : "outlined"}
                              disabled={(watchedValues.importantTraits?.length || 0) >= 5 && !watchedValues.importantTraits?.includes(trait)}
                              sx={{
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                  transform: 'scale(1.05)'
                                }
                              }}
                            />
                          ))}
                        </Box>
                      </Card>
                    </Grid>

                    {/* Стоп-факторы */}
                    <Grid item xs={12}>
                      <Card sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                          <Block sx={{ mr: 2, color: 'warning.main' }} />
                          <Typography variant="h6">
                            Стоп-факторы
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                          Отметьте то, что для вас неприемлемо
                        </Typography>
                        
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6}>
                            <Controller
                              name="dealBreakers.smoking"
                              control={control}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={<Switch {...field} checked={field.value} />}
                                  label="Курение"
                                />
                              )}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Controller
                              name="dealBreakers.drinking"
                              control={control}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={<Switch {...field} checked={field.value} />}
                                  label="Употребление алкоголя"
                                />
                              )}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Controller
                              name="dealBreakers.hasChildren"
                              control={control}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={<Switch {...field} checked={field.value} />}
                                  label="Наличие детей"
                                />
                              )}
                            />
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Controller
                              name="dealBreakers.wantsChildren"
                              control={control}
                              render={({ field }) => (
                                <FormControlLabel
                                  control={<Switch {...field} checked={field.value} />}
                                  label="Желание иметь детей"
                                />
                              )}
                            />
                          </Grid>
                        </Grid>
                      </Card>
                    </Grid>
                  </Grid>

                  {/* Navigation */}
                  <Stack 
                    direction="row" 
                    justifyContent="space-between" 
                    alignItems="center"
                    sx={{ mt: 4 }}
                  >
                    <Button
                      variant="outlined"
                      onClick={handleBack}
                      startIcon={<ArrowBack />}
                      disabled={onboardingLoading}
                    >
                      Назад
                    </Button>

                    <Button
                      type="submit"
                      variant="contained"
                      endIcon={<ArrowForward />}
                      disabled={!isValid || onboardingLoading}
                      sx={{
                        background: isValid 
                          ? `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                          : undefined
                      }}
                    >
                      {onboardingLoading ? <CircularProgress size={20} /> : 'Продолжить'}
                    </Button>
                  </Stack>
                </form>
              </Paper>
            </Fade>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PreferencesPage;
