# Fix AuthContext imports - improved version
$files = Get-ChildItem -Path "pages" -Filter "*.tsx" -Recurse

Write-Host "Checking all .tsx files for AuthContext imports..."

$fixedCount = 0
foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw

    if ($content -match "src/contexts/AuthContext") {
        Write-Host "Fixing: $($file.FullName)"

        # Replace all variations of AuthContext import
        $newContent = $content -replace "from '../../src/contexts/AuthContext'", "from '../../src/providers/AuthProvider'"
        $newContent = $newContent -replace "from '../../../src/contexts/AuthContext'", "from '../../../src/providers/AuthProvider'"

        Set-Content -Path $file.FullName -Value $newContent -NoNewline
        $fixedCount++
    }
}

Write-Host "Fixed $fixedCount files!"
