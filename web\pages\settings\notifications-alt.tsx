import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Switch,
  FormControlLabel,
  FormGroup,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  useTheme,
  useMediaQuery,
  Fade,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Slider,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import {
  ArrowBack,
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  PhoneAndroid as PhoneIcon,
  Schedule as ScheduleIcon,
  VolumeUp as VolumeIcon,
  Vibration as VibrationIcon,
  Info as InfoIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  Event as EventIcon,
  Star as StarIcon,
  Visibility as VisibilityIcon,
  Security as SecurityIcon,
  Campaign as CampaignIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSettings } from '../../src/contexts/SettingsContext';

const NotificationSettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    settings, 
    loading, 
    error, 
    updateNotificationSettings,
    loadSettings 
  } = useSettings();

  const [localSettings, setLocalSettings] = useState<any>(null);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [testNotification, setTestNotification] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    
    if (settings?.notifications) {
      setLocalSettings(settings.notifications);
    }
  }, [user, router, settings]);

  const handleSettingChange = (category: string, key: string, value: any) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [category]: {
        ...prev?.[category],
        [key]: value
      }
    }));
  };

  const handleMainToggle = (category: string, enabled: boolean) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [category]: {
        ...prev?.[category],
        enabled
      }
    }));
  };

  const handleSave = async () => {
    if (!localSettings) return;
    
    try {
      setSaving(true);
      await updateNotificationSettings({ notifications: localSettings });
      setSuccess('Настройки уведомлений обновлены');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (settings?.notifications) {
      setLocalSettings(settings.notifications);
    }
  };

  const handleTestNotification = async () => {
    setTestNotification(true);
    // Simulate test notification
    setTimeout(() => {
      setTestNotification(false);
      setSuccess('Тестовое уведомление отправлено');
    }, 1000);
  };

  const notificationCategories = [
    {
      id: 'push',
      title: 'Push-уведомления',
      description: 'Уведомления на экране устройства',
      icon: <PhoneIcon color="primary" />,
      settings: [
        {
          key: 'newMatches',
          title: 'Новые совпадения',
          description: 'Когда кто-то лайкнул вас взаимно',
          icon: <FavoriteIcon fontSize="small" />
        },
        {
          key: 'newMessages',
          title: 'Новые сообщения',
          description: 'Входящие сообщения в чатах',
          icon: <MessageIcon fontSize="small" />
        },
        {
          key: 'profileViews',
          title: 'Просмотры профиля',
          description: 'Когда кто-то посмотрел ваш профиль',
          icon: <VisibilityIcon fontSize="small" />
        },
        {
          key: 'likes',
          title: 'Лайки',
          description: 'Когда кто-то поставил вам лайк',
          icon: <FavoriteIcon fontSize="small" />
        },
        {
          key: 'superLikes',
          title: 'Супер-лайки',
          description: 'Когда получили супер-лайк',
          icon: <StarIcon fontSize="small" />
        },
        {
          key: 'meetingReminders',
          title: 'Напоминания о встречах',
          description: 'Уведомления о запланированных встречах',
          icon: <EventIcon fontSize="small" />
        },
        {
          key: 'eventReminders',
          title: 'События',
          description: 'Напоминания о событиях и мероприятиях',
          icon: <EventIcon fontSize="small" />
        },
        {
          key: 'securityAlerts',
          title: 'Безопасность',
          description: 'Важные уведомления о безопасности',
          icon: <SecurityIcon fontSize="small" />
        },
        {
          key: 'promotions',
          title: 'Акции и предложения',
          description: 'Специальные предложения и скидки',
          icon: <CampaignIcon fontSize="small" />
        }
      ]
    },
    {
      id: 'email',
      title: 'Email-уведомления',
      description: 'Уведомления на электронную почту',
      icon: <EmailIcon color="primary" />,
      settings: [
        {
          key: 'newMatches',
          title: 'Новые совпадения',
          description: 'Еженедельная сводка новых совпадений'
        },
        {
          key: 'newMessages',
          title: 'Новые сообщения',
          description: 'Уведомления о пропущенных сообщениях'
        },
        {
          key: 'profileViews',
          title: 'Просмотры профиля',
          description: 'Кто посмотрел ваш профиль'
        },
        {
          key: 'likes',
          title: 'Лайки',
          description: 'Сводка полученных лайков'
        },
        {
          key: 'superLikes',
          title: 'Супер-лайки',
          description: 'Уведомления о супер-лайках'
        },
        {
          key: 'promotions',
          title: 'Акции',
          description: 'Специальные предложения и новости'
        },
        {
          key: 'newsletter',
          title: 'Новостная рассылка',
          description: 'Советы по знакомствам и новости приложения'
        },
        {
          key: 'securityAlerts',
          title: 'Безопасность',
          description: 'Важные уведомления о безопасности аккаунта'
        },
        {
          key: 'accountUpdates',
          title: 'Обновления аккаунта',
          description: 'Изменения в настройках и подписке'
        }
      ]
    },
    {
      id: 'sms',
      title: 'SMS-уведомления',
      description: 'Текстовые сообщения на телефон',
      icon: <SmsIcon color="primary" />,
      settings: [
        {
          key: 'securityAlerts',
          title: 'Безопасность',
          description: 'Подозрительная активность в аккаунте'
        },
        {
          key: 'loginAlerts',
          title: 'Входы в аккаунт',
          description: 'Уведомления о входе с новых устройств'
        },
        {
          key: 'passwordChanges',
          title: 'Смена пароля',
          description: 'Подтверждение смены пароля'
        },
        {
          key: 'accountChanges',
          title: 'Изменения аккаунта',
          description: 'Важные изменения в настройках аккаунта'
        }
      ]
    },
    {
      id: 'inApp',
      title: 'Уведомления в приложении',
      description: 'Настройки отображения уведомлений',
      icon: <NotificationsActiveIcon color="primary" />,
      settings: [
        {
          key: 'sound',
          title: 'Звук',
          description: 'Воспроизводить звук уведомлений',
          icon: <VolumeIcon fontSize="small" />
        },
        {
          key: 'vibration',
          title: 'Вибрация',
          description: 'Вибрация при получении уведомлений',
          icon: <VibrationIcon fontSize="small" />
        },
        {
          key: 'badge',
          title: 'Значок на иконке',
          description: 'Показывать количество непрочитанных',
          icon: <NotificationsIcon fontSize="small" />
        },
        {
          key: 'preview',
          title: 'Предварительный просмотр',
          description: 'Показывать содержимое в уведомлениях',
          icon: <VisibilityIcon fontSize="small" />
        }
      ]
    }
  ];

  const frequencyOptions = [
    { value: 'immediate', label: 'Мгновенно' },
    { value: 'daily', label: 'Раз в день' },
    { value: 'weekly', label: 'Раз в неделю' },
    { value: 'never', label: 'Никогда' }
  ];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Настройки уведомлений - Likes & Love</title>
        <meta 
          name="description" 
          content="Настройки уведомлений в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <NotificationsIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Настройки уведомлений
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  onClick={handleTestNotification}
                  disabled={loading || saving || testNotification}
                >
                  {testNotification ? 'Отправка...' : 'Тест'}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleReset}
                  disabled={loading || saving}
                >
                  Сбросить
                </Button>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                  disabled={loading || saving || !localSettings}
                >
                  {saving ? 'Сохранение...' : 'Сохранить'}
                </Button>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка настроек уведомлений...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* Global Settings */}
                  <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Общие настройки
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Частота уведомлений</InputLabel>
                          <Select
                            value={localSettings?.frequency || 'immediate'}
                            onChange={(e) => setLocalSettings((prev: any) => ({ ...prev, frequency: e.target.value }))}
                            label="Частота уведомлений"
                            disabled={saving}
                          >
                            {frequencyOptions.map((option) => (
                              <MenuItem key={option.value} value={option.value}>
                                {option.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <Typography variant="body2" gutterBottom>
                          Тихие часы
                        </Typography>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={localSettings?.quietHours?.enabled || false}
                              onChange={(e) => handleSettingChange('quietHours', 'enabled', e.target.checked)}
                              disabled={saving}
                            />
                          }
                          label="Включить тихие часы"
                        />

                        {localSettings?.quietHours?.enabled && (
                          <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                            <TextField
                              label="Начало"
                              type="time"
                              value={localSettings?.quietHours?.startTime || '22:00'}
                              onChange={(e) => handleSettingChange('quietHours', 'startTime', e.target.value)}
                              disabled={saving}
                              InputLabelProps={{ shrink: true }}
                            />
                            <TextField
                              label="Конец"
                              type="time"
                              value={localSettings?.quietHours?.endTime || '08:00'}
                              onChange={(e) => handleSettingChange('quietHours', 'endTime', e.target.value)}
                              disabled={saving}
                              InputLabelProps={{ shrink: true }}
                            />
                          </Box>
                        )}
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Notification Categories */}
                  {notificationCategories.map((category, categoryIndex) => (
                    <Accordion key={category.id} defaultExpanded={categoryIndex === 0}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                          {category.icon}
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="h6">{category.title}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {category.description}
                            </Typography>
                          </Box>
                          <Switch
                            checked={localSettings?.[category.id]?.enabled || false}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleMainToggle(category.id, e.target.checked);
                            }}
                            disabled={saving}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <List>
                          {category.settings.map((setting, settingIndex) => (
                            <React.Fragment key={setting.key}>
                              <ListItem sx={{ px: 0, py: 1 }}>
                                <ListItemIcon>
                                  {setting.icon || <NotificationsIcon fontSize="small" />}
                                </ListItemIcon>
                                <ListItemText
                                  primary={setting.title}
                                  secondary={setting.description}
                                />
                                <ListItemSecondaryAction>
                                  <Switch
                                    checked={localSettings?.[category.id]?.[setting.key] || false}
                                    onChange={(e) => handleSettingChange(category.id, setting.key, e.target.checked)}
                                    disabled={saving || !localSettings?.[category.id]?.enabled}
                                  />
                                </ListItemSecondaryAction>
                              </ListItem>
                              {settingIndex < category.settings.length - 1 && <Divider />}
                            </React.Fragment>
                          ))}
                        </List>
                      </AccordionDetails>
                    </Accordion>
                  ))}

                  {/* Notification Preview */}
                  <Card sx={{ mt: 4, backgroundColor: 'background.default' }}>
                    <CardHeader
                      avatar={<InfoIcon color="primary" />}
                      title="Предварительный просмотр"
                      titleTypographyProps={{ variant: 'h6' }}
                    />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Так будут выглядеть ваши уведомления:
                      </Typography>

                      <Box sx={{
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 1,
                        p: 2,
                        mt: 2,
                        backgroundColor: 'background.paper'
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <NotificationsActiveIcon color="primary" />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Likes & Love
                            </Typography>
                            <Typography variant="body2">
                              У вас новое совпадение! 💕
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        * Внешний вид может отличаться в зависимости от устройства
                      </Typography>
                    </CardContent>
                  </Card>

                  {/* Tips */}
                  <Alert severity="info" sx={{ mt: 4 }}>
                    <Typography variant="body2">
                      <strong>Совет:</strong> Рекомендуем оставить включенными уведомления о безопасности
                      и новых сообщениях, чтобы не пропустить важную информацию.
                    </Typography>
                  </Alert>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default NotificationSettingsPage;
