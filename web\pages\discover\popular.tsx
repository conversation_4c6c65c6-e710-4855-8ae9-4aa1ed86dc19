import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Badge,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Refresh as RefreshIcon,
  Favorite as LikeIcon,
  Message as MessageIcon,
  Verified as VerifiedIcon,
  TrendingUp as TrendingIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Visibility as ViewIcon,
  FavoriteOutlined as LikesIcon,
  People as MatchesIcon,
  ArrowUpward as UpIcon,
  ArrowDownward as DownIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getPopularProfiles,
  swipeUser
} from '../../src/services/discoverService';
import { 
  PopularProfile,
  SwipeAction 
} from '../../src/types/discover.types';

const PopularProfilesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [popularProfiles, setPopularProfiles] = useState<PopularProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month'>('week');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadPopularProfiles();
  }, [user, router, timeframe]);

  const loadPopularProfiles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const profiles = await getPopularProfiles(timeframe);
      setPopularProfiles(profiles);
    } catch (err: any) {
      setError('Ошибка загрузки популярных профилей');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (userId: string) => {
    try {
      setActionLoading(userId);
      setError(null);

      const swipeAction: SwipeAction = {
        userId,
        action: 'like',
        timestamp: new Date().toISOString()
      };

      const result = await swipeUser(swipeAction);
      
      if (result.success) {
        if (result.isMatch) {
          setSuccess('Это совпадение! 🎉');
        } else {
          setSuccess('Лайк отправлен');
        }
        
        // Remove user from list
        setPopularProfiles(prev => prev.filter(p => p.user.id !== userId));
      } else {
        setError(result.error || 'Ошибка отправки лайка');
      }
    } catch (err: any) {
      setError('Ошибка отправки лайка');
    } finally {
      setActionLoading(null);
    }
  };

  const getTrendingIcon = (reason: PopularProfile['trendingReason']) => {
    switch (reason) {
      case 'most_liked':
        return <LikesIcon />;
      case 'most_viewed':
        return <ViewIcon />;
      case 'most_matched':
        return <MatchesIcon />;
      case 'rising_star':
        return <StarIcon />;
      default:
        return <TrendingIcon />;
    }
  };

  const getTrendingLabel = (reason: PopularProfile['trendingReason']) => {
    switch (reason) {
      case 'most_liked':
        return 'Самый популярный';
      case 'most_viewed':
        return 'Самый просматриваемый';
      case 'most_matched':
        return 'Больше всего совпадений';
      case 'rising_star':
        return 'Восходящая звезда';
      default:
        return 'Популярный';
    }
  };

  const getTrendingColor = (reason: PopularProfile['trendingReason']) => {
    switch (reason) {
      case 'most_liked':
        return 'error';
      case 'most_viewed':
        return 'primary';
      case 'most_matched':
        return 'success';
      case 'rising_star':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getRankColor = (rank: number) => {
    if (rank === 1) return 'gold';
    if (rank === 2) return 'silver';
    if (rank === 3) return '#CD7F32'; // bronze
    return theme.palette.text.secondary;
  };

  const getTimeframeLabel = (timeframe: string) => {
    switch (timeframe) {
      case 'day':
        return 'за день';
      case 'week':
        return 'за неделю';
      case 'month':
        return 'за месяц';
      default:
        return '';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Популярные профили - Likes & Love</title>
        <meta 
          name="description" 
          content="Самые популярные профили в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/discover/popular" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <TrendingIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'primary.main' }} />
                Популярные профили
              </Typography>
              <IconButton onClick={loadPopularProfiles} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Timeframe Filter */}
            <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Период</InputLabel>
                <Select
                  value={timeframe}
                  label="Период"
                  onChange={(e) => setTimeframe(e.target.value as any)}
                >
                  <MenuItem value="day">За день</MenuItem>
                  <MenuItem value="week">За неделю</MenuItem>
                  <MenuItem value="month">За месяц</MenuItem>
                </Select>
              </FormControl>
            </Paper>

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка популярных профилей...
                </Typography>
              </Box>
            ) : popularProfiles.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <TrendingIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Популярные профили не найдены
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Попробуйте изменить период или зайдите позже
                </Typography>
                <Button
                  variant="contained"
                  onClick={loadPopularProfiles}
                >
                  Обновить
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Топ {popularProfiles.length} популярных профилей {getTimeframeLabel(timeframe)}
                  </Typography>
                  
                  <Grid container spacing={3}>
                    {popularProfiles.map((profile) => (
                      <Grid item xs={12} sm={6} md={4} key={profile.user.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ position: 'relative' }}>
                            {/* Rank Badge */}
                            <Box sx={{
                              position: 'absolute',
                              top: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                label={`#${profile.rank}`}
                                size="small"
                                sx={{ 
                                  backgroundColor: 'rgba(0,0,0,0.7)', 
                                  color: getRankColor(profile.rank),
                                  fontWeight: 'bold'
                                }}
                              />
                            </Box>

                            {/* Trending Badge */}
                            <Box sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={getTrendingIcon(profile.trendingReason)}
                                label={getTrendingLabel(profile.trendingReason)}
                                size="small"
                                color={getTrendingColor(profile.trendingReason) as any}
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>

                            {/* Change Indicator */}
                            {profile.changeFromLastWeek !== 0 && (
                              <Box sx={{
                                position: 'absolute',
                                bottom: 8,
                                left: 8,
                                zIndex: 1
                              }}>
                                <Chip
                                  icon={profile.changeFromLastWeek > 0 ? <UpIcon /> : <DownIcon />}
                                  label={`${Math.abs(profile.changeFromLastWeek)}`}
                                  size="small"
                                  color={profile.changeFromLastWeek > 0 ? 'success' : 'error'}
                                  sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                                />
                              </Box>
                            )}

                            {/* Verification Badge */}
                            {profile.user.verificationStatus.phone && (
                              <Box sx={{
                                position: 'absolute',
                                bottom: 8,
                                right: 8,
                                zIndex: 1,
                                backgroundColor: 'primary.main',
                                borderRadius: '50%',
                                p: 0.5
                              }}>
                                <VerifiedIcon sx={{ color: 'white', fontSize: 16 }} />
                              </Box>
                            )}

                            <CardMedia
                              component="img"
                              height="250"
                              image={profile.user.photos[0]?.url || '/default-avatar.png'}
                              alt={profile.user.firstName}
                              sx={{ objectFit: 'cover' }}
                            />
                          </Box>

                          <CardContent sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" gutterBottom>
                              {profile.user.firstName}, {profile.user.age}
                            </Typography>

                            {profile.user.location && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {profile.user.location.city}
                                  {profile.user.location.distance && ` • ${profile.user.location.distance} км`}
                                </Typography>
                              </Box>
                            )}

                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary">
                                Рейтинг популярности: {profile.popularityScore.toFixed(1)}
                              </Typography>
                            </Box>

                            {profile.user.occupation && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {profile.user.occupation}
                                </Typography>
                              </Box>
                            )}

                            {profile.user.education && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {profile.user.education}
                                </Typography>
                              </Box>
                            )}

                            {profile.user.bio && (
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                {profile.user.bio.length > 100 
                                  ? `${profile.user.bio.substring(0, 100)}...`
                                  : profile.user.bio
                                }
                              </Typography>
                            )}

                            {profile.user.interests.length > 0 && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {profile.user.interests.slice(0, 3).map((interest, index) => (
                                  <Chip
                                    key={index}
                                    label={interest}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                                {profile.user.interests.length > 3 && (
                                  <Chip
                                    label={`+${profile.user.interests.length - 3}`}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            )}
                          </CardContent>

                          <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                            <Button
                              variant="outlined"
                              onClick={() => router.push(`/users/${profile.user.id}`)}
                              size="small"
                            >
                              Профиль
                            </Button>
                            
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                color="primary"
                                onClick={() => handleLike(profile.user.id)}
                                disabled={actionLoading === profile.user.id}
                                sx={{
                                  backgroundColor: 'error.light',
                                  color: 'error.dark',
                                  '&:hover': { backgroundColor: 'error.main', color: 'white' }
                                }}
                              >
                                {actionLoading === profile.user.id ? (
                                  <CircularProgress size={20} />
                                ) : (
                                  <LikeIcon />
                                )}
                              </IconButton>
                              
                              <IconButton
                                color="primary"
                                onClick={() => router.push(`/chat/new?userId=${profile.user.id}`)}
                              >
                                <MessageIcon />
                              </IconButton>
                            </Box>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default PopularProfilesPage;
