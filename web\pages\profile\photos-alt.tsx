import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardMedia,
  CardActions,
  IconButton,
  Button,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Fab,
  Badge,
  Tooltip,
  Snackbar
} from '@mui/material';
import {
  Delete,
  Star,
  StarBorder,
  Add,
  PhotoCamera,
  Visibility,
  Edit,
  CloudUpload,
  DragIndicator
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import axios from 'axios';

interface Photo {
  id: string;
  url: string;
  isMain: boolean;
  order: number;
  createdAt: string;
  metadata?: {
    width?: number;
    height?: number;
    size?: number;
  };
}

const PhotosPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [photoToDelete, setPhotoToDelete] = useState<Photo | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  useEffect(() => {
    loadPhotos();
  }, []);

  const loadPhotos = async () => {
    try {
      const response = await axios.get('/api/profile/photos');
      setPhotos(response.data.photos.sort((a: Photo, b: Photo) => a.order - b.order));
    } catch (err) {
      setError('Ошибка загрузки фотографий');
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const maxPhotos = 9;
    if (photos.length + files.length > maxPhotos) {
      setError(`Максимальное количество фотографий: ${maxPhotos}`);
      return;
    }

    setUploading(true);
    setError('');

    const formData = new FormData();
    Array.from(files).forEach(file => {
      formData.append('photos', file);
    });

    try {
      const response = await axios.post('/api/profile/photos', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      setPhotos([...photos, ...response.data.photos]);
      setSuccess('Фотографии успешно загружены');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка загрузки фотографий');
    } finally {
      setUploading(false);
    }
  };

  const handleSetMainPhoto = async (photo: Photo) => {
    try {
      await axios.put(`/api/profile/photos/${photo.id}/main`);
      
      setPhotos(photos.map(p => ({
        ...p,
        isMain: p.id === photo.id
      })));
      
      setSuccess('Главная фотография обновлена');
    } catch (err) {
      setError('Ошибка обновления главной фотографии');
    }
  };

  const handleDeletePhoto = async () => {
    if (!photoToDelete) return;

    try {
      await axios.delete(`/api/profile/photos/${photoToDelete.id}`);
      
      setPhotos(photos.filter(p => p.id !== photoToDelete.id));
      setDeleteDialogOpen(false);
      setPhotoToDelete(null);
      setSuccess('Фотография удалена');
    } catch (err) {
      setError('Ошибка удаления фотографии');
    }
  };

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;

    const items = Array.from(photos);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedPhotos = items.map((photo, index) => ({
      ...photo,
      order: index
    }));

    setPhotos(updatedPhotos);

    try {
      await axios.put('/api/profile/photos/reorder', {
        photoIds: updatedPhotos.map(p => p.id)
      });
    } catch (err) {
      setError('Ошибка изменения порядка фотографий');
      loadPhotos(); // Восстановить исходный порядок
    }
  };

  const openDeleteDialog = (photo: Photo) => {
    setPhotoToDelete(photo);
    setDeleteDialogOpen(true);
  };

  const openPreview = (photo: Photo) => {
    setSelectedPhoto(photo);
    setPreviewOpen(true);
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4">
                Мои фотографии
              </Typography>
              <Button
                variant="outlined"
                onClick={() => router.push('/profile/edit')}
              >
                Редактировать профиль
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
                {error}
              </Alert>
            )}

            <Snackbar
              open={!!success}
              autoHideDuration={3000}
              onClose={() => setSuccess('')}
              message={success}
            />

            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" color="text.secondary" paragraph>
                Загрузите до 9 фотографий. Первая фотография будет главной в вашем профиле.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Рекомендации: используйте качественные фото, где хорошо видно ваше лицо.
                Избегайте групповых фото на главной фотографии.
              </Typography>
            </Box>

            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="photos" direction="horizontal">
                {(provided) => (
                  <Grid
                    container
                    spacing={2}
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                  >
                    {photos.map((photo, index) => (
                      <Draggable key={photo.id} draggableId={photo.id} index={index}>
                        {(provided, snapshot) => (
                          <Grid
                            item
                            xs={12}
                            sm={6}
                            md={4}
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                          >
                            <Card
                              sx={{
                                position: 'relative',
                                opacity: snapshot.isDragging ? 0.5 : 1,
                                transform: snapshot.isDragging ? 'scale(1.05)' : 'scale(1)',
                                transition: 'transform 0.2s'
                              }}
                            >
                              {photo.isMain && (
                                <Badge
                                  badgeContent="Главная"
                                  color="primary"
                                  sx={{
                                    position: 'absolute',
                                    top: 10,
                                    left: 10,
                                    zIndex: 1
                                  }}
                                />
                              )}
                              
                              <Box
                                {...provided.dragHandleProps}
                                sx={{
                                  position: 'absolute',
                                  top: 10,
                                  right: 10,
                                  zIndex: 1,
                                  backgroundColor: 'rgba(0,0,0,0.5)',
                                  borderRadius: 1,
                                  p: 0.5,
                                  cursor: 'grab'
                                }}
                              >
                                <DragIndicator sx={{ color: 'white' }} />
                              </Box>

                              <CardMedia
                                component="img"
                                height="300"
                                image={photo.url}
                                alt={`Фото ${index + 1}`}
                                sx={{ cursor: 'pointer' }}
                                onClick={() => openPreview(photo)}
                              />
                              
                              <CardActions sx={{ justifyContent: 'space-between' }}>
                                <Tooltip title={photo.isMain ? 'Главная фотография' : 'Сделать главной'}>
                                  <IconButton
                                    onClick={() => handleSetMainPhoto(photo)}
                                    disabled={photo.isMain}
                                  >
                                    {photo.isMain ? <Star color="primary" /> : <StarBorder />}
                                  </IconButton>
                                </Tooltip>
                                
                                <Box>
                                  <Tooltip title="Просмотр">
                                    <IconButton onClick={() => openPreview(photo)}>
                                      <Visibility />
                                    </IconButton>
                                  </Tooltip>
                                  
                                  <Tooltip title="Удалить">
                                    <IconButton
                                      onClick={() => openDeleteDialog(photo)}
                                      disabled={photo.isMain && photos.length > 1}
                                    >
                                      <Delete />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </CardActions>
                            </Card>
                          </Grid>
                        )}
                      </Draggable>
                    ))}
                    
                    {provided.placeholder}
                    
                    {photos.length < 9 && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Card
                          sx={{
                            height: 356,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: '2px dashed',
                            borderColor: 'divider',
                            backgroundColor: 'background.default',
                            cursor: 'pointer',
                            '&:hover': {
                              borderColor: 'primary.main',
                              backgroundColor: 'action.hover'
                            }
                          }}
                          onClick={() => document.getElementById('photo-upload')?.click()}
                        >
                          <Box textAlign="center">
                            <CloudUpload sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                            <Typography variant="body1" color="text.secondary">
                              Загрузить фото
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              JPG, PNG до 10MB
                            </Typography>
                          </Box>
                        </Card>
                      </Grid>
                    )}
                  </Grid>
                )}
              </Droppable>
            </DragDropContext>

            <input
              id="photo-upload"
              type="file"
              multiple
              accept="image/*"
              style={{ display: 'none' }}
              onChange={handleFileSelect}
              disabled={uploading}
            />

            {uploading && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <CircularProgress size={24} />
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Загрузка фотографий...
                </Typography>
              </Box>
            )}
          </Paper>
        </Box>
      </Container>

      {/* Диалог удаления */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Удалить фотографию?</DialogTitle>
        <DialogContent>
          <Typography>
            Вы уверены, что хотите удалить эту фотографию? Это действие нельзя отменить.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Отмена
          </Button>
          <Button onClick={handleDeletePhoto} color="error" variant="contained">
            Удалить
          </Button>
        </DialogActions>
      </Dialog>

      {/* Превью фотографии */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogContent sx={{ p: 0 }}>
          {selectedPhoto && (
            <img
              src={selectedPhoto.url}
              alt="Preview"
              style={{ width: '100%', height: 'auto' }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>
            Закрыть
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default PhotosPage;
