import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  Badge,
  IconButton,
  Menu,
  MenuItem,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Send as SendIcon,
  Star as StarIcon,
  MoreVert as MoreVertIcon,
  Message as MessageIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getSentLikes, 
  getLikesStats 
} from '../../src/services/likesService';
import { Like, FilterOptions, LikesStats } from '../../src/types/likes.types';

const SentLikesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [likes, setLikes] = useState<Like[]>([]);
  const [stats, setStats] = useState<LikesStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedLike, setSelectedLike] = useState<Like | null>(null);

  // Pagination and filters
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState<'createdAt' | 'lastActiveAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [filterType, setFilterType] = useState<'all' | 'like' | 'super_like'>('all');
  const [filterMutual, setFilterMutual] = useState<'all' | 'mutual' | 'not_mutual'>('all');
  const [filterVerified, setFilterVerified] = useState<boolean | undefined>(undefined);

  const limit = 20;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadLikes();
    loadStats();
  }, [user, router, page, sortBy, sortOrder, filterType, filterMutual, filterVerified]);

  const loadLikes = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {
        page,
        limit,
        sortBy,
        sortOrder,
        verifiedOnly: filterVerified
      };

      if (filterType !== 'all') {
        filters.type = filterType as any;
      }

      if (filterMutual === 'mutual') {
        // Фильтр для взаимных лайков будет обрабатываться на сервере
        filters.isMutual = true;
      } else if (filterMutual === 'not_mutual') {
        filters.isMutual = false;
      }

      const response = await getSentLikes(filters);
      setLikes(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      setError('Ошибка загрузки отправленных лайков');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getLikesStats();
      setStats(statsData);
    } catch (err: any) {
      // Не показываем ошибку для статистики
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, like: Like) => {
    setAnchorEl(event.currentTarget);
    setSelectedLike(like);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedLike(null);
  };

  const handleViewProfile = (userId: string) => {
    router.push(`/users/${userId}`);
    handleMenuClose();
  };

  const handleSendMessage = (userId: string) => {
    router.push(`/chat?userId=${userId}`);
    handleMenuClose();
  };

  const formatTime = (createdAt: string) => {
    const now = new Date();
    const likeTime = new Date(createdAt);
    const diffInHours = Math.floor((now.getTime() - likeTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getStatusIcon = (like: Like) => {
    if (like.isMutual) {
      return <CheckCircleIcon color="success" />;
    }
    return <ScheduleIcon color="action" />;
  };

  const getStatusText = (like: Like) => {
    if (like.isMutual) {
      return 'Взаимный лайк';
    }
    return 'Ожидает ответа';
  };

  const getStatusColor = (like: Like) => {
    if (like.isMutual) {
      return 'success';
    }
    return 'default';
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Отправленные лайки - Likes & Love</title>
        <meta 
          name="description" 
          content="Посмотрите лайки, которые вы отправили в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/likes/sent" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <SendIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'primary.main' }} />
                    Отправленные лайки
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {stats ? `${stats.totalSent} всего, ${stats.todaySent} сегодня` : 'Загрузка статистики...'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Полученные
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => router.push('/likes/mutual')}
                    size={isMobile ? "small" : "medium"}
                  >
                    Взаимные
                  </Button>
                </Box>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {/* Stats cards */}
              {stats && (
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="primary.main">
                          {stats.totalSent}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Всего отправлено
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="warning.main">
                          {stats.superLikesSent}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Супер-лайков
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="success.main">
                          {stats.totalMutual}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Взаимных
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="h6" color="info.main">
                          {Math.round(stats.responseRate)}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Ответов
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              )}

              {/* Filters */}
              <Box sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Сортировка</InputLabel>
                      <Select
                        value={`${sortBy}-${sortOrder}`}
                        label="Сортировка"
                        onChange={(e) => {
                          const [newSortBy, newSortOrder] = e.target.value.split('-');
                          setSortBy(newSortBy as any);
                          setSortOrder(newSortOrder as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="createdAt-desc">Сначала новые</MenuItem>
                        <MenuItem value="createdAt-asc">Сначала старые</MenuItem>
                        <MenuItem value="lastActiveAt-desc">По активности</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Тип</InputLabel>
                      <Select
                        value={filterType}
                        label="Тип"
                        onChange={(e) => {
                          setFilterType(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="like">Лайки</MenuItem>
                        <MenuItem value="super_like">Супер-лайки</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Статус</InputLabel>
                      <Select
                        value={filterMutual}
                        label="Статус"
                        onChange={(e) => {
                          setFilterMutual(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="mutual">Взаимные</MenuItem>
                        <MenuItem value="not_mutual">Без ответа</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Верификация</InputLabel>
                      <Select
                        value={filterVerified === undefined ? 'all' : filterVerified ? 'verified' : 'unverified'}
                        label="Верификация"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFilterVerified(
                            value === 'all' ? undefined : value === 'verified'
                          );
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="verified">Верифицированные</MenuItem>
                        <MenuItem value="unverified">Неверифицированные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка отправленных лайков...
                  </Typography>
                </Box>
              ) : likes.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <SendIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Вы пока никому не отправляли лайки
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Начните знакомиться и отправляйте лайки понравившимся людям
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/discover')}
                  >
                    Начать знакомства
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Likes grid */}
                    <Grid container spacing={2}>
                      {likes.map((like) => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={like.id}>
                          <Card 
                            sx={{ 
                              '&:hover': {
                                boxShadow: theme.shadows[4]
                              }
                            }}
                          >
                            <CardContent sx={{ pb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Badge
                                  overlap="circular"
                                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                                  badgeContent={
                                    like.toUser?.isOnline ? (
                                      <Box
                                        sx={{
                                          width: 12,
                                          height: 12,
                                          borderRadius: '50%',
                                          backgroundColor: 'success.main',
                                          border: '2px solid white'
                                        }}
                                      />
                                    ) : null
                                  }
                                >
                                  <Avatar
                                    src={like.toUser?.avatarUrl}
                                    sx={{ width: 56, height: 56 }}
                                  >
                                    {like.toUser?.firstName[0]}
                                  </Avatar>
                                </Badge>
                                <Box sx={{ ml: 2, flexGrow: 1 }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Typography variant="subtitle2" noWrap>
                                      {like.toUser?.firstName}
                                    </Typography>
                                    {like.toUser?.verificationStatus.phone && (
                                      <VerifiedIcon 
                                        sx={{ 
                                          ml: 0.5, 
                                          fontSize: 16, 
                                          color: 'primary.main' 
                                        }} 
                                      />
                                    )}
                                  </Box>
                                  <Typography variant="caption" color="text.secondary">
                                    {like.toUser?.age} лет
                                  </Typography>
                                </Box>
                                <IconButton
                                  size="small"
                                  onClick={(e) => handleMenuOpen(e, like)}
                                >
                                  <MoreVertIcon />
                                </IconButton>
                              </Box>

                              <Box sx={{ mb: 1 }}>
                                <Chip
                                  icon={like.type === 'super_like' ? <StarIcon /> : <SendIcon />}
                                  label={like.type === 'super_like' ? 'Супер-лайк' : 'Лайк'}
                                  size="small"
                                  color={like.type === 'super_like' ? 'warning' : 'primary'}
                                  variant="outlined"
                                  sx={{ mr: 1 }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {formatTime(like.createdAt)}
                                </Typography>
                              </Box>

                              {like.message && (
                                <Typography variant="body2" sx={{ mb: 1, fontStyle: 'italic' }}>
                                  "{like.message}"
                                </Typography>
                              )}

                              <Chip
                                icon={getStatusIcon(like)}
                                label={getStatusText(like)}
                                size="small"
                                color={getStatusColor(like) as any}
                                sx={{ mb: 1 }}
                              />
                            </CardContent>

                            <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
                              {like.isMutual ? (
                                <Button
                                  size="small"
                                  variant="contained"
                                  onClick={() => handleSendMessage(like.toUserId)}
                                  startIcon={<MessageIcon />}
                                  fullWidth
                                >
                                  Написать
                                </Button>
                              ) : (
                                <Button
                                  size="small"
                                  onClick={() => handleViewProfile(like.toUserId)}
                                  fullWidth
                                >
                                  Посмотреть профиль
                                </Button>
                              )}
                            </CardActions>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Pagination
                          count={totalPages}
                          page={page}
                          onChange={(_, newPage) => setPage(newPage)}
                          color="primary"
                          size={isMobile ? "small" : "medium"}
                        />
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Context menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedLike && handleViewProfile(selectedLike.toUserId)}>
            <VisibilityIcon sx={{ mr: 1 }} />
            Посмотреть профиль
          </MenuItem>
          {selectedLike?.isMutual && (
            <MenuItem onClick={() => selectedLike && handleSendMessage(selectedLike.toUserId)}>
              <MessageIcon sx={{ mr: 1 }} />
              Написать сообщение
            </MenuItem>
          )}
        </Menu>
      </Layout>
    </>
  );
};

export default SentLikesPage;
