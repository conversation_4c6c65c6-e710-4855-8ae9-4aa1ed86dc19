import React from 'react';
import Head from 'next/head';
import { GetStaticProps } from 'next';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Stack,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Chat as ChatIcon,
  Star as StarIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../src/providers/AuthProvider';
import Layout from '../components/Layout/Layout';

const IndexPage: React.FC = () => {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const theme = useTheme();
  const { t } = useTranslation('common');

  // Если пользователь авторизован, перенаправляем на dashboard
  React.useEffect(() => {
    if (!isLoading && user) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          backgroundColor: theme.palette.background.default
        }}
      >
        <Typography variant="h6">Загрузка...</Typography>
      </Box>
    );
  }

  const features = [
    {
      icon: <FavoriteIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: '💘 Магия совместимости',
      description: '🧠 Наш ИИ анализирует 50+ параметров личности и находит твою идеальную пару! Химия гарантирована!'
    },
    {
      icon: <SecurityIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: '🛡️ Абсолютная безопасность',
      description: '🔒 Твои секреты в полной безопасности! Современное шифрование защищает каждое сообщение и фото.'
    },
    {
      icon: <VerifiedIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: '✅ Только реальные люди',
      description: '👤 Каждый профиль проверяется вручную! Никаких ботов - только настоящие люди, ищущие любовь.'
    },
    {
      icon: <ChatIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
      title: '💬 Страстное общение',
      description: '🔥 HD видеозвонки, голосовые сообщения, стикеры и подарки! Влюбляйтесь на расстоянии!'
    }
  ];

  const stats = [
    { number: '1M+', label: 'Активных пользователей' },
    { number: '50K+', label: 'Счастливых пар' },
    { number: '4.8', label: 'Рейтинг в App Store' },
    { number: '99%', label: 'Безопасность данных' },
  ];

  // Для неавторизованных пользователей показываем приветственную страницу
  return (
    <>
      <Head>
        <title>Likes Love - Знакомства для серьезных отношений | Найди свою любовь</title>
        <meta name="description" content="Likes Love - лучшее приложение для знакомств в России. Умный алгоритм подбора, безопасность, верификация профилей. Найди свою вторую половинку уже сегодня!" />
        <meta name="keywords" content="знакомства, любовь, отношения, свидания, пара, серьезные отношения, приложение знакомств, dating app" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="canonical" href="https://likes-love.com" />

        {/* Open Graph */}
        <meta property="og:title" content="Likes Love - Знакомства для серьезных отношений" />
        <meta property="og:description" content="Найди свою любовь с помощью умного алгоритма подбора. Безопасно, удобно, эффективно." />
        <meta property="og:image" content="https://likes-love.com/og-image.jpg" />
        <meta property="og:url" content="https://likes-love.com" />
        <meta property="og:type" content="website" />
        <meta property="og:site_name" content="Likes Love" />
        <meta property="og:locale" content="ru_RU" />

        {/* Twitter Cards */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Likes Love - Знакомства для серьезных отношений" />
        <meta name="twitter:description" content="Найди свою любовь с помощью умного алгоритма подбора" />
        <meta name="twitter:image" content="https://likes-love.com/twitter-image.jpg" />

        {/* Additional SEO */}
        <meta name="robots" content="index, follow" />
        <meta name="author" content="Likes Love Team" />
        <meta name="language" content="Russian" />
        <meta name="geo.region" content="RU" />
        <meta name="geo.country" content="Russia" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Likes Love",
              "description": "Приложение для знакомств и серьезных отношений",
              "url": "https://likes-love.com",
              "applicationCategory": "SocialNetworkingApplication",
              "operatingSystem": "Web, iOS, Android",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "RUB"
              },
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "15000"
              },
              "creator": {
                "@type": "Organization",
                "name": "Likes Love",
                "url": "https://likes-love.com"
              }
            })
          }}
        />
      </Head>

      <Layout title="Знакомства - Найди свою любовь">
        <Box sx={{ minHeight: '100vh', backgroundColor: theme.palette.background.default }}>
          {/* Hero Section */}
          <Container maxWidth="lg" sx={{ py: 8 }}>
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  <Typography
                    variant="h2"
                    sx={{
                      fontWeight: 'bold',
                      mb: 3,
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}
                  >
                    💕 {t('home.title')}
                  </Typography>
                  <Typography variant="h5" sx={{ mb: 4, color: theme.palette.text.secondary }}>
                    🔥 {t('home.subtitle')}
                  </Typography>
                  <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
                    <Chip icon={<StarIcon />} label={`4.8 ${t('home.rating')}`} color="primary" />
                    <Chip icon={<VerifiedIcon />} label={`100% ${t('home.safe')}`} color="success" />
                    <Chip icon={<FavoriteIcon />} label={`50K+ ${t('home.couples')}`} color="secondary" />
                  </Stack>
                  <Stack direction="row" spacing={2}>
                    <Button
                      variant="contained"
                      size="large"
                      sx={{ py: 2, px: 4, fontSize: '1.2rem' }}
                      onClick={() => router.push('/auth')}
                    >
                      💕 {t('home.find_love_button')}
                    </Button>
                    <Button
                      variant="outlined"
                      size="large"
                      sx={{ py: 2, px: 4 }}
                      onClick={() => router.push('/about')}
                    >
                      {t('home.learn_more')}
                    </Button>
                  </Stack>
                </motion.div>
              </Grid>
              <Grid item xs={12} md={6}>
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <Box sx={{ position: 'relative', textAlign: 'center' }}>
                    <img
                      src="https://picsum.photos/400/600?random=hero"
                      alt="Счастливая пара"
                      style={{
                        width: '100%',
                        maxWidth: 400,
                        borderRadius: 20,
                        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
                      }}
                    />
                  </Box>
                </motion.div>
              </Grid>
            </Grid>
          </Container>

          {/* Stats Section */}
          <Box sx={{ py: 6, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
            <Container maxWidth="lg">
              <Grid container spacing={4}>
                {stats.map((stat, index) => (
                  <Grid item xs={6} md={3} key={index}>
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
                        <Typography variant="h3" sx={{ fontWeight: 'bold', color: theme.palette.primary.main, mb: 1 }}>
                          {stat.number}
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          {stat.label}
                        </Typography>
                      </Card>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Container>
          </Box>

          {/* Features Section */}
          <Container maxWidth="lg" sx={{ py: 8 }}>
            <Typography variant="h3" align="center" sx={{ mb: 6, fontWeight: 'bold' }}>
              🌟 Почему миллионы влюбляются в Likes Love?
            </Typography>
            <Grid container spacing={4}>
              {features.map((feature, index) => (
                <Grid item xs={12} md={6} lg={3} key={index}>
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card sx={{
                      height: '100%',
                      textAlign: 'center',
                      p: 3,
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        transition: 'transform 0.3s ease'
                      }
                    }}>
                      <CardContent>
                        <Box sx={{ mb: 2 }}>
                          {feature.icon}
                        </Box>
                        <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                          {feature.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {feature.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </Container>

          {/* Navigation Section */}
          <Box sx={{ py: 6, backgroundColor: alpha(theme.palette.secondary.main, 0.05) }}>
            <Container maxWidth="lg">
              <Typography variant="h4" align="center" sx={{ mb: 4, fontWeight: 'bold' }}>
                Исследуйте наше приложение
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center', cursor: 'pointer' }} onClick={() => router.push('/about')}>
                    <PeopleIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mb: 2 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>О нас</Typography>
                    <Typography variant="body2" color="text.secondary">Узнайте нашу историю</Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center', cursor: 'pointer' }} onClick={() => router.push('/privacy')}>
                    <SecurityIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mb: 2 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>Конфиденциальность</Typography>
                    <Typography variant="body2" color="text.secondary">Защита ваших данных</Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center', cursor: 'pointer' }} onClick={() => router.push('/terms')}>
                    <VerifiedIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mb: 2 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>Условия</Typography>
                    <Typography variant="body2" color="text.secondary">Правила использования</Typography>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ p: 3, textAlign: 'center', cursor: 'pointer' }} onClick={() => router.push('/temp-test-functionality')}>
                    <ChatIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mb: 2 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>Тестирование</Typography>
                    <Typography variant="body2" color="text.secondary">Проверка функций</Typography>
                  </Card>
                </Grid>
              </Grid>
            </Container>
          </Box>

          {/* CTA Section */}
          <Box sx={{
            py: 8,
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            color: 'white'
          }}>
            <Container maxWidth="md" sx={{ textAlign: 'center' }}>
              <Typography variant="h3" sx={{ mb: 3, fontWeight: 'bold' }}>
                💖 Готовы встретить любовь всей жизни?
              </Typography>
              <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                🔥 Присоединяйтесь к 2 миллионам счастливых пользователей!
                ✨ Твоя идеальная пара уже ждет тебя в нашем приложении!
              </Typography>
              <Button
                variant="contained"
                size="large"
                sx={{
                  py: 2,
                  px: 6,
                  fontSize: '1.2rem',
                  backgroundColor: 'white',
                  color: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: alpha('#fff', 0.9)
                  }
                }}
                onClick={() => router.push('/auth')}
              >
                💕 Начать поиск любви БЕСПЛАТНО
              </Button>
            </Container>
          </Box>
        </Box>
      </Layout>
    </>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
    },
  };
};

export default IndexPage;
