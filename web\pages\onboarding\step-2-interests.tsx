import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Stack,
  Chip,
  Grid,
  TextField,
  Alert,
  Collapse,
  IconButton,
  useTheme,
  useMediaQuery,
  Fade,
  CircularProgress
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Add as AddIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import OnboardingProgress from '../../components/Onboarding/OnboardingProgress';
import { useAuth } from '../../src/providers/AuthProvider';
import { useOnboarding } from '../../src/contexts/OnboardingContext';
import { getInterestCategories } from '../../src/services/onboardingService';
import { InterestCategory, Interest } from '../../src/types/onboarding.types';

const InterestsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    data, 
    steps, 
    currentStep, 
    updateInterests, 
    nextStep, 
    previousStep,
    saveProgress,
    loading: onboardingLoading 
  } = useOnboarding();

  const [categories, setCategories] = useState<InterestCategory[]>([]);
  const [selectedInterests, setSelectedInterests] = useState<string[]>(data.interests.selectedInterests || []);
  const [customInterest, setCustomInterest] = useState('');
  const [customInterests, setCustomInterests] = useState<string[]>(data.interests.customInterests || []);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCustomInput, setShowCustomInput] = useState(false);

  const minInterests = 3;
  const maxInterests = 10;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInterestCategories();
  }, [user, router]);

  const loadInterestCategories = async () => {
    try {
      setLoading(true);
      const categoriesData = await getInterestCategories();
      setCategories(categoriesData);
    } catch (err: any) {
      setError('Ошибка загрузки категорий интересов');
    } finally {
      setLoading(false);
    }
  };

  const handleInterestToggle = (interestId: string) => {
    setSelectedInterests(prev => {
      if (prev.includes(interestId)) {
        return prev.filter(id => id !== interestId);
      } else if (prev.length < maxInterests) {
        return [...prev, interestId];
      }
      return prev;
    });
  };

  const handleAddCustomInterest = () => {
    if (customInterest.trim() && !customInterests.includes(customInterest.trim()) && customInterests.length < 3) {
      setCustomInterests(prev => [...prev, customInterest.trim()]);
      setCustomInterest('');
      setShowCustomInput(false);
    }
  };

  const handleRemoveCustomInterest = (interest: string) => {
    setCustomInterests(prev => prev.filter(item => item !== interest));
  };

  const handleNext = async () => {
    if (selectedInterests.length < minInterests) {
      setError(`Выберите минимум ${minInterests} интереса`);
      return;
    }

    try {
      setError(null);
      updateInterests({
        selectedInterests,
        customInterests
      });
      
      await saveProgress();
      nextStep();
    } catch (err: any) {
      setError('Ошибка сохранения интересов');
    }
  };

  const handleBack = () => {
    updateInterests({
      selectedInterests,
      customInterests
    });
    previousStep();
  };

  const filteredCategories = categories.map(category => ({
    ...category,
    interests: category.interests.filter(interest =>
      interest.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.interests.length > 0);

  const isInterestSelected = (interestId: string) => selectedInterests.includes(interestId);
  const canSelectMore = selectedInterests.length < maxInterests;
  const canProceed = selectedInterests.length >= minInterests;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Выберите интересы - Likes & Love</title>
        <meta 
          name="description" 
          content="Выберите ваши интересы и увлечения для лучшего подбора совместимых партнеров" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <OnboardingProgress
              steps={steps}
              currentStep={currentStep}
              variant={isMobile ? 'minimal' : 'horizontal'}
              showLabels={!isMobile}
            />

            <Fade in timeout={600}>
              <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <CategoryIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    Расскажите о ваших интересах
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                    Выберите то, что вам нравится. Это поможет найти людей с похожими увлечениями.
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Выберите от {minInterests} до {maxInterests} интересов
                  </Typography>
                </Box>

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                {/* Progress indicator */}
                <Box sx={{ mb: 3, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Выбрано: {selectedInterests.length} из {maxInterests}
                  </Typography>
                  <Box sx={{ 
                    width: '100%', 
                    height: 4, 
                    backgroundColor: theme.palette.grey[200], 
                    borderRadius: 2,
                    mt: 1,
                    overflow: 'hidden'
                  }}>
                    <Box sx={{
                      width: `${(selectedInterests.length / maxInterests) * 100}%`,
                      height: '100%',
                      backgroundColor: selectedInterests.length >= minInterests 
                        ? theme.palette.success.main 
                        : theme.palette.primary.main,
                      transition: 'all 0.3s ease'
                    }} />
                  </Box>
                </Box>

                {/* Search */}
                <TextField
                  fullWidth
                  placeholder="Поиск интересов..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                  sx={{ mb: 3 }}
                />

                {loading ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Box sx={{ mb: 4 }}>
                    {filteredCategories.map((category) => (
                      <Box key={category.id} sx={{ mb: 4 }}>
                        <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
                          {category.name}
                        </Typography>
                        <Grid container spacing={1}>
                          {category.interests.map((interest) => (
                            <Grid item key={interest.id}>
                              <Chip
                                label={interest.name}
                                onClick={() => handleInterestToggle(interest.id)}
                                color={isInterestSelected(interest.id) ? "primary" : "default"}
                                variant={isInterestSelected(interest.id) ? "filled" : "outlined"}
                                disabled={!canSelectMore && !isInterestSelected(interest.id)}
                                sx={{
                                  m: 0.5,
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    transform: 'scale(1.05)'
                                  }
                                }}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    ))}

                    {/* Custom interests */}
                    <Box sx={{ mt: 4 }}>
                      <Typography variant="h6" gutterBottom>
                        Свои интересы
                      </Typography>
                      
                      <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
                        {customInterests.map((interest, index) => (
                          <Chip
                            key={index}
                            label={interest}
                            onDelete={() => handleRemoveCustomInterest(interest)}
                            color="secondary"
                            deleteIcon={<CloseIcon />}
                          />
                        ))}
                        
                        {customInterests.length < 3 && (
                          <Chip
                            label="Добавить свой"
                            onClick={() => setShowCustomInput(true)}
                            icon={<AddIcon />}
                            variant="outlined"
                            color="primary"
                          />
                        )}
                      </Stack>

                      <Collapse in={showCustomInput}>
                        <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
                          <TextField
                            size="small"
                            placeholder="Введите ваш интерес"
                            value={customInterest}
                            onChange={(e) => setCustomInterest(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleAddCustomInterest()}
                            sx={{ flexGrow: 1 }}
                          />
                          <Button
                            variant="contained"
                            onClick={handleAddCustomInterest}
                            disabled={!customInterest.trim()}
                          >
                            Добавить
                          </Button>
                          <IconButton onClick={() => setShowCustomInput(false)}>
                            <CloseIcon />
                          </IconButton>
                        </Stack>
                      </Collapse>
                    </Box>
                  </Box>
                )}

                {/* Navigation */}
                <Stack 
                  direction="row" 
                  justifyContent="space-between" 
                  alignItems="center"
                  sx={{ mt: 4 }}
                >
                  <Button
                    variant="outlined"
                    onClick={handleBack}
                    startIcon={<ArrowBack />}
                    disabled={onboardingLoading}
                  >
                    Назад
                  </Button>

                  <Button
                    variant="contained"
                    onClick={handleNext}
                    endIcon={<ArrowForward />}
                    disabled={!canProceed || onboardingLoading}
                    sx={{
                      background: canProceed 
                        ? `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                        : undefined
                    }}
                  >
                    {onboardingLoading ? <CircularProgress size={20} /> : 'Продолжить'}
                  </Button>
                </Stack>
              </Paper>
            </Fade>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default InterestsPage;
