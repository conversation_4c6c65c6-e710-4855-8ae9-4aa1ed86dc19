import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Select,
  MenuItem,
  InputLabel,
  Alert,
  CircularProgress,
  Chip,
  Autocomplete,
  Switch,
  Slider,
  useTheme,
  useMediaQuery,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  ArrowBack,
  Add as AddIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ru } from 'date-fns/locale';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  createMeeting,
  getMeetingCategories,
  getPopularTags,
  searchVenues
} from '../../src/services/meetingsService';
import { 
  CreateMeetingRequest,
  MeetingLocation 
} from '../../src/types/meetings.types';

const schema = yup.object({
  title: yup.string().required('Название обязательно').min(3, 'Минимум 3 символа'),
  description: yup.string().max(500, 'Максимум 500 символов'),
  type: yup.string().required('Выберите тип встречи'),
  privacy: yup.string().required('Выберите приватность'),
  scheduledAt: yup.date().required('Выберите дату и время').min(new Date(), 'Дата не может быть в прошлом'),
  duration: yup.number().required('Укажите длительность').min(15, 'Минимум 15 минут').max(480, 'Максимум 8 часов'),
  category: yup.string().required('Выберите категорию'),
  location: yup.object({
    type: yup.string().required('Выберите тип локации'),
    name: yup.string().required('Укажите место встречи'),
    address: yup.string().when('type', {
      is: 'physical',
      then: (schema) => schema.required('Укажите адрес'),
      otherwise: (schema) => schema.notRequired()
    })
  }).required(),
  maxParticipants: yup.number().min(2, 'Минимум 2 участника').max(100, 'Максимум 100 участников'),
  tags: yup.array().of(yup.string()).max(10, 'Максимум 10 тегов')
});

const CreateMeetingPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);
  const [popularTags, setPopularTags] = useState<string[]>([]);
  const [venues, setVenues] = useState<any[]>([]);
  const [selectedPhotos, setSelectedPhotos] = useState<File[]>([]);

  const steps = ['Основная информация', 'Место и время', 'Участники и настройки'];

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid }
  } = useForm<CreateMeetingRequest>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      description: '',
      type: 'casual',
      privacy: 'public',
      scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      duration: 120,
      category: '',
      location: {
        type: 'physical',
        name: '',
        address: ''
      },
      maxParticipants: 10,
      tags: [],
      cost: {
        type: 'free',
        currency: 'RUB'
      },
      requirements: {
        verifiedOnly: false,
        premiumOnly: false
      },
      reminders: {
        enabled: true,
        times: [60, 15] // 1 hour and 15 minutes before
      }
    },
    mode: 'onChange'
  });

  const watchedLocationType = watch('location.type');
  const watchedCostType = watch('cost.type');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInitialData();
  }, [user, router]);

  const loadInitialData = async () => {
    try {
      const [categoriesData, tagsData] = await Promise.all([
        getMeetingCategories(),
        getPopularTags(20)
      ]);
      
      setCategories(categoriesData);
      setPopularTags(tagsData.map(t => t.tag));
    } catch (err: any) {
      setError('Ошибка загрузки данных');
    }
  };

  const handleVenueSearch = async (query: string) => {
    if (query.length < 3) return;
    
    try {
      const venuesData = await searchVenues(query);
      setVenues(venuesData);
    } catch (err: any) {
      console.error('Error searching venues:', err);
    }
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length + selectedPhotos.length > 5) {
      setError('Максимум 5 фотографий');
      return;
    }
    setSelectedPhotos(prev => [...prev, ...files]);
  };

  const removePhoto = (index: number) => {
    setSelectedPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const onSubmit = async (data: CreateMeetingRequest) => {
    try {
      setLoading(true);
      setError(null);

      const meetingData: CreateMeetingRequest = {
        ...data,
        photos: selectedPhotos
      };

      const meeting = await createMeeting(meetingData);
      setSuccess('Встреча создана успешно!');
      
      setTimeout(() => {
        router.push(`/meetings/${meeting.id}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка создания встречи');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null;
  }

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Название встречи"
                    placeholder="Например: Прогулка по парку"
                    error={!!errors.title}
                    helperText={errors.title?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={4}
                    label="Описание"
                    placeholder="Расскажите подробнее о встрече..."
                    error={!!errors.description}
                    helperText={errors.description?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.type}>
                <InputLabel>Тип встречи</InputLabel>
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="Тип встречи">
                      <MenuItem value="date">Свидание</MenuItem>
                      <MenuItem value="group_meeting">Групповая встреча</MenuItem>
                      <MenuItem value="activity">Активность</MenuItem>
                      <MenuItem value="casual">Неформальная</MenuItem>
                      <MenuItem value="business">Деловая</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.category}>
                <InputLabel>Категория</InputLabel>
                <Controller
                  name="category"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="Категория">
                      {categories.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Приватность</FormLabel>
                <Controller
                  name="privacy"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value="public" control={<Radio />} label="Публичная" />
                      <FormControlLabel value="friends" control={<Radio />} label="Для друзей" />
                      <FormControlLabel value="private" control={<Radio />} label="Приватная" />
                    </RadioGroup>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="tags"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    multiple
                    freeSolo
                    options={popularTags}
                    value={field.value || []}
                    onChange={(_, value) => field.onChange(value)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option}
                          {...getTagProps({ index })}
                          key={option}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Теги"
                        placeholder="Добавьте теги..."
                        error={!!errors.tags}
                        helperText={errors.tags?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Тип локации</FormLabel>
                <Controller
                  name="location.type"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value="physical" control={<Radio />} label="Физическое место" />
                      <FormControlLabel value="online" control={<Radio />} label="Онлайн" />
                      <FormControlLabel value="hybrid" control={<Radio />} label="Гибридная" />
                    </RadioGroup>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="location.name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Название места"
                    placeholder={watchedLocationType === 'online' ? 'Zoom, Google Meet и т.д.' : 'Название заведения или места'}
                    error={!!errors.location?.name}
                    helperText={errors.location?.name?.message}
                  />
                )}
              />
            </Grid>

            {watchedLocationType === 'physical' && (
              <Grid item xs={12}>
                <Controller
                  name="location.address"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Адрес"
                      placeholder="Улица, дом, город"
                      error={!!errors.location?.address}
                      helperText={errors.location?.address?.message}
                    />
                  )}
                />
              </Grid>
            )}

            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ru}>
                <Controller
                  name="scheduledAt"
                  control={control}
                  render={({ field }) => (
                    <DateTimePicker
                      {...field}
                      label="Дата и время"
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!errors.scheduledAt,
                          helperText: errors.scheduledAt?.message
                        }
                      }}
                    />
                  )}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="duration"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Длительность (минуты)"
                    inputProps={{ min: 15, max: 480, step: 15 }}
                    error={!!errors.duration}
                    helperText={errors.duration?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Controller
                name="maxParticipants"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label="Максимум участников"
                    inputProps={{ min: 2, max: 100 }}
                    error={!!errors.maxParticipants}
                    helperText={errors.maxParticipants?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Тип оплаты</InputLabel>
                <Controller
                  name="cost.type"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="Тип оплаты">
                      <MenuItem value="free">Бесплатно</MenuItem>
                      <MenuItem value="paid">Платно</MenuItem>
                      <MenuItem value="split">Разделить счет</MenuItem>
                      <MenuItem value="host_pays">Организатор платит</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            {watchedCostType === 'paid' && (
              <Grid item xs={12} sm={6}>
                <Controller
                  name="cost.amount"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      type="number"
                      label="Стоимость"
                      inputProps={{ min: 0 }}
                    />
                  )}
                />
              </Grid>
            )}

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Требования к участникам
              </Typography>
              
              <Controller
                name="requirements.verifiedOnly"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value} />}
                    label="Только проверенные пользователи"
                  />
                )}
              />
              
              <Controller
                name="requirements.premiumOnly"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value} />}
                    label="Только премиум пользователи"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Напоминания
              </Typography>
              
              <Controller
                name="reminders.enabled"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Switch {...field} checked={field.value} />}
                    label="Включить напоминания"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Фотографии встречи
              </Typography>
              
              <input
                accept="image/*"
                style={{ display: 'none' }}
                id="photo-upload"
                multiple
                type="file"
                onChange={handlePhotoUpload}
              />
              <label htmlFor="photo-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<ImageIcon />}
                  disabled={selectedPhotos.length >= 5}
                >
                  Добавить фото ({selectedPhotos.length}/5)
                </Button>
              </label>

              {selectedPhotos.length > 0 && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                  {selectedPhotos.map((photo, index) => (
                    <Box key={index} sx={{ position: 'relative' }}>
                      <img
                        src={URL.createObjectURL(photo)}
                        alt={`Photo ${index + 1}`}
                        style={{
                          width: 100,
                          height: 100,
                          objectFit: 'cover',
                          borderRadius: 8
                        }}
                      />
                      <Button
                        size="small"
                        onClick={() => removePhoto(index)}
                        sx={{
                          position: 'absolute',
                          top: -8,
                          right: -8,
                          minWidth: 24,
                          width: 24,
                          height: 24,
                          borderRadius: '50%'
                        }}
                      >
                        ×
                      </Button>
                    </Box>
                  ))}
                </Box>
              )}
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>Создать встречу - Likes & Love</title>
        <meta 
          name="description" 
          content="Создайте новую встречу в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                Создать встречу
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Paper elevation={3} sx={{ p: 4 }}>
              {/* Stepper */}
              <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Form */}
              <form onSubmit={handleSubmit(onSubmit)}>
                {renderStepContent(activeStep)}

                {/* Navigation Buttons */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                  <Button
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Назад
                  </Button>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {activeStep === steps.length - 1 ? (
                      <Button
                        type="submit"
                        variant="contained"
                        disabled={loading || !isValid}
                        startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}
                      >
                        {loading ? 'Создание...' : 'Создать встречу'}
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        onClick={handleNext}
                      >
                        Далее
                      </Button>
                    )}
                  </Box>
                </Box>
              </form>
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CreateMeetingPage;
