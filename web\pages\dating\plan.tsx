/**
 * Страница планирования свиданий (премиум функция)
 * Enterprise-grade решение с интеграцией торговых центров
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  FaHeart,
  FaMapMarkerAlt,
  FaClock,
  FaCalendarAlt,
  FaUtensils,
  FaCoffee,
  FaFilm,
  FaShoppingBag,
  FaCrown
} from 'react-icons/fa';
import Layout from '../../components/Layout/Layout';
import { usePremium } from '../../src/hooks/usePremium';

interface DateVenue {
  id: string;
  name: string;
  type: 'restaurant' | 'cafe' | 'cinema' | 'shopping' | 'activity';
  description: string;
  address: string;
  rating: number;
  priceRange: '$' | '$$' | '$$$';
  image: string;
  shoppingCenterId?: string;
  availableSlots: string[];
}

const DatePlanningPage: React.FC = () => {
  const router = useRouter();
  const { userId, userName } = router.query;
  const { isPremium, hasFeature } = usePremium();
  
  const [activeStep, setActiveStep] = useState(0);
  const [selectedVenue, setSelectedVenue] = useState<DateVenue | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [venues, setVenues] = useState<DateVenue[]>([]);
  const [confirmDialog, setConfirmDialog] = useState(false);

  // Проверка премиум доступа
  useEffect(() => {
    if (!isPremium || !hasFeature('dating_planning')) {
      router.push('/premium?feature=dating-planning');
      return;
    }
    
    loadVenues();
  }, [isPremium, hasFeature]);

  const loadVenues = async () => {
    setLoading(true);
    try {
      // TODO: Загрузка реальных заведений из API
      const mockVenues: DateVenue[] = [
        {
          id: 'venue1',
          name: 'Романтическое кафе "Амур"',
          type: 'cafe',
          description: 'Уютное кафе с романтической атмосферой',
          address: 'ТЦ "Европейский", 3 этаж',
          rating: 4.8,
          priceRange: '$$',
          image: '/images/venues/cafe-amur.jpg',
          shoppingCenterId: 'tc-evropeyskiy',
          availableSlots: ['18:00', '19:00', '20:00', '21:00']
        },
        {
          id: 'venue2',
          name: 'Ресторан "Первое свидание"',
          type: 'restaurant',
          description: 'Изысканная кухня для особых моментов',
          address: 'ТЦ "Афимолл", 4 этаж',
          rating: 4.9,
          priceRange: '$$$',
          image: '/images/venues/restaurant-date.jpg',
          shoppingCenterId: 'tc-afimoll',
          availableSlots: ['19:00', '20:00', '21:00']
        },
        {
          id: 'venue3',
          name: 'Кинотеатр "Романтика"',
          type: 'cinema',
          description: 'VIP-залы для двоих',
          address: 'ТЦ "Галерея", 5 этаж',
          rating: 4.7,
          priceRange: '$$',
          image: '/images/venues/cinema-romance.jpg',
          shoppingCenterId: 'tc-galereya',
          availableSlots: ['18:30', '20:45', '22:30']
        }
      ];
      
      setVenues(mockVenues);
    } catch (error) {
      console.error('Error loading venues:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleVenueSelect = (venue: DateVenue) => {
    setSelectedVenue(venue);
    handleNext();
  };

  const handleDateTimeSelect = (date: string, time: string) => {
    setSelectedDate(date);
    setSelectedTime(time);
    handleNext();
  };

  const handleSendInvitation = async () => {
    if (!selectedVenue || !selectedDate || !selectedTime) return;
    
    setLoading(true);
    try {
      // TODO: Отправка приглашения через API
      const invitation = {
        recipientId: userId,
        venueId: selectedVenue.id,
        venueName: selectedVenue.name,
        venueAddress: selectedVenue.address,
        date: selectedDate,
        time: selectedTime,
        message: message || `Приглашаю тебя на свидание в ${selectedVenue.name}! 💕`
      };

      await new Promise(resolve => setTimeout(resolve, 2000)); // Симуляция API

      setConfirmDialog(true);
    } catch (error) {
      console.error('Error sending invitation:', error);
    } finally {
      setLoading(false);
    }
  };

  const getVenueIcon = (type: string) => {
    switch (type) {
      case 'restaurant': return <FaUtensils />;
      case 'cafe': return <FaCoffee />;
      case 'cinema': return <FaFilm />;
      case 'shopping': return <FaShoppingBag />;
      default: return <FaHeart />;
    }
  };

  const steps = [
    'Выберите место',
    'Выберите дату и время',
    'Добавьте сообщение',
    'Отправьте приглашение'
  ];

  if (!isPremium) {
    return null; // Перенаправление уже произошло
  }

  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 3 }}>
        {/* Заголовок */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom sx={{ 
            background: 'linear-gradient(45deg, #FF6B6B, #FF8E8E)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 'bold'
          }}>
            Планирование свидания
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32 }}>
              {(userName as string)?.charAt(0) || 'П'}
            </Avatar>
            с {userName}
            <Chip icon={<FaCrown />} label="Premium" color="warning" size="small" />
          </Typography>
        </Box>

        {/* Stepper */}
        <Stepper activeStep={activeStep} orientation="vertical" sx={{ mb: 4 }}>
          {steps.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                {/* Шаг 1: Выбор места */}
                {index === 0 && (
                  <Box>
                    <Typography variant="body1" gutterBottom>
                      Выберите место для свидания в торговых центрах:
                    </Typography>
                    {loading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                        <CircularProgress />
                      </Box>
                    ) : (
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        {venues.map((venue) => (
                          <Grid item xs={12} md={6} key={venue.id}>
                            <Card 
                              sx={{ 
                                cursor: 'pointer',
                                transition: 'transform 0.2s',
                                '&:hover': { transform: 'scale(1.02)' }
                              }}
                              onClick={() => handleVenueSelect(venue)}
                            >
                              <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  {getVenueIcon(venue.type)}
                                  <Typography variant="h6" sx={{ ml: 1 }}>
                                    {venue.name}
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary" gutterBottom>
                                  {venue.description}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                                  <FaMapMarkerAlt size={12} />
                                  <Typography variant="caption">
                                    {venue.address}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                                  <Chip label={`⭐ ${venue.rating}`} size="small" />
                                  <Chip label={venue.priceRange} size="small" variant="outlined" />
                                </Box>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    )}
                  </Box>
                )}

                {/* Шаг 2: Выбор даты и времени */}
                {index === 1 && selectedVenue && (
                  <Box>
                    <Alert severity="info" sx={{ mb: 2 }}>
                      Место: {selectedVenue.name}
                    </Alert>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          type="date"
                          label="Дата свидания"
                          value={selectedDate}
                          onChange={(e) => setSelectedDate(e.target.value)}
                          InputLabelProps={{ shrink: true }}
                          inputProps={{ min: new Date().toISOString().split('T')[0] }}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Время</InputLabel>
                          <Select
                            value={selectedTime}
                            onChange={(e) => setSelectedTime(e.target.value)}
                            label="Время"
                          >
                            {selectedVenue.availableSlots.map((slot) => (
                              <MenuItem key={slot} value={slot}>
                                {slot}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>
                    {selectedDate && selectedTime && (
                      <Box sx={{ mt: 2 }}>
                        <Button
                          variant="contained"
                          onClick={() => handleDateTimeSelect(selectedDate, selectedTime)}
                          startIcon={<FaCalendarAlt />}
                        >
                          Подтвердить дату и время
                        </Button>
                      </Box>
                    )}
                  </Box>
                )}

                {/* Шаг 3: Сообщение */}
                {index === 2 && (
                  <Box>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Сообщение (необязательно)"
                      placeholder={`Приглашаю тебя на свидание в ${selectedVenue?.name}! 💕`}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      sx={{ mb: 2 }}
                    />
                    <Button
                      variant="contained"
                      onClick={handleNext}
                    >
                      Продолжить
                    </Button>
                  </Box>
                )}

                {/* Шаг 4: Подтверждение */}
                {index === 3 && (
                  <Box>
                    <Card sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          Детали свидания:
                        </Typography>
                        <Divider sx={{ my: 1 }} />
                        <Typography><strong>Место:</strong> {selectedVenue?.name}</Typography>
                        <Typography><strong>Адрес:</strong> {selectedVenue?.address}</Typography>
                        <Typography><strong>Дата:</strong> {selectedDate}</Typography>
                        <Typography><strong>Время:</strong> {selectedTime}</Typography>
                        {message && (
                          <Typography><strong>Сообщение:</strong> {message}</Typography>
                        )}
                      </CardContent>
                    </Card>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={handleSendInvitation}
                      disabled={loading}
                      startIcon={loading ? <CircularProgress size={16} /> : <FaHeart />}
                      sx={{
                        background: 'linear-gradient(45deg, #FF6B6B, #FF8E8E)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #FF5252, #FF7979)'
                        }
                      }}
                    >
                      {loading ? 'Отправляем приглашение...' : 'Отправить приглашение'}
                    </Button>
                  </Box>
                )}

                {index < 3 && index !== 1 && (
                  <Box sx={{ mb: 1, mt: 2 }}>
                    <Button
                      disabled={index === 0}
                      onClick={handleBack}
                      sx={{ mr: 1 }}
                    >
                      Назад
                    </Button>
                  </Box>
                )}
              </StepContent>
            </Step>
          ))}
        </Stepper>

        {/* Диалог подтверждения */}
        <Dialog open={confirmDialog} onClose={() => setConfirmDialog(false)}>
          <DialogTitle>🎉 Приглашение отправлено!</DialogTitle>
          <DialogContent>
            <Typography>
              Ваше приглашение на свидание отправлено {userName}. 
              Вы получите уведомление, когда они ответят.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => router.push('/matches')}>
              К совпадениям
            </Button>
            <Button onClick={() => router.push(`/chats/${userId}`)} variant="contained">
              Открыть чат
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default DatePlanningPage;
