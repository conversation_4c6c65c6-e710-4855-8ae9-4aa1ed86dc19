/**
 * Страница списка всех торговых центров
 * Редирект на основную страницу торговых центров
 */

import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout/Layout';
import { Container, Typography, CircularProgress, Box } from '@mui/material';

const ShoppingCenterIndexPage = () => {
  const router = useRouter();

  useEffect(() => {
    // Редирект на основную страницу торговых центров
    router.replace('/shopping-centers');
  }, [router]);

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <CircularProgress />
          <Typography variant="h6">
            Перенаправление на страницу торговых центров...
          </Typography>
        </Box>
      </Container>
    </Layout>
  );
};

export default ShoppingCenterIndexPage;
