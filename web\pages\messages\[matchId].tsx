import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Avatar,
  TextField,
  IconButton,
  Divider,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Stack,
  Chip,
  Badge,
  Fab,
  InputAdornment,
  List,
  ListItem,
  AppBar,
  Toolbar
} from '@mui/material';
import { 
  ArrowBack,
  Send,
  PhotoCamera,
  Videocam,
  Phone,
  Mic,
  MicOff,
  EmojiEmotions,
  AttachFile,
  MoreVert,
  Info,
  Block,
  Report,
  Archive,
  Delete,
  Star,
  StarBorder,
  AccessTime,
  Done,
  DoneAll,
  Add,
  Stop
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import axios from 'axios';
import { formatDistanceToNow, format } from 'date-fns';
import { ru } from 'date-fns/locale';

interface ChatMessage {
  id: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  isRead: boolean;
  messageType: 'text' | 'image' | 'video' | 'audio' | 'gif' | 'sticker';
  mediaUrl?: string;
  thumbnailUrl?: string;
  duration?: number; // для аудио/видео
  replyTo?: {
    id: string;
    text: string;
    senderName: string;
  };
}

interface OtherUser {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  isTyping: boolean;
  lastSeen?: Date;
  verificationStatus: 'none' | 'photo' | 'phone' | 'social' | 'gosuslugi';
  age: number;
  location: string;
}

interface MatchInfo {
  id: string;
  matchedAt: Date;
  isBlocked: boolean;
  blockedBy?: string;
  reportedBy?: string;
  isFavorite: boolean;
}

const ChatPage: React.FC = () => {
  const router = useRouter();
  const { matchId } = router.query;
  const { user } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [otherUser, setOtherUser] = useState<OtherUser | null>(null);
  const [matchInfo, setMatchInfo] = useState<MatchInfo | null>(null);
  
  const [messageText, setMessageText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [showUserInfo, setShowUserInfo] = useState(false);
  const [showMediaPicker, setShowMediaPicker] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (matchId && user) {
      loadChatData();
      markMessagesAsRead();
    }
  }, [matchId, user]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // WebSocket для real-time сообщений
    const ws = new WebSocket(`ws://localhost:3001/chat/${matchId}`);
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'new_message':
          setMessages(prev => [...prev, data.message]);
          break;
        case 'message_read':
          setMessages(prev => 
            prev.map(msg => 
              msg.id === data.messageId 
                ? { ...msg, isRead: true }
                : msg
            )
          );
          break;
        case 'user_typing':
          setOtherUser(prev => 
            prev ? { ...prev, isTyping: data.isTyping } : null
          );
          break;
        case 'user_online':
          setOtherUser(prev => 
            prev ? { ...prev, isOnline: data.isOnline } : null
          );
          break;
      }
    };

    return () => {
      ws.close();
    };
  }, [matchId]);

  const loadChatData = async () => {
    try {
      setLoading(true);
      
      const [messagesRes, userRes, matchRes] = await Promise.all([
        axios.get(`/api/messages/${matchId}/messages`),
        axios.get(`/api/messages/${matchId}/user`),
        axios.get(`/api/messages/${matchId}/match-info`)
      ]);

      setMessages(messagesRes.data);
      setOtherUser(userRes.data);
      setMatchInfo(matchRes.data);
      
    } catch (error) {
      console.error('Ошибка загрузки чата:', error);
    } finally {
      setLoading(false);
    }
  };

  const markMessagesAsRead = async () => {
    try {
      await axios.patch(`/api/messages/${matchId}/read`);
    } catch (error) {
      console.error('Ошибка отметки сообщений как прочитанных:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() || sendingMessage) return;

    const tempMessage: ChatMessage = {
      id: Date.now().toString(),
      text: messageText,
      senderId: user!.id,
      senderName: user!.profile?.displayName || 'Вы',
      timestamp: new Date(),
      isRead: false,
      messageType: 'text'
    };

    setMessages(prev => [...prev, tempMessage]);
    setMessageText('');
    setSendingMessage(true);

    try {
      const response = await axios.post(`/api/messages/${matchId}/send`, {
        text: messageText,
        messageType: 'text'
      });

      // Обновляем временное сообщение реальным ID
      setMessages(prev => 
        prev.map(msg => 
          msg.id === tempMessage.id 
            ? { ...msg, id: response.data.id }
            : msg
        )
      );
    } catch (error) {
      console.error('Ошибка отправки сообщения:', error);
      // Удаляем временное сообщение при ошибке
      setMessages(prev => prev.filter(msg => msg.id !== tempMessage.id));
    } finally {
      setSendingMessage(false);
    }
  };

  const handleTyping = (text: string) => {
    setMessageText(text);
    
    if (!isTyping && text.length > 0) {
      setIsTyping(true);
      // Отправляем статус набора через WebSocket
      // ws.send(JSON.stringify({ type: 'typing', isTyping: true }));
    }

    // Сбрасываем таймер
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      // ws.send(JSON.stringify({ type: 'typing', isTyping: false }));
    }, 1000);
  };

  const handleFileUpload = async (file: File, type: 'image' | 'video') => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('messageType', type);

    try {
      const response = await axios.post(`/api/messages/${matchId}/upload`, formData);
      // Сообщение добавится через WebSocket
    } catch (error) {
      console.error('Ошибка загрузки файла:', error);
    }
  };

  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      const audioChunks: Blob[] = [];
      
      mediaRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        const formData = new FormData();
        formData.append('audio', audioBlob, 'voice-message.wav');
        formData.append('messageType', 'audio');

        try {
          await axios.post(`/api/messages/${matchId}/upload`, formData);
        } catch (error) {
          console.error('Ошибка отправки голосового сообщения:', error);
        }

        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Ошибка записи голоса:', error);
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleBlockUser = async () => {
    try {
      await axios.post(`/api/users/${otherUser?.id}/block`);
      router.push('/messages');
    } catch (error) {
      console.error('Ошибка блокировки пользователя:', error);
    }
    handleMenuClose();
  };

  const handleReportUser = async () => {
    try {
      await axios.post(`/api/users/${otherUser?.id}/report`);
      // Показать уведомление об успешной отправке жалобы
    } catch (error) {
      console.error('Ошибка отправки жалобы:', error);
    }
    handleMenuClose();
  };

  const toggleFavorite = async () => {
    try {
      await axios.patch(`/api/messages/${matchId}/favorite`, {
        isFavorite: !matchInfo?.isFavorite
      });
      
      setMatchInfo(prev => 
        prev ? { ...prev, isFavorite: !prev.isFavorite } : null
      );
    } catch (error) {
      console.error('Ошибка добавления в избранное:', error);
    }
    handleMenuClose();
  };

  const getMessageTime = (timestamp: Date) => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    
    if (now.toDateString() === messageDate.toDateString()) {
      return format(messageDate, 'HH:mm');
    } else {
      return format(messageDate, 'dd.MM HH:mm');
    }
  };

  const getMessageIcon = (message: ChatMessage) => {
    if (message.senderId === user?.id) {
      return message.isRead ? 
        <DoneAll fontSize="small" sx={{ color: '#4fc3f7' }} /> : 
        <Done fontSize="small" sx={{ color: 'text.secondary' }} />;
    }
    return null;
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const isMyMessage = message.senderId === user?.id;
    const isLastInGroup = index === messages.length - 1 || 
      messages[index + 1]?.senderId !== message.senderId;

    return (
      <Box
        key={message.id}
        sx={{
          display: 'flex',
          justifyContent: isMyMessage ? 'flex-end' : 'flex-start',
          mb: isLastInGroup ? 2 : 0.5,
          mx: 2
        }}
      >
        <Paper
          elevation={1}
          sx={{
            p: 1.5,
            maxWidth: '70%',
            backgroundColor: isMyMessage ? 'primary.main' : 'grey.100',
            color: isMyMessage ? 'white' : 'text.primary',
            borderRadius: 2,
            borderTopRightRadius: isMyMessage ? 1 : 2,
            borderTopLeftRadius: isMyMessage ? 2 : 1
          }}
        >
          {message.replyTo && (
            <Box
              sx={{
                borderLeft: 3,
                borderColor: isMyMessage ? 'primary.light' : 'primary.main',
                pl: 1,
                mb: 1,
                opacity: 0.8
              }}
            >
              <Typography variant="caption" fontWeight="bold">
                {message.replyTo.senderName}
              </Typography>
              <Typography variant="caption" display="block">
                {message.replyTo.text}
              </Typography>
            </Box>
          )}

          {message.messageType === 'text' && (
            <Typography variant="body1">
              {message.text}
            </Typography>
          )}

          {message.messageType === 'image' && (
            <Box>
              <img
                src={message.mediaUrl}
                alt="Изображение"
                style={{
                  maxWidth: '100%',
                  borderRadius: 8,
                  display: 'block'
                }}
              />
              {message.text && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {message.text}
                </Typography>
              )}
            </Box>
          )}

          {message.messageType === 'video' && (
            <Box>
              <video
                src={message.mediaUrl}
                controls
                style={{
                  maxWidth: '100%',
                  borderRadius: 8,
                  display: 'block'
                }}
              />
              {message.text && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {message.text}
                </Typography>
              )}
            </Box>
          )}

          {message.messageType === 'audio' && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Mic fontSize="small" />
              <audio src={message.mediaUrl} controls style={{ width: '200px' }} />
              <Typography variant="caption">
                {message.duration ? `${message.duration}s` : ''}
              </Typography>
            </Box>
          )}

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mt: 0.5
            }}
          >
            <Typography variant="caption" sx={{ opacity: 0.8 }}>
              {getMessageTime(message.timestamp)}
            </Typography>
            {getMessageIcon(message)}
          </Box>
        </Paper>
      </Box>
    );
  };

  if (!user || loading) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <CircularProgress />
            <Typography variant="h6" sx={{ mt: 2 }}>
              Загрузка чата...
            </Typography>
          </Box>
        </Container>
      </Layout>
    );
  }

  if (!otherUser || !matchInfo) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="h6">Чат не найден</Typography>
            <Button
              variant="contained"
              onClick={() => router.push('/messages')}
              sx={{ mt: 2 }}
            >
              Вернуться к сообщениям
            </Button>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* Верхняя панель */}
        <AppBar position="static" color="default" elevation={1}>
          <Toolbar>
            <IconButton
              edge="start"
              onClick={() => router.push('/messages')}
              sx={{ mr: 1 }}
            >
              <ArrowBack />
            </IconButton>

            <Box
              sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                flex: 1,
                cursor: 'pointer'
              }}
              onClick={() => setShowUserInfo(true)}
            >
              <Badge
                variant="dot"
                sx={{
                  '& .MuiBadge-dot': {
                    backgroundColor: otherUser.isOnline ? '#44b700' : 'transparent'
                  }
                }}
              >
                <Avatar src={otherUser.avatar} alt={otherUser.name} />
              </Badge>

              <Box sx={{ ml: 2 }}>
                <Typography variant="h6">
                  {otherUser.name}
                  {otherUser.verificationStatus !== 'none' && (
                    <span style={{ marginLeft: 8, fontSize: 14 }}>
                      {otherUser.verificationStatus === 'gosuslugi' && '🇷🇺'}
                      {otherUser.verificationStatus === 'social' && '✅'}
                      {otherUser.verificationStatus === 'photo' && '📷'}
                      {otherUser.verificationStatus === 'phone' && '📱'}
                    </span>
                  )}
                </Typography>
                
                <Typography variant="body2" color="text.secondary">
                  {otherUser.isOnline ? 'в сети' : 
                   otherUser.isTyping ? 'печатает...' :
                   otherUser.lastSeen ? 
                     `был(а) ${formatDistanceToNow(new Date(otherUser.lastSeen), { addSuffix: true, locale: ru })}` :
                     'не в сети'
                  }
                </Typography>
              </Box>
            </Box>

            <Stack direction="row" spacing={1}>
              <IconButton onClick={() => router.push(`/calls/start?userId=${otherUser.id}&type=audio`)}>
                <Phone />
              </IconButton>
              
              <IconButton onClick={() => router.push(`/calls/start?userId=${otherUser.id}&type=video`)}>
                <Videocam />
              </IconButton>

              <IconButton onClick={toggleFavorite}>
                {matchInfo.isFavorite ? <Star color="warning" /> : <StarBorder />}
              </IconButton>

              <IconButton onClick={handleMenuOpen}>
                <MoreVert />
              </IconButton>
            </Stack>
          </Toolbar>
        </AppBar>

        {/* Область сообщений */}
        <Box sx={{ flex: 1, overflow: 'auto', backgroundColor: '#f5f5f5' }}>
          {messages.length === 0 ? (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <Typography variant="h6" gutterBottom>
                Начните общение!
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Вы совпали {formatDistanceToNow(new Date(matchInfo.matchedAt), { addSuffix: true, locale: ru })}
              </Typography>
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {messages.map((message, index) => renderMessage(message, index))}
            </List>
          )}
          <div ref={messagesEndRef} />
        </Box>

        {/* Поле ввода */}
        <Paper elevation={3} sx={{ p: 1 }}>
          <Stack direction="row" spacing={1} alignItems="flex-end">
            <IconButton onClick={() => setShowMediaPicker(true)}>
              <Add />
            </IconButton>

            <TextField
              fullWidth
              multiline
              maxRows={4}
              placeholder="Напишите сообщение..."
              value={messageText}
              onChange={(e) => handleTyping(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              variant="outlined"
              size="small"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 3
                }
              }}
            />

            {messageText.trim() ? (
              <IconButton 
                color="primary" 
                onClick={handleSendMessage}
                disabled={sendingMessage}
              >
                <Send />
              </IconButton>
            ) : (
              <IconButton
                color="primary"
                onMouseDown={startVoiceRecording}
                onMouseUp={stopVoiceRecording}
                onMouseLeave={stopVoiceRecording}
                sx={{
                  backgroundColor: isRecording ? 'error.main' : 'transparent',
                  color: isRecording ? 'white' : 'primary.main'
                }}
              >
                {isRecording ? <Stop /> : <Mic />}
              </IconButton>
            )}
          </Stack>
        </Paper>

        {/* Меню действий */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => router.push(`/messages/${matchId}/info`)}>
            <Info fontSize="small" sx={{ mr: 1 }} />
            Информация о чате
          </MenuItem>
          
          <MenuItem onClick={handleReportUser}>
            <Report fontSize="small" sx={{ mr: 1 }} />
            Пожаловаться
          </MenuItem>
          
          <Divider />
          
          <MenuItem onClick={handleBlockUser} sx={{ color: 'error.main' }}>
            <Block fontSize="small" sx={{ mr: 1 }} />
            Заблокировать
          </MenuItem>
        </Menu>

        {/* Скрытый input для файлов */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*,video/*"
          style={{ display: 'none' }}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              const type = file.type.startsWith('image/') ? 'image' : 'video';
              handleFileUpload(file, type);
            }
          }}
        />

        {/* Dialog для выбора медиа */}
        <Dialog open={showMediaPicker} onClose={() => setShowMediaPicker(false)}>
          <DialogTitle>Отправить</DialogTitle>
          <DialogContent>
            <Stack spacing={2}>
              <Button
                fullWidth
                startIcon={<PhotoCamera />}
                onClick={() => {
                  fileInputRef.current?.click();
                  setShowMediaPicker(false);
                }}
              >
                Фото или видео
              </Button>
              
              <Button
                fullWidth
                startIcon={<EmojiEmotions />}
                onClick={() => {
                  // Открыть picker стикеров/эмодзи
                  setShowMediaPicker(false);
                }}
              >
                Стикер
              </Button>
            </Stack>
          </DialogContent>
        </Dialog>
      </Box>
    </Layout>
  );
};

export default ChatPage;
