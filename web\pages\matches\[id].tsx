import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Divider,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  Send as SendIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon,
  LocationOn as LocationIcon,
  Cake as CakeIcon,
  Work as WorkIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getMatchDetails,
  sendMessage 
} from '../../src/services/likesService';
import { MatchDetailsResponse, SendMessageRequest } from '../../src/types/likes.types';

const MatchDetailsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { id } = router.query;

  const [matchDetails, setMatchDetails] = useState<MatchDetailsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    if (id && typeof id === 'string') {
      loadMatchDetails(id);
    }
  }, [user, router, id]);

  const loadMatchDetails = async (matchId: string) => {
    try {
      setLoading(true);
      setError(null);
      const details = await getMatchDetails(matchId);
      setMatchDetails(details);
    } catch (err: any) {
      setError('Ошибка загрузки деталей совпадения');
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() || !matchDetails || sending) return;

    try {
      setSending(true);
      setError(null);

      const request: SendMessageRequest = {
        conversationId: matchDetails.conversation.id,
        text: messageText.trim(),
        type: 'text'
      };

      const result = await sendMessage(request);
      
      if (result.success && result.message) {
        setSuccess('Сообщение отправлено');
        setMessageText('');
        
        // Добавляем сообщение в локальное состояние
        setMatchDetails(prev => {
          if (!prev) return prev;
          return {
            ...prev,
            conversation: {
              ...prev.conversation,
              messages: [...prev.conversation.messages, result.message!]
            }
          };
        });
      } else {
        setError(result.error || 'Ошибка отправки сообщения');
      }
    } catch (err: any) {
      setError('Ошибка отправки сообщения');
    } finally {
      setSending(false);
    }
  };

  const getOtherUser = () => {
    if (!matchDetails || !user) return null;
    return matchDetails.match.user1Id === user.id 
      ? matchDetails.match.user2 
      : matchDetails.match.user1;
  };

  const formatTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка совпадения - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 }, textAlign: 'center' }}>
              <CircularProgress size={60} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Загрузка деталей совпадения...
              </Typography>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (error && !matchDetails) {
    return (
      <>
        <Head>
          <title>Ошибка загрузки - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Alert severity="error">
                {error}
              </Alert>
              <Button
                variant="contained"
                onClick={() => router.back()}
                sx={{ mt: 2 }}
              >
                Назад
              </Button>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  const otherUser = getOtherUser();
  if (!matchDetails || !otherUser) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{`Совпадение с ${otherUser.firstName} - Likes & Love`}</title>
        <meta 
          name="description" 
          content={`Детали совпадения с ${otherUser.firstName} в приложении знакомств Likes & Love`} 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                Совпадение с {otherUser.firstName}
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Grid container spacing={3}>
              {/* User Profile Card */}
              <Grid item xs={12} md={4}>
                <Fade in timeout={600}>
                  <Card>
                    <CardContent>
                      <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <Avatar
                          src={otherUser.avatarUrl}
                          sx={{ 
                            width: 120, 
                            height: 120, 
                            mx: 'auto', 
                            mb: 2,
                            border: `4px solid ${theme.palette.primary.main}`
                          }}
                        >
                          {otherUser.firstName[0]}
                        </Avatar>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                          <Typography variant="h5">
                            {otherUser.firstName}
                          </Typography>
                          {otherUser.verificationStatus.phone && (
                            <VerifiedIcon 
                              sx={{ 
                                ml: 1, 
                                fontSize: 20, 
                                color: 'primary.main' 
                              }} 
                            />
                          )}
                        </Box>
                        
                        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                          {otherUser.age} лет
                        </Typography>

                        {otherUser.isOnline ? (
                          <Chip
                            label="Онлайн"
                            color="success"
                            size="small"
                          />
                        ) : (
                          <Typography variant="caption" color="text.secondary">
                            <ScheduleIcon sx={{ fontSize: 12, mr: 0.5, verticalAlign: 'middle' }} />
                            {formatTime(otherUser.lastActiveAt)}
                          </Typography>
                        )}
                      </Box>

                      <Divider sx={{ mb: 3 }} />

                      {/* Match Info */}
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Информация о совпадении
                        </Typography>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            Совпадение: {formatTime(matchDetails.match.matchedAt)}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <FavoriteIcon color="error" sx={{ mr: 1 }} />
                          <Typography variant="body2">
                            Совместимость: {matchDetails.match.compatibilityScore}%
                          </Typography>
                          <Chip
                            label={matchDetails.match.compatibilityScore >= 80 ? 'Высокая' : 
                                   matchDetails.match.compatibilityScore >= 60 ? 'Средняя' : 'Низкая'}
                            size="small"
                            color={getCompatibilityColor(matchDetails.match.compatibilityScore) as any}
                            sx={{ ml: 1 }}
                          />
                        </Box>

                        {matchDetails.match.commonInterests.length > 0 && (
                          <Box>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              Общие интересы:
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {matchDetails.match.commonInterests.map((interest, index) => (
                                <Chip
                                  key={index}
                                  label={interest}
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                />
                              ))}
                            </Box>
                          </Box>
                        )}
                      </Box>

                      <Divider sx={{ mb: 3 }} />

                      {/* User Details */}
                      <Box>
                        <Typography variant="h6" gutterBottom>
                          О пользователе
                        </Typography>

                        {otherUser.bio && (
                          <Typography variant="body2" sx={{ mb: 2 }}>
                            {otherUser.bio}
                          </Typography>
                        )}

                        {otherUser.location && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {otherUser.location.city}
                              {otherUser.location.distance && ` • ${otherUser.location.distance} км`}
                            </Typography>
                          </Box>
                        )}

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <CakeIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {otherUser.age} лет
                          </Typography>
                        </Box>

                        {otherUser.interests.length > 0 && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                              Интересы:
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {otherUser.interests.map((interest, index) => (
                                <Chip
                                  key={index}
                                  label={interest}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </Box>
                        )}
                      </Box>

                      <Box sx={{ mt: 3 }}>
                        <Button
                          fullWidth
                          variant="outlined"
                          onClick={() => router.push(`/users/${otherUser.id}`)}
                        >
                          Посмотреть полный профиль
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Fade>
              </Grid>

              {/* Chat Section */}
              <Grid item xs={12} md={8}>
                <Fade in timeout={800}>
                  <Paper elevation={3} sx={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
                    {/* Chat Header */}
                    <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                      <Typography variant="h6">
                        Чат с {otherUser.firstName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {matchDetails.conversation.messages.length > 0 
                          ? `${matchDetails.conversation.messages.length} сообщений`
                          : 'Начните разговор'
                        }
                      </Typography>
                    </Box>

                    {/* Messages */}
                    <Box sx={{ flexGrow: 1, p: 2, overflowY: 'auto' }}>
                      {matchDetails.conversation.messages.length === 0 ? (
                        <Box sx={{ textAlign: 'center', py: 4 }}>
                          <MessageIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                          <Typography variant="h6" gutterBottom>
                            Начните разговор!
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                            Напишите первое сообщение, чтобы познакомиться
                          </Typography>
                          
                          {/* Ice breakers */}
                          {matchDetails.suggestions.iceBreakers.length > 0 && (
                            <Box>
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                Предложения для начала разговора:
                              </Typography>
                              {matchDetails.suggestions.iceBreakers.slice(0, 3).map((iceBreaker, index) => (
                                <Button
                                  key={index}
                                  variant="outlined"
                                  size="small"
                                  onClick={() => setMessageText(iceBreaker)}
                                  sx={{ m: 0.5, textTransform: 'none' }}
                                >
                                  "{iceBreaker}"
                                </Button>
                              ))}
                            </Box>
                          )}
                        </Box>
                      ) : (
                        <Box>
                          {matchDetails.conversation.messages.map((message) => (
                            <Box
                              key={message.id}
                              sx={{
                                display: 'flex',
                                justifyContent: message.senderId === user.id ? 'flex-end' : 'flex-start',
                                mb: 2
                              }}
                            >
                              <Box
                                sx={{
                                  maxWidth: '70%',
                                  p: 2,
                                  borderRadius: 2,
                                  backgroundColor: message.senderId === user.id 
                                    ? theme.palette.primary.main 
                                    : theme.palette.grey[200],
                                  color: message.senderId === user.id ? 'white' : 'text.primary'
                                }}
                              >
                                <Typography variant="body2">
                                  {message.text}
                                </Typography>
                                <Typography 
                                  variant="caption" 
                                  sx={{ 
                                    opacity: 0.7,
                                    display: 'block',
                                    mt: 0.5
                                  }}
                                >
                                  {formatTime(message.sentAt)}
                                </Typography>
                              </Box>
                            </Box>
                          ))}
                        </Box>
                      )}
                    </Box>

                    {/* Message Input */}
                    {matchDetails.conversation.canSendMessage ? (
                      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                        <TextField
                          fullWidth
                          placeholder="Напишите сообщение..."
                          value={messageText}
                          onChange={(e) => setMessageText(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSendMessage();
                            }
                          }}
                          multiline
                          maxRows={3}
                          InputProps={{
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton
                                  onClick={handleSendMessage}
                                  disabled={!messageText.trim() || sending}
                                  color="primary"
                                >
                                  {sending ? <CircularProgress size={24} /> : <SendIcon />}
                                </IconButton>
                              </InputAdornment>
                            )
                          }}
                        />
                      </Box>
                    ) : (
                      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', textAlign: 'center' }}>
                        <Alert severity="warning">
                          Вы не можете отправлять сообщения этому пользователю
                        </Alert>
                      </Box>
                    )}
                  </Paper>
                </Fade>
              </Grid>
            </Grid>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default MatchDetailsPage;
