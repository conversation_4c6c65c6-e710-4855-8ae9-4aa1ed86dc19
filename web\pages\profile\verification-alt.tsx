import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Alert,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Stack,
  LinearProgress,
  Divider
} from '@mui/material';
import { 
  CheckCircle, 
  Cancel, 
  Schedule, 
  PhotoCamera, 
  Email, 
  Phone,
  AccountBalance,
  Instagram,
  Verified,
  Security,
  Upload,
  VideoCall
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import axios from 'axios';

interface VerificationMethod {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: 'not_started' | 'pending' | 'completed' | 'failed';
  isAvailable: boolean;
  difficulty: 'easy' | 'medium' | 'hard';
  trustScore: number;
  estimatedTime: string;
  benefits: string[];
}

const ProfileVerificationPage: React.FC = () => {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [verificationMethods, setVerificationMethods] = useState<VerificationMethod[]>([]);

  useEffect(() => {
    initializeVerificationMethods();
  }, [user]);

  const initializeVerificationMethods = () => {
    const methods: VerificationMethod[] = [
      {
        id: 'photo',
        title: 'Верификация фото',
        description: 'Загрузите фото с документом для подтверждения личности',
        icon: <PhotoCamera />,
        status: user?.profile?.verificationStatus?.photo ? 'completed' : 'not_started',
        isAvailable: true,
        difficulty: 'easy',
        trustScore: 30,
        estimatedTime: '5-10 минут',
        benefits: ['Подтверждение личности', 'Больше доверия к профилю', 'Приоритет в поиске']
      },
      {
        id: 'email',
        title: 'Подтверждение email',
        description: 'Подтвердите ваш email адрес',
        icon: <Email />,
        status: user?.emailVerified ? 'completed' : 'not_started',
        isAvailable: true,
        difficulty: 'easy',
        trustScore: 10,
        estimatedTime: '1-2 минуты',
        benefits: ['Восстановление аккаунта', 'Уведомления на почту']
      },
      {
        id: 'phone',
        title: 'Подтверждение телефона',
        description: 'Подтвердите ваш номер телефона через SMS',
        icon: <Phone />,
        status: user?.phoneVerified ? 'completed' : 'not_started',
        isAvailable: true,
        difficulty: 'easy',
        trustScore: 15,
        estimatedTime: '2-3 минуты',
        benefits: ['Двухфакторная аутентификация', 'SMS уведомления']
      },
      {
        id: 'gosuslugi',
        title: 'Верификация через Госуслуги',
        description: 'Официальное подтверждение личности через портал Госуслуг',
        icon: <AccountBalance />,
        status: 'not_started', // Проверить в профиле
        isAvailable: true,
        difficulty: 'medium',
        trustScore: 50,
        estimatedTime: '5-15 минут',
        benefits: ['Полное подтверждение личности', 'Максимальное доверие', 'VIP статус']
      },
      {
        id: 'social_instagram',
        title: 'Связка с Instagram',
        description: 'Подтвердите аккаунт через связку с Instagram',
        icon: <Instagram />,
        status: user?.profile?.socialLinks?.instagram ? 'completed' : 'not_started',
        isAvailable: true,
        difficulty: 'easy',
        trustScore: 20,
        estimatedTime: '3-5 минут',
        benefits: ['Дополнительные фото', 'Подтверждение активности', 'Социальная валидация']
      },
      {
        id: 'video_call',
        title: 'Видео-верификация',
        description: 'Пройдите видео-звонок с модератором для подтверждения личности',
        icon: <VideoCall />,
        status: 'not_started',
        isAvailable: false, // Доступно только для Premium
        difficulty: 'hard',
        trustScore: 40,
        estimatedTime: '10-20 минут',
        benefits: ['100% подтверждение личности', 'Защита от ботов', 'Премиум статус']
      }
    ];

    setVerificationMethods(methods);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'pending':
        return <Schedule color="warning" />;
      case 'failed':
        return <Cancel color="error" />;
      default:
        return null;
    }
  };

  const getTotalTrustScore = () => {
    return verificationMethods
      .filter(method => method.status === 'completed')
      .reduce((total, method) => total + method.trustScore, 0);
  };

  const getVerificationLevel = () => {
    const score = getTotalTrustScore();
    if (score >= 100) return { level: 'Максимальный', color: 'success' as const };
    if (score >= 70) return { level: 'Высокий', color: 'primary' as const };
    if (score >= 40) return { level: 'Средний', color: 'warning' as const };
    return { level: 'Базовый', color: 'default' as const };
  };

  const handleVerificationStart = async (methodId: string) => {
    switch (methodId) {
      case 'photo':
        handlePhotoVerification();
        break;
      case 'email':
        router.push('/auth/verify-email?resend=true');
        break;
      case 'phone':
        router.push('/auth/verify-phone');
        break;
      case 'gosuslugi':
        window.location.href = '/api/auth/gosuslugi';
        break;
      case 'social_instagram':
        window.location.href = '/api/auth/instagram/verify';
        break;
      case 'video_call':
        if (!user?.premium?.isActive) {
          router.push('/subscription?feature=video_verification');
        } else {
          scheduleVideoCall();
        }
        break;
    }
  };

  const handlePhotoVerification = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = true;
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        uploadVerificationPhotos(Array.from(files));
      }
    };
    input.click();
  };

  const uploadVerificationPhotos = async (files: File[]) => {
    if (files.length < 2) {
      alert('Загрузите как минимум 2 фото: с документом и селфи');
      return;
    }

    try {
      setUploadingPhoto(true);
      
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`photo_${index}`, file);
      });

      const response = await axios.post('/api/verification/upload-photos', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (response.data.success) {
        // Обновляем статус верификации
        setVerificationMethods(prev => 
          prev.map(method => 
            method.id === 'photo' 
              ? { ...method, status: 'pending' as const }
              : method
          )
        );
        
        alert('Фотографии загружены и отправлены на модерацию. Результат придет в течение 24 часов.');
      }
    } catch (error: any) {
      alert(error.response?.data?.message || 'Ошибка загрузки фотографий');
    } finally {
      setUploadingPhoto(false);
    }
  };

  const scheduleVideoCall = async () => {
    try {
      const response = await axios.post('/api/verification/schedule-video-call');
      if (response.data.success) {
        router.push('/calls/schedule');
      }
    } catch (error: any) {
      alert(error.response?.data?.message || 'Ошибка планирования видео-звонка');
    }
  };

  const verificationLevel = getVerificationLevel();

  return (
    <Layout>
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Верификация профиля
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            Подтвердите свою личность, чтобы повысить доверие к вашему профилю 
            и получить доступ к дополнительным функциям.
          </Typography>

          {/* Прогресс верификации */}
          <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Уровень доверия к профилю
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{ flex: 1, mr: 2 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={(getTotalTrustScore() / 165) * 100}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Typography variant="body2" sx={{ minWidth: 80 }}>
                {getTotalTrustScore()}/165 баллов
              </Typography>
            </Box>

            <Stack direction="row" spacing={2} alignItems="center">
              <Chip 
                label={verificationLevel.level}
                color={verificationLevel.color}
                icon={<Verified />}
              />
              <Typography variant="body2" color="text.secondary">
                Завершено {verificationMethods.filter(m => m.status === 'completed').length} из {verificationMethods.length} верификаций
              </Typography>
            </Stack>
          </Paper>

          {/* Методы верификации */}
          <Grid container spacing={3}>
            {verificationMethods.map((method) => (
              <Grid item xs={12} md={6} key={method.id}>
                <Card 
                  elevation={2}
                  sx={{ 
                    height: '100%',
                    opacity: method.isAvailable ? 1 : 0.7,
                    borderLeft: method.status === 'completed' ? 4 : 0,
                    borderColor: 'success.main'
                  }}
                >
                  <CardContent>
                    <Stack direction="row" spacing={2} alignItems="flex-start" mb={2}>
                      <Box sx={{ color: 'primary.main' }}>
                        {method.icon}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                          <Typography variant="h6">
                            {method.title}
                          </Typography>
                          {getStatusIcon(method.status)}
                        </Stack>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {method.description}
                        </Typography>
                      </Box>
                    </Stack>

                    <Stack direction="row" spacing={1} mb={2}>
                      <Chip 
                        label={`+${method.trustScore} баллов`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                      <Chip 
                        label={method.estimatedTime}
                        size="small"
                        variant="outlined"
                      />
                      <Chip 
                        label={method.difficulty === 'easy' ? 'Легко' : method.difficulty === 'medium' ? 'Средне' : 'Сложно'}
                        size="small"
                        color={method.difficulty === 'easy' ? 'success' : method.difficulty === 'medium' ? 'warning' : 'error'}
                        variant="outlined"
                      />
                    </Stack>

                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      <strong>Преимущества:</strong>
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 2 }}>
                      {method.benefits.map((benefit, index) => (
                        <Typography key={index} component="li" variant="body2" color="text.secondary">
                          {benefit}
                        </Typography>
                      ))}
                    </Box>
                  </CardContent>

                  <CardActions sx={{ justifyContent: 'flex-end', p: 2 }}>
                    {method.status === 'completed' ? (
                      <Chip 
                        label="Завершено"
                        color="success"
                        icon={<CheckCircle />}
                      />
                    ) : method.status === 'pending' ? (
                      <Chip 
                        label="На модерации"
                        color="warning"
                        icon={<Schedule />}
                      />
                    ) : (
                      <Button
                        variant={method.difficulty === 'hard' ? 'outlined' : 'contained'}
                        onClick={() => handleVerificationStart(method.id)}
                        disabled={!method.isAvailable || uploadingPhoto}
                        startIcon={uploadingPhoto && method.id === 'photo' ? <Upload /> : method.icon}
                      >
                        {uploadingPhoto && method.id === 'photo' 
                          ? 'Загрузка...' 
                          : !method.isAvailable 
                            ? 'Premium' 
                            : 'Начать'
                        }
                      </Button>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Информация о преимуществах */}
          <Paper elevation={1} sx={{ p: 3, mt: 4, bgcolor: 'background.default' }}>
            <Typography variant="h6" gutterBottom>
              Зачем верифицировать профиль?
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" gutterBottom>
                  🛡️ Безопасность
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Защита от мошенников и фейковых аккаунтов
                </Typography>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" gutterBottom>
                  ⭐ Больше совпадений
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Верифицированные профили показываются в приоритете
                </Typography>
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" gutterBottom>
                  🎯 Дополнительные функции
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Доступ к премиум возможностям и эксклюзивным событиям
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default ProfileVerificationPage;
