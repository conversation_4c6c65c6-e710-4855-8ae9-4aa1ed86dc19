"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/groups",{

/***/ "(pages-dir-browser)/./src/services/chatService.ts":
/*!*************************************!*\
  !*** ./src/services/chatService.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   acceptCall: () => (/* binding */ acceptCall),\n/* harmony export */   addGroupMembers: () => (/* binding */ addGroupMembers),\n/* harmony export */   addMessageReaction: () => (/* binding */ addMessageReaction),\n/* harmony export */   archiveConversation: () => (/* binding */ archiveConversation),\n/* harmony export */   blockUser: () => (/* binding */ blockUser),\n/* harmony export */   cancelScheduledMessage: () => (/* binding */ cancelScheduledMessage),\n/* harmony export */   connectToChat: () => (/* binding */ connectToChat),\n/* harmony export */   createConversation: () => (/* binding */ createConversation),\n/* harmony export */   createGroupChat: () => (/* binding */ createGroupChat),\n/* harmony export */   declineCall: () => (/* binding */ declineCall),\n/* harmony export */   deleteConversation: () => (/* binding */ deleteConversation),\n/* harmony export */   deleteMessage: () => (/* binding */ deleteMessage),\n/* harmony export */   disconnectFromChat: () => (/* binding */ disconnectFromChat),\n/* harmony export */   editMessage: () => (/* binding */ editMessage),\n/* harmony export */   endCall: () => (/* binding */ endCall),\n/* harmony export */   forwardMessage: () => (/* binding */ forwardMessage),\n/* harmony export */   getBlockedUsers: () => (/* binding */ getBlockedUsers),\n/* harmony export */   getCallHistory: () => (/* binding */ getCallHistory),\n/* harmony export */   getCallSession: () => (/* binding */ getCallSession),\n/* harmony export */   getChatSettings: () => (/* binding */ getChatSettings),\n/* harmony export */   getChatStatistics: () => (/* binding */ getChatStatistics),\n/* harmony export */   getConversation: () => (/* binding */ getConversation),\n/* harmony export */   getConversations: () => (/* binding */ getConversations),\n/* harmony export */   getGroupInfo: () => (/* binding */ getGroupInfo),\n/* harmony export */   getGroupMembers: () => (/* binding */ getGroupMembers),\n/* harmony export */   getGroups: () => (/* binding */ getGroups),\n/* harmony export */   getMessages: () => (/* binding */ getMessages),\n/* harmony export */   getOwnedStickerPacks: () => (/* binding */ getOwnedStickerPacks),\n/* harmony export */   getScheduledMessages: () => (/* binding */ getScheduledMessages),\n/* harmony export */   getStickerPacks: () => (/* binding */ getStickerPacks),\n/* harmony export */   initiateCall: () => (/* binding */ initiateCall),\n/* harmony export */   leaveGroup: () => (/* binding */ leaveGroup),\n/* harmony export */   markMessagesAsRead: () => (/* binding */ markMessagesAsRead),\n/* harmony export */   muteConversation: () => (/* binding */ muteConversation),\n/* harmony export */   pinConversation: () => (/* binding */ pinConversation),\n/* harmony export */   purchaseStickerPack: () => (/* binding */ purchaseStickerPack),\n/* harmony export */   removeGroupMember: () => (/* binding */ removeGroupMember),\n/* harmony export */   removeMessageReaction: () => (/* binding */ removeMessageReaction),\n/* harmony export */   reportConversation: () => (/* binding */ reportConversation),\n/* harmony export */   reportMessage: () => (/* binding */ reportMessage),\n/* harmony export */   scheduleMessage: () => (/* binding */ scheduleMessage),\n/* harmony export */   searchConversations: () => (/* binding */ searchConversations),\n/* harmony export */   searchMessages: () => (/* binding */ searchMessages),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage),\n/* harmony export */   setTyping: () => (/* binding */ setTyping),\n/* harmony export */   transcribeVoiceMessage: () => (/* binding */ transcribeVoiceMessage),\n/* harmony export */   unarchiveConversation: () => (/* binding */ unarchiveConversation),\n/* harmony export */   unblockUser: () => (/* binding */ unblockUser),\n/* harmony export */   unmuteConversation: () => (/* binding */ unmuteConversation),\n/* harmony export */   unpinConversation: () => (/* binding */ unpinConversation),\n/* harmony export */   updateChatSettings: () => (/* binding */ updateChatSettings),\n/* harmony export */   updateGroupInfo: () => (/* binding */ updateGroupInfo),\n/* harmony export */   updateMemberRole: () => (/* binding */ updateMemberRole),\n/* harmony export */   uploadChatFile: () => (/* binding */ uploadChatFile)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(pages-dir-browser)/./node_modules/axios/index.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config */ \"(pages-dir-browser)/./src/config/index.ts\");\n\n\nconst chatApi = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"\".concat(_config__WEBPACK_IMPORTED_MODULE_0__.config.api.baseUrl, \"/chat\"),\n    withCredentials: true\n});\n// Conversations API\nconst getConversations = async (filter)=>{\n    const params = new URLSearchParams();\n    if (filter) {\n        Object.entries(filter).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined && value !== null) {\n                params.append(key, value.toString());\n            }\n        });\n    }\n    const { data } = await chatApi.get(\"/conversations?\".concat(params.toString()));\n    return data;\n};\nconst getConversation = async (conversationId)=>{\n    const { data } = await chatApi.get(\"/conversations/\".concat(conversationId));\n    return data.conversation;\n};\nconst createConversation = async (request)=>{\n    try {\n        const { data } = await chatApi.post('/conversations', request);\n        return {\n            success: true,\n            conversation: data.conversation\n        };\n    } catch (error) {\n        var _error_response_data, _error_response;\n        return {\n            success: false,\n            error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ошибка создания беседы'\n        };\n    }\n};\nconst archiveConversation = async (conversationId)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/archive\"));\n};\nconst unarchiveConversation = async (conversationId)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/unarchive\"));\n};\nconst muteConversation = async (conversationId, duration)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/mute\"), {\n        duration\n    });\n};\nconst unmuteConversation = async (conversationId)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/unmute\"));\n};\nconst pinConversation = async (conversationId)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/pin\"));\n};\nconst unpinConversation = async (conversationId)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/unpin\"));\n};\nconst deleteConversation = async (conversationId)=>{\n    await chatApi.delete(\"/conversations/\".concat(conversationId));\n};\n// Messages API\nconst getMessages = async function(conversationId) {\n    let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 50;\n    const { data } = await chatApi.get(\"/conversations/\".concat(conversationId, \"/messages?page=\").concat(page, \"&limit=\").concat(limit));\n    return data;\n};\nconst sendMessage = async (request)=>{\n    try {\n        let requestData = {\n            conversationId: request.conversationId,\n            text: request.text,\n            type: request.type,\n            replyToMessageId: request.replyToMessageId,\n            metadata: request.metadata\n        };\n        if (request.attachments && request.attachments.length > 0) {\n            const formData = new FormData();\n            Object.entries(requestData).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    if (typeof value === 'object') {\n                        formData.append(key, JSON.stringify(value));\n                    } else {\n                        formData.append(key, value.toString());\n                    }\n                }\n            });\n            request.attachments.forEach((file, index)=>{\n                formData.append(\"attachment_\".concat(index), file);\n            });\n            const { data } = await chatApi.post('/messages', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return {\n                success: true,\n                message: data.message\n            };\n        } else {\n            const { data } = await chatApi.post('/messages', requestData);\n            return {\n                success: true,\n                message: data.message\n            };\n        }\n    } catch (error) {\n        var _error_response_data, _error_response;\n        return {\n            success: false,\n            error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ошибка отправки сообщения'\n        };\n    }\n};\nconst markMessagesAsRead = async (conversationId, messageIds)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/mark-read\"), {\n        messageIds\n    });\n};\nconst deleteMessage = async (messageId)=>{\n    await chatApi.delete(\"/messages/\".concat(messageId));\n};\nconst editMessage = async (messageId, newText)=>{\n    await chatApi.put(\"/messages/\".concat(messageId), {\n        text: newText\n    });\n};\nconst addMessageReaction = async (messageId, emoji)=>{\n    await chatApi.post(\"/messages/\".concat(messageId, \"/reactions\"), {\n        emoji\n    });\n};\nconst removeMessageReaction = async (messageId, reactionId)=>{\n    await chatApi.delete(\"/messages/\".concat(messageId, \"/reactions/\").concat(reactionId));\n};\nconst forwardMessage = async (messageId, conversationIds)=>{\n    await chatApi.post(\"/messages/\".concat(messageId, \"/forward\"), {\n        conversationIds\n    });\n};\n// Calls API\nconst initiateCall = async (request)=>{\n    try {\n        const { data } = await chatApi.post('/calls/initiate', request);\n        return {\n            success: true,\n            session: data.session\n        };\n    } catch (error) {\n        var _error_response_data, _error_response;\n        return {\n            success: false,\n            error: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Ошибка инициации звонка'\n        };\n    }\n};\nconst acceptCall = async (sessionId)=>{\n    await chatApi.post(\"/calls/\".concat(sessionId, \"/accept\"));\n};\nconst declineCall = async (sessionId)=>{\n    await chatApi.post(\"/calls/\".concat(sessionId, \"/decline\"));\n};\nconst endCall = async (sessionId)=>{\n    await chatApi.post(\"/calls/\".concat(sessionId, \"/end\"));\n};\nconst getCallSession = async (sessionId)=>{\n    const { data } = await chatApi.get(\"/calls/\".concat(sessionId));\n    return data.session;\n};\nconst getCallHistory = async (conversationId)=>{\n    const params = conversationId ? \"?conversationId=\".concat(conversationId) : '';\n    const { data } = await chatApi.get(\"/calls/history\".concat(params));\n    return data.sessions;\n};\n// Typing indicators\nconst setTyping = async (conversationId, isTyping)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/typing\"), {\n        isTyping\n    });\n};\n// User blocking\nconst blockUser = async (userId)=>{\n    await chatApi.post(\"/users/\".concat(userId, \"/block\"));\n};\nconst unblockUser = async (userId)=>{\n    await chatApi.post(\"/users/\".concat(userId, \"/unblock\"));\n};\nconst getBlockedUsers = async ()=>{\n    const { data } = await chatApi.get('/users/blocked');\n    return data.blockedUserIds;\n};\n// Reporting\nconst reportConversation = async (conversationId, reason, details)=>{\n    await chatApi.post(\"/conversations/\".concat(conversationId, \"/report\"), {\n        reason,\n        details\n    });\n};\nconst reportMessage = async (messageId, reason, details)=>{\n    await chatApi.post(\"/messages/\".concat(messageId, \"/report\"), {\n        reason,\n        details\n    });\n};\n// Settings\nconst getChatSettings = async ()=>{\n    const { data } = await chatApi.get('/settings');\n    return data.settings;\n};\nconst updateChatSettings = async (settings)=>{\n    await chatApi.put('/settings', settings);\n};\n// Group chats\nconst getGroups = async ()=>{\n    const { data } = await chatApi.get('/groups');\n    return data.groups;\n};\nconst createGroupChat = async (name, description, participantIds)=>{\n    const { data } = await chatApi.post('/groups', {\n        name,\n        description,\n        participantIds\n    });\n    return data.conversation;\n};\nconst getGroupInfo = async (groupId)=>{\n    const { data } = await chatApi.get(\"/groups/\".concat(groupId, \"/info\"));\n    return data.groupInfo;\n};\nconst updateGroupInfo = async (groupId, updates)=>{\n    await chatApi.put(\"/groups/\".concat(groupId, \"/info\"), updates);\n};\nconst getGroupMembers = async (groupId)=>{\n    const { data } = await chatApi.get(\"/groups/\".concat(groupId, \"/members\"));\n    return data.members;\n};\nconst addGroupMembers = async (groupId, userIds)=>{\n    await chatApi.post(\"/groups/\".concat(groupId, \"/members\"), {\n        userIds\n    });\n};\nconst removeGroupMember = async (groupId, userId)=>{\n    await chatApi.delete(\"/groups/\".concat(groupId, \"/members/\").concat(userId));\n};\nconst updateMemberRole = async (groupId, userId, role)=>{\n    await chatApi.put(\"/groups/\".concat(groupId, \"/members/\").concat(userId, \"/role\"), {\n        role\n    });\n};\nconst leaveGroup = async (groupId)=>{\n    await chatApi.post(\"/groups/\".concat(groupId, \"/leave\"));\n};\n// Stickers\nconst getStickerPacks = async ()=>{\n    const { data } = await chatApi.get('/stickers/packs');\n    return data.packs;\n};\nconst purchaseStickerPack = async (packId)=>{\n    await chatApi.post(\"/stickers/packs/\".concat(packId, \"/purchase\"));\n};\nconst getOwnedStickerPacks = async ()=>{\n    const { data } = await chatApi.get('/stickers/owned');\n    return data.packs;\n};\n// Statistics\nconst getChatStatistics = async ()=>{\n    const { data } = await chatApi.get('/statistics');\n    return data.statistics;\n};\n// Search\nconst searchMessages = async (query, conversationId)=>{\n    const params = new URLSearchParams({\n        query\n    });\n    if (conversationId) {\n        params.append('conversationId', conversationId);\n    }\n    const { data } = await chatApi.get(\"/search/messages?\".concat(params.toString()));\n    return data.messages;\n};\nconst searchConversations = async (query)=>{\n    const { data } = await chatApi.get(\"/search/conversations?query=\".concat(encodeURIComponent(query)));\n    return data.conversations;\n};\n// File upload\nconst uploadChatFile = async (file, type)=>{\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('type', type);\n    const { data } = await chatApi.post('/upload', formData, {\n        headers: {\n            'Content-Type': 'multipart/form-data'\n        }\n    });\n    return data.url;\n};\n// Voice messages\nconst transcribeVoiceMessage = async (messageId)=>{\n    const { data } = await chatApi.post(\"/messages/\".concat(messageId, \"/transcribe\"));\n    return data.transcription;\n};\n// Message scheduling\nconst scheduleMessage = async (conversationId, text, scheduledAt)=>{\n    await chatApi.post('/messages/schedule', {\n        conversationId,\n        text,\n        scheduledAt\n    });\n};\nconst getScheduledMessages = async ()=>{\n    const { data } = await chatApi.get('/messages/scheduled');\n    return data.messages;\n};\nconst cancelScheduledMessage = async (messageId)=>{\n    await chatApi.delete(\"/messages/scheduled/\".concat(messageId));\n};\n// WebSocket connection for real-time messaging\nconst connectToChat = (conversationId, onMessage)=>{\n    // WebSocket implementation would go here\n    // This is a placeholder for the real-time connection\n    console.log(\"Connecting to chat \".concat(conversationId));\n};\nconst disconnectFromChat = (conversationId)=>{\n    // WebSocket disconnection would go here\n    console.log(\"Disconnecting from chat \".concat(conversationId));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/services/chatService.ts\n"));

/***/ })

});