import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Select,
  MenuItem,
  InputLabel,
  Alert,
  CircularProgress,
  Chip,
  Autocomplete,
  Switch,
  useTheme,
  useMediaQuery,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  ArrowBack,
  Add as AddIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Image as ImageIcon,
  Event as EventIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ru } from 'date-fns/locale';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  createEvent,
  getEventCategories,
  getPopularTags
} from '../../src/services/eventsService';
import { 
  CreateEventRequest,
  EventLocation 
} from '../../src/types/events.types';

const schema = yup.object({
  title: yup.string().required('Название обязательно').min(3, 'Минимум 3 символа'),
  description: yup.string().required('Описание обязательно').min(10, 'Минимум 10 символов'),
  shortDescription: yup.string().max(200, 'Максимум 200 символов'),
  type: yup.string().required('Выберите тип события'),
  category: yup.string().required('Выберите категорию'),
  visibility: yup.string().required('Выберите видимость'),
  startDate: yup.date().required('Выберите дату начала').min(new Date(), 'Дата не может быть в прошлом'),
  endDate: yup.date().required('Выберите дату окончания').min(yup.ref('startDate'), 'Дата окончания должна быть после начала'),
  timezone: yup.string().required('Выберите часовой пояс'),
  location: yup.object({
    type: yup.string().required('Выберите тип локации'),
    name: yup.string().required('Укажите место события'),
    address: yup.string().when('type', {
      is: 'physical',
      then: (schema) => schema.required('Укажите адрес'),
      otherwise: (schema) => schema.notRequired()
    })
  }).required(),
  capacity: yup.number().required('Укажите вместимость').min(1, 'Минимум 1 участник').max(10000, 'Максимум 10000 участников'),
  tags: yup.array().of(yup.string()).max(15, 'Максимум 15 тегов')
});

const CreateEventPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);
  const [popularTags, setPopularTags] = useState<string[]>([]);
  const [selectedCoverImage, setSelectedCoverImage] = useState<File | null>(null);
  const [selectedPhotos, setSelectedPhotos] = useState<File[]>([]);

  const steps = ['Основная информация', 'Место и время', 'Участники и билеты', 'Медиа и настройки'];

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid }
  } = useForm<CreateEventRequest>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      description: '',
      shortDescription: '',
      type: 'meetup',
      category: '',
      visibility: 'public',
      startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // +2 hours
      timezone: 'Europe/Moscow',
      location: {
        type: 'physical',
        name: '',
        address: ''
      },
      capacity: 50,
      tags: [],
      ageRestriction: {
        min: 18
      },
      requirements: {
        verifiedOnly: false,
        premiumOnly: false,
        inviteOnly: false,
        approvalRequired: false
      },
      settings: {
        allowComments: true,
        allowSharing: true,
        showParticipants: true,
        enableChat: true,
        enableNetworking: true,
        sendReminders: true,
        collectFeedback: true
      },
      isRecurring: false
    },
    mode: 'onChange'
  });

  const watchedLocationType = watch('location.type');
  const watchedIsRecurring = watch('isRecurring');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInitialData();
  }, [user, router]);

  const loadInitialData = async () => {
    try {
      const [categoriesData, tagsData] = await Promise.all([
        getEventCategories(),
        getPopularTags(30)
      ]);
      
      setCategories(categoriesData);
      setPopularTags(tagsData.map(t => t.tag));
    } catch (err: any) {
      setError('Ошибка загрузки данных');
    }
  };

  const handleCoverImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError('Размер обложки не должен превышать 5MB');
        return;
      }
      setSelectedCoverImage(file);
    }
  };

  const handlePhotosUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length + selectedPhotos.length > 10) {
      setError('Максимум 10 фотографий');
      return;
    }
    setSelectedPhotos(prev => [...prev, ...files]);
  };

  const removePhoto = (index: number) => {
    setSelectedPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const onSubmit = async (data: CreateEventRequest) => {
    try {
      setLoading(true);
      setError(null);

      const eventData: CreateEventRequest = {
        ...data,
        coverImage: selectedCoverImage || undefined,
        photos: selectedPhotos
      };

      const event = await createEvent(eventData);
      setSuccess('Событие создано успешно!');
      
      setTimeout(() => {
        router.push(`/events/${event.id}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка создания события');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null;
  }

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Название события"
                    placeholder="Например: Мастер-класс по фотографии"
                    error={!!errors.title}
                    helperText={errors.title?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="shortDescription"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Краткое описание"
                    placeholder="Краткое описание для превью..."
                    error={!!errors.shortDescription}
                    helperText={errors.shortDescription?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={6}
                    label="Полное описание"
                    placeholder="Подробно расскажите о событии..."
                    error={!!errors.description}
                    helperText={errors.description?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.type}>
                <InputLabel>Тип события</InputLabel>
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="Тип события">
                      <MenuItem value="conference">Конференция</MenuItem>
                      <MenuItem value="workshop">Мастер-класс</MenuItem>
                      <MenuItem value="meetup">Митап</MenuItem>
                      <MenuItem value="party">Вечеринка</MenuItem>
                      <MenuItem value="concert">Концерт</MenuItem>
                      <MenuItem value="sports">Спорт</MenuItem>
                      <MenuItem value="cultural">Культура</MenuItem>
                      <MenuItem value="networking">Нетворкинг</MenuItem>
                      <MenuItem value="educational">Образование</MenuItem>
                      <MenuItem value="charity">Благотворительность</MenuItem>
                      <MenuItem value="other">Другое</MenuItem>
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.category}>
                <InputLabel>Категория</InputLabel>
                <Controller
                  name="category"
                  control={control}
                  render={({ field }) => (
                    <Select {...field} label="Категория">
                      {categories.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Видимость</FormLabel>
                <Controller
                  name="visibility"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value="public" control={<Radio />} label="Публичное" />
                      <FormControlLabel value="private" control={<Radio />} label="Приватное" />
                      <FormControlLabel value="invite_only" control={<Radio />} label="Только по приглашениям" />
                    </RadioGroup>
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="tags"
                control={control}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    multiple
                    freeSolo
                    options={popularTags}
                    value={field.value || []}
                    onChange={(_, value) => field.onChange(value)}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option}
                          {...getTagProps({ index })}
                          key={option}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Теги"
                        placeholder="Добавьте теги..."
                        error={!!errors.tags}
                        helperText={errors.tags?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>
          </Grid>
        );

      default:
        return <Typography>Шаг {step + 1} в разработке...</Typography>;
    }
  };

  return (
    <>
      <Head>
        <title>Создать событие - Likes & Love</title>
        <meta 
          name="description" 
          content="Создайте новое событие в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <EventIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Создать событие
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Paper elevation={3} sx={{ p: 4 }}>
              {/* Stepper */}
              <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Form */}
              <form onSubmit={handleSubmit(onSubmit)}>
                {renderStepContent(activeStep)}

                {/* Navigation Buttons */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                  <Button
                    onClick={handleBack}
                    disabled={activeStep === 0}
                  >
                    Назад
                  </Button>

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {activeStep === steps.length - 1 ? (
                      <Button
                        type="submit"
                        variant="contained"
                        disabled={loading || !isValid}
                        startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}
                      >
                        {loading ? 'Создание...' : 'Создать событие'}
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        onClick={handleNext}
                      >
                        Далее
                      </Button>
                    )}
                  </Box>
                </Box>
              </form>
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CreateEventPage;
