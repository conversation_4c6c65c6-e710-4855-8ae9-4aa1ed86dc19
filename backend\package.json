{"name": "@likes-love/backend", "version": "1.0.0", "description": "Backend for Likes-Love dating app", "author": "Likes-Love Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:dev": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "prisma:generate": "prisma generate", "init:configs": "ts-node src/scripts/init-default-configs.ts", "setup": "npm run init:configs", "seed:test": "ts-node prisma/seed-test-users.ts", "db:reset": "prisma migrate reset --force && npm run seed:test", "prisma:studio": "prisma studio"}, "dependencies": {"@nestjs-modules/ioredis": "^1.1.2", "@nestjs/common": "^10.4.19", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.19", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.19", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.2.1", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.19", "@prisma/client": "^5.7.1", "@sentry/node": "^7.86.0", "@types/bcrypt": "^5.0.2", "aws-sdk": "^2.1520.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-validator-jsonschema": "^5.0.0", "compression": "^1.8.0", "cookie": "^0.7.0", "cors": "^2.8.5", "express": "^5.1.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "geoip-lite": "^1.4.10", "helmet": "^8.1.0", "ioredis": "^5.6.1", "joi": "^17.11.0", "minio": "^7.1.3", "multer": "^2.0.1", "nestjs-i18n": "^10.5.1", "nestjs-minio-client": "^2.2.0", "nestjs-pino": "^3.5.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "path-to-regexp": "^6.3.0", "pino-http": "^8.5.1", "qrcode": "^1.5.3", "qs": "^6.11.2", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "stripe": "^18.1.1", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.17.0", "xss": "^1.0.15", "firebase-admin": "^13.4.0", "twilio": "^5.6.1", "nodemailer": "^7.0.3", "bull": "^4.16.5", "rate-limiter-flexible": "^7.1.1", "square": "^39.0.0", "paypal-rest-sdk": "^1.8.1", "maxmind": "^4.3.1", "node-wifi": "^2.0.15", "@abandonware/noble": "^1.9.2-15", "crypto-js": "^4.2.0", "jsonwebtoken": "^9.0.2", "validator": "^13.12.0"}, "devDependencies": {"@nestjs/cli": "10.4.9", "@nestjs/schematics": "10.0.3", "@nestjs/testing": "^10.4.19", "@types/bcryptjs": "^3.0.0", "@types/express": "5.0.2", "@types/geoip-lite": "^1.4.4", "@types/jest": "^29.5.11", "@types/multer": "1.4.11", "@types/node": "22.15.21", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/superagent": "^8.1.9", "@types/supertest": "^6.0.3", "@types/trusted-types": "^2.0.7", "@types/use-sync-external-store": "^1.2.0", "@types/validator": "^13.12.2", "eslint": "9.27.0", "husky": "^9.0.11", "jest-environment-node": "^30.0.0-beta.3", "jest-mock-extended": "^4.0.0-beta1", "prisma": "5.7.1", "supertest": "^7.1.1", "ts-jest": "29.1.1", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}