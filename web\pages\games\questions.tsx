import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Avatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid
} from '@mui/material';
import {
  ArrowBack,
  Quiz as QuizIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Share as ShareIcon,
  Timer as TimerIcon,
  People as PeopleIcon,
  EmojiEvents as TrophyIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

// Типы для игры в вопросы
interface Question {
  id: string;
  text: string;
  category: 'icebreaker' | 'deep' | 'fun' | 'romantic' | 'philosophical';
  difficulty: 'easy' | 'medium' | 'hard';
  isCustom?: boolean;
}

interface GameSession {
  id: string;
  partnerId?: string;
  partnerName?: string;
  partnerAvatar?: string;
  mode: 'solo' | 'pair';
  questions: Question[];
  currentQuestionIndex: number;
  answers: {
    [questionId: string]: {
      userAnswer?: string;
      partnerAnswer?: string;
      timestamp: string;
    };
  };
  startedAt: string;
  status: 'active' | 'completed' | 'waiting';
}

interface StartGameForm {
  mode: 'solo' | 'pair';
  partnerId?: string;
  category: string;
  difficulty: string;
  questionCount: number;
}

interface AnswerForm {
  answer: string;
}

// Схемы валидации
const startGameSchema = yup.object({
  mode: yup.string().oneOf(['solo', 'pair']).required('Выберите режим игры'),
  partnerId: yup.string().when('mode', {
    is: 'pair',
    then: (schema) => schema.required('Выберите партнера'),
    otherwise: (schema) => schema.notRequired()
  }),
  category: yup.string().required('Выберите категорию'),
  difficulty: yup.string().required('Выберите сложность'),
  questionCount: yup.number().min(5).max(20).required('Выберите количество вопросов')
});

const answerSchema = yup.object({
  answer: yup.string().required('Введите ответ').max(500, 'Максимум 500 символов')
});

const QuestionsGamePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [gameSession, setGameSession] = useState<GameSession | null>(null);
  const [setupDialogOpen, setSetupDialogOpen] = useState(true);
  const [matches, setMatches] = useState<any[]>([]);

  const {
    control: setupControl,
    handleSubmit: handleSetupSubmit,
    formState: { errors: setupErrors },
    watch: watchSetup
  } = useForm<StartGameForm>({
    resolver: yupResolver(startGameSchema),
    defaultValues: {
      mode: 'solo',
      category: 'icebreaker',
      difficulty: 'easy',
      questionCount: 10
    }
  });

  const {
    control: answerControl,
    handleSubmit: handleAnswerSubmit,
    formState: { errors: answerErrors },
    reset: resetAnswer
  } = useForm<AnswerForm>({
    resolver: yupResolver(answerSchema),
    defaultValues: {
      answer: ''
    }
  });

  const watchedMode = watchSetup('mode');

  // Категории вопросов
  const categories = [
    { value: 'icebreaker', label: 'Знакомство', description: 'Легкие вопросы для начала общения' },
    { value: 'deep', label: 'Глубокие', description: 'Серьезные вопросы о жизни и ценностях' },
    { value: 'fun', label: 'Веселые', description: 'Забавные и необычные вопросы' },
    { value: 'romantic', label: 'Романтические', description: 'Вопросы о любви и отношениях' },
    { value: 'philosophical', label: 'Философские', description: 'Вопросы о смысле жизни' }
  ];

  const difficulties = [
    { value: 'easy', label: 'Легкий', description: 'Простые вопросы' },
    { value: 'medium', label: 'Средний', description: 'Умеренно сложные вопросы' },
    { value: 'hard', label: 'Сложный', description: 'Глубокие и сложные вопросы' }
  ];

  // Загрузка совпадений при монтировании компонента
  useEffect(() => {
    loadMatches();
  }, []);

  const loadMatches = async () => {
    try {
      // Здесь будет вызов API для получения совпадений
      // const response = await getMatches();
      
      // Мок данные
      const mockMatches = [
        { id: 'user1', name: 'Анна Петрова', avatar: '/avatars/anna.jpg', isOnline: true },
        { id: 'user2', name: 'Елена Сидорова', avatar: '/avatars/elena.jpg', isOnline: false },
        { id: 'user3', name: 'Мария Иванова', avatar: '/avatars/maria.jpg', isOnline: true }
      ];

      setMatches(mockMatches);
    } catch (err) {
      console.error('Ошибка при загрузке совпадений:', err);
    }
  };

  const generateQuestions = (category: string, difficulty: string, count: number): Question[] => {
    // Мок вопросы для разных категорий
    const questionBank: { [key: string]: Question[] } = {
      icebreaker: [
        { id: 'ice1', text: 'Какое ваше любимое время года и почему?', category: 'icebreaker', difficulty: 'easy' },
        { id: 'ice2', text: 'Если бы вы могли иметь любую суперсилу, какую бы выбрали?', category: 'icebreaker', difficulty: 'easy' },
        { id: 'ice3', text: 'Какой фильм вы можете пересматривать бесконечно?', category: 'icebreaker', difficulty: 'easy' }
      ],
      deep: [
        { id: 'deep1', text: 'Что для вас означает счастье?', category: 'deep', difficulty: 'medium' },
        { id: 'deep2', text: 'Какой урок жизни был для вас самым важным?', category: 'deep', difficulty: 'hard' },
        { id: 'deep3', text: 'Как вы справляетесь с трудными временами?', category: 'deep', difficulty: 'medium' }
      ],
      fun: [
        { id: 'fun1', text: 'Если бы вы были животным, кем бы были и почему?', category: 'fun', difficulty: 'easy' },
        { id: 'fun2', text: 'Какой самый странный факт о себе вы можете рассказать?', category: 'fun', difficulty: 'easy' },
        { id: 'fun3', text: 'Если бы у вас была машина времени, куда бы вы отправились?', category: 'fun', difficulty: 'medium' }
      ],
      romantic: [
        { id: 'rom1', text: 'Как вы представляете идеальное свидание?', category: 'romantic', difficulty: 'easy' },
        { id: 'rom2', text: 'Что для вас важнее всего в отношениях?', category: 'romantic', difficulty: 'medium' },
        { id: 'rom3', text: 'Верите ли вы в любовь с первого взгляда?', category: 'romantic', difficulty: 'easy' }
      ],
      philosophical: [
        { id: 'phil1', text: 'В чем, по-вашему, смысл жизни?', category: 'philosophical', difficulty: 'hard' },
        { id: 'phil2', text: 'Что бы вы изменили в мире, если бы могли?', category: 'philosophical', difficulty: 'hard' },
        { id: 'phil3', text: 'Как вы думаете, что происходит после смерти?', category: 'philosophical', difficulty: 'hard' }
      ]
    };

    const categoryQuestions = questionBank[category] || questionBank.icebreaker;
    const filteredQuestions = categoryQuestions.filter(q => q.difficulty === difficulty);
    
    // Если недостаточно вопросов, добавляем из других сложностей
    let selectedQuestions = [...filteredQuestions];
    if (selectedQuestions.length < count) {
      const additionalQuestions = categoryQuestions.filter(q => q.difficulty !== difficulty);
      selectedQuestions = [...selectedQuestions, ...additionalQuestions];
    }

    return selectedQuestions.slice(0, count);
  };

  const handleStartGame = async (data: StartGameForm) => {
    try {
      setLoading(true);
      setError(null);

      const questions = generateQuestions(data.category, data.difficulty, data.questionCount);
      
      const newSession: GameSession = {
        id: `session_${Date.now()}`,
        mode: data.mode,
        partnerId: data.partnerId,
        partnerName: data.partnerId ? matches.find(m => m.id === data.partnerId)?.name : undefined,
        partnerAvatar: data.partnerId ? matches.find(m => m.id === data.partnerId)?.avatar : undefined,
        questions,
        currentQuestionIndex: 0,
        answers: {},
        startedAt: new Date().toISOString(),
        status: data.mode === 'pair' ? 'waiting' : 'active'
      };

      setGameSession(newSession);
      setSetupDialogOpen(false);
      
      if (data.mode === 'pair') {
        setSuccess(`Приглашение отправлено ${newSession.partnerName}. Ожидаем ответа...`);
      }
    } catch (err: any) {
      setError(err.message || 'Ошибка при создании игры');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitAnswer = async (data: AnswerForm) => {
    if (!gameSession) return;

    try {
      setLoading(true);
      setError(null);

      const currentQuestion = gameSession.questions[gameSession.currentQuestionIndex];
      const updatedAnswers = {
        ...gameSession.answers,
        [currentQuestion.id]: {
          ...gameSession.answers[currentQuestion.id],
          userAnswer: data.answer,
          timestamp: new Date().toISOString()
        }
      };

      const updatedSession = {
        ...gameSession,
        answers: updatedAnswers,
        currentQuestionIndex: gameSession.currentQuestionIndex + 1
      };

      setGameSession(updatedSession);
      resetAnswer();

      // Проверяем, закончилась ли игра
      if (updatedSession.currentQuestionIndex >= updatedSession.questions.length) {
        updatedSession.status = 'completed';
        setSuccess('Игра завершена! Получите 25 очков опыта.');
      }
    } catch (err: any) {
      setError(err.message || 'Ошибка при отправке ответа');
    } finally {
      setLoading(false);
    }
  };

  const handleNewGame = () => {
    setGameSession(null);
    setSetupDialogOpen(true);
  };

  const handleBack = () => {
    router.push('/games');
  };

  const getCurrentQuestion = () => {
    if (!gameSession || gameSession.currentQuestionIndex >= gameSession.questions.length) {
      return null;
    }
    return gameSession.questions[gameSession.currentQuestionIndex];
  };

  const getProgress = () => {
    if (!gameSession) return 0;
    return (gameSession.currentQuestionIndex / gameSession.questions.length) * 100;
  };

  if (!user) {
    return (
      <Layout title="Игра в вопросы">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  const currentQuestion = getCurrentQuestion();

  return (
    <>
      <Head>
        <title>Игра в вопросы - Likes & Love</title>
        <meta name="description" content="Играйте в вопросы и узнавайте друг друга лучше в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Игра в вопросы">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Игра в вопросы
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Отвечайте на интересные вопросы и узнавайте друг друга лучше
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Игровая сессия */}
            {gameSession && (
              <>
                {/* Информация о сессии */}
                <Card sx={{ mb: 4 }}>
                  <CardContent>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          {gameSession.mode === 'pair' && gameSession.partnerAvatar && (
                            <Avatar src={gameSession.partnerAvatar} />
                          )}
                          <Box>
                            <Typography variant="h6" fontWeight="bold">
                              {gameSession.mode === 'solo' ? 'Одиночная игра' : `Игра с ${gameSession.partnerName}`}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Вопрос {gameSession.currentQuestionIndex + 1} из {gameSession.questions.length}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ textAlign: { xs: 'left', sm: 'right' } }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            Прогресс: {Math.round(getProgress())}%
                          </Typography>
                          <Box sx={{ width: '100%', maxWidth: 200, ml: { xs: 0, sm: 'auto' } }}>
                            <Box sx={{ 
                              height: 8, 
                              borderRadius: 4, 
                              backgroundColor: 'grey.300',
                              position: 'relative'
                            }}>
                              <Box sx={{
                                height: '100%',
                                borderRadius: 4,
                                backgroundColor: 'primary.main',
                                width: `${getProgress()}%`,
                                transition: 'width 0.3s ease'
                              }} />
                            </Box>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>

                {/* Текущий вопрос */}
                {currentQuestion ? (
                  <Card sx={{ mb: 4 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 3 }}>
                        <LightbulbIcon color="primary" sx={{ mt: 0.5 }} />
                        <Typography variant="h6" fontWeight="bold">
                          {currentQuestion.text}
                        </Typography>
                      </Box>

                      <form onSubmit={handleAnswerSubmit(handleSubmitAnswer)}>
                        <Controller
                          name="answer"
                          control={answerControl}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="Ваш ответ"
                              multiline
                              rows={4}
                              fullWidth
                              error={!!answerErrors.answer}
                              helperText={answerErrors.answer?.message || `${field.value?.length || 0}/500`}
                              sx={{ mb: 3 }}
                            />
                          )}
                        />

                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <Button
                            type="submit"
                            variant="contained"
                            disabled={loading}
                            startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                          >
                            {loading ? 'Отправка...' : 'Отправить ответ'}
                          </Button>
                        </Box>
                      </form>
                    </CardContent>
                  </Card>
                ) : (
                  /* Игра завершена */
                  <Card sx={{ textAlign: 'center', py: 4 }}>
                    <CardContent>
                      <TrophyIcon sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
                      <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
                        Игра завершена!
                      </Typography>
                      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                        Вы ответили на все {gameSession.questions.length} вопросов
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                        <Button
                          variant="contained"
                          onClick={handleNewGame}
                          startIcon={<RefreshIcon />}
                        >
                          Новая игра
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={handleBack}
                        >
                          К играм
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                )}

                {/* История ответов */}
                {Object.keys(gameSession.answers).length > 0 && (
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Ваши ответы
                      </Typography>
                      {gameSession.questions.slice(0, gameSession.currentQuestionIndex).map((question, index) => {
                        const answer = gameSession.answers[question.id];
                        return (
                          <Box key={question.id} sx={{ mb: 3, pb: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                            <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                              {index + 1}. {question.text}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {answer?.userAnswer}
                            </Typography>
                          </Box>
                        );
                      })}
                    </CardContent>
                  </Card>
                )}
              </>
            )}

            {/* Диалог настройки игры */}
            <Dialog
              open={setupDialogOpen}
              onClose={() => !gameSession && handleBack()}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Настройка игры
              </DialogTitle>
              <DialogContent>
                <form onSubmit={handleSetupSubmit(handleStartGame)}>
                  <Grid container spacing={3} sx={{ mt: 1 }}>
                    <Grid item xs={12}>
                      <Controller
                        name="mode"
                        control={setupControl}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Режим игры</InputLabel>
                            <Select {...field} label="Режим игры">
                              <MenuItem value="solo">Одиночная игра</MenuItem>
                              <MenuItem value="pair">Игра с партнером</MenuItem>
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>

                    {watchedMode === 'pair' && (
                      <Grid item xs={12}>
                        <Controller
                          name="partnerId"
                          control={setupControl}
                          render={({ field }) => (
                            <FormControl fullWidth error={!!setupErrors.partnerId}>
                              <InputLabel>Выберите партнера</InputLabel>
                              <Select {...field} label="Выберите партнера">
                                {matches.map((match) => (
                                  <MenuItem key={match.id} value={match.id}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <Avatar src={match.avatar} sx={{ width: 24, height: 24 }} />
                                      {match.name}
                                      {match.isOnline && (
                                        <Chip label="Онлайн" size="small" color="success" />
                                      )}
                                    </Box>
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>
                    )}

                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="category"
                        control={setupControl}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Категория</InputLabel>
                            <Select {...field} label="Категория">
                              {categories.map((category) => (
                                <MenuItem key={category.value} value={category.value}>
                                  {category.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Controller
                        name="difficulty"
                        control={setupControl}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Сложность</InputLabel>
                            <Select {...field} label="Сложность">
                              {difficulties.map((difficulty) => (
                                <MenuItem key={difficulty.value} value={difficulty.value}>
                                  {difficulty.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Controller
                        name="questionCount"
                        control={setupControl}
                        render={({ field }) => (
                          <FormControl fullWidth>
                            <InputLabel>Количество вопросов</InputLabel>
                            <Select {...field} label="Количество вопросов">
                              <MenuItem value={5}>5 вопросов (5 мин)</MenuItem>
                              <MenuItem value={10}>10 вопросов (10 мин)</MenuItem>
                              <MenuItem value={15}>15 вопросов (15 мин)</MenuItem>
                              <MenuItem value={20}>20 вопросов (20 мин)</MenuItem>
                            </Select>
                          </FormControl>
                        )}
                      />
                    </Grid>
                  </Grid>
                </form>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => !gameSession && handleBack()}>
                  Отмена
                </Button>
                <Button
                  onClick={handleSetupSubmit(handleStartGame)}
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <QuizIcon />}
                >
                  {loading ? 'Создание...' : 'Начать игру'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default QuestionsGamePage;
