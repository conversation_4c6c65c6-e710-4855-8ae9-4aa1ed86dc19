import React, { useState } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Avatar,
  Button,
  Grid,
  Chip,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Divider,
  Paper
} from '@mui/material';
import {
  FaEdit,
  FaCamera,
  FaHeart,
  FaMapMarkerAlt,
  FaBirthdayCake,
  FaGraduationCap,
  FaBriefcase,
  FaMusic,
  FaGamepad,
  FaBook,
  FaPlane
} from 'react-icons/fa';
import Layout from '../components/Layout/Layout';
import { useAuth } from '../src/providers/AuthProvider';
import PhotoViewer from '../components/PhotoViewer/PhotoViewer';

const ProfilePage: NextPage = () => {
  const { user } = useAuth();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editField, setEditField] = useState('');
  const [photoModalOpen, setPhotoModalOpen] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  // Мок данные профиля
  const profileData = {
    name: user?.name || 'Анна Петрова',
    age: 28,
    location: 'Москва, Россия',
    education: 'МГУ, Психология',
    work: 'UX Designer в IT компании',
    bio: 'Люблю путешествия, фотографию и хорошие книги. Ищу серьезные отношения с интересным человеком.',
    interests: ['Путешествия', 'Фотография', 'Книги', 'Йога', 'Кулинария', 'Музыка'],
    photos: [
      'https://picsum.photos/400/500?random=1',
      'https://picsum.photos/400/500?random=2',
      'https://picsum.photos/400/500?random=3',
      'https://picsum.photos/400/500?random=4'
    ],
    verified: true,
    premium: true
  };

  const handleEditClick = (field: string) => {
    setEditField(field);
    setEditDialogOpen(true);
  };

  const handlePhotoClick = (index: number) => {
    setSelectedPhotoIndex(index);
    setPhotoModalOpen(true);
  };

  return (
    <>
      <Head>
        <title>Мой профиль - Likes Love</title>
        <meta name="description" content="Управляйте своим профилем в приложении Likes Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout showHeader={true} showFooter={true}>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Мой профиль
          </Typography>

          <Grid container spacing={4}>
            {/* Основная информация */}
            <Grid item xs={12} md={4}>
              <Card sx={{ position: 'sticky', top: 100 }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Box sx={{ position: 'relative', display: 'inline-block', mb: 2 }}>
                    <Avatar
                      src={profileData.photos[0]}
                      sx={{ width: 150, height: 150, mx: 'auto' }}
                    />
                    <IconButton
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        right: 0,
                        bgcolor: 'primary.main',
                        color: 'white',
                        '&:hover': { bgcolor: 'primary.dark' }
                      }}
                      size="small"
                    >
                      <FaCamera />
                    </IconButton>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
                    <Typography variant="h5" fontWeight="bold">
                      {profileData.name}
                    </Typography>
                    {profileData.verified && (
                      <Chip
                        label="Верифицирован"
                        color="primary"
                        size="small"
                        sx={{ fontSize: '0.7rem' }}
                      />
                    )}
                  </Box>

                  {profileData.premium && (
                    <Chip
                      label="Premium"
                      sx={{
                        background: 'linear-gradient(45deg, #FFD700, #FFA500)',
                        color: 'white',
                        fontWeight: 'bold',
                        mb: 2
                      }}
                    />
                  )}

                  <Stack spacing={1} sx={{ textAlign: 'left', mt: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FaBirthdayCake color="#666" />
                      <Typography variant="body2">{profileData.age} лет</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FaMapMarkerAlt color="#666" />
                      <Typography variant="body2">{profileData.location}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FaGraduationCap color="#666" />
                      <Typography variant="body2">{profileData.education}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FaBriefcase color="#666" />
                      <Typography variant="body2">{profileData.work}</Typography>
                    </Box>
                  </Stack>

                  <Button
                    variant="contained"
                    fullWidth
                    sx={{ mt: 3 }}
                    onClick={() => handleEditClick('main')}
                    startIcon={<FaEdit />}
                  >
                    Редактировать профиль
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* Детальная информация */}
            <Grid item xs={12} md={8}>
              <Stack spacing={3}>
                {/* О себе */}
                <Paper sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="bold">
                      О себе
                    </Typography>
                    <IconButton onClick={() => handleEditClick('bio')}>
                      <FaEdit />
                    </IconButton>
                  </Box>
                  <Typography variant="body1" color="text.secondary">
                    {profileData.bio}
                  </Typography>
                </Paper>

                {/* Интересы */}
                <Paper sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="bold">
                      Интересы
                    </Typography>
                    <IconButton onClick={() => handleEditClick('interests')}>
                      <FaEdit />
                    </IconButton>
                  </Box>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {profileData.interests.map((interest, index) => (
                      <Chip
                        key={index}
                        label={interest}
                        variant="outlined"
                        color="primary"
                      />
                    ))}
                  </Box>
                </Paper>

                {/* Фотографии */}
                <Paper sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="bold">
                      Фотографии
                    </Typography>
                    <IconButton onClick={() => handleEditClick('photos')}>
                      <FaEdit />
                    </IconButton>
                  </Box>
                  <Grid container spacing={2}>
                    {profileData.photos.map((photo, index) => (
                      <Grid item xs={6} sm={4} md={3} key={index}>
                        <Box
                          component="img"
                          src={photo}
                          alt={`Фото ${index + 1}`}
                          onClick={() => handlePhotoClick(index)}
                          sx={{
                            width: '100%',
                            height: 150,
                            objectFit: 'cover',
                            borderRadius: 2,
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              opacity: 0.8,
                              transform: 'scale(1.05)'
                            }
                          }}
                        />
                      </Grid>
                    ))}
                    <Grid item xs={6} sm={4} md={3}>
                      <Box
                        sx={{
                          width: '100%',
                          height: 150,
                          border: '2px dashed #ccc',
                          borderRadius: 2,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          cursor: 'pointer',
                          '&:hover': { borderColor: 'primary.main' }
                        }}
                      >
                        <FaCamera size={24} color="#ccc" />
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Статистика */}
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Статистика
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary" fontWeight="bold">
                          156
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Лайков получено
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="secondary" fontWeight="bold">
                          23
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Совпадений
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="success.main" fontWeight="bold">
                          12
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Активных чатов
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Stack>
            </Grid>
          </Grid>
        </Container>

        {/* Диалог редактирования */}
        <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Редактировать {editField}</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              multiline
              rows={4}
              placeholder="Введите новую информацию..."
              sx={{ mt: 1 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialogOpen(false)}>
              Отмена
            </Button>
            <Button variant="contained" onClick={() => setEditDialogOpen(false)}>
              Сохранить
            </Button>
          </DialogActions>
        </Dialog>

        {/* Просмотр фотографий */}
        <PhotoViewer
          open={photoModalOpen}
          onClose={() => setPhotoModalOpen(false)}
          photos={profileData.photos}
          initialIndex={selectedPhotoIndex}
          userName={profileData.name}
        />
      </Layout>
    </>
  );
};

export default ProfilePage;
