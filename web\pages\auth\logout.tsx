import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { 
  Container, 
  Paper, 
  Typography, 
  Box, 
  Alert,
  CircularProgress,
  Button,
  FormControlLabel,
  Checkbox,
  Divider,
  Stack
} from '@mui/material';
import { 
  Logout as LogoutIcon,
  CheckCircle,
  Error as ErrorIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { logoutWithData } from '../../src/services/authService';
import { LogoutData } from '../../src/types/auth.types';

interface LogoutPageProps {}

const LogoutPage: React.FC<LogoutPageProps> = () => {
  const router = useRouter();
  const { user, logout: authLogout } = useAuth();
  const { reason } = router.query;
  
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [logoutAllDevices, setLogoutAllDevices] = useState(false);

  // Автоматический выход при определенных причинах
  useEffect(() => {
    if (reason === 'security_logout' || reason === 'session_expired') {
      handleLogout(true);
    }
  }, [reason]);

  const getReasonMessage = (logoutReason?: string | string[]) => {
    switch (logoutReason) {
      case 'security_logout':
        return 'Выход выполнен по соображениям безопасности';
      case 'session_expired':
        return 'Ваша сессия истекла';
      case 'user_logout':
      default:
        return 'Вы уверены, что хотите выйти из системы?';
    }
  };

  const handleLogout = async (forced: boolean = false) => {
    if (!forced && !user) {
      router.push('/auth/login');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const logoutData: LogoutData = {
        allDevices: logoutAllDevices || forced,
        reason: (reason as LogoutData['reason']) || 'user_logout'
      };

      // Вызываем API для выхода
      await logoutWithData(logoutData);
      
      // Очищаем локальный контекст
      await authLogout();
      
      setSuccess(true);
      
      // Редирект через 2 секунды
      setTimeout(() => {
        router.push('/auth/login?logout=success');
      }, 2000);
      
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка при выходе из системы');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  const renderContent = () => {
    if (success) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CheckCircle 
            sx={{ 
              fontSize: 80, 
              color: 'success.main', 
              mb: 3 
            }} 
          />
          <Typography variant="h5" gutterBottom color="success.main">
            Выход выполнен успешно
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Вы будете перенаправлены на страницу входа
          </Typography>
        </Box>
      );
    }

    if (loading) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <CircularProgress size={60} sx={{ mb: 3 }} />
          <Typography variant="h6" gutterBottom>
            Выход из системы...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Пожалуйста, подождите
          </Typography>
        </Box>
      );
    }

    return (
      <Box sx={{ py: 2 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <LogoutIcon sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Выход из системы
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {getReasonMessage(reason)}
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {reason !== 'security_logout' && reason !== 'session_expired' && (
          <>
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={logoutAllDevices}
                    onChange={(e) => setLogoutAllDevices(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2">
                      Выйти на всех устройствах
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Завершить все активные сессии для повышения безопасности
                    </Typography>
                  </Box>
                }
              />
            </Box>

            <Divider sx={{ my: 3 }} />

            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="outlined"
                onClick={handleCancel}
                disabled={loading}
                size="large"
              >
                Отмена
              </Button>
              <Button
                variant="contained"
                color="warning"
                onClick={() => handleLogout(false)}
                disabled={loading}
                size="large"
                startIcon={<LogoutIcon />}
              >
                Выйти
              </Button>
            </Stack>
          </>
        )}

        {(reason === 'security_logout' || reason === 'session_expired') && (
          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SecurityIcon />
                <Typography variant="body2">
                  {reason === 'security_logout' 
                    ? 'Обнаружена подозрительная активность. Выход выполняется автоматически.'
                    : 'Ваша сессия истекла. Необходимо войти в систему заново.'
                  }
                </Typography>
              </Box>
            </Alert>
            <Button
              variant="contained"
              onClick={() => router.push('/auth/login')}
              size="large"
            >
              Перейти к входу
            </Button>
          </Box>
        )}
      </Box>
    );
  };

  return (
    <>
      <Head>
        <title>Выход из системы - Likes & Love</title>
        <meta 
          name="description" 
          content="Безопасный выход из аккаунта приложения знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              {renderContent()}
            </Paper>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default LogoutPage;
