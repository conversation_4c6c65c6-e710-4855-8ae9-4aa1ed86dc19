import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Block as BlockIcon,
  CheckCircle as VerifyIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Download as ExportIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { adminService } from '../../services/adminService';
import { User, UserFilter } from '../../types/user.types';

interface AdminUsersPageProps {}

const AdminUsersPage: React.FC<AdminUsersPageProps> = () => {
  const router = useRouter();
  const { user: currentUser, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<UserFilter>({});
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    message: string;
    action: () => void;
  }>({
    open: false,
    title: '',
    message: '',
    action: () => {}
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/admin/users');
      return;
    }

    if (currentUser?.role !== 'admin' && currentUser?.role !== 'moderator') {
      router.push('/');
      return;
    }

    loadUsers();
  }, [isAuthenticated, currentUser, router, page, searchQuery, filters]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await adminService.getUsers({
        page,
        pageSize,
        search: searchQuery,
        ...filters
      });

      setUsers(response.users);
      setTotalUsers(response.total);
    } catch (err: any) {
      setError('Ошибка загрузки пользователей');
      console.error('Users loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(1);
  };

  const handleFilterChange = (filterKey: keyof UserFilter, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
    setPage(1);
  };

  const handleUserAction = (user: User, action: string) => {
    setSelectedUser(user);
    setActionMenuAnchor(null);

    switch (action) {
      case 'view':
        router.push(`/admin/users/${user.id}`);
        break;
      case 'edit':
        router.push(`/admin/users/${user.id}/edit`);
        break;
      case 'block':
        setConfirmDialog({
          open: true,
          title: 'Заблокировать пользователя',
          message: `Вы уверены, что хотите заблокировать пользователя ${user.name}?`,
          action: () => blockUser(user.id)
        });
        break;
      case 'unblock':
        setConfirmDialog({
          open: true,
          title: 'Разблокировать пользователя',
          message: `Вы уверены, что хотите разблокировать пользователя ${user.name}?`,
          action: () => unblockUser(user.id)
        });
        break;
      case 'verify':
        setConfirmDialog({
          open: true,
          title: 'Верифицировать пользователя',
          message: `Вы уверены, что хотите верифицировать пользователя ${user.name}?`,
          action: () => verifyUser(user.id)
        });
        break;
      case 'delete':
        setConfirmDialog({
          open: true,
          title: 'Удалить пользователя',
          message: `ВНИМАНИЕ! Вы уверены, что хотите удалить пользователя ${user.name}? Это действие нельзя отменить.`,
          action: () => deleteUser(user.id)
        });
        break;
    }
  };

  const blockUser = async (userId: string) => {
    try {
      await adminService.blockUser(userId);
      await loadUsers();
      setConfirmDialog({ ...confirmDialog, open: false });
    } catch (error) {
      setError('Ошибка блокировки пользователя');
    }
  };

  const unblockUser = async (userId: string) => {
    try {
      await adminService.unblockUser(userId);
      await loadUsers();
      setConfirmDialog({ ...confirmDialog, open: false });
    } catch (error) {
      setError('Ошибка разблокировки пользователя');
    }
  };

  const verifyUser = async (userId: string) => {
    try {
      await adminService.verifyUser(userId);
      await loadUsers();
      setConfirmDialog({ ...confirmDialog, open: false });
    } catch (error) {
      setError('Ошибка верификации пользователя');
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      await adminService.deleteUser(userId);
      await loadUsers();
      setConfirmDialog({ ...confirmDialog, open: false });
    } catch (error) {
      setError('Ошибка удаления пользователя');
    }
  };

  const handleExportUsers = async () => {
    try {
      const blob = await adminService.exportUsers(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      setError('Ошибка экспорта пользователей');
    }
  };

  const getUserStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'blocked':
        return 'error';
      case 'pending':
        return 'warning';
      case 'suspended':
        return 'error';
      default:
        return 'default';
    }
  };

  const getUserStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активен';
      case 'blocked':
        return 'Заблокирован';
      case 'pending':
        return 'Ожидает';
      case 'suspended':
        return 'Приостановлен';
      default:
        return status;
    }
  };

  return (
    <Layout>
      <Head>
        <title>Управление пользователями - Админ-панель</title>
        <meta name="description" content="Управление пользователями платформы LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Управление пользователями
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Всего пользователей: {totalUsers.toLocaleString()}
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={handleExportUsers}
          >
            Экспорт
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Поиск по имени, email..."
                  value={searchQuery}
                  onChange={handleSearch}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Статус</InputLabel>
                  <Select
                    value={filters.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    label="Статус"
                  >
                    <MenuItem value="">Все</MenuItem>
                    <MenuItem value="active">Активные</MenuItem>
                    <MenuItem value="blocked">Заблокированные</MenuItem>
                    <MenuItem value="pending">Ожидающие</MenuItem>
                    <MenuItem value="suspended">Приостановленные</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Верификация</InputLabel>
                  <Select
                    value={filters.verified || ''}
                    onChange={(e) => handleFilterChange('verified', e.target.value)}
                    label="Верификация"
                  >
                    <MenuItem value="">Все</MenuItem>
                    <MenuItem value="true">Верифицированные</MenuItem>
                    <MenuItem value="false">Не верифицированные</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Роль</InputLabel>
                  <Select
                    value={filters.role || ''}
                    onChange={(e) => handleFilterChange('role', e.target.value)}
                    label="Роль"
                  >
                    <MenuItem value="">Все</MenuItem>
                    <MenuItem value="user">Пользователь</MenuItem>
                    <MenuItem value="partner">Партнер</MenuItem>
                    <MenuItem value="moderator">Модератор</MenuItem>
                    <MenuItem value="admin">Администратор</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<FilterIcon />}
                  onClick={() => {
                    setFilters({});
                    setSearchQuery('');
                    setPage(1);
                  }}
                >
                  Сбросить
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>