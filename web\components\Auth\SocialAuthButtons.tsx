import React from 'react';
import { But<PERSON>, Stack } from '@mui/material';
import { Google, Apple } from '@mui/icons-material';
import { useAuth } from '../../src/providers/AuthProvider';

const SocialAuthButtons: React.FC = () => {
  const { socialLogin } = useAuth();

  const handleSocialLogin = async (provider: string) => {
    try {
      await socialLogin(provider);
    } catch (error) {
      console.error(`${provider} login error:`, error);
    }
  };

  return (
    <Stack spacing={2}>
      <Button
        fullWidth
        variant="outlined"
        startIcon={<Google />}
        onClick={() => handleSocialLogin('google')}
        sx={{ 
          borderColor: '#4285f4',
          color: '#4285f4',
          '&:hover': {
            borderColor: '#357ae8',
            backgroundColor: 'rgba(66, 133, 244, 0.04)'
          }
        }}
      >
        Войти через Google
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={() => handleSocialLogin('vk')}
        sx={{ 
          borderColor: '#0077ff',
          color: '#0077ff',
          '&:hover': {
            borderColor: '#0066dd',
            backgroundColor: 'rgba(0, 119, 255, 0.04)'
          }
        }}
      >
        Войти через VK
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={() => handleSocialLogin('yandex')}
        sx={{ 
          borderColor: '#fc0',
          color: '#000',
          '&:hover': {
            borderColor: '#e6b800',
            backgroundColor: 'rgba(255, 204, 0, 0.04)'
          }
        }}
      >
        Войти через Яндекс ID
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={() => handleSocialLogin('sber')}
        sx={{ 
          borderColor: '#21a038',
          color: '#21a038',
          '&:hover': {
            borderColor: '#1d8f32',
            backgroundColor: 'rgba(33, 160, 56, 0.04)'
          }
        }}
      >
        Войти через Сбер ID
      </Button>

      <Button
        fullWidth
        variant="outlined"
        onClick={() => handleSocialLogin('gosuslugi')}
        sx={{ 
          borderColor: '#0065b1',
          color: '#0065b1',
          '&:hover': {
            borderColor: '#005a9e',
            backgroundColor: 'rgba(0, 101, 177, 0.04)'
          }
        }}
      >
        Войти через Госуслуги
      </Button>
    </Stack>
  );
};

export default SocialAuthButtons;
