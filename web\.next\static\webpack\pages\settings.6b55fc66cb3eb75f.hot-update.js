"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "(pages-dir-browser)/./pages/settings/index.tsx":
/*!**********************************!*\
  !*** ./pages/settings/index.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,Container,Divider,Fade,FormControl,Grid,InputLabel,List,ListItem,ListItemIcon,ListItemSecondaryAction,ListItemText,MenuItem,Paper,Select,Switch,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,Container,Divider,Fade,FormControl,Grid,InputLabel,List,ListItem,ListItemIcon,ListItemSecondaryAction,ListItemText,MenuItem,Paper,Select,Switch,Typography,useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,ArrowBack,ChevronRight,Diamond,Info,LocationOn,Lock,Notifications,Palette,Schedule,Security,Settings,Star,Verified,Warning!=!@mui/icons-material */ \"(pages-dir-browser)/__barrel_optimize__?names=AccountCircle,ArrowBack,ChevronRight,Diamond,Info,LocationOn,Lock,Notifications,Palette,Schedule,Security,Settings,Star,Verified,Warning!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout/Layout */ \"(pages-dir-browser)/./components/Layout/Layout.tsx\");\n/* harmony import */ var _src_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../src/providers/AuthProvider */ \"(pages-dir-browser)/./src/providers/AuthProvider.tsx\");\n/* harmony import */ var _src_providers_SettingsProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../src/providers/SettingsProvider */ \"(pages-dir-browser)/./src/providers/SettingsProvider.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SettingsPage = ()=>{\n    var _user_photos_, _user_photos, _user_firstName, _user_subscription;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const theme = (0,_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useTheme)();\n    const isMobile = (0,_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useMediaQuery)(theme.breakpoints.down('md'));\n    const { user } = (0,_src_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { settings, loading, error, loadSettings, updatePreferences } = (0,_src_providers_SettingsProvider__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const [quickSettingsLoading, setQuickSettingsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (!user) {\n                router.push('/auth/login');\n                return;\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleQuickToggle = async (setting, value)=>{\n        try {\n            setQuickSettingsLoading(setting);\n            const updates = {};\n            switch(setting){\n                case 'theme':\n                    updates.theme = value ? 'dark' : 'light';\n                    break;\n                case 'notifications':\n                    updates.notifications = {\n                        ...settings === null || settings === void 0 ? void 0 : settings.notifications,\n                        messages: value\n                    };\n                    break;\n                case 'location':\n                    updates.showLocation = value;\n                    break;\n                case 'online':\n                    updates.showOnlineStatus = value;\n                    break;\n            }\n            if (setting === 'notifications') {\n                // This would be handled by updateNotificationSettings\n                // For now, just show success\n                setSuccess('Настройки уведомлений обновлены');\n            } else {\n                await updatePreferences({\n                    preferences: updates\n                });\n                setSuccess('Настройки обновлены');\n            }\n        } catch (err) {\n        // Error handled by context\n        } finally{\n            setQuickSettingsLoading(null);\n        }\n    };\n    const settingsMenuItems = [\n        {\n            id: 'account',\n            title: 'Аккаунт',\n            description: 'Профиль, верификация, подписка',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.AccountCircle, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 120,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/account',\n            badge: (user === null || user === void 0 ? void 0 : user.isVerified) ? 'Верифицирован' : null,\n            badgeColor: 'success'\n        },\n        {\n            id: 'privacy',\n            title: 'Приватность',\n            description: 'Видимость профиля, блокировки',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Lock, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 129,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/privacy',\n            badge: (settings === null || settings === void 0 ? void 0 : settings.privacy.showOnline) ? null : 'Скрыт',\n            badgeColor: 'warning'\n        },\n        {\n            id: 'notifications',\n            title: 'Уведомления',\n            description: 'Push, email, SMS уведомления',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Notifications, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 138,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/notifications',\n            badge: (settings === null || settings === void 0 ? void 0 : settings.notifications.messages) ? 'Включены' : 'Отключены',\n            badgeColor: (settings === null || settings === void 0 ? void 0 : settings.notifications.messages) ? 'success' : 'default'\n        },\n        {\n            id: 'security',\n            title: 'Безопасность',\n            description: 'Пароль, 2FA, активные сессии',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Security, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 147,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/security',\n            badge: null,\n            badgeColor: 'success'\n        },\n        {\n            id: 'blocked',\n            title: 'Заблокированные',\n            description: 'Управление заблокированными пользователями',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Warning, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 156,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/blocked',\n            badge: null,\n            badgeColor: 'error'\n        },\n        {\n            id: 'data',\n            title: 'Мои данные',\n            description: 'Экспорт, удаление аккаунта',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Info, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 165,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/data',\n            badge: null,\n            badgeColor: 'default'\n        }\n    ];\n    const quickSettings = [\n        {\n            id: 'theme',\n            title: 'Темная тема',\n            description: 'Переключить на темную тему',\n            enabled: (settings === null || settings === void 0 ? void 0 : settings.theme) === 'dark',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Palette, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 178,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 'notifications',\n            title: 'Push уведомления',\n            description: 'Получать уведомления на устройство',\n            enabled: (settings === null || settings === void 0 ? void 0 : settings.notifications.messages) || false,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Notifications, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 185,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 'location',\n            title: 'Показывать местоположение',\n            description: 'Отображать ваше местоположение в профиле',\n            enabled: false,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.LocationOn, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 192,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 'online',\n            title: 'Статус онлайн',\n            description: 'Показывать когда вы в сети',\n            enabled: (settings === null || settings === void 0 ? void 0 : settings.privacy.showOnline) || false,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Schedule, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 199,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Настройки - Likes & Love\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Настройки аккаунта в приложении знакомств Likes & Love\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                    maxWidth: \"md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                        sx: {\n                            py: {\n                                xs: 2,\n                                md: 4\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                sx: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    mb: 4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.ArrowBack, {}, void 0, false, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 28\n                                        }, void 0),\n                                        onClick: ()=>router.back(),\n                                        sx: {\n                                            mr: 2\n                                        },\n                                        children: \"Назад\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                        variant: isMobile ? \"h5\" : \"h4\",\n                                        sx: {\n                                            flexGrow: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Settings, {\n                                                sx: {\n                                                    mr: 2,\n                                                    verticalAlign: 'middle'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Настройки\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                severity: \"error\",\n                                sx: {\n                                    mb: 3\n                                },\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, undefined),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                severity: \"success\",\n                                sx: {\n                                    mb: 3\n                                },\n                                onClose: ()=>setSuccess(null),\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                sx: {\n                                    textAlign: 'center',\n                                    py: 8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.CircularProgress, {\n                                        size: 60\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                        variant: \"body1\",\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: \"Загрузка настроек...\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Fade, {\n                                in: true,\n                                timeout: 600,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 3,\n                                            sx: {\n                                                p: 3,\n                                                mb: 4\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                sx: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                                        src: (_user_photos = user.photos) === null || _user_photos === void 0 ? void 0 : (_user_photos_ = _user_photos[0]) === null || _user_photos_ === void 0 ? void 0 : _user_photos_.url,\n                                                        sx: {\n                                                            width: 80,\n                                                            height: 80\n                                                        },\n                                                        children: (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        sx: {\n                                                            flexGrow: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                sx: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: 1,\n                                                                    mb: 1\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                        variant: \"h6\",\n                                                                        children: [\n                                                                            user.firstName,\n                                                                            \" \",\n                                                                            user.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    user.isVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Verified, {\n                                                                        color: \"primary\",\n                                                                        fontSize: \"small\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    ((_user_subscription = user.subscription) === null || _user_subscription === void 0 ? void 0 : _user_subscription.plan) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Chip, {\n                                                                        label: user.subscription.plan.displayName,\n                                                                        color: \"primary\",\n                                                                        size: \"small\",\n                                                                        icon: user.subscription.plan.name === 'vip' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Diamond, {}, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 77\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Star, {}, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 95\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                variant: \"body2\",\n                                                                color: \"text.secondary\",\n                                                                gutterBottom: true,\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                variant: \"caption\",\n                                                                color: \"text.secondary\",\n                                                                children: [\n                                                                    \"Аккаунт создан: \",\n                                                                    new Date(user.createdAt).toLocaleDateString('ru-RU')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                        variant: \"outlined\",\n                                                        onClick: ()=>router.push('/profile/edit'),\n                                                        children: \"Редактировать\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 2,\n                                            sx: {\n                                                p: 3,\n                                                mb: 4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Быстрые настройки\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.List, {\n                                                    children: quickSettings.map((setting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItem, {\n                                                                    sx: {\n                                                                        px: 0\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemIcon, {\n                                                                            children: setting.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemText, {\n                                                                            primary: setting.title,\n                                                                            secondary: setting.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemSecondaryAction, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                                                checked: setting.enabled,\n                                                                                onChange: (e)=>handleQuickToggle(setting.id, e.target.checked),\n                                                                                disabled: quickSettingsLoading === setting.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 322,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                index < quickSettings.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Divider, {}, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 64\n                                                                }, undefined)\n                                                            ]\n                                                        }, setting.id, true, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 2,\n                                            sx: {\n                                                mb: 4\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.List, {\n                                                children: settingsMenuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItem, {\n                                                                button: true,\n                                                                onClick: ()=>router.push(item.path),\n                                                                sx: {\n                                                                    py: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemIcon, {\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemText, {\n                                                                        primary: item.title,\n                                                                        secondary: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                        sx: {\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Chip, {\n                                                                                label: item.badge,\n                                                                                size: \"small\",\n                                                                                color: item.badgeColor,\n                                                                                variant: \"outlined\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.ChevronRight, {\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            index < settingsMenuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Divider, {}, void 0, false, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 68\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 2,\n                                            sx: {\n                                                p: 3,\n                                                mb: 4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Предпочтения приложения\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                    container: true,\n                                                    spacing: 3,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Язык\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: (settings === null || settings === void 0 ? void 0 : settings.language) || 'ru',\n                                                                        label: \"Язык\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"ru\",\n                                                                                children: \"Русский\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"en\",\n                                                                                children: \"English\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Часовой пояс\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: 'Europe/Moscow',\n                                                                        label: \"Часовой пояс\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"Europe/Moscow\",\n                                                                                children: \"Москва (UTC+3)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 399,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"Europe/Kiev\",\n                                                                                children: \"Киев (UTC+2)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"Asia/Almaty\",\n                                                                                children: \"Алматы (UTC+6)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Формат даты\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: 'DD/MM/YYYY',\n                                                                        label: \"Формат даты\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"DD/MM/YYYY\",\n                                                                                children: \"ДД/ММ/ГГГГ\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"MM/DD/YYYY\",\n                                                                                children: \"ММ/ДД/ГГГГ\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"YYYY-MM-DD\",\n                                                                                children: \"ГГГГ-ММ-ДД\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Формат времени\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: '24h',\n                                                                        label: \"Формат времени\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"24h\",\n                                                                                children: \"24 часа\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"12h\",\n                                                                                children: \"12 часов\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"caption\",\n                                                    color: \"text.secondary\",\n                                                    sx: {\n                                                        mt: 2,\n                                                        display: 'block'\n                                                    },\n                                                    children: \"Для изменения этих настроек перейдите в соответствующие разделы\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 1,\n                                            sx: {\n                                                p: 3,\n                                                backgroundColor: 'background.default'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"О приложении\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                    container: true,\n                                                    spacing: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 6,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    color: \"text.secondary\",\n                                                                    children: \"Версия\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    children: \"1.0.0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 6,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    color: \"text.secondary\",\n                                                                    children: \"Сборка\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    children: \"2024.01.15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                sx: {\n                                                                    display: 'flex',\n                                                                    gap: 1,\n                                                                    mt: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\",\n                                                                        onClick: ()=>router.push('/help'),\n                                                                        children: \"Помощь\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\",\n                                                                        onClick: ()=>router.push('/about'),\n                                                                        children: \"О нас\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\",\n                                                                        onClick: ()=>router.push('/privacy-policy'),\n                                                                        children: \"Политика\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(SettingsPage, \"rfINC/Ifb6yyO+BjF+iV9Ms2yPw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useTheme,\n        _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useMediaQuery,\n        _src_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _src_providers_SettingsProvider__WEBPACK_IMPORTED_MODULE_6__.useSettings\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/settings/index.tsx\n"));

/***/ })

});