import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemButton,
  Badge,
  IconButton,
  Divider,
  Tabs,
  Tab,
  Chip,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
  Stack,
  Fab
} from '@mui/material';
import { 
  Search,
  MoreVert,
  Archive,
  Delete,
  Block,
  PhotoCamera,
  Videocam,
  Phone,
  Add,
  FilterList,
  Star,
  AccessTime,
  Done,
  DoneAll
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import axios from 'axios';
import { formatDistanceToNow } from 'date-fns';
import { ru } from 'date-fns/locale';

interface Message {
  id: string;
  text: string;
  senderId: string;
  timestamp: Date;
  isRead: boolean;
  messageType: 'text' | 'image' | 'video' | 'audio' | 'gif';
}

interface ChatPreview {
  id: string;
  matchId: string;
  otherUser: {
    id: string;
    name: string;
    avatar: string;
    isOnline: boolean;
    lastSeen?: Date;
    verificationStatus: 'none' | 'photo' | 'phone' | 'social' | 'gosuslugi';
  };
  lastMessage: Message;
  unreadCount: number;
  isArchived: boolean;
  isMuted: boolean;
  isPinned: boolean;
  blockedBy?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`messages-tabpanel-${index}`}
      aria-labelledby={`messages-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const MessagesPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [chats, setChats] = useState<ChatPreview[]>([]);
  const [filteredChats, setFilteredChats] = useState<ChatPreview[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTab, setCurrentTab] = useState(0);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadChats();
    }
  }, [user]);

  useEffect(() => {
    filterChats();
  }, [chats, searchQuery, currentTab]);

  const loadChats = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/messages/chats');
      setChats(response.data);
    } catch (error) {
      console.error('Ошибка загрузки чатов:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterChats = () => {
    let filtered = [...chats];

    // Фильтрация по вкладкам
    switch (currentTab) {
      case 0: // Все
        filtered = chats.filter(chat => !chat.isArchived);
        break;
      case 1: // Непрочитанные
        filtered = chats.filter(chat => chat.unreadCount > 0 && !chat.isArchived);
        break;
      case 2: // Архив
        filtered = chats.filter(chat => chat.isArchived);
        break;
      case 3: // Избранные
        filtered = chats.filter(chat => chat.isPinned && !chat.isArchived);
        break;
    }

    // Поиск
    if (searchQuery) {
      filtered = filtered.filter(chat =>
        chat.otherUser.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.lastMessage.text.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Сортировка
    filtered.sort((a, b) => {
      // Сначала закрепленные
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      // Затем по времени последнего сообщения
      return new Date(b.lastMessage.timestamp).getTime() - new Date(a.lastMessage.timestamp).getTime();
    });

    setFilteredChats(filtered);
  };

  const handleChatClick = (chatId: string, matchId: string) => {
    router.push(`/messages/${matchId}`);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, chatId: string) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setSelectedChat(chatId);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedChat(null);
  };

  const handleArchiveChat = async () => {
    if (!selectedChat) return;

    try {
      await axios.patch(`/api/messages/chats/${selectedChat}`, {
        isArchived: true
      });
      
      setChats(prevChats =>
        prevChats.map(chat =>
          chat.id === selectedChat
            ? { ...chat, isArchived: true }
            : chat
        )
      );
    } catch (error) {
      console.error('Ошибка архивирования чата:', error);
    }
    
    handleMenuClose();
  };

  const handlePinChat = async () => {
    if (!selectedChat) return;

    try {
      const chat = chats.find(c => c.id === selectedChat);
      await axios.patch(`/api/messages/chats/${selectedChat}`, {
        isPinned: !chat?.isPinned
      });
      
      setChats(prevChats =>
        prevChats.map(chat =>
          chat.id === selectedChat
            ? { ...chat, isPinned: !chat.isPinned }
            : chat
        )
      );
    } catch (error) {
      console.error('Ошибка закрепления чата:', error);
    }
    
    handleMenuClose();
  };

  const handleDeleteChat = async () => {
    if (!selectedChat) return;

    try {
      await axios.delete(`/api/messages/chats/${selectedChat}`);
      
      setChats(prevChats =>
        prevChats.filter(chat => chat.id !== selectedChat)
      );
    } catch (error) {
      console.error('Ошибка удаления чата:', error);
    }
    
    handleMenuClose();
  };

  const getMessagePreview = (message: Message) => {
    switch (message.messageType) {
      case 'image':
        return '📷 Фото';
      case 'video':
        return '🎥 Видео';
      case 'audio':
        return '🎤 Голосовое сообщение';
      case 'gif':
        return '🎭 GIF';
      default:
        return message.text;
    }
  };

  const getMessageIcon = (message: Message) => {
    if (message.senderId === user?.id) {
      return message.isRead ? <DoneAll fontSize="small" color="primary" /> : <Done fontSize="small" />;
    }
    return null;
  };

  const getOnlineStatus = (user: ChatPreview['otherUser']) => {
    if (user.isOnline) {
      return (
        <Badge
          variant="dot"
          sx={{
            '& .MuiBadge-dot': {
              backgroundColor: '#44b700',
              color: '#44b700',
              '&::after': {
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                borderRadius: '50%',
                animation: 'ripple 1.2s infinite ease-in-out',
                border: '1px solid currentColor',
                content: '""'
              }
            }
          }}
        >
          <Avatar src={user.avatar} alt={user.name} />
        </Badge>
      );
    }

    return <Avatar src={user.avatar} alt={user.name} />;
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'gosuslugi':
        return '🇷🇺';
      case 'social':
        return '✅';
      case 'photo':
        return '📷';
      case 'phone':
        return '📱';
      default:
        return '';
    }
  };

  const getUnreadCount = () => {
    return chats.reduce((total, chat) => total + chat.unreadCount, 0);
  };

  if (!user) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="h6">Загрузка сообщений...</Typography>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="md">
        <Box sx={{ mt: 2, mb: 4 }}>
          {/* Заголовок */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h4">
              Сообщения
              {getUnreadCount() > 0 && (
                <Badge
                  badgeContent={getUnreadCount()}
                  color="error"
                  sx={{ ml: 2 }}
                />
              )}
            </Typography>
            
            <Stack direction="row" spacing={1}>
              <IconButton color="primary">
                <FilterList />
              </IconButton>
              <IconButton color="primary">
                <Search />
              </IconButton>
            </Stack>
          </Box>

          {/* Поиск */}
          <TextField
            fullWidth
            placeholder="Поиск сообщений..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
          />

          {/* Вкладки */}
          <Paper elevation={1} sx={{ mb: 3 }}>
            <Tabs
              value={currentTab}
              onChange={(_, newValue) => setCurrentTab(newValue)}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label="Все" />
              <Tab 
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    Непрочитанные
                    {chats.filter(c => c.unreadCount > 0 && !c.isArchived).length > 0 && (
                      <Chip
                        size="small"
                        label={chats.filter(c => c.unreadCount > 0 && !c.isArchived).length}
                        color="error"
                      />
                    )}
                  </Box>
                }
              />
              <Tab label="Архив" />
              <Tab label="Избранные" />
            </Tabs>
          </Paper>

          {/* Список чатов */}
          <Paper elevation={2}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : filteredChats.length === 0 ? (
              <Box sx={{ textAlign: 'center', p: 4 }}>
                {searchQuery ? (
                  <Typography variant="body1" color="text.secondary">
                    Ничего не найдено по запросу "{searchQuery}"
                  </Typography>
                ) : currentTab === 0 ? (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      У вас пока нет сообщений
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Начните общаться с людьми, которые вам понравились!
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => router.push('/discover')}
                      sx={{ mt: 2 }}
                    >
                      Найти совпадения
                    </Button>
                  </Box>
                ) : (
                  <Typography variant="body1" color="text.secondary">
                    {currentTab === 1 && 'Нет непрочитанных сообщений'}
                    {currentTab === 2 && 'Архив пуст'}
                    {currentTab === 3 && 'Нет избранных чатов'}
                  </Typography>
                )}
              </Box>
            ) : (
              <List>
                {filteredChats.map((chat, index) => (
                  <React.Fragment key={chat.id}>
                    <ListItemButton
                      onClick={() => handleChatClick(chat.id, chat.matchId)}
                      sx={{
                        bgcolor: chat.unreadCount > 0 ? 'action.hover' : 'transparent',
                        '&:hover': {
                          bgcolor: 'action.selected'
                        }
                      }}
                    >
                      <ListItemAvatar>
                        {getOnlineStatus(chat.otherUser)}
                      </ListItemAvatar>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontWeight: chat.unreadCount > 0 ? 'bold' : 'normal'
                              }}
                            >
                              {chat.otherUser.name}
                            </Typography>
                            
                            {chat.isPinned && <Star fontSize="small" color="warning" />}
                            
                            {chat.otherUser.verificationStatus !== 'none' && (
                              <span style={{ fontSize: '12px' }}>
                                {getVerificationIcon(chat.otherUser.verificationStatus)}
                              </span>
                            )}
                          </Box>
                        }
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getMessageIcon(chat.lastMessage)}
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                fontWeight: chat.unreadCount > 0 ? 'bold' : 'normal',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                flex: 1
                              }}
                            >
                              {getMessagePreview(chat.lastMessage)}
                            </Typography>
                          </Box>
                        }
                      />
                      
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(chat.lastMessage.timestamp), {
                            addSuffix: true,
                            locale: ru
                          })}
                        </Typography>
                        
                        <Stack direction="row" spacing={1} alignItems="center">
                          {chat.unreadCount > 0 && (
                            <Badge
                              badgeContent={chat.unreadCount}
                              color="error"
                              max={99}
                            />
                          )}
                          
                          {chat.isMuted && (
                            <Box sx={{ fontSize: '16px' }}>🔇</Box>
                          )}
                          
                          <IconButton
                            size="small"
                            onClick={(e) => handleMenuOpen(e, chat.id)}
                          >
                            <MoreVert fontSize="small" />
                          </IconButton>
                        </Stack>
                      </Box>
                    </ListItemButton>
                    
                    {index < filteredChats.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>

          {/* Контекстное меню */}
          <Menu
            anchorEl={menuAnchor}
            open={Boolean(menuAnchor)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handlePinChat}>
              <Star fontSize="small" sx={{ mr: 1 }} />
              {selectedChat && chats.find(c => c.id === selectedChat)?.isPinned 
                ? 'Открепить' 
                : 'Закрепить'
              }
            </MenuItem>
            <MenuItem onClick={handleArchiveChat}>
              <Archive fontSize="small" sx={{ mr: 1 }} />
              Архивировать
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleDeleteChat} sx={{ color: 'error.main' }}>
              <Delete fontSize="small" sx={{ mr: 1 }} />
              Удалить чат
            </MenuItem>
          </Menu>

          {/* FAB для новых сообщений */}
          <Fab
            color="primary"
            sx={{
              position: 'fixed',
              bottom: 80,
              right: 16
            }}
            onClick={() => router.push('/discover')}
          >
            <Add />
          </Fab>
        </Box>
      </Container>
    </Layout>
  );
};

export default MessagesPage;
