"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_tanstack_react-query-devtools_build_modern_index_js"],{

/***/ "(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/chunk/V5T5VJKG.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-devtools/build/chunk/V5T5VJKG.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $TRACK: () => (/* binding */ $TRACK),\n/* harmony export */   DEV: () => (/* binding */ DEV),\n/* harmony export */   Dynamic: () => (/* binding */ Dynamic),\n/* harmony export */   For: () => (/* binding */ For),\n/* harmony export */   Index: () => (/* binding */ Index),\n/* harmony export */   Match: () => (/* binding */ Match),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Show: () => (/* binding */ Show),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   addEventListener: () => (/* binding */ addEventListener),\n/* harmony export */   batch: () => (/* binding */ batch),\n/* harmony export */   className: () => (/* binding */ className),\n/* harmony export */   clearDelegatedEvents: () => (/* binding */ clearDelegatedEvents),\n/* harmony export */   convertRemToPixels: () => (/* binding */ convertRemToPixels),\n/* harmony export */   createComponent: () => (/* binding */ createComponent),\n/* harmony export */   createComputed: () => (/* binding */ createComputed),\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   createEffect: () => (/* binding */ createEffect),\n/* harmony export */   createMemo: () => (/* binding */ createMemo),\n/* harmony export */   createRenderEffect: () => (/* binding */ createRenderEffect),\n/* harmony export */   createRoot: () => (/* binding */ createRoot),\n/* harmony export */   createSignal: () => (/* binding */ createSignal),\n/* harmony export */   createUniqueId: () => (/* binding */ createUniqueId),\n/* harmony export */   delegateEvents: () => (/* binding */ delegateEvents),\n/* harmony export */   deleteNestedDataByPath: () => (/* binding */ deleteNestedDataByPath),\n/* harmony export */   displayValue: () => (/* binding */ displayValue),\n/* harmony export */   getMutationStatusColor: () => (/* binding */ getMutationStatusColor),\n/* harmony export */   getOwner: () => (/* binding */ getOwner),\n/* harmony export */   getPreferredColorScheme: () => (/* binding */ getPreferredColorScheme),\n/* harmony export */   getQueryStatusColor: () => (/* binding */ getQueryStatusColor),\n/* harmony export */   getQueryStatusColorByLabel: () => (/* binding */ getQueryStatusColorByLabel),\n/* harmony export */   getQueryStatusLabel: () => (/* binding */ getQueryStatusLabel),\n/* harmony export */   getSidedProp: () => (/* binding */ getSidedProp),\n/* harmony export */   insert: () => (/* binding */ insert),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   mergeProps: () => (/* binding */ mergeProps),\n/* harmony export */   mutationSortFns: () => (/* binding */ mutationSortFns),\n/* harmony export */   on: () => (/* binding */ on),\n/* harmony export */   onCleanup: () => (/* binding */ onCleanup),\n/* harmony export */   onMount: () => (/* binding */ onMount),\n/* harmony export */   render: () => (/* binding */ render),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   setAttribute: () => (/* binding */ setAttribute),\n/* harmony export */   setupStyleSheet: () => (/* binding */ setupStyleSheet),\n/* harmony export */   sortFns: () => (/* binding */ sortFns),\n/* harmony export */   splitProps: () => (/* binding */ splitProps),\n/* harmony export */   spread: () => (/* binding */ spread),\n/* harmony export */   stringify: () => (/* binding */ stringify),\n/* harmony export */   template: () => (/* binding */ template),\n/* harmony export */   untrack: () => (/* binding */ untrack),\n/* harmony export */   updateNestedDataByPath: () => (/* binding */ updateNestedDataByPath),\n/* harmony export */   use: () => (/* binding */ use),\n/* harmony export */   useContext: () => (/* binding */ useContext),\n/* harmony export */   useTransition: () => (/* binding */ useTransition)\n/* harmony export */ });\n// ../../node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/dist/solid.js\nvar sharedConfig = {\n  context: void 0,\n  registry: void 0,\n  effects: void 0,\n  done: false,\n  getContextId() {\n    return getContextId(this.context.count);\n  },\n  getNextContextId() {\n    return getContextId(this.context.count++);\n  }\n};\nfunction getContextId(count) {\n  const num = String(count), len = num.length - 1;\n  return sharedConfig.context.id + (len ? String.fromCharCode(96 + len) : \"\") + num;\n}\nfunction setHydrateContext(context) {\n  sharedConfig.context = context;\n}\nfunction nextHydrateContext() {\n  return {\n    ...sharedConfig.context,\n    id: sharedConfig.getNextContextId(),\n    count: 0\n  };\n}\nvar IS_DEV = false;\nvar equalFn = (a, b) => a === b;\nvar $PROXY = Symbol(\"solid-proxy\");\nvar SUPPORTS_PROXY = typeof Proxy === \"function\";\nvar $TRACK = Symbol(\"solid-track\");\nvar signalOptions = {\n  equals: equalFn\n};\nvar ERROR = null;\nvar runEffects = runQueue;\nvar STALE = 1;\nvar PENDING = 2;\nvar UNOWNED = {\n  owned: null,\n  cleanups: null,\n  context: null,\n  owner: null\n};\nvar NO_INIT = {};\nvar Owner = null;\nvar Transition = null;\nvar Scheduler = null;\nvar ExternalSourceConfig = null;\nvar Listener = null;\nvar Updates = null;\nvar Effects = null;\nvar ExecCount = 0;\nfunction createRoot(fn, detachedOwner) {\n  const listener = Listener, owner = Owner, unowned = fn.length === 0, current = detachedOwner === void 0 ? owner : detachedOwner, root = unowned ? UNOWNED : {\n    owned: null,\n    cleanups: null,\n    context: current ? current.context : null,\n    owner: current\n  }, updateFn = unowned ? fn : () => fn(() => untrack(() => cleanNode(root)));\n  Owner = root;\n  Listener = null;\n  try {\n    return runUpdates(updateFn, true);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n}\nfunction createSignal(value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const s = {\n    value,\n    observers: null,\n    observerSlots: null,\n    comparator: options.equals || void 0\n  };\n  const setter = (value2) => {\n    if (typeof value2 === \"function\") {\n      if (Transition && Transition.running && Transition.sources.has(s)) value2 = value2(s.tValue);\n      else value2 = value2(s.value);\n    }\n    return writeSignal(s, value2);\n  };\n  return [readSignal.bind(s), setter];\n}\nfunction createComputed(fn, value, options) {\n  const c = createComputation(fn, value, true, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);\n  else updateComputation(c);\n}\nfunction createRenderEffect(fn, value, options) {\n  const c = createComputation(fn, value, false, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);\n  else updateComputation(c);\n}\nfunction createEffect(fn, value, options) {\n  runEffects = runUserEffects;\n  const c = createComputation(fn, value, false, STALE), s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  if (!options || !options.render) c.user = true;\n  Effects ? Effects.push(c) : updateComputation(c);\n}\nfunction createMemo(fn, value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const c = createComputation(fn, value, true, 0);\n  c.observers = null;\n  c.observerSlots = null;\n  c.comparator = options.equals || void 0;\n  if (Scheduler && Transition && Transition.running) {\n    c.tState = STALE;\n    Updates.push(c);\n  } else updateComputation(c);\n  return readSignal.bind(c);\n}\nfunction isPromise(v) {\n  return v && typeof v === \"object\" && \"then\" in v;\n}\nfunction createResource(pSource, pFetcher, pOptions) {\n  let source;\n  let fetcher;\n  let options;\n  {\n    source = true;\n    fetcher = pSource;\n    options = {};\n  }\n  let pr = null, initP = NO_INIT, id = null, loadedUnderTransition = false, scheduled = false, resolved = \"initialValue\" in options, dynamic = typeof source === \"function\" && createMemo(source);\n  const contexts = /* @__PURE__ */ new Set(), [value, setValue] = (options.storage || createSignal)(options.initialValue), [error, setError] = createSignal(void 0), [track, trigger] = createSignal(void 0, {\n    equals: false\n  }), [state, setState] = createSignal(resolved ? \"ready\" : \"unresolved\");\n  if (sharedConfig.context) {\n    id = sharedConfig.getNextContextId();\n    if (options.ssrLoadFrom === \"initial\") initP = options.initialValue;\n    else if (sharedConfig.load && sharedConfig.has(id)) initP = sharedConfig.load(id);\n  }\n  function loadEnd(p, v, error2, key) {\n    if (pr === p) {\n      pr = null;\n      key !== void 0 && (resolved = true);\n      if ((p === initP || v === initP) && options.onHydrated)\n        queueMicrotask(\n          () => options.onHydrated(key, {\n            value: v\n          })\n        );\n      initP = NO_INIT;\n      if (Transition && p && loadedUnderTransition) {\n        Transition.promises.delete(p);\n        loadedUnderTransition = false;\n        runUpdates(() => {\n          Transition.running = true;\n          completeLoad(v, error2);\n        }, false);\n      } else completeLoad(v, error2);\n    }\n    return v;\n  }\n  function completeLoad(v, err) {\n    runUpdates(() => {\n      if (err === void 0) setValue(() => v);\n      setState(err !== void 0 ? \"errored\" : resolved ? \"ready\" : \"unresolved\");\n      setError(err);\n      for (const c of contexts.keys()) c.decrement();\n      contexts.clear();\n    }, false);\n  }\n  function read() {\n    const c = SuspenseContext && useContext(SuspenseContext), v = value(), err = error();\n    if (err !== void 0 && !pr) throw err;\n    if (Listener && !Listener.user && c) {\n      createComputed(() => {\n        track();\n        if (pr) {\n          if (c.resolved && Transition && loadedUnderTransition) Transition.promises.add(pr);\n          else if (!contexts.has(c)) {\n            c.increment();\n            contexts.add(c);\n          }\n        }\n      });\n    }\n    return v;\n  }\n  function load(refetching = true) {\n    if (refetching !== false && scheduled) return;\n    scheduled = false;\n    const lookup = dynamic ? dynamic() : source;\n    loadedUnderTransition = Transition && Transition.running;\n    if (lookup == null || lookup === false) {\n      loadEnd(pr, untrack(value));\n      return;\n    }\n    if (Transition && pr) Transition.promises.delete(pr);\n    const p = initP !== NO_INIT ? initP : untrack(\n      () => fetcher(lookup, {\n        value: value(),\n        refetching\n      })\n    );\n    if (!isPromise(p)) {\n      loadEnd(pr, p, void 0, lookup);\n      return p;\n    }\n    pr = p;\n    if (\"value\" in p) {\n      if (p.status === \"success\") loadEnd(pr, p.value, void 0, lookup);\n      else loadEnd(pr, void 0, castError(p.value), lookup);\n      return p;\n    }\n    scheduled = true;\n    queueMicrotask(() => scheduled = false);\n    runUpdates(() => {\n      setState(resolved ? \"refreshing\" : \"pending\");\n      trigger();\n    }, false);\n    return p.then(\n      (v) => loadEnd(p, v, void 0, lookup),\n      (e) => loadEnd(p, void 0, castError(e), lookup)\n    );\n  }\n  Object.defineProperties(read, {\n    state: {\n      get: () => state()\n    },\n    error: {\n      get: () => error()\n    },\n    loading: {\n      get() {\n        const s = state();\n        return s === \"pending\" || s === \"refreshing\";\n      }\n    },\n    latest: {\n      get() {\n        if (!resolved) return read();\n        const err = error();\n        if (err && !pr) throw err;\n        return value();\n      }\n    }\n  });\n  if (dynamic) createComputed(() => load(false));\n  else load(false);\n  return [\n    read,\n    {\n      refetch: load,\n      mutate: setValue\n    }\n  ];\n}\nfunction batch(fn) {\n  return runUpdates(fn, false);\n}\nfunction untrack(fn) {\n  if (!ExternalSourceConfig && Listener === null) return fn();\n  const listener = Listener;\n  Listener = null;\n  try {\n    if (ExternalSourceConfig) return ExternalSourceConfig.untrack(fn);\n    return fn();\n  } finally {\n    Listener = listener;\n  }\n}\nfunction on(deps, fn, options) {\n  const isArray3 = Array.isArray(deps);\n  let prevInput;\n  let defer = options && options.defer;\n  return (prevValue) => {\n    let input;\n    if (isArray3) {\n      input = Array(deps.length);\n      for (let i = 0; i < deps.length; i++) input[i] = deps[i]();\n    } else input = deps();\n    if (defer) {\n      defer = false;\n      return prevValue;\n    }\n    const result = untrack(() => fn(input, prevInput, prevValue));\n    prevInput = input;\n    return result;\n  };\n}\nfunction onMount(fn) {\n  createEffect(() => untrack(fn));\n}\nfunction onCleanup(fn) {\n  if (Owner === null) ;\n  else if (Owner.cleanups === null) Owner.cleanups = [fn];\n  else Owner.cleanups.push(fn);\n  return fn;\n}\nfunction getOwner() {\n  return Owner;\n}\nfunction runWithOwner(o, fn) {\n  const prev = Owner;\n  const prevListener = Listener;\n  Owner = o;\n  Listener = null;\n  try {\n    return runUpdates(fn, true);\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = prev;\n    Listener = prevListener;\n  }\n}\nfunction startTransition(fn) {\n  if (Transition && Transition.running) {\n    fn();\n    return Transition.done;\n  }\n  const l = Listener;\n  const o = Owner;\n  return Promise.resolve().then(() => {\n    Listener = l;\n    Owner = o;\n    let t;\n    if (Scheduler || SuspenseContext) {\n      t = Transition || (Transition = {\n        sources: /* @__PURE__ */ new Set(),\n        effects: [],\n        promises: /* @__PURE__ */ new Set(),\n        disposed: /* @__PURE__ */ new Set(),\n        queue: /* @__PURE__ */ new Set(),\n        running: true\n      });\n      t.done || (t.done = new Promise((res) => t.resolve = res));\n      t.running = true;\n    }\n    runUpdates(fn, false);\n    Listener = Owner = null;\n    return t ? t.done : void 0;\n  });\n}\nvar [transPending, setTransPending] = /* @__PURE__ */ createSignal(false);\nfunction useTransition() {\n  return [transPending, startTransition];\n}\nfunction createContext(defaultValue, options) {\n  const id = Symbol(\"context\");\n  return {\n    id,\n    Provider: createProvider(id),\n    defaultValue\n  };\n}\nfunction useContext(context) {\n  let value;\n  return Owner && Owner.context && (value = Owner.context[context.id]) !== void 0 ? value : context.defaultValue;\n}\nfunction children(fn) {\n  const children2 = createMemo(fn);\n  const memo = createMemo(() => resolveChildren(children2()));\n  memo.toArray = () => {\n    const c = memo();\n    return Array.isArray(c) ? c : c != null ? [c] : [];\n  };\n  return memo;\n}\nvar SuspenseContext;\nfunction readSignal() {\n  const runningTransition = Transition && Transition.running;\n  if (this.sources && (runningTransition ? this.tState : this.state)) {\n    if ((runningTransition ? this.tState : this.state) === STALE) updateComputation(this);\n    else {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(this), false);\n      Updates = updates;\n    }\n  }\n  if (Listener) {\n    const sSlot = this.observers ? this.observers.length : 0;\n    if (!Listener.sources) {\n      Listener.sources = [this];\n      Listener.sourceSlots = [sSlot];\n    } else {\n      Listener.sources.push(this);\n      Listener.sourceSlots.push(sSlot);\n    }\n    if (!this.observers) {\n      this.observers = [Listener];\n      this.observerSlots = [Listener.sources.length - 1];\n    } else {\n      this.observers.push(Listener);\n      this.observerSlots.push(Listener.sources.length - 1);\n    }\n  }\n  if (runningTransition && Transition.sources.has(this)) return this.tValue;\n  return this.value;\n}\nfunction writeSignal(node, value, isComp) {\n  let current = Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value;\n  if (!node.comparator || !node.comparator(current, value)) {\n    if (Transition) {\n      const TransitionRunning = Transition.running;\n      if (TransitionRunning || !isComp && Transition.sources.has(node)) {\n        Transition.sources.add(node);\n        node.tValue = value;\n      }\n      if (!TransitionRunning) node.value = value;\n    } else node.value = value;\n    if (node.observers && node.observers.length) {\n      runUpdates(() => {\n        for (let i = 0; i < node.observers.length; i += 1) {\n          const o = node.observers[i];\n          const TransitionRunning = Transition && Transition.running;\n          if (TransitionRunning && Transition.disposed.has(o)) continue;\n          if (TransitionRunning ? !o.tState : !o.state) {\n            if (o.pure) Updates.push(o);\n            else Effects.push(o);\n            if (o.observers) markDownstream(o);\n          }\n          if (!TransitionRunning) o.state = STALE;\n          else o.tState = STALE;\n        }\n        if (Updates.length > 1e6) {\n          Updates = [];\n          if (IS_DEV) ;\n          throw new Error();\n        }\n      }, false);\n    }\n  }\n  return value;\n}\nfunction updateComputation(node) {\n  if (!node.fn) return;\n  cleanNode(node);\n  const time = ExecCount;\n  runComputation(\n    node,\n    Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value,\n    time\n  );\n  if (Transition && !Transition.running && Transition.sources.has(node)) {\n    queueMicrotask(() => {\n      runUpdates(() => {\n        Transition && (Transition.running = true);\n        Listener = Owner = node;\n        runComputation(node, node.tValue, time);\n        Listener = Owner = null;\n      }, false);\n    });\n  }\n}\nfunction runComputation(node, value, time) {\n  let nextValue;\n  const owner = Owner, listener = Listener;\n  Listener = Owner = node;\n  try {\n    nextValue = node.fn(value);\n  } catch (err) {\n    if (node.pure) {\n      if (Transition && Transition.running) {\n        node.tState = STALE;\n        node.tOwned && node.tOwned.forEach(cleanNode);\n        node.tOwned = void 0;\n      } else {\n        node.state = STALE;\n        node.owned && node.owned.forEach(cleanNode);\n        node.owned = null;\n      }\n    }\n    node.updatedAt = time + 1;\n    return handleError(err);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n  if (!node.updatedAt || node.updatedAt <= time) {\n    if (node.updatedAt != null && \"observers\" in node) {\n      writeSignal(node, nextValue, true);\n    } else if (Transition && Transition.running && node.pure) {\n      Transition.sources.add(node);\n      node.tValue = nextValue;\n    } else node.value = nextValue;\n    node.updatedAt = time;\n  }\n}\nfunction createComputation(fn, init, pure, state = STALE, options) {\n  const c = {\n    fn,\n    state,\n    updatedAt: null,\n    owned: null,\n    sources: null,\n    sourceSlots: null,\n    cleanups: null,\n    value: init,\n    owner: Owner,\n    context: Owner ? Owner.context : null,\n    pure\n  };\n  if (Transition && Transition.running) {\n    c.state = 0;\n    c.tState = state;\n  }\n  if (Owner === null) ;\n  else if (Owner !== UNOWNED) {\n    if (Transition && Transition.running && Owner.pure) {\n      if (!Owner.tOwned) Owner.tOwned = [c];\n      else Owner.tOwned.push(c);\n    } else {\n      if (!Owner.owned) Owner.owned = [c];\n      else Owner.owned.push(c);\n    }\n  }\n  if (ExternalSourceConfig && c.fn) {\n    const [track, trigger] = createSignal(void 0, {\n      equals: false\n    });\n    const ordinary = ExternalSourceConfig.factory(c.fn, trigger);\n    onCleanup(() => ordinary.dispose());\n    const triggerInTransition = () => startTransition(trigger).then(() => inTransition.dispose());\n    const inTransition = ExternalSourceConfig.factory(c.fn, triggerInTransition);\n    c.fn = (x) => {\n      track();\n      return Transition && Transition.running ? inTransition.track(x) : ordinary.track(x);\n    };\n  }\n  return c;\n}\nfunction runTop(node) {\n  const runningTransition = Transition && Transition.running;\n  if ((runningTransition ? node.tState : node.state) === 0) return;\n  if ((runningTransition ? node.tState : node.state) === PENDING) return lookUpstream(node);\n  if (node.suspense && untrack(node.suspense.inFallback)) return node.suspense.effects.push(node);\n  const ancestors = [node];\n  while ((node = node.owner) && (!node.updatedAt || node.updatedAt < ExecCount)) {\n    if (runningTransition && Transition.disposed.has(node)) return;\n    if (runningTransition ? node.tState : node.state) ancestors.push(node);\n  }\n  for (let i = ancestors.length - 1; i >= 0; i--) {\n    node = ancestors[i];\n    if (runningTransition) {\n      let top = node, prev = ancestors[i + 1];\n      while ((top = top.owner) && top !== prev) {\n        if (Transition.disposed.has(top)) return;\n      }\n    }\n    if ((runningTransition ? node.tState : node.state) === STALE) {\n      updateComputation(node);\n    } else if ((runningTransition ? node.tState : node.state) === PENDING) {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(node, ancestors[0]), false);\n      Updates = updates;\n    }\n  }\n}\nfunction runUpdates(fn, init) {\n  if (Updates) return fn();\n  let wait = false;\n  if (!init) Updates = [];\n  if (Effects) wait = true;\n  else Effects = [];\n  ExecCount++;\n  try {\n    const res = fn();\n    completeUpdates(wait);\n    return res;\n  } catch (err) {\n    if (!wait) Effects = null;\n    Updates = null;\n    handleError(err);\n  }\n}\nfunction completeUpdates(wait) {\n  if (Updates) {\n    if (Scheduler && Transition && Transition.running) scheduleQueue(Updates);\n    else runQueue(Updates);\n    Updates = null;\n  }\n  if (wait) return;\n  let res;\n  if (Transition) {\n    if (!Transition.promises.size && !Transition.queue.size) {\n      const sources = Transition.sources;\n      const disposed = Transition.disposed;\n      Effects.push.apply(Effects, Transition.effects);\n      res = Transition.resolve;\n      for (const e2 of Effects) {\n        \"tState\" in e2 && (e2.state = e2.tState);\n        delete e2.tState;\n      }\n      Transition = null;\n      runUpdates(() => {\n        for (const d of disposed) cleanNode(d);\n        for (const v of sources) {\n          v.value = v.tValue;\n          if (v.owned) {\n            for (let i = 0, len = v.owned.length; i < len; i++) cleanNode(v.owned[i]);\n          }\n          if (v.tOwned) v.owned = v.tOwned;\n          delete v.tValue;\n          delete v.tOwned;\n          v.tState = 0;\n        }\n        setTransPending(false);\n      }, false);\n    } else if (Transition.running) {\n      Transition.running = false;\n      Transition.effects.push.apply(Transition.effects, Effects);\n      Effects = null;\n      setTransPending(true);\n      return;\n    }\n  }\n  const e = Effects;\n  Effects = null;\n  if (e.length) runUpdates(() => runEffects(e), false);\n  if (res) res();\n}\nfunction runQueue(queue) {\n  for (let i = 0; i < queue.length; i++) runTop(queue[i]);\n}\nfunction scheduleQueue(queue) {\n  for (let i = 0; i < queue.length; i++) {\n    const item = queue[i];\n    const tasks = Transition.queue;\n    if (!tasks.has(item)) {\n      tasks.add(item);\n      Scheduler(() => {\n        tasks.delete(item);\n        runUpdates(() => {\n          Transition.running = true;\n          runTop(item);\n        }, false);\n        Transition && (Transition.running = false);\n      });\n    }\n  }\n}\nfunction runUserEffects(queue) {\n  let i, userLength = 0;\n  for (i = 0; i < queue.length; i++) {\n    const e = queue[i];\n    if (!e.user) runTop(e);\n    else queue[userLength++] = e;\n  }\n  if (sharedConfig.context) {\n    if (sharedConfig.count) {\n      sharedConfig.effects || (sharedConfig.effects = []);\n      sharedConfig.effects.push(...queue.slice(0, userLength));\n      return;\n    }\n    setHydrateContext();\n  }\n  if (sharedConfig.effects && (sharedConfig.done || !sharedConfig.count)) {\n    queue = [...sharedConfig.effects, ...queue];\n    userLength += sharedConfig.effects.length;\n    delete sharedConfig.effects;\n  }\n  for (i = 0; i < userLength; i++) runTop(queue[i]);\n}\nfunction lookUpstream(node, ignore) {\n  const runningTransition = Transition && Transition.running;\n  if (runningTransition) node.tState = 0;\n  else node.state = 0;\n  for (let i = 0; i < node.sources.length; i += 1) {\n    const source = node.sources[i];\n    if (source.sources) {\n      const state = runningTransition ? source.tState : source.state;\n      if (state === STALE) {\n        if (source !== ignore && (!source.updatedAt || source.updatedAt < ExecCount))\n          runTop(source);\n      } else if (state === PENDING) lookUpstream(source, ignore);\n    }\n  }\n}\nfunction markDownstream(node) {\n  const runningTransition = Transition && Transition.running;\n  for (let i = 0; i < node.observers.length; i += 1) {\n    const o = node.observers[i];\n    if (runningTransition ? !o.tState : !o.state) {\n      if (runningTransition) o.tState = PENDING;\n      else o.state = PENDING;\n      if (o.pure) Updates.push(o);\n      else Effects.push(o);\n      o.observers && markDownstream(o);\n    }\n  }\n}\nfunction cleanNode(node) {\n  let i;\n  if (node.sources) {\n    while (node.sources.length) {\n      const source = node.sources.pop(), index = node.sourceSlots.pop(), obs = source.observers;\n      if (obs && obs.length) {\n        const n = obs.pop(), s = source.observerSlots.pop();\n        if (index < obs.length) {\n          n.sourceSlots[s] = index;\n          obs[index] = n;\n          source.observerSlots[index] = s;\n        }\n      }\n    }\n  }\n  if (node.tOwned) {\n    for (i = node.tOwned.length - 1; i >= 0; i--) cleanNode(node.tOwned[i]);\n    delete node.tOwned;\n  }\n  if (Transition && Transition.running && node.pure) {\n    reset(node, true);\n  } else if (node.owned) {\n    for (i = node.owned.length - 1; i >= 0; i--) cleanNode(node.owned[i]);\n    node.owned = null;\n  }\n  if (node.cleanups) {\n    for (i = node.cleanups.length - 1; i >= 0; i--) node.cleanups[i]();\n    node.cleanups = null;\n  }\n  if (Transition && Transition.running) node.tState = 0;\n  else node.state = 0;\n}\nfunction reset(node, top) {\n  if (!top) {\n    node.tState = 0;\n    Transition.disposed.add(node);\n  }\n  if (node.owned) {\n    for (let i = 0; i < node.owned.length; i++) reset(node.owned[i]);\n  }\n}\nfunction castError(err) {\n  if (err instanceof Error) return err;\n  return new Error(typeof err === \"string\" ? err : \"Unknown error\", {\n    cause: err\n  });\n}\nfunction runErrors(err, fns, owner) {\n  try {\n    for (const f of fns) f(err);\n  } catch (e) {\n    handleError(e, owner && owner.owner || null);\n  }\n}\nfunction handleError(err, owner = Owner) {\n  const fns = ERROR && owner && owner.context && owner.context[ERROR];\n  const error = castError(err);\n  if (!fns) throw error;\n  if (Effects)\n    Effects.push({\n      fn() {\n        runErrors(error, fns, owner);\n      },\n      state: STALE\n    });\n  else runErrors(error, fns, owner);\n}\nfunction resolveChildren(children2) {\n  if (typeof children2 === \"function\" && !children2.length) return resolveChildren(children2());\n  if (Array.isArray(children2)) {\n    const results = [];\n    for (let i = 0; i < children2.length; i++) {\n      const result = resolveChildren(children2[i]);\n      Array.isArray(result) ? results.push.apply(results, result) : results.push(result);\n    }\n    return results;\n  }\n  return children2;\n}\nfunction createProvider(id, options) {\n  return function provider(props) {\n    let res;\n    createRenderEffect(\n      () => res = untrack(() => {\n        Owner.context = {\n          ...Owner.context,\n          [id]: props.value\n        };\n        return children(() => props.children);\n      }),\n      void 0\n    );\n    return res;\n  };\n}\nvar FALLBACK = Symbol(\"fallback\");\nfunction dispose(d) {\n  for (let i = 0; i < d.length; i++) d[i]();\n}\nfunction mapArray(list, mapFn, options = {}) {\n  let items = [], mapped = [], disposers = [], len = 0, indexes = mapFn.length > 1 ? [] : null;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    let newItems = list() || [], newLen = newItems.length, i, j;\n    newItems[$TRACK];\n    return untrack(() => {\n      let newIndices, newIndicesNext, temp, tempdisposers, tempIndexes, start, end, newEnd, item;\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          indexes && (indexes = []);\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot((disposer) => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n      } else if (len === 0) {\n        mapped = new Array(newLen);\n        for (j = 0; j < newLen; j++) {\n          items[j] = newItems[j];\n          mapped[j] = createRoot(mapper);\n        }\n        len = newLen;\n      } else {\n        temp = new Array(newLen);\n        tempdisposers = new Array(newLen);\n        indexes && (tempIndexes = new Array(newLen));\n        for (start = 0, end = Math.min(len, newLen); start < end && items[start] === newItems[start]; start++) ;\n        for (end = len - 1, newEnd = newLen - 1; end >= start && newEnd >= start && items[end] === newItems[newEnd]; end--, newEnd--) {\n          temp[newEnd] = mapped[end];\n          tempdisposers[newEnd] = disposers[end];\n          indexes && (tempIndexes[newEnd] = indexes[end]);\n        }\n        newIndices = /* @__PURE__ */ new Map();\n        newIndicesNext = new Array(newEnd + 1);\n        for (j = newEnd; j >= start; j--) {\n          item = newItems[j];\n          i = newIndices.get(item);\n          newIndicesNext[j] = i === void 0 ? -1 : i;\n          newIndices.set(item, j);\n        }\n        for (i = start; i <= end; i++) {\n          item = items[i];\n          j = newIndices.get(item);\n          if (j !== void 0 && j !== -1) {\n            temp[j] = mapped[i];\n            tempdisposers[j] = disposers[i];\n            indexes && (tempIndexes[j] = indexes[i]);\n            j = newIndicesNext[j];\n            newIndices.set(item, j);\n          } else disposers[i]();\n        }\n        for (j = start; j < newLen; j++) {\n          if (j in temp) {\n            mapped[j] = temp[j];\n            disposers[j] = tempdisposers[j];\n            if (indexes) {\n              indexes[j] = tempIndexes[j];\n              indexes[j](j);\n            }\n          } else mapped[j] = createRoot(mapper);\n        }\n        mapped = mapped.slice(0, len = newLen);\n        items = newItems.slice(0);\n      }\n      return mapped;\n    });\n    function mapper(disposer) {\n      disposers[j] = disposer;\n      if (indexes) {\n        const [s, set] = createSignal(j);\n        indexes[j] = set;\n        return mapFn(newItems[j], s);\n      }\n      return mapFn(newItems[j]);\n    }\n  };\n}\nfunction indexArray(list, mapFn, options = {}) {\n  let items = [], mapped = [], disposers = [], signals = [], len = 0, i;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    const newItems = list() || [], newLen = newItems.length;\n    newItems[$TRACK];\n    return untrack(() => {\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          signals = [];\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot((disposer) => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n        return mapped;\n      }\n      if (items[0] === FALLBACK) {\n        disposers[0]();\n        disposers = [];\n        items = [];\n        mapped = [];\n        len = 0;\n      }\n      for (i = 0; i < newLen; i++) {\n        if (i < items.length && items[i] !== newItems[i]) {\n          signals[i](() => newItems[i]);\n        } else if (i >= items.length) {\n          mapped[i] = createRoot(mapper);\n        }\n      }\n      for (; i < items.length; i++) {\n        disposers[i]();\n      }\n      len = signals.length = disposers.length = newLen;\n      items = newItems.slice(0);\n      return mapped = mapped.slice(0, len);\n    });\n    function mapper(disposer) {\n      disposers[i] = disposer;\n      const [s, set] = createSignal(newItems[i]);\n      signals[i] = set;\n      return mapFn(s, i);\n    }\n  };\n}\nvar hydrationEnabled = false;\nfunction createComponent(Comp, props) {\n  if (hydrationEnabled) {\n    if (sharedConfig.context) {\n      const c = sharedConfig.context;\n      setHydrateContext(nextHydrateContext());\n      const r = untrack(() => Comp(props || {}));\n      setHydrateContext(c);\n      return r;\n    }\n  }\n  return untrack(() => Comp(props || {}));\n}\nfunction trueFn() {\n  return true;\n}\nvar propTraps = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver;\n    return _.get(property);\n  },\n  has(_, property) {\n    if (property === $PROXY) return true;\n    return _.has(property);\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property);\n      },\n      set: trueFn,\n      deleteProperty: trueFn\n    };\n  },\n  ownKeys(_) {\n    return _.keys();\n  }\n};\nfunction resolveSource(s) {\n  return !(s = typeof s === \"function\" ? s() : s) ? {} : s;\n}\nfunction resolveSources() {\n  for (let i = 0, length = this.length; i < length; ++i) {\n    const v = this[i]();\n    if (v !== void 0) return v;\n  }\n}\nfunction mergeProps(...sources) {\n  let proxy = false;\n  for (let i = 0; i < sources.length; i++) {\n    const s = sources[i];\n    proxy = proxy || !!s && $PROXY in s;\n    sources[i] = typeof s === \"function\" ? (proxy = true, createMemo(s)) : s;\n  }\n  if (SUPPORTS_PROXY && proxy) {\n    return new Proxy(\n      {\n        get(property) {\n          for (let i = sources.length - 1; i >= 0; i--) {\n            const v = resolveSource(sources[i])[property];\n            if (v !== void 0) return v;\n          }\n        },\n        has(property) {\n          for (let i = sources.length - 1; i >= 0; i--) {\n            if (property in resolveSource(sources[i])) return true;\n          }\n          return false;\n        },\n        keys() {\n          const keys = [];\n          for (let i = 0; i < sources.length; i++)\n            keys.push(...Object.keys(resolveSource(sources[i])));\n          return [...new Set(keys)];\n        }\n      },\n      propTraps\n    );\n  }\n  const sourcesMap = {};\n  const defined = /* @__PURE__ */ Object.create(null);\n  for (let i = sources.length - 1; i >= 0; i--) {\n    const source = sources[i];\n    if (!source) continue;\n    const sourceKeys = Object.getOwnPropertyNames(source);\n    for (let i2 = sourceKeys.length - 1; i2 >= 0; i2--) {\n      const key = sourceKeys[i2];\n      if (key === \"__proto__\" || key === \"constructor\") continue;\n      const desc = Object.getOwnPropertyDescriptor(source, key);\n      if (!defined[key]) {\n        defined[key] = desc.get ? {\n          enumerable: true,\n          configurable: true,\n          get: resolveSources.bind(sourcesMap[key] = [desc.get.bind(source)])\n        } : desc.value !== void 0 ? desc : void 0;\n      } else {\n        const sources2 = sourcesMap[key];\n        if (sources2) {\n          if (desc.get) sources2.push(desc.get.bind(source));\n          else if (desc.value !== void 0) sources2.push(() => desc.value);\n        }\n      }\n    }\n  }\n  const target = {};\n  const definedKeys = Object.keys(defined);\n  for (let i = definedKeys.length - 1; i >= 0; i--) {\n    const key = definedKeys[i], desc = defined[key];\n    if (desc && desc.get) Object.defineProperty(target, key, desc);\n    else target[key] = desc ? desc.value : void 0;\n  }\n  return target;\n}\nfunction splitProps(props, ...keys) {\n  if (SUPPORTS_PROXY && $PROXY in props) {\n    const blocked = new Set(keys.length > 1 ? keys.flat() : keys[0]);\n    const res = keys.map((k) => {\n      return new Proxy(\n        {\n          get(property) {\n            return k.includes(property) ? props[property] : void 0;\n          },\n          has(property) {\n            return k.includes(property) && property in props;\n          },\n          keys() {\n            return k.filter((property) => property in props);\n          }\n        },\n        propTraps\n      );\n    });\n    res.push(\n      new Proxy(\n        {\n          get(property) {\n            return blocked.has(property) ? void 0 : props[property];\n          },\n          has(property) {\n            return blocked.has(property) ? false : property in props;\n          },\n          keys() {\n            return Object.keys(props).filter((k) => !blocked.has(k));\n          }\n        },\n        propTraps\n      )\n    );\n    return res;\n  }\n  const otherObject = {};\n  const objects = keys.map(() => ({}));\n  for (const propName of Object.getOwnPropertyNames(props)) {\n    const desc = Object.getOwnPropertyDescriptor(props, propName);\n    const isDefaultDesc = !desc.get && !desc.set && desc.enumerable && desc.writable && desc.configurable;\n    let blocked = false;\n    let objectIndex = 0;\n    for (const k of keys) {\n      if (k.includes(propName)) {\n        blocked = true;\n        isDefaultDesc ? objects[objectIndex][propName] = desc.value : Object.defineProperty(objects[objectIndex], propName, desc);\n      }\n      ++objectIndex;\n    }\n    if (!blocked) {\n      isDefaultDesc ? otherObject[propName] = desc.value : Object.defineProperty(otherObject, propName, desc);\n    }\n  }\n  return [...objects, otherObject];\n}\nfunction lazy(fn) {\n  let comp;\n  let p;\n  const wrap = (props) => {\n    const ctx = sharedConfig.context;\n    if (ctx) {\n      const [s, set] = createSignal();\n      sharedConfig.count || (sharedConfig.count = 0);\n      sharedConfig.count++;\n      (p || (p = fn())).then((mod) => {\n        !sharedConfig.done && setHydrateContext(ctx);\n        sharedConfig.count--;\n        set(() => mod.default);\n        setHydrateContext();\n      });\n      comp = s;\n    } else if (!comp) {\n      const [s] = createResource(() => (p || (p = fn())).then((mod) => mod.default));\n      comp = s;\n    }\n    let Comp;\n    return createMemo(\n      () => (Comp = comp()) ? untrack(() => {\n        if (IS_DEV) ;\n        if (!ctx || sharedConfig.done) return Comp(props);\n        const c = sharedConfig.context;\n        setHydrateContext(ctx);\n        const r = Comp(props);\n        setHydrateContext(c);\n        return r;\n      }) : \"\"\n    );\n  };\n  wrap.preload = () => p || ((p = fn()).then((mod) => comp = () => mod.default), p);\n  return wrap;\n}\nvar counter = 0;\nfunction createUniqueId() {\n  const ctx = sharedConfig.context;\n  return ctx ? sharedConfig.getNextContextId() : `cl-${counter++}`;\n}\nvar narrowedError = (name) => `Stale read from <${name}>.`;\nfunction For(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(mapArray(() => props.each, props.children, fallback || void 0));\n}\nfunction Index(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(indexArray(() => props.each, props.children, fallback || void 0));\n}\nfunction Show(props) {\n  const keyed = props.keyed;\n  const conditionValue = createMemo(() => props.when, void 0, void 0);\n  const condition = keyed ? conditionValue : createMemo(conditionValue, void 0, {\n    equals: (a, b) => !a === !b\n  });\n  return createMemo(\n    () => {\n      const c = condition();\n      if (c) {\n        const child = props.children;\n        const fn = typeof child === \"function\" && child.length > 0;\n        return fn ? untrack(\n          () => child(\n            keyed ? c : () => {\n              if (!untrack(condition)) throw narrowedError(\"Show\");\n              return conditionValue();\n            }\n          )\n        ) : child;\n      }\n      return props.fallback;\n    },\n    void 0,\n    void 0\n  );\n}\nfunction Switch(props) {\n  const chs = children(() => props.children);\n  const switchFunc = createMemo(() => {\n    const ch = chs();\n    const mps = Array.isArray(ch) ? ch : [ch];\n    let func = () => void 0;\n    for (let i = 0; i < mps.length; i++) {\n      const index = i;\n      const mp = mps[i];\n      const prevFunc = func;\n      const conditionValue = createMemo(\n        () => prevFunc() ? void 0 : mp.when,\n        void 0,\n        void 0\n      );\n      const condition = mp.keyed ? conditionValue : createMemo(conditionValue, void 0, {\n        equals: (a, b) => !a === !b\n      });\n      func = () => prevFunc() || (condition() ? [index, conditionValue, mp] : void 0);\n    }\n    return func;\n  });\n  return createMemo(\n    () => {\n      const sel = switchFunc()();\n      if (!sel) return props.fallback;\n      const [index, conditionValue, mp] = sel;\n      const child = mp.children;\n      const fn = typeof child === \"function\" && child.length > 0;\n      return fn ? untrack(\n        () => child(\n          mp.keyed ? conditionValue() : () => {\n            if (untrack(switchFunc)()?.[0] !== index) throw narrowedError(\"Match\");\n            return conditionValue();\n          }\n        )\n      ) : child;\n    },\n    void 0,\n    void 0\n  );\n}\nfunction Match(props) {\n  return props;\n}\nvar DEV = void 0;\n\n// ../../node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/web/dist/web.js\nvar booleans = [\n  \"allowfullscreen\",\n  \"async\",\n  \"autofocus\",\n  \"autoplay\",\n  \"checked\",\n  \"controls\",\n  \"default\",\n  \"disabled\",\n  \"formnovalidate\",\n  \"hidden\",\n  \"indeterminate\",\n  \"inert\",\n  \"ismap\",\n  \"loop\",\n  \"multiple\",\n  \"muted\",\n  \"nomodule\",\n  \"novalidate\",\n  \"open\",\n  \"playsinline\",\n  \"readonly\",\n  \"required\",\n  \"reversed\",\n  \"seamless\",\n  \"selected\"\n];\nvar Properties = /* @__PURE__ */ new Set([\n  \"className\",\n  \"value\",\n  \"readOnly\",\n  \"formNoValidate\",\n  \"isMap\",\n  \"noModule\",\n  \"playsInline\",\n  ...booleans\n]);\nvar ChildProperties = /* @__PURE__ */ new Set([\n  \"innerHTML\",\n  \"textContent\",\n  \"innerText\",\n  \"children\"\n]);\nvar Aliases = /* @__PURE__ */ Object.assign(/* @__PURE__ */ Object.create(null), {\n  className: \"class\",\n  htmlFor: \"for\"\n});\nvar PropAliases = /* @__PURE__ */ Object.assign(/* @__PURE__ */ Object.create(null), {\n  class: \"className\",\n  formnovalidate: {\n    $: \"formNoValidate\",\n    BUTTON: 1,\n    INPUT: 1\n  },\n  ismap: {\n    $: \"isMap\",\n    IMG: 1\n  },\n  nomodule: {\n    $: \"noModule\",\n    SCRIPT: 1\n  },\n  playsinline: {\n    $: \"playsInline\",\n    VIDEO: 1\n  },\n  readonly: {\n    $: \"readOnly\",\n    INPUT: 1,\n    TEXTAREA: 1\n  }\n});\nfunction getPropAlias(prop, tagName) {\n  const a = PropAliases[prop];\n  return typeof a === \"object\" ? a[tagName] ? a[\"$\"] : void 0 : a;\n}\nvar DelegatedEvents = /* @__PURE__ */ new Set([\n  \"beforeinput\",\n  \"click\",\n  \"dblclick\",\n  \"contextmenu\",\n  \"focusin\",\n  \"focusout\",\n  \"input\",\n  \"keydown\",\n  \"keyup\",\n  \"mousedown\",\n  \"mousemove\",\n  \"mouseout\",\n  \"mouseover\",\n  \"mouseup\",\n  \"pointerdown\",\n  \"pointermove\",\n  \"pointerout\",\n  \"pointerover\",\n  \"pointerup\",\n  \"touchend\",\n  \"touchmove\",\n  \"touchstart\"\n]);\nvar SVGElements = /* @__PURE__ */ new Set([\n  \"altGlyph\",\n  \"altGlyphDef\",\n  \"altGlyphItem\",\n  \"animate\",\n  \"animateColor\",\n  \"animateMotion\",\n  \"animateTransform\",\n  \"circle\",\n  \"clipPath\",\n  \"color-profile\",\n  \"cursor\",\n  \"defs\",\n  \"desc\",\n  \"ellipse\",\n  \"feBlend\",\n  \"feColorMatrix\",\n  \"feComponentTransfer\",\n  \"feComposite\",\n  \"feConvolveMatrix\",\n  \"feDiffuseLighting\",\n  \"feDisplacementMap\",\n  \"feDistantLight\",\n  \"feDropShadow\",\n  \"feFlood\",\n  \"feFuncA\",\n  \"feFuncB\",\n  \"feFuncG\",\n  \"feFuncR\",\n  \"feGaussianBlur\",\n  \"feImage\",\n  \"feMerge\",\n  \"feMergeNode\",\n  \"feMorphology\",\n  \"feOffset\",\n  \"fePointLight\",\n  \"feSpecularLighting\",\n  \"feSpotLight\",\n  \"feTile\",\n  \"feTurbulence\",\n  \"filter\",\n  \"font\",\n  \"font-face\",\n  \"font-face-format\",\n  \"font-face-name\",\n  \"font-face-src\",\n  \"font-face-uri\",\n  \"foreignObject\",\n  \"g\",\n  \"glyph\",\n  \"glyphRef\",\n  \"hkern\",\n  \"image\",\n  \"line\",\n  \"linearGradient\",\n  \"marker\",\n  \"mask\",\n  \"metadata\",\n  \"missing-glyph\",\n  \"mpath\",\n  \"path\",\n  \"pattern\",\n  \"polygon\",\n  \"polyline\",\n  \"radialGradient\",\n  \"rect\",\n  \"set\",\n  \"stop\",\n  \"svg\",\n  \"switch\",\n  \"symbol\",\n  \"text\",\n  \"textPath\",\n  \"tref\",\n  \"tspan\",\n  \"use\",\n  \"view\",\n  \"vkern\"\n]);\nvar SVGNamespace = {\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\"\n};\nfunction reconcileArrays(parentNode, a, b) {\n  let bLength = b.length, aEnd = a.length, bEnd = bLength, aStart = 0, bStart = 0, after = a[aEnd - 1].nextSibling, map = null;\n  while (aStart < aEnd || bStart < bEnd) {\n    if (a[aStart] === b[bStart]) {\n      aStart++;\n      bStart++;\n      continue;\n    }\n    while (a[aEnd - 1] === b[bEnd - 1]) {\n      aEnd--;\n      bEnd--;\n    }\n    if (aEnd === aStart) {\n      const node = bEnd < bLength ? bStart ? b[bStart - 1].nextSibling : b[bEnd - bStart] : after;\n      while (bStart < bEnd) parentNode.insertBefore(b[bStart++], node);\n    } else if (bEnd === bStart) {\n      while (aStart < aEnd) {\n        if (!map || !map.has(a[aStart])) a[aStart].remove();\n        aStart++;\n      }\n    } else if (a[aStart] === b[bEnd - 1] && b[bStart] === a[aEnd - 1]) {\n      const node = a[--aEnd].nextSibling;\n      parentNode.insertBefore(b[bStart++], a[aStart++].nextSibling);\n      parentNode.insertBefore(b[--bEnd], node);\n      a[aEnd] = b[bEnd];\n    } else {\n      if (!map) {\n        map = /* @__PURE__ */ new Map();\n        let i = bStart;\n        while (i < bEnd) map.set(b[i], i++);\n      }\n      const index = map.get(a[aStart]);\n      if (index != null) {\n        if (bStart < index && index < bEnd) {\n          let i = aStart, sequence = 1, t;\n          while (++i < aEnd && i < bEnd) {\n            if ((t = map.get(a[i])) == null || t !== index + sequence) break;\n            sequence++;\n          }\n          if (sequence > index - bStart) {\n            const node = a[aStart];\n            while (bStart < index) parentNode.insertBefore(b[bStart++], node);\n          } else parentNode.replaceChild(b[bStart++], a[aStart++]);\n        } else aStart++;\n      } else a[aStart++].remove();\n    }\n  }\n}\nvar $$EVENTS = \"_$DX_DELEGATE\";\nfunction render(code, element, init, options = {}) {\n  let disposer;\n  createRoot((dispose2) => {\n    disposer = dispose2;\n    element === document ? code() : insert(element, code(), element.firstChild ? null : void 0, init);\n  }, options.owner);\n  return () => {\n    disposer();\n    element.textContent = \"\";\n  };\n}\nfunction template(html, isImportNode, isSVG, isMathML) {\n  let node;\n  const create = () => {\n    const t = isMathML ? document.createElementNS(\"http://www.w3.org/1998/Math/MathML\", \"template\") : document.createElement(\"template\");\n    t.innerHTML = html;\n    return isSVG ? t.content.firstChild.firstChild : isMathML ? t.firstChild : t.content.firstChild;\n  };\n  const fn = isImportNode ? () => untrack(() => document.importNode(node || (node = create()), true)) : () => (node || (node = create())).cloneNode(true);\n  fn.cloneNode = fn;\n  return fn;\n}\nfunction delegateEvents(eventNames, document2 = window.document) {\n  const e = document2[$$EVENTS] || (document2[$$EVENTS] = /* @__PURE__ */ new Set());\n  for (let i = 0, l = eventNames.length; i < l; i++) {\n    const name = eventNames[i];\n    if (!e.has(name)) {\n      e.add(name);\n      document2.addEventListener(name, eventHandler);\n    }\n  }\n}\nfunction clearDelegatedEvents(document2 = window.document) {\n  if (document2[$$EVENTS]) {\n    for (let name of document2[$$EVENTS].keys()) document2.removeEventListener(name, eventHandler);\n    delete document2[$$EVENTS];\n  }\n}\nfunction setAttribute(node, name, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttribute(name);\n  else node.setAttribute(name, value);\n}\nfunction setAttributeNS(node, namespace, name, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttributeNS(namespace, name);\n  else node.setAttributeNS(namespace, name, value);\n}\nfunction setBoolAttribute(node, name, value) {\n  if (isHydrating(node)) return;\n  value ? node.setAttribute(name, \"\") : node.removeAttribute(name);\n}\nfunction className(node, value) {\n  if (isHydrating(node)) return;\n  if (value == null) node.removeAttribute(\"class\");\n  else node.className = value;\n}\nfunction addEventListener(node, name, handler, delegate) {\n  if (delegate) {\n    if (Array.isArray(handler)) {\n      node[`$$${name}`] = handler[0];\n      node[`$$${name}Data`] = handler[1];\n    } else node[`$$${name}`] = handler;\n  } else if (Array.isArray(handler)) {\n    const handlerFn = handler[0];\n    node.addEventListener(name, handler[0] = (e) => handlerFn.call(node, handler[1], e));\n  } else node.addEventListener(name, handler, typeof handler !== \"function\" && handler);\n}\nfunction classList(node, value, prev = {}) {\n  const classKeys = Object.keys(value || {}), prevKeys = Object.keys(prev);\n  let i, len;\n  for (i = 0, len = prevKeys.length; i < len; i++) {\n    const key = prevKeys[i];\n    if (!key || key === \"undefined\" || value[key]) continue;\n    toggleClassKey(node, key, false);\n    delete prev[key];\n  }\n  for (i = 0, len = classKeys.length; i < len; i++) {\n    const key = classKeys[i], classValue = !!value[key];\n    if (!key || key === \"undefined\" || prev[key] === classValue || !classValue) continue;\n    toggleClassKey(node, key, true);\n    prev[key] = classValue;\n  }\n  return prev;\n}\nfunction style(node, value, prev) {\n  if (!value) return prev ? setAttribute(node, \"style\") : value;\n  const nodeStyle = node.style;\n  if (typeof value === \"string\") return nodeStyle.cssText = value;\n  typeof prev === \"string\" && (nodeStyle.cssText = prev = void 0);\n  prev || (prev = {});\n  value || (value = {});\n  let v, s;\n  for (s in prev) {\n    value[s] == null && nodeStyle.removeProperty(s);\n    delete prev[s];\n  }\n  for (s in value) {\n    v = value[s];\n    if (v !== prev[s]) {\n      nodeStyle.setProperty(s, v);\n      prev[s] = v;\n    }\n  }\n  return prev;\n}\nfunction spread(node, props = {}, isSVG, skipChildren) {\n  const prevProps = {};\n  if (!skipChildren) {\n    createRenderEffect(\n      () => prevProps.children = insertExpression(node, props.children, prevProps.children)\n    );\n  }\n  createRenderEffect(() => typeof props.ref === \"function\" && use(props.ref, node));\n  createRenderEffect(() => assign(node, props, isSVG, true, prevProps, true));\n  return prevProps;\n}\nfunction use(fn, element, arg) {\n  return untrack(() => fn(element, arg));\n}\nfunction insert(parent, accessor, marker, initial) {\n  if (marker !== void 0 && !initial) initial = [];\n  if (typeof accessor !== \"function\") return insertExpression(parent, accessor, initial, marker);\n  createRenderEffect((current) => insertExpression(parent, accessor(), current, marker), initial);\n}\nfunction assign(node, props, isSVG, skipChildren, prevProps = {}, skipRef = false) {\n  props || (props = {});\n  for (const prop in prevProps) {\n    if (!(prop in props)) {\n      if (prop === \"children\") continue;\n      prevProps[prop] = assignProp(node, prop, null, prevProps[prop], isSVG, skipRef, props);\n    }\n  }\n  for (const prop in props) {\n    if (prop === \"children\") {\n      continue;\n    }\n    const value = props[prop];\n    prevProps[prop] = assignProp(node, prop, value, prevProps[prop], isSVG, skipRef, props);\n  }\n}\nfunction getNextElement(template2) {\n  let node, key, hydrating = isHydrating();\n  if (!hydrating || !(node = sharedConfig.registry.get(key = getHydrationKey()))) {\n    return template2();\n  }\n  if (sharedConfig.completed) sharedConfig.completed.add(node);\n  sharedConfig.registry.delete(key);\n  return node;\n}\nfunction isHydrating(node) {\n  return !!sharedConfig.context && !sharedConfig.done && (!node || node.isConnected);\n}\nfunction toPropertyName(name) {\n  return name.toLowerCase().replace(/-([a-z])/g, (_, w) => w.toUpperCase());\n}\nfunction toggleClassKey(node, key, value) {\n  const classNames = key.trim().split(/\\s+/);\n  for (let i = 0, nameLen = classNames.length; i < nameLen; i++)\n    node.classList.toggle(classNames[i], value);\n}\nfunction assignProp(node, prop, value, prev, isSVG, skipRef, props) {\n  let isCE, isProp, isChildProp, propAlias, forceProp;\n  if (prop === \"style\") return style(node, value, prev);\n  if (prop === \"classList\") return classList(node, value, prev);\n  if (value === prev) return prev;\n  if (prop === \"ref\") {\n    if (!skipRef) value(node);\n  } else if (prop.slice(0, 3) === \"on:\") {\n    const e = prop.slice(3);\n    prev && node.removeEventListener(e, prev, typeof prev !== \"function\" && prev);\n    value && node.addEventListener(e, value, typeof value !== \"function\" && value);\n  } else if (prop.slice(0, 10) === \"oncapture:\") {\n    const e = prop.slice(10);\n    prev && node.removeEventListener(e, prev, true);\n    value && node.addEventListener(e, value, true);\n  } else if (prop.slice(0, 2) === \"on\") {\n    const name = prop.slice(2).toLowerCase();\n    const delegate = DelegatedEvents.has(name);\n    if (!delegate && prev) {\n      const h = Array.isArray(prev) ? prev[0] : prev;\n      node.removeEventListener(name, h);\n    }\n    if (delegate || value) {\n      addEventListener(node, name, value, delegate);\n      delegate && delegateEvents([name]);\n    }\n  } else if (prop.slice(0, 5) === \"attr:\") {\n    setAttribute(node, prop.slice(5), value);\n  } else if (prop.slice(0, 5) === \"bool:\") {\n    setBoolAttribute(node, prop.slice(5), value);\n  } else if ((forceProp = prop.slice(0, 5) === \"prop:\") || (isChildProp = ChildProperties.has(prop)) || !isSVG && ((propAlias = getPropAlias(prop, node.tagName)) || (isProp = Properties.has(prop))) || (isCE = node.nodeName.includes(\"-\") || \"is\" in props)) {\n    if (forceProp) {\n      prop = prop.slice(5);\n      isProp = true;\n    } else if (isHydrating(node)) return value;\n    if (prop === \"class\" || prop === \"className\") className(node, value);\n    else if (isCE && !isProp && !isChildProp) node[toPropertyName(prop)] = value;\n    else node[propAlias || prop] = value;\n  } else {\n    const ns = isSVG && prop.indexOf(\":\") > -1 && SVGNamespace[prop.split(\":\")[0]];\n    if (ns) setAttributeNS(node, ns, prop, value);\n    else setAttribute(node, Aliases[prop] || prop, value);\n  }\n  return value;\n}\nfunction eventHandler(e) {\n  if (sharedConfig.registry && sharedConfig.events) {\n    if (sharedConfig.events.find(([el, ev]) => ev === e)) return;\n  }\n  let node = e.target;\n  const key = `$$${e.type}`;\n  const oriTarget = e.target;\n  const oriCurrentTarget = e.currentTarget;\n  const retarget = (value) => Object.defineProperty(e, \"target\", {\n    configurable: true,\n    value\n  });\n  const handleNode = () => {\n    const handler = node[key];\n    if (handler && !node.disabled) {\n      const data = node[`${key}Data`];\n      data !== void 0 ? handler.call(node, data, e) : handler.call(node, e);\n      if (e.cancelBubble) return;\n    }\n    node.host && typeof node.host !== \"string\" && !node.host._$host && node.contains(e.target) && retarget(node.host);\n    return true;\n  };\n  const walkUpTree = () => {\n    while (handleNode() && (node = node._$host || node.parentNode || node.host)) ;\n  };\n  Object.defineProperty(e, \"currentTarget\", {\n    configurable: true,\n    get() {\n      return node || document;\n    }\n  });\n  if (sharedConfig.registry && !sharedConfig.done) sharedConfig.done = _$HY.done = true;\n  if (e.composedPath) {\n    const path = e.composedPath();\n    retarget(path[0]);\n    for (let i = 0; i < path.length - 2; i++) {\n      node = path[i];\n      if (!handleNode()) break;\n      if (node._$host) {\n        node = node._$host;\n        walkUpTree();\n        break;\n      }\n      if (node.parentNode === oriCurrentTarget) {\n        break;\n      }\n    }\n  } else walkUpTree();\n  retarget(oriTarget);\n}\nfunction insertExpression(parent, value, current, marker, unwrapArray) {\n  const hydrating = isHydrating(parent);\n  if (hydrating) {\n    !current && (current = [...parent.childNodes]);\n    let cleaned = [];\n    for (let i = 0; i < current.length; i++) {\n      const node = current[i];\n      if (node.nodeType === 8 && node.data.slice(0, 2) === \"!$\") node.remove();\n      else cleaned.push(node);\n    }\n    current = cleaned;\n  }\n  while (typeof current === \"function\") current = current();\n  if (value === current) return current;\n  const t = typeof value, multi = marker !== void 0;\n  parent = multi && current[0] && current[0].parentNode || parent;\n  if (t === \"string\" || t === \"number\") {\n    if (hydrating) return current;\n    if (t === \"number\") {\n      value = value.toString();\n      if (value === current) return current;\n    }\n    if (multi) {\n      let node = current[0];\n      if (node && node.nodeType === 3) {\n        node.data !== value && (node.data = value);\n      } else node = document.createTextNode(value);\n      current = cleanChildren(parent, current, marker, node);\n    } else {\n      if (current !== \"\" && typeof current === \"string\") {\n        current = parent.firstChild.data = value;\n      } else current = parent.textContent = value;\n    }\n  } else if (value == null || t === \"boolean\") {\n    if (hydrating) return current;\n    current = cleanChildren(parent, current, marker);\n  } else if (t === \"function\") {\n    createRenderEffect(() => {\n      let v = value();\n      while (typeof v === \"function\") v = v();\n      current = insertExpression(parent, v, current, marker);\n    });\n    return () => current;\n  } else if (Array.isArray(value)) {\n    const array = [];\n    const currentArray = current && Array.isArray(current);\n    if (normalizeIncomingArray(array, value, current, unwrapArray)) {\n      createRenderEffect(() => current = insertExpression(parent, array, current, marker, true));\n      return () => current;\n    }\n    if (hydrating) {\n      if (!array.length) return current;\n      if (marker === void 0) return current = [...parent.childNodes];\n      let node = array[0];\n      if (node.parentNode !== parent) return current;\n      const nodes = [node];\n      while ((node = node.nextSibling) !== marker) nodes.push(node);\n      return current = nodes;\n    }\n    if (array.length === 0) {\n      current = cleanChildren(parent, current, marker);\n      if (multi) return current;\n    } else if (currentArray) {\n      if (current.length === 0) {\n        appendNodes(parent, array, marker);\n      } else reconcileArrays(parent, current, array);\n    } else {\n      current && cleanChildren(parent);\n      appendNodes(parent, array);\n    }\n    current = array;\n  } else if (value.nodeType) {\n    if (hydrating && value.parentNode) return current = multi ? [value] : value;\n    if (Array.isArray(current)) {\n      if (multi) return current = cleanChildren(parent, current, marker, value);\n      cleanChildren(parent, current, null, value);\n    } else if (current == null || current === \"\" || !parent.firstChild) {\n      parent.appendChild(value);\n    } else parent.replaceChild(value, parent.firstChild);\n    current = value;\n  } else ;\n  return current;\n}\nfunction normalizeIncomingArray(normalized, array, current, unwrap) {\n  let dynamic = false;\n  for (let i = 0, len = array.length; i < len; i++) {\n    let item = array[i], prev = current && current[normalized.length], t;\n    if (item == null || item === true || item === false) ;\n    else if ((t = typeof item) === \"object\" && item.nodeType) {\n      normalized.push(item);\n    } else if (Array.isArray(item)) {\n      dynamic = normalizeIncomingArray(normalized, item, prev) || dynamic;\n    } else if (t === \"function\") {\n      if (unwrap) {\n        while (typeof item === \"function\") item = item();\n        dynamic = normalizeIncomingArray(\n          normalized,\n          Array.isArray(item) ? item : [item],\n          Array.isArray(prev) ? prev : [prev]\n        ) || dynamic;\n      } else {\n        normalized.push(item);\n        dynamic = true;\n      }\n    } else {\n      const value = String(item);\n      if (prev && prev.nodeType === 3 && prev.data === value) normalized.push(prev);\n      else normalized.push(document.createTextNode(value));\n    }\n  }\n  return dynamic;\n}\nfunction appendNodes(parent, array, marker = null) {\n  for (let i = 0, len = array.length; i < len; i++) parent.insertBefore(array[i], marker);\n}\nfunction cleanChildren(parent, current, marker, replacement) {\n  if (marker === void 0) return parent.textContent = \"\";\n  const node = replacement || document.createTextNode(\"\");\n  if (current.length) {\n    let inserted = false;\n    for (let i = current.length - 1; i >= 0; i--) {\n      const el = current[i];\n      if (node !== el) {\n        const isParent = el.parentNode === parent;\n        if (!inserted && !i)\n          isParent ? parent.replaceChild(node, el) : parent.insertBefore(node, marker);\n        else isParent && el.remove();\n      } else inserted = true;\n    }\n  } else parent.insertBefore(node, marker);\n  return [node];\n}\nfunction getHydrationKey() {\n  return sharedConfig.getNextContextId();\n}\nvar isServer = false;\nvar SVG_NAMESPACE = \"http://www.w3.org/2000/svg\";\nfunction createElement(tagName, isSVG = false) {\n  return isSVG ? document.createElementNS(SVG_NAMESPACE, tagName) : document.createElement(tagName);\n}\nfunction Portal(props) {\n  const { useShadow } = props, marker = document.createTextNode(\"\"), mount = () => props.mount || document.body, owner = getOwner();\n  let content;\n  let hydrating = !!sharedConfig.context;\n  createEffect(\n    () => {\n      if (hydrating) getOwner().user = hydrating = false;\n      content || (content = runWithOwner(owner, () => createMemo(() => props.children)));\n      const el = mount();\n      if (el instanceof HTMLHeadElement) {\n        const [clean, setClean] = createSignal(false);\n        const cleanup = () => setClean(true);\n        createRoot((dispose2) => insert(el, () => !clean() ? content() : dispose2(), null));\n        onCleanup(cleanup);\n      } else {\n        const container = createElement(props.isSVG ? \"g\" : \"div\", props.isSVG), renderRoot = useShadow && container.attachShadow ? container.attachShadow({\n          mode: \"open\"\n        }) : container;\n        Object.defineProperty(container, \"_$host\", {\n          get() {\n            return marker.parentNode;\n          },\n          configurable: true\n        });\n        insert(renderRoot, content);\n        el.appendChild(container);\n        props.ref && props.ref(container);\n        onCleanup(() => el.removeChild(container));\n      }\n    },\n    void 0,\n    {\n      render: !hydrating\n    }\n  );\n  return marker;\n}\nfunction createDynamic(component, props) {\n  const cached = createMemo(component);\n  return createMemo(() => {\n    const component2 = cached();\n    switch (typeof component2) {\n      case \"function\":\n        return untrack(() => component2(props));\n      case \"string\":\n        const isSvg = SVGElements.has(component2);\n        const el = sharedConfig.context ? getNextElement() : createElement(component2, isSvg);\n        spread(el, props, isSvg);\n        return el;\n    }\n  });\n}\nfunction Dynamic(props) {\n  const [, others] = splitProps(props, [\"component\"]);\n  return createDynamic(() => props.component, others);\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/double-indexed-kv.js\nvar DoubleIndexedKV = class {\n  constructor() {\n    this.keyToValue = /* @__PURE__ */ new Map();\n    this.valueToKey = /* @__PURE__ */ new Map();\n  }\n  set(key, value) {\n    this.keyToValue.set(key, value);\n    this.valueToKey.set(value, key);\n  }\n  getByKey(key) {\n    return this.keyToValue.get(key);\n  }\n  getByValue(value) {\n    return this.valueToKey.get(value);\n  }\n  clear() {\n    this.keyToValue.clear();\n    this.valueToKey.clear();\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/registry.js\nvar Registry = class {\n  constructor(generateIdentifier) {\n    this.generateIdentifier = generateIdentifier;\n    this.kv = new DoubleIndexedKV();\n  }\n  register(value, identifier) {\n    if (this.kv.getByValue(value)) {\n      return;\n    }\n    if (!identifier) {\n      identifier = this.generateIdentifier(value);\n    }\n    this.kv.set(identifier, value);\n  }\n  clear() {\n    this.kv.clear();\n  }\n  getIdentifier(value) {\n    return this.kv.getByValue(value);\n  }\n  getValue(identifier) {\n    return this.kv.getByKey(identifier);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/class-registry.js\nvar ClassRegistry = class extends Registry {\n  constructor() {\n    super((c) => c.name);\n    this.classToAllowedProps = /* @__PURE__ */ new Map();\n  }\n  register(value, options) {\n    if (typeof options === \"object\") {\n      if (options.allowProps) {\n        this.classToAllowedProps.set(value, options.allowProps);\n      }\n      super.register(value, options.identifier);\n    } else {\n      super.register(value, options);\n    }\n  }\n  getAllowedProps(value) {\n    return this.classToAllowedProps.get(value);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/util.js\nfunction valuesOfObj(record) {\n  if (\"values\" in Object) {\n    return Object.values(record);\n  }\n  const values = [];\n  for (const key in record) {\n    if (record.hasOwnProperty(key)) {\n      values.push(record[key]);\n    }\n  }\n  return values;\n}\nfunction find(record, predicate) {\n  const values = valuesOfObj(record);\n  if (\"find\" in values) {\n    return values.find(predicate);\n  }\n  const valuesNotNever = values;\n  for (let i = 0; i < valuesNotNever.length; i++) {\n    const value = valuesNotNever[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return void 0;\n}\nfunction forEach(record, run) {\n  Object.entries(record).forEach(([key, value]) => run(value, key));\n}\nfunction includes(arr, value) {\n  return arr.indexOf(value) !== -1;\n}\nfunction findArr(record, predicate) {\n  for (let i = 0; i < record.length; i++) {\n    const value = record[i];\n    if (predicate(value)) {\n      return value;\n    }\n  }\n  return void 0;\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/custom-transformer-registry.js\nvar CustomTransformerRegistry = class {\n  constructor() {\n    this.transfomers = {};\n  }\n  register(transformer) {\n    this.transfomers[transformer.name] = transformer;\n  }\n  findApplicable(v) {\n    return find(this.transfomers, (transformer) => transformer.isApplicable(v));\n  }\n  findByName(name) {\n    return this.transfomers[name];\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/is.js\nvar getType = (payload) => Object.prototype.toString.call(payload).slice(8, -1);\nvar isUndefined = (payload) => typeof payload === \"undefined\";\nvar isNull = (payload) => payload === null;\nvar isPlainObject = (payload) => {\n  if (typeof payload !== \"object\" || payload === null)\n    return false;\n  if (payload === Object.prototype)\n    return false;\n  if (Object.getPrototypeOf(payload) === null)\n    return true;\n  return Object.getPrototypeOf(payload) === Object.prototype;\n};\nvar isEmptyObject = (payload) => isPlainObject(payload) && Object.keys(payload).length === 0;\nvar isArray = (payload) => Array.isArray(payload);\nvar isString = (payload) => typeof payload === \"string\";\nvar isNumber = (payload) => typeof payload === \"number\" && !isNaN(payload);\nvar isBoolean = (payload) => typeof payload === \"boolean\";\nvar isRegExp = (payload) => payload instanceof RegExp;\nvar isMap = (payload) => payload instanceof Map;\nvar isSet = (payload) => payload instanceof Set;\nvar isSymbol = (payload) => getType(payload) === \"Symbol\";\nvar isDate = (payload) => payload instanceof Date && !isNaN(payload.valueOf());\nvar isError = (payload) => payload instanceof Error;\nvar isNaNValue = (payload) => typeof payload === \"number\" && isNaN(payload);\nvar isPrimitive = (payload) => isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\nvar isBigint = (payload) => typeof payload === \"bigint\";\nvar isInfinite = (payload) => payload === Infinity || payload === -Infinity;\nvar isTypedArray = (payload) => ArrayBuffer.isView(payload) && !(payload instanceof DataView);\nvar isURL = (payload) => payload instanceof URL;\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/pathstringifier.js\nvar escapeKey = (key) => key.replace(/\\./g, \"\\\\.\");\nvar stringifyPath = (path) => path.map(String).map(escapeKey).join(\".\");\nvar parsePath = (string) => {\n  const result = [];\n  let segment = \"\";\n  for (let i = 0; i < string.length; i++) {\n    let char = string.charAt(i);\n    const isEscapedDot = char === \"\\\\\" && string.charAt(i + 1) === \".\";\n    if (isEscapedDot) {\n      segment += \".\";\n      i++;\n      continue;\n    }\n    const isEndOfSegment = char === \".\";\n    if (isEndOfSegment) {\n      result.push(segment);\n      segment = \"\";\n      continue;\n    }\n    segment += char;\n  }\n  const lastSegment = segment;\n  result.push(lastSegment);\n  return result;\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/transformer.js\nfunction simpleTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform\n  };\n}\nvar simpleRules = [\n  simpleTransformation(isUndefined, \"undefined\", () => null, () => void 0),\n  simpleTransformation(isBigint, \"bigint\", (v) => v.toString(), (v) => {\n    if (typeof BigInt !== \"undefined\") {\n      return BigInt(v);\n    }\n    console.error(\"Please add a BigInt polyfill.\");\n    return v;\n  }),\n  simpleTransformation(isDate, \"Date\", (v) => v.toISOString(), (v) => new Date(v)),\n  simpleTransformation(isError, \"Error\", (v, superJson) => {\n    const baseError = {\n      name: v.name,\n      message: v.message\n    };\n    superJson.allowedErrorProps.forEach((prop) => {\n      baseError[prop] = v[prop];\n    });\n    return baseError;\n  }, (v, superJson) => {\n    const e = new Error(v.message);\n    e.name = v.name;\n    e.stack = v.stack;\n    superJson.allowedErrorProps.forEach((prop) => {\n      e[prop] = v[prop];\n    });\n    return e;\n  }),\n  simpleTransformation(isRegExp, \"regexp\", (v) => \"\" + v, (regex) => {\n    const body = regex.slice(1, regex.lastIndexOf(\"/\"));\n    const flags = regex.slice(regex.lastIndexOf(\"/\") + 1);\n    return new RegExp(body, flags);\n  }),\n  simpleTransformation(\n    isSet,\n    \"set\",\n    // (sets only exist in es6+)\n    // eslint-disable-next-line es5/no-es6-methods\n    (v) => [...v.values()],\n    (v) => new Set(v)\n  ),\n  simpleTransformation(isMap, \"map\", (v) => [...v.entries()], (v) => new Map(v)),\n  simpleTransformation((v) => isNaNValue(v) || isInfinite(v), \"number\", (v) => {\n    if (isNaNValue(v)) {\n      return \"NaN\";\n    }\n    if (v > 0) {\n      return \"Infinity\";\n    } else {\n      return \"-Infinity\";\n    }\n  }, Number),\n  simpleTransformation((v) => v === 0 && 1 / v === -Infinity, \"number\", () => {\n    return \"-0\";\n  }, Number),\n  simpleTransformation(isURL, \"URL\", (v) => v.toString(), (v) => new URL(v))\n];\nfunction compositeTransformation(isApplicable, annotation, transform, untransform) {\n  return {\n    isApplicable,\n    annotation,\n    transform,\n    untransform\n  };\n}\nvar symbolRule = compositeTransformation((s, superJson) => {\n  if (isSymbol(s)) {\n    const isRegistered = !!superJson.symbolRegistry.getIdentifier(s);\n    return isRegistered;\n  }\n  return false;\n}, (s, superJson) => {\n  const identifier = superJson.symbolRegistry.getIdentifier(s);\n  return [\"symbol\", identifier];\n}, (v) => v.description, (_, a, superJson) => {\n  const value = superJson.symbolRegistry.getValue(a[1]);\n  if (!value) {\n    throw new Error(\"Trying to deserialize unknown symbol\");\n  }\n  return value;\n});\nvar constructorToName = [\n  Int8Array,\n  Uint8Array,\n  Int16Array,\n  Uint16Array,\n  Int32Array,\n  Uint32Array,\n  Float32Array,\n  Float64Array,\n  Uint8ClampedArray\n].reduce((obj, ctor) => {\n  obj[ctor.name] = ctor;\n  return obj;\n}, {});\nvar typedArrayRule = compositeTransformation(isTypedArray, (v) => [\"typed-array\", v.constructor.name], (v) => [...v], (v, a) => {\n  const ctor = constructorToName[a[1]];\n  if (!ctor) {\n    throw new Error(\"Trying to deserialize unknown typed array\");\n  }\n  return new ctor(v);\n});\nfunction isInstanceOfRegisteredClass(potentialClass, superJson) {\n  if (potentialClass?.constructor) {\n    const isRegistered = !!superJson.classRegistry.getIdentifier(potentialClass.constructor);\n    return isRegistered;\n  }\n  return false;\n}\nvar classRule = compositeTransformation(isInstanceOfRegisteredClass, (clazz, superJson) => {\n  const identifier = superJson.classRegistry.getIdentifier(clazz.constructor);\n  return [\"class\", identifier];\n}, (clazz, superJson) => {\n  const allowedProps = superJson.classRegistry.getAllowedProps(clazz.constructor);\n  if (!allowedProps) {\n    return { ...clazz };\n  }\n  const result = {};\n  allowedProps.forEach((prop) => {\n    result[prop] = clazz[prop];\n  });\n  return result;\n}, (v, a, superJson) => {\n  const clazz = superJson.classRegistry.getValue(a[1]);\n  if (!clazz) {\n    throw new Error(\"Trying to deserialize unknown class - check https://github.com/blitz-js/superjson/issues/116#issuecomment-773996564\");\n  }\n  return Object.assign(Object.create(clazz.prototype), v);\n});\nvar customRule = compositeTransformation((value, superJson) => {\n  return !!superJson.customTransformerRegistry.findApplicable(value);\n}, (value, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return [\"custom\", transformer.name];\n}, (value, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findApplicable(value);\n  return transformer.serialize(value);\n}, (v, a, superJson) => {\n  const transformer = superJson.customTransformerRegistry.findByName(a[1]);\n  if (!transformer) {\n    throw new Error(\"Trying to deserialize unknown custom value\");\n  }\n  return transformer.deserialize(v);\n});\nvar compositeRules = [classRule, symbolRule, customRule, typedArrayRule];\nvar transformValue = (value, superJson) => {\n  const applicableCompositeRule = findArr(compositeRules, (rule) => rule.isApplicable(value, superJson));\n  if (applicableCompositeRule) {\n    return {\n      value: applicableCompositeRule.transform(value, superJson),\n      type: applicableCompositeRule.annotation(value, superJson)\n    };\n  }\n  const applicableSimpleRule = findArr(simpleRules, (rule) => rule.isApplicable(value, superJson));\n  if (applicableSimpleRule) {\n    return {\n      value: applicableSimpleRule.transform(value, superJson),\n      type: applicableSimpleRule.annotation\n    };\n  }\n  return void 0;\n};\nvar simpleRulesByAnnotation = {};\nsimpleRules.forEach((rule) => {\n  simpleRulesByAnnotation[rule.annotation] = rule;\n});\nvar untransformValue = (json, type, superJson) => {\n  if (isArray(type)) {\n    switch (type[0]) {\n      case \"symbol\":\n        return symbolRule.untransform(json, type, superJson);\n      case \"class\":\n        return classRule.untransform(json, type, superJson);\n      case \"custom\":\n        return customRule.untransform(json, type, superJson);\n      case \"typed-array\":\n        return typedArrayRule.untransform(json, type, superJson);\n      default:\n        throw new Error(\"Unknown transformation: \" + type);\n    }\n  } else {\n    const transformation = simpleRulesByAnnotation[type];\n    if (!transformation) {\n      throw new Error(\"Unknown transformation: \" + type);\n    }\n    return transformation.untransform(json, superJson);\n  }\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/accessDeep.js\nvar getNthKey = (value, n) => {\n  const keys = value.keys();\n  while (n > 0) {\n    keys.next();\n    n--;\n  }\n  return keys.next().value;\n};\nfunction validatePath(path) {\n  if (includes(path, \"__proto__\")) {\n    throw new Error(\"__proto__ is not allowed as a property\");\n  }\n  if (includes(path, \"prototype\")) {\n    throw new Error(\"prototype is not allowed as a property\");\n  }\n  if (includes(path, \"constructor\")) {\n    throw new Error(\"constructor is not allowed as a property\");\n  }\n}\nvar getDeep = (object, path) => {\n  validatePath(path);\n  for (let i = 0; i < path.length; i++) {\n    const key = path[i];\n    if (isSet(object)) {\n      object = getNthKey(object, +key);\n    } else if (isMap(object)) {\n      const row = +key;\n      const type = +path[++i] === 0 ? \"key\" : \"value\";\n      const keyOfRow = getNthKey(object, row);\n      switch (type) {\n        case \"key\":\n          object = keyOfRow;\n          break;\n        case \"value\":\n          object = object.get(keyOfRow);\n          break;\n      }\n    } else {\n      object = object[key];\n    }\n  }\n  return object;\n};\nvar setDeep = (object, path, mapper) => {\n  validatePath(path);\n  if (path.length === 0) {\n    return mapper(object);\n  }\n  let parent = object;\n  for (let i = 0; i < path.length - 1; i++) {\n    const key = path[i];\n    if (isArray(parent)) {\n      const index = +key;\n      parent = parent[index];\n    } else if (isPlainObject(parent)) {\n      parent = parent[key];\n    } else if (isSet(parent)) {\n      const row = +key;\n      parent = getNthKey(parent, row);\n    } else if (isMap(parent)) {\n      const isEnd = i === path.length - 2;\n      if (isEnd) {\n        break;\n      }\n      const row = +key;\n      const type = +path[++i] === 0 ? \"key\" : \"value\";\n      const keyOfRow = getNthKey(parent, row);\n      switch (type) {\n        case \"key\":\n          parent = keyOfRow;\n          break;\n        case \"value\":\n          parent = parent.get(keyOfRow);\n          break;\n      }\n    }\n  }\n  const lastKey = path[path.length - 1];\n  if (isArray(parent)) {\n    parent[+lastKey] = mapper(parent[+lastKey]);\n  } else if (isPlainObject(parent)) {\n    parent[lastKey] = mapper(parent[lastKey]);\n  }\n  if (isSet(parent)) {\n    const oldValue = getNthKey(parent, +lastKey);\n    const newValue = mapper(oldValue);\n    if (oldValue !== newValue) {\n      parent.delete(oldValue);\n      parent.add(newValue);\n    }\n  }\n  if (isMap(parent)) {\n    const row = +path[path.length - 2];\n    const keyToRow = getNthKey(parent, row);\n    const type = +lastKey === 0 ? \"key\" : \"value\";\n    switch (type) {\n      case \"key\": {\n        const newKey = mapper(keyToRow);\n        parent.set(newKey, parent.get(keyToRow));\n        if (newKey !== keyToRow) {\n          parent.delete(keyToRow);\n        }\n        break;\n      }\n      case \"value\": {\n        parent.set(keyToRow, mapper(parent.get(keyToRow)));\n        break;\n      }\n    }\n  }\n  return object;\n};\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/plainer.js\nfunction traverse(tree, walker2, origin = []) {\n  if (!tree) {\n    return;\n  }\n  if (!isArray(tree)) {\n    forEach(tree, (subtree, key) => traverse(subtree, walker2, [...origin, ...parsePath(key)]));\n    return;\n  }\n  const [nodeValue, children2] = tree;\n  if (children2) {\n    forEach(children2, (child, key) => {\n      traverse(child, walker2, [...origin, ...parsePath(key)]);\n    });\n  }\n  walker2(nodeValue, origin);\n}\nfunction applyValueAnnotations(plain, annotations, superJson) {\n  traverse(annotations, (type, path) => {\n    plain = setDeep(plain, path, (v) => untransformValue(v, type, superJson));\n  });\n  return plain;\n}\nfunction applyReferentialEqualityAnnotations(plain, annotations) {\n  function apply(identicalPaths, path) {\n    const object = getDeep(plain, parsePath(path));\n    identicalPaths.map(parsePath).forEach((identicalObjectPath) => {\n      plain = setDeep(plain, identicalObjectPath, () => object);\n    });\n  }\n  if (isArray(annotations)) {\n    const [root, other] = annotations;\n    root.forEach((identicalPath) => {\n      plain = setDeep(plain, parsePath(identicalPath), () => plain);\n    });\n    if (other) {\n      forEach(other, apply);\n    }\n  } else {\n    forEach(annotations, apply);\n  }\n  return plain;\n}\nvar isDeep = (object, superJson) => isPlainObject(object) || isArray(object) || isMap(object) || isSet(object) || isInstanceOfRegisteredClass(object, superJson);\nfunction addIdentity(object, path, identities) {\n  const existingSet = identities.get(object);\n  if (existingSet) {\n    existingSet.push(path);\n  } else {\n    identities.set(object, [path]);\n  }\n}\nfunction generateReferentialEqualityAnnotations(identitites, dedupe) {\n  const result = {};\n  let rootEqualityPaths = void 0;\n  identitites.forEach((paths) => {\n    if (paths.length <= 1) {\n      return;\n    }\n    if (!dedupe) {\n      paths = paths.map((path) => path.map(String)).sort((a, b) => a.length - b.length);\n    }\n    const [representativePath, ...identicalPaths] = paths;\n    if (representativePath.length === 0) {\n      rootEqualityPaths = identicalPaths.map(stringifyPath);\n    } else {\n      result[stringifyPath(representativePath)] = identicalPaths.map(stringifyPath);\n    }\n  });\n  if (rootEqualityPaths) {\n    if (isEmptyObject(result)) {\n      return [rootEqualityPaths];\n    } else {\n      return [rootEqualityPaths, result];\n    }\n  } else {\n    return isEmptyObject(result) ? void 0 : result;\n  }\n}\nvar walker = (object, identities, superJson, dedupe, path = [], objectsInThisPath = [], seenObjects = /* @__PURE__ */ new Map()) => {\n  const primitive = isPrimitive(object);\n  if (!primitive) {\n    addIdentity(object, path, identities);\n    const seen = seenObjects.get(object);\n    if (seen) {\n      return dedupe ? {\n        transformedValue: null\n      } : seen;\n    }\n  }\n  if (!isDeep(object, superJson)) {\n    const transformed2 = transformValue(object, superJson);\n    const result2 = transformed2 ? {\n      transformedValue: transformed2.value,\n      annotations: [transformed2.type]\n    } : {\n      transformedValue: object\n    };\n    if (!primitive) {\n      seenObjects.set(object, result2);\n    }\n    return result2;\n  }\n  if (includes(objectsInThisPath, object)) {\n    return {\n      transformedValue: null\n    };\n  }\n  const transformationResult = transformValue(object, superJson);\n  const transformed = transformationResult?.value ?? object;\n  const transformedValue = isArray(transformed) ? [] : {};\n  const innerAnnotations = {};\n  forEach(transformed, (value, index) => {\n    if (index === \"__proto__\" || index === \"constructor\" || index === \"prototype\") {\n      throw new Error(`Detected property ${index}. This is a prototype pollution risk, please remove it from your object.`);\n    }\n    const recursiveResult = walker(value, identities, superJson, dedupe, [...path, index], [...objectsInThisPath, object], seenObjects);\n    transformedValue[index] = recursiveResult.transformedValue;\n    if (isArray(recursiveResult.annotations)) {\n      innerAnnotations[index] = recursiveResult.annotations;\n    } else if (isPlainObject(recursiveResult.annotations)) {\n      forEach(recursiveResult.annotations, (tree, key) => {\n        innerAnnotations[escapeKey(index) + \".\" + key] = tree;\n      });\n    }\n  });\n  const result = isEmptyObject(innerAnnotations) ? {\n    transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type] : void 0\n  } : {\n    transformedValue,\n    annotations: !!transformationResult ? [transformationResult.type, innerAnnotations] : innerAnnotations\n  };\n  if (!primitive) {\n    seenObjects.set(object, result);\n  }\n  return result;\n};\n\n// ../../node_modules/.pnpm/is-what@4.1.16/node_modules/is-what/dist/index.js\nfunction getType2(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\nfunction isArray2(payload) {\n  return getType2(payload) === \"Array\";\n}\nfunction isPlainObject2(payload) {\n  if (getType2(payload) !== \"Object\")\n    return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\n\n// ../../node_modules/.pnpm/copy-anything@3.0.5/node_modules/copy-anything/dist/index.js\nfunction assignProp2(carry, key, newVal, originalObject, includeNonenumerable) {\n  const propType = {}.propertyIsEnumerable.call(originalObject, key) ? \"enumerable\" : \"nonenumerable\";\n  if (propType === \"enumerable\")\n    carry[key] = newVal;\n  if (includeNonenumerable && propType === \"nonenumerable\") {\n    Object.defineProperty(carry, key, {\n      value: newVal,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    });\n  }\n}\nfunction copy(target, options = {}) {\n  if (isArray2(target)) {\n    return target.map((item) => copy(item, options));\n  }\n  if (!isPlainObject2(target)) {\n    return target;\n  }\n  const props = Object.getOwnPropertyNames(target);\n  const symbols = Object.getOwnPropertySymbols(target);\n  return [...props, ...symbols].reduce((carry, key) => {\n    if (isArray2(options.props) && !options.props.includes(key)) {\n      return carry;\n    }\n    const val = target[key];\n    const newVal = copy(val, options);\n    assignProp2(carry, key, newVal, target, options.nonenumerable);\n    return carry;\n  }, {});\n}\n\n// ../../node_modules/.pnpm/superjson@2.2.1/node_modules/superjson/dist/index.js\nvar SuperJSON = class {\n  /**\n   * @param dedupeReferentialEqualities  If true, SuperJSON will make sure only one instance of referentially equal objects are serialized and the rest are replaced with `null`.\n   */\n  constructor({ dedupe = false } = {}) {\n    this.classRegistry = new ClassRegistry();\n    this.symbolRegistry = new Registry((s) => s.description ?? \"\");\n    this.customTransformerRegistry = new CustomTransformerRegistry();\n    this.allowedErrorProps = [];\n    this.dedupe = dedupe;\n  }\n  serialize(object) {\n    const identities = /* @__PURE__ */ new Map();\n    const output = walker(object, identities, this, this.dedupe);\n    const res = {\n      json: output.transformedValue\n    };\n    if (output.annotations) {\n      res.meta = {\n        ...res.meta,\n        values: output.annotations\n      };\n    }\n    const equalityAnnotations = generateReferentialEqualityAnnotations(identities, this.dedupe);\n    if (equalityAnnotations) {\n      res.meta = {\n        ...res.meta,\n        referentialEqualities: equalityAnnotations\n      };\n    }\n    return res;\n  }\n  deserialize(payload) {\n    const { json, meta } = payload;\n    let result = copy(json);\n    if (meta?.values) {\n      result = applyValueAnnotations(result, meta.values, this);\n    }\n    if (meta?.referentialEqualities) {\n      result = applyReferentialEqualityAnnotations(result, meta.referentialEqualities);\n    }\n    return result;\n  }\n  stringify(object) {\n    return JSON.stringify(this.serialize(object));\n  }\n  parse(string) {\n    return this.deserialize(JSON.parse(string));\n  }\n  registerClass(v, options) {\n    this.classRegistry.register(v, options);\n  }\n  registerSymbol(v, identifier) {\n    this.symbolRegistry.register(v, identifier);\n  }\n  registerCustom(transformer, name) {\n    this.customTransformerRegistry.register({\n      name,\n      ...transformer\n    });\n  }\n  allowErrorProps(...props) {\n    this.allowedErrorProps.push(...props);\n  }\n};\nSuperJSON.defaultInstance = new SuperJSON();\nSuperJSON.serialize = SuperJSON.defaultInstance.serialize.bind(SuperJSON.defaultInstance);\nSuperJSON.deserialize = SuperJSON.defaultInstance.deserialize.bind(SuperJSON.defaultInstance);\nSuperJSON.stringify = SuperJSON.defaultInstance.stringify.bind(SuperJSON.defaultInstance);\nSuperJSON.parse = SuperJSON.defaultInstance.parse.bind(SuperJSON.defaultInstance);\nSuperJSON.registerClass = SuperJSON.defaultInstance.registerClass.bind(SuperJSON.defaultInstance);\nSuperJSON.registerSymbol = SuperJSON.defaultInstance.registerSymbol.bind(SuperJSON.defaultInstance);\nSuperJSON.registerCustom = SuperJSON.defaultInstance.registerCustom.bind(SuperJSON.defaultInstance);\nSuperJSON.allowErrorProps = SuperJSON.defaultInstance.allowErrorProps.bind(SuperJSON.defaultInstance);\nvar serialize = SuperJSON.serialize;\nSuperJSON.deserialize;\nvar stringify = SuperJSON.stringify;\nSuperJSON.parse;\nSuperJSON.registerClass;\nSuperJSON.registerCustom;\nSuperJSON.registerSymbol;\nSuperJSON.allowErrorProps;\n\n// src/utils.tsx\nfunction getQueryStatusLabel(query) {\n  return query.state.fetchStatus === \"fetching\" ? \"fetching\" : !query.getObserversCount() ? \"inactive\" : query.state.fetchStatus === \"paused\" ? \"paused\" : query.isStale() ? \"stale\" : \"fresh\";\n}\nfunction getSidedProp(prop, side) {\n  return `${prop}${side.charAt(0).toUpperCase() + side.slice(1)}`;\n}\nfunction getQueryStatusColor({\n  queryState,\n  observerCount,\n  isStale\n}) {\n  return queryState.fetchStatus === \"fetching\" ? \"blue\" : !observerCount ? \"gray\" : queryState.fetchStatus === \"paused\" ? \"purple\" : isStale ? \"yellow\" : \"green\";\n}\nfunction getMutationStatusColor({\n  status,\n  isPaused\n}) {\n  return isPaused ? \"purple\" : status === \"error\" ? \"red\" : status === \"pending\" ? \"yellow\" : status === \"success\" ? \"green\" : \"gray\";\n}\nfunction getQueryStatusColorByLabel(label) {\n  return label === \"fresh\" ? \"green\" : label === \"stale\" ? \"yellow\" : label === \"paused\" ? \"purple\" : label === \"inactive\" ? \"gray\" : \"blue\";\n}\nvar displayValue = (value, beautify = false) => {\n  const {\n    json\n  } = serialize(value);\n  return JSON.stringify(json, null, beautify ? 2 : void 0);\n};\nvar getStatusRank = (q) => q.state.fetchStatus !== \"idle\" ? 0 : !q.getObserversCount() ? 3 : q.isStale() ? 2 : 1;\nvar queryHashSort = (a, b) => a.queryHash.localeCompare(b.queryHash);\nvar dateSort = (a, b) => a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1;\nvar statusAndDateSort = (a, b) => {\n  if (getStatusRank(a) === getStatusRank(b)) {\n    return dateSort(a, b);\n  }\n  return getStatusRank(a) > getStatusRank(b) ? 1 : -1;\n};\nvar sortFns = {\n  status: statusAndDateSort,\n  \"query hash\": queryHashSort,\n  \"last updated\": dateSort\n};\nvar getMutationStatusRank = (m) => m.state.isPaused ? 0 : m.state.status === \"error\" ? 2 : m.state.status === \"pending\" ? 1 : 3;\nvar mutationDateSort = (a, b) => a.state.submittedAt < b.state.submittedAt ? 1 : -1;\nvar mutationStatusSort = (a, b) => {\n  if (getMutationStatusRank(a) === getMutationStatusRank(b)) {\n    return mutationDateSort(a, b);\n  }\n  return getMutationStatusRank(a) > getMutationStatusRank(b) ? 1 : -1;\n};\nvar mutationSortFns = {\n  status: mutationStatusSort,\n  \"last updated\": mutationDateSort\n};\nvar convertRemToPixels = (rem) => {\n  return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);\n};\nvar getPreferredColorScheme = () => {\n  const [colorScheme, setColorScheme] = createSignal(\"dark\");\n  onMount(() => {\n    const query = window.matchMedia(\"(prefers-color-scheme: dark)\");\n    setColorScheme(query.matches ? \"dark\" : \"light\");\n    const listener = (e) => {\n      setColorScheme(e.matches ? \"dark\" : \"light\");\n    };\n    query.addEventListener(\"change\", listener);\n    onCleanup(() => query.removeEventListener(\"change\", listener));\n  });\n  return colorScheme;\n};\nvar updateNestedDataByPath = (oldData, updatePath, value) => {\n  if (updatePath.length === 0) {\n    return value;\n  }\n  if (oldData instanceof Map) {\n    const newData = new Map(oldData);\n    if (updatePath.length === 1) {\n      newData.set(updatePath[0], value);\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData.set(head, updateNestedDataByPath(newData.get(head), tail, value));\n    return newData;\n  }\n  if (oldData instanceof Set) {\n    const setAsArray = updateNestedDataByPath(Array.from(oldData), updatePath, value);\n    return new Set(setAsArray);\n  }\n  if (Array.isArray(oldData)) {\n    const newData = [...oldData];\n    if (updatePath.length === 1) {\n      newData[updatePath[0]] = value;\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData[head] = updateNestedDataByPath(newData[head], tail, value);\n    return newData;\n  }\n  if (oldData instanceof Object) {\n    const newData = {\n      ...oldData\n    };\n    if (updatePath.length === 1) {\n      newData[updatePath[0]] = value;\n      return newData;\n    }\n    const [head, ...tail] = updatePath;\n    newData[head] = updateNestedDataByPath(newData[head], tail, value);\n    return newData;\n  }\n  return oldData;\n};\nvar deleteNestedDataByPath = (oldData, deletePath) => {\n  if (oldData instanceof Map) {\n    const newData = new Map(oldData);\n    if (deletePath.length === 1) {\n      newData.delete(deletePath[0]);\n      return newData;\n    }\n    const [head, ...tail] = deletePath;\n    newData.set(head, deleteNestedDataByPath(newData.get(head), tail));\n    return newData;\n  }\n  if (oldData instanceof Set) {\n    const setAsArray = deleteNestedDataByPath(Array.from(oldData), deletePath);\n    return new Set(setAsArray);\n  }\n  if (Array.isArray(oldData)) {\n    const newData = [...oldData];\n    if (deletePath.length === 1) {\n      return newData.filter((_, idx) => idx.toString() !== deletePath[0]);\n    }\n    const [head, ...tail] = deletePath;\n    newData[head] = deleteNestedDataByPath(newData[head], tail);\n    return newData;\n  }\n  if (oldData instanceof Object) {\n    const newData = {\n      ...oldData\n    };\n    if (deletePath.length === 1) {\n      delete newData[deletePath[0]];\n      return newData;\n    }\n    const [head, ...tail] = deletePath;\n    newData[head] = deleteNestedDataByPath(newData[head], tail);\n    return newData;\n  }\n  return oldData;\n};\nvar setupStyleSheet = (nonce, target) => {\n  if (!nonce) return;\n  const styleExists = document.querySelector(\"#_goober\") || target?.querySelector(\"#_goober\");\n  if (styleExists) return;\n  const styleTag = document.createElement(\"style\");\n  const textNode = document.createTextNode(\"\");\n  styleTag.appendChild(textNode);\n  styleTag.id = \"_goober\";\n  styleTag.setAttribute(\"nonce\", nonce);\n  if (target) {\n    target.appendChild(styleTag);\n  } else {\n    document.head.appendChild(styleTag);\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcXVlcnktZGV2dG9vbHMvYnVpbGQvY2h1bmsvVjVUNVZKS0cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0M7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGlCQUFpQjtBQUN2QyxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLHdCQUF3QiwyQkFBMkI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLFFBQVE7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELFNBQVM7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFrQjtBQUNwQztBQUNBO0FBQ0Esa0JBQWtCLGtCQUFrQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsa0JBQWtCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLGdCQUFnQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHlCQUF5QjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwyQkFBMkI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxRQUFRO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLG9DQUFvQyxRQUFRO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxRQUFRO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsdUJBQXVCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixjQUFjO0FBQ2hDO0FBQ0EsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0Esb0JBQW9CLFlBQVk7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EscURBQXFELGlEQUFpRDtBQUN0RyxpREFBaUQsb0VBQW9FO0FBQ3JIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixZQUFZO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0Esd0JBQXdCLFlBQVk7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsYUFBYSxrQkFBa0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBLHdDQUF3QyxZQUFZO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixvQkFBb0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxRQUFRO0FBQ25EO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLDJDQUEyQyxRQUFRO0FBQ25EO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsMEJBQTBCLG9CQUFvQjtBQUM5QztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxRQUFRO0FBQzNDO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxTQUFTO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1YsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLFFBQVE7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxVQUFVO0FBQ2pFO0FBQ0Esa0RBQWtELEtBQUs7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLFVBQVU7QUFDVixRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQ7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLE9BQU87QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixLQUFLO0FBQ3JCLGdCQUFnQixLQUFLO0FBQ3JCLE1BQU0sZUFBZSxLQUFLO0FBQzFCLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EseUNBQXlDO0FBQ3pDLDJDQUEyQztBQUMzQztBQUNBLHFDQUFxQyxTQUFTO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsU0FBUztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEIsc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFO0FBQ2hFLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyxhQUFhO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixPQUFPO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLElBQUk7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixxQkFBcUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixvQkFBb0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxRQUFRO0FBQ1IsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsU0FBUztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsUUFBUTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsWUFBWTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwyQkFBMkI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixtQkFBbUI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsbUJBQW1CO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxJQUFJO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGlCQUFpQjtBQUNuQztBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHFCQUFxQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLE1BQU07QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxJQUFJO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpQkFBaUIsSUFBSTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksYUFBYTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxLQUFLLEVBQUUsNkNBQTZDO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFc3RCIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHF1ZXJ5LWRldnRvb2xzXFxidWlsZFxcY2h1bmtcXFY1VDVWSktHLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zb2xpZC1qc0AxLjkuNS9ub2RlX21vZHVsZXMvc29saWQtanMvZGlzdC9zb2xpZC5qc1xudmFyIHNoYXJlZENvbmZpZyA9IHtcbiAgY29udGV4dDogdm9pZCAwLFxuICByZWdpc3RyeTogdm9pZCAwLFxuICBlZmZlY3RzOiB2b2lkIDAsXG4gIGRvbmU6IGZhbHNlLFxuICBnZXRDb250ZXh0SWQoKSB7XG4gICAgcmV0dXJuIGdldENvbnRleHRJZCh0aGlzLmNvbnRleHQuY291bnQpO1xuICB9LFxuICBnZXROZXh0Q29udGV4dElkKCkge1xuICAgIHJldHVybiBnZXRDb250ZXh0SWQodGhpcy5jb250ZXh0LmNvdW50KyspO1xuICB9XG59O1xuZnVuY3Rpb24gZ2V0Q29udGV4dElkKGNvdW50KSB7XG4gIGNvbnN0IG51bSA9IFN0cmluZyhjb3VudCksIGxlbiA9IG51bS5sZW5ndGggLSAxO1xuICByZXR1cm4gc2hhcmVkQ29uZmlnLmNvbnRleHQuaWQgKyAobGVuID8gU3RyaW5nLmZyb21DaGFyQ29kZSg5NiArIGxlbikgOiBcIlwiKSArIG51bTtcbn1cbmZ1bmN0aW9uIHNldEh5ZHJhdGVDb250ZXh0KGNvbnRleHQpIHtcbiAgc2hhcmVkQ29uZmlnLmNvbnRleHQgPSBjb250ZXh0O1xufVxuZnVuY3Rpb24gbmV4dEh5ZHJhdGVDb250ZXh0KCkge1xuICByZXR1cm4ge1xuICAgIC4uLnNoYXJlZENvbmZpZy5jb250ZXh0LFxuICAgIGlkOiBzaGFyZWRDb25maWcuZ2V0TmV4dENvbnRleHRJZCgpLFxuICAgIGNvdW50OiAwXG4gIH07XG59XG52YXIgSVNfREVWID0gZmFsc2U7XG52YXIgZXF1YWxGbiA9IChhLCBiKSA9PiBhID09PSBiO1xudmFyICRQUk9YWSA9IFN5bWJvbChcInNvbGlkLXByb3h5XCIpO1xudmFyIFNVUFBPUlRTX1BST1hZID0gdHlwZW9mIFByb3h5ID09PSBcImZ1bmN0aW9uXCI7XG52YXIgJFRSQUNLID0gU3ltYm9sKFwic29saWQtdHJhY2tcIik7XG52YXIgc2lnbmFsT3B0aW9ucyA9IHtcbiAgZXF1YWxzOiBlcXVhbEZuXG59O1xudmFyIEVSUk9SID0gbnVsbDtcbnZhciBydW5FZmZlY3RzID0gcnVuUXVldWU7XG52YXIgU1RBTEUgPSAxO1xudmFyIFBFTkRJTkcgPSAyO1xudmFyIFVOT1dORUQgPSB7XG4gIG93bmVkOiBudWxsLFxuICBjbGVhbnVwczogbnVsbCxcbiAgY29udGV4dDogbnVsbCxcbiAgb3duZXI6IG51bGxcbn07XG52YXIgTk9fSU5JVCA9IHt9O1xudmFyIE93bmVyID0gbnVsbDtcbnZhciBUcmFuc2l0aW9uID0gbnVsbDtcbnZhciBTY2hlZHVsZXIgPSBudWxsO1xudmFyIEV4dGVybmFsU291cmNlQ29uZmlnID0gbnVsbDtcbnZhciBMaXN0ZW5lciA9IG51bGw7XG52YXIgVXBkYXRlcyA9IG51bGw7XG52YXIgRWZmZWN0cyA9IG51bGw7XG52YXIgRXhlY0NvdW50ID0gMDtcbmZ1bmN0aW9uIGNyZWF0ZVJvb3QoZm4sIGRldGFjaGVkT3duZXIpIHtcbiAgY29uc3QgbGlzdGVuZXIgPSBMaXN0ZW5lciwgb3duZXIgPSBPd25lciwgdW5vd25lZCA9IGZuLmxlbmd0aCA9PT0gMCwgY3VycmVudCA9IGRldGFjaGVkT3duZXIgPT09IHZvaWQgMCA/IG93bmVyIDogZGV0YWNoZWRPd25lciwgcm9vdCA9IHVub3duZWQgPyBVTk9XTkVEIDoge1xuICAgIG93bmVkOiBudWxsLFxuICAgIGNsZWFudXBzOiBudWxsLFxuICAgIGNvbnRleHQ6IGN1cnJlbnQgPyBjdXJyZW50LmNvbnRleHQgOiBudWxsLFxuICAgIG93bmVyOiBjdXJyZW50XG4gIH0sIHVwZGF0ZUZuID0gdW5vd25lZCA/IGZuIDogKCkgPT4gZm4oKCkgPT4gdW50cmFjaygoKSA9PiBjbGVhbk5vZGUocm9vdCkpKTtcbiAgT3duZXIgPSByb290O1xuICBMaXN0ZW5lciA9IG51bGw7XG4gIHRyeSB7XG4gICAgcmV0dXJuIHJ1blVwZGF0ZXModXBkYXRlRm4sIHRydWUpO1xuICB9IGZpbmFsbHkge1xuICAgIExpc3RlbmVyID0gbGlzdGVuZXI7XG4gICAgT3duZXIgPSBvd25lcjtcbiAgfVxufVxuZnVuY3Rpb24gY3JlYXRlU2lnbmFsKHZhbHVlLCBvcHRpb25zKSB7XG4gIG9wdGlvbnMgPSBvcHRpb25zID8gT2JqZWN0LmFzc2lnbih7fSwgc2lnbmFsT3B0aW9ucywgb3B0aW9ucykgOiBzaWduYWxPcHRpb25zO1xuICBjb25zdCBzID0ge1xuICAgIHZhbHVlLFxuICAgIG9ic2VydmVyczogbnVsbCxcbiAgICBvYnNlcnZlclNsb3RzOiBudWxsLFxuICAgIGNvbXBhcmF0b3I6IG9wdGlvbnMuZXF1YWxzIHx8IHZvaWQgMFxuICB9O1xuICBjb25zdCBzZXR0ZXIgPSAodmFsdWUyKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZTIgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgaWYgKFRyYW5zaXRpb24gJiYgVHJhbnNpdGlvbi5ydW5uaW5nICYmIFRyYW5zaXRpb24uc291cmNlcy5oYXMocykpIHZhbHVlMiA9IHZhbHVlMihzLnRWYWx1ZSk7XG4gICAgICBlbHNlIHZhbHVlMiA9IHZhbHVlMihzLnZhbHVlKTtcbiAgICB9XG4gICAgcmV0dXJuIHdyaXRlU2lnbmFsKHMsIHZhbHVlMik7XG4gIH07XG4gIHJldHVybiBbcmVhZFNpZ25hbC5iaW5kKHMpLCBzZXR0ZXJdO1xufVxuZnVuY3Rpb24gY3JlYXRlQ29tcHV0ZWQoZm4sIHZhbHVlLCBvcHRpb25zKSB7XG4gIGNvbnN0IGMgPSBjcmVhdGVDb21wdXRhdGlvbihmbiwgdmFsdWUsIHRydWUsIFNUQUxFKTtcbiAgaWYgKFNjaGVkdWxlciAmJiBUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZykgVXBkYXRlcy5wdXNoKGMpO1xuICBlbHNlIHVwZGF0ZUNvbXB1dGF0aW9uKGMpO1xufVxuZnVuY3Rpb24gY3JlYXRlUmVuZGVyRWZmZWN0KGZuLCB2YWx1ZSwgb3B0aW9ucykge1xuICBjb25zdCBjID0gY3JlYXRlQ29tcHV0YXRpb24oZm4sIHZhbHVlLCBmYWxzZSwgU1RBTEUpO1xuICBpZiAoU2NoZWR1bGVyICYmIFRyYW5zaXRpb24gJiYgVHJhbnNpdGlvbi5ydW5uaW5nKSBVcGRhdGVzLnB1c2goYyk7XG4gIGVsc2UgdXBkYXRlQ29tcHV0YXRpb24oYyk7XG59XG5mdW5jdGlvbiBjcmVhdGVFZmZlY3QoZm4sIHZhbHVlLCBvcHRpb25zKSB7XG4gIHJ1bkVmZmVjdHMgPSBydW5Vc2VyRWZmZWN0cztcbiAgY29uc3QgYyA9IGNyZWF0ZUNvbXB1dGF0aW9uKGZuLCB2YWx1ZSwgZmFsc2UsIFNUQUxFKSwgcyA9IFN1c3BlbnNlQ29udGV4dCAmJiB1c2VDb250ZXh0KFN1c3BlbnNlQ29udGV4dCk7XG4gIGlmIChzKSBjLnN1c3BlbnNlID0gcztcbiAgaWYgKCFvcHRpb25zIHx8ICFvcHRpb25zLnJlbmRlcikgYy51c2VyID0gdHJ1ZTtcbiAgRWZmZWN0cyA/IEVmZmVjdHMucHVzaChjKSA6IHVwZGF0ZUNvbXB1dGF0aW9uKGMpO1xufVxuZnVuY3Rpb24gY3JlYXRlTWVtbyhmbiwgdmFsdWUsIG9wdGlvbnMpIHtcbiAgb3B0aW9ucyA9IG9wdGlvbnMgPyBPYmplY3QuYXNzaWduKHt9LCBzaWduYWxPcHRpb25zLCBvcHRpb25zKSA6IHNpZ25hbE9wdGlvbnM7XG4gIGNvbnN0IGMgPSBjcmVhdGVDb21wdXRhdGlvbihmbiwgdmFsdWUsIHRydWUsIDApO1xuICBjLm9ic2VydmVycyA9IG51bGw7XG4gIGMub2JzZXJ2ZXJTbG90cyA9IG51bGw7XG4gIGMuY29tcGFyYXRvciA9IG9wdGlvbnMuZXF1YWxzIHx8IHZvaWQgMDtcbiAgaWYgKFNjaGVkdWxlciAmJiBUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZykge1xuICAgIGMudFN0YXRlID0gU1RBTEU7XG4gICAgVXBkYXRlcy5wdXNoKGMpO1xuICB9IGVsc2UgdXBkYXRlQ29tcHV0YXRpb24oYyk7XG4gIHJldHVybiByZWFkU2lnbmFsLmJpbmQoYyk7XG59XG5mdW5jdGlvbiBpc1Byb21pc2Uodikge1xuICByZXR1cm4gdiAmJiB0eXBlb2YgdiA9PT0gXCJvYmplY3RcIiAmJiBcInRoZW5cIiBpbiB2O1xufVxuZnVuY3Rpb24gY3JlYXRlUmVzb3VyY2UocFNvdXJjZSwgcEZldGNoZXIsIHBPcHRpb25zKSB7XG4gIGxldCBzb3VyY2U7XG4gIGxldCBmZXRjaGVyO1xuICBsZXQgb3B0aW9ucztcbiAge1xuICAgIHNvdXJjZSA9IHRydWU7XG4gICAgZmV0Y2hlciA9IHBTb3VyY2U7XG4gICAgb3B0aW9ucyA9IHt9O1xuICB9XG4gIGxldCBwciA9IG51bGwsIGluaXRQID0gTk9fSU5JVCwgaWQgPSBudWxsLCBsb2FkZWRVbmRlclRyYW5zaXRpb24gPSBmYWxzZSwgc2NoZWR1bGVkID0gZmFsc2UsIHJlc29sdmVkID0gXCJpbml0aWFsVmFsdWVcIiBpbiBvcHRpb25zLCBkeW5hbWljID0gdHlwZW9mIHNvdXJjZSA9PT0gXCJmdW5jdGlvblwiICYmIGNyZWF0ZU1lbW8oc291cmNlKTtcbiAgY29uc3QgY29udGV4dHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpLCBbdmFsdWUsIHNldFZhbHVlXSA9IChvcHRpb25zLnN0b3JhZ2UgfHwgY3JlYXRlU2lnbmFsKShvcHRpb25zLmluaXRpYWxWYWx1ZSksIFtlcnJvciwgc2V0RXJyb3JdID0gY3JlYXRlU2lnbmFsKHZvaWQgMCksIFt0cmFjaywgdHJpZ2dlcl0gPSBjcmVhdGVTaWduYWwodm9pZCAwLCB7XG4gICAgZXF1YWxzOiBmYWxzZVxuICB9KSwgW3N0YXRlLCBzZXRTdGF0ZV0gPSBjcmVhdGVTaWduYWwocmVzb2x2ZWQgPyBcInJlYWR5XCIgOiBcInVucmVzb2x2ZWRcIik7XG4gIGlmIChzaGFyZWRDb25maWcuY29udGV4dCkge1xuICAgIGlkID0gc2hhcmVkQ29uZmlnLmdldE5leHRDb250ZXh0SWQoKTtcbiAgICBpZiAob3B0aW9ucy5zc3JMb2FkRnJvbSA9PT0gXCJpbml0aWFsXCIpIGluaXRQID0gb3B0aW9ucy5pbml0aWFsVmFsdWU7XG4gICAgZWxzZSBpZiAoc2hhcmVkQ29uZmlnLmxvYWQgJiYgc2hhcmVkQ29uZmlnLmhhcyhpZCkpIGluaXRQID0gc2hhcmVkQ29uZmlnLmxvYWQoaWQpO1xuICB9XG4gIGZ1bmN0aW9uIGxvYWRFbmQocCwgdiwgZXJyb3IyLCBrZXkpIHtcbiAgICBpZiAocHIgPT09IHApIHtcbiAgICAgIHByID0gbnVsbDtcbiAgICAgIGtleSAhPT0gdm9pZCAwICYmIChyZXNvbHZlZCA9IHRydWUpO1xuICAgICAgaWYgKChwID09PSBpbml0UCB8fCB2ID09PSBpbml0UCkgJiYgb3B0aW9ucy5vbkh5ZHJhdGVkKVxuICAgICAgICBxdWV1ZU1pY3JvdGFzayhcbiAgICAgICAgICAoKSA9PiBvcHRpb25zLm9uSHlkcmF0ZWQoa2V5LCB7XG4gICAgICAgICAgICB2YWx1ZTogdlxuICAgICAgICAgIH0pXG4gICAgICAgICk7XG4gICAgICBpbml0UCA9IE5PX0lOSVQ7XG4gICAgICBpZiAoVHJhbnNpdGlvbiAmJiBwICYmIGxvYWRlZFVuZGVyVHJhbnNpdGlvbikge1xuICAgICAgICBUcmFuc2l0aW9uLnByb21pc2VzLmRlbGV0ZShwKTtcbiAgICAgICAgbG9hZGVkVW5kZXJUcmFuc2l0aW9uID0gZmFsc2U7XG4gICAgICAgIHJ1blVwZGF0ZXMoKCkgPT4ge1xuICAgICAgICAgIFRyYW5zaXRpb24ucnVubmluZyA9IHRydWU7XG4gICAgICAgICAgY29tcGxldGVMb2FkKHYsIGVycm9yMik7XG4gICAgICAgIH0sIGZhbHNlKTtcbiAgICAgIH0gZWxzZSBjb21wbGV0ZUxvYWQodiwgZXJyb3IyKTtcbiAgICB9XG4gICAgcmV0dXJuIHY7XG4gIH1cbiAgZnVuY3Rpb24gY29tcGxldGVMb2FkKHYsIGVycikge1xuICAgIHJ1blVwZGF0ZXMoKCkgPT4ge1xuICAgICAgaWYgKGVyciA9PT0gdm9pZCAwKSBzZXRWYWx1ZSgoKSA9PiB2KTtcbiAgICAgIHNldFN0YXRlKGVyciAhPT0gdm9pZCAwID8gXCJlcnJvcmVkXCIgOiByZXNvbHZlZCA/IFwicmVhZHlcIiA6IFwidW5yZXNvbHZlZFwiKTtcbiAgICAgIHNldEVycm9yKGVycik7XG4gICAgICBmb3IgKGNvbnN0IGMgb2YgY29udGV4dHMua2V5cygpKSBjLmRlY3JlbWVudCgpO1xuICAgICAgY29udGV4dHMuY2xlYXIoKTtcbiAgICB9LCBmYWxzZSk7XG4gIH1cbiAgZnVuY3Rpb24gcmVhZCgpIHtcbiAgICBjb25zdCBjID0gU3VzcGVuc2VDb250ZXh0ICYmIHVzZUNvbnRleHQoU3VzcGVuc2VDb250ZXh0KSwgdiA9IHZhbHVlKCksIGVyciA9IGVycm9yKCk7XG4gICAgaWYgKGVyciAhPT0gdm9pZCAwICYmICFwcikgdGhyb3cgZXJyO1xuICAgIGlmIChMaXN0ZW5lciAmJiAhTGlzdGVuZXIudXNlciAmJiBjKSB7XG4gICAgICBjcmVhdGVDb21wdXRlZCgoKSA9PiB7XG4gICAgICAgIHRyYWNrKCk7XG4gICAgICAgIGlmIChwcikge1xuICAgICAgICAgIGlmIChjLnJlc29sdmVkICYmIFRyYW5zaXRpb24gJiYgbG9hZGVkVW5kZXJUcmFuc2l0aW9uKSBUcmFuc2l0aW9uLnByb21pc2VzLmFkZChwcik7XG4gICAgICAgICAgZWxzZSBpZiAoIWNvbnRleHRzLmhhcyhjKSkge1xuICAgICAgICAgICAgYy5pbmNyZW1lbnQoKTtcbiAgICAgICAgICAgIGNvbnRleHRzLmFkZChjKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gdjtcbiAgfVxuICBmdW5jdGlvbiBsb2FkKHJlZmV0Y2hpbmcgPSB0cnVlKSB7XG4gICAgaWYgKHJlZmV0Y2hpbmcgIT09IGZhbHNlICYmIHNjaGVkdWxlZCkgcmV0dXJuO1xuICAgIHNjaGVkdWxlZCA9IGZhbHNlO1xuICAgIGNvbnN0IGxvb2t1cCA9IGR5bmFtaWMgPyBkeW5hbWljKCkgOiBzb3VyY2U7XG4gICAgbG9hZGVkVW5kZXJUcmFuc2l0aW9uID0gVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmc7XG4gICAgaWYgKGxvb2t1cCA9PSBudWxsIHx8IGxvb2t1cCA9PT0gZmFsc2UpIHtcbiAgICAgIGxvYWRFbmQocHIsIHVudHJhY2sodmFsdWUpKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKFRyYW5zaXRpb24gJiYgcHIpIFRyYW5zaXRpb24ucHJvbWlzZXMuZGVsZXRlKHByKTtcbiAgICBjb25zdCBwID0gaW5pdFAgIT09IE5PX0lOSVQgPyBpbml0UCA6IHVudHJhY2soXG4gICAgICAoKSA9PiBmZXRjaGVyKGxvb2t1cCwge1xuICAgICAgICB2YWx1ZTogdmFsdWUoKSxcbiAgICAgICAgcmVmZXRjaGluZ1xuICAgICAgfSlcbiAgICApO1xuICAgIGlmICghaXNQcm9taXNlKHApKSB7XG4gICAgICBsb2FkRW5kKHByLCBwLCB2b2lkIDAsIGxvb2t1cCk7XG4gICAgICByZXR1cm4gcDtcbiAgICB9XG4gICAgcHIgPSBwO1xuICAgIGlmIChcInZhbHVlXCIgaW4gcCkge1xuICAgICAgaWYgKHAuc3RhdHVzID09PSBcInN1Y2Nlc3NcIikgbG9hZEVuZChwciwgcC52YWx1ZSwgdm9pZCAwLCBsb29rdXApO1xuICAgICAgZWxzZSBsb2FkRW5kKHByLCB2b2lkIDAsIGNhc3RFcnJvcihwLnZhbHVlKSwgbG9va3VwKTtcbiAgICAgIHJldHVybiBwO1xuICAgIH1cbiAgICBzY2hlZHVsZWQgPSB0cnVlO1xuICAgIHF1ZXVlTWljcm90YXNrKCgpID0+IHNjaGVkdWxlZCA9IGZhbHNlKTtcbiAgICBydW5VcGRhdGVzKCgpID0+IHtcbiAgICAgIHNldFN0YXRlKHJlc29sdmVkID8gXCJyZWZyZXNoaW5nXCIgOiBcInBlbmRpbmdcIik7XG4gICAgICB0cmlnZ2VyKCk7XG4gICAgfSwgZmFsc2UpO1xuICAgIHJldHVybiBwLnRoZW4oXG4gICAgICAodikgPT4gbG9hZEVuZChwLCB2LCB2b2lkIDAsIGxvb2t1cCksXG4gICAgICAoZSkgPT4gbG9hZEVuZChwLCB2b2lkIDAsIGNhc3RFcnJvcihlKSwgbG9va3VwKVxuICAgICk7XG4gIH1cbiAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMocmVhZCwge1xuICAgIHN0YXRlOiB7XG4gICAgICBnZXQ6ICgpID0+IHN0YXRlKClcbiAgICB9LFxuICAgIGVycm9yOiB7XG4gICAgICBnZXQ6ICgpID0+IGVycm9yKClcbiAgICB9LFxuICAgIGxvYWRpbmc6IHtcbiAgICAgIGdldCgpIHtcbiAgICAgICAgY29uc3QgcyA9IHN0YXRlKCk7XG4gICAgICAgIHJldHVybiBzID09PSBcInBlbmRpbmdcIiB8fCBzID09PSBcInJlZnJlc2hpbmdcIjtcbiAgICAgIH1cbiAgICB9LFxuICAgIGxhdGVzdDoge1xuICAgICAgZ2V0KCkge1xuICAgICAgICBpZiAoIXJlc29sdmVkKSByZXR1cm4gcmVhZCgpO1xuICAgICAgICBjb25zdCBlcnIgPSBlcnJvcigpO1xuICAgICAgICBpZiAoZXJyICYmICFwcikgdGhyb3cgZXJyO1xuICAgICAgICByZXR1cm4gdmFsdWUoKTtcbiAgICAgIH1cbiAgICB9XG4gIH0pO1xuICBpZiAoZHluYW1pYykgY3JlYXRlQ29tcHV0ZWQoKCkgPT4gbG9hZChmYWxzZSkpO1xuICBlbHNlIGxvYWQoZmFsc2UpO1xuICByZXR1cm4gW1xuICAgIHJlYWQsXG4gICAge1xuICAgICAgcmVmZXRjaDogbG9hZCxcbiAgICAgIG11dGF0ZTogc2V0VmFsdWVcbiAgICB9XG4gIF07XG59XG5mdW5jdGlvbiBiYXRjaChmbikge1xuICByZXR1cm4gcnVuVXBkYXRlcyhmbiwgZmFsc2UpO1xufVxuZnVuY3Rpb24gdW50cmFjayhmbikge1xuICBpZiAoIUV4dGVybmFsU291cmNlQ29uZmlnICYmIExpc3RlbmVyID09PSBudWxsKSByZXR1cm4gZm4oKTtcbiAgY29uc3QgbGlzdGVuZXIgPSBMaXN0ZW5lcjtcbiAgTGlzdGVuZXIgPSBudWxsO1xuICB0cnkge1xuICAgIGlmIChFeHRlcm5hbFNvdXJjZUNvbmZpZykgcmV0dXJuIEV4dGVybmFsU291cmNlQ29uZmlnLnVudHJhY2soZm4pO1xuICAgIHJldHVybiBmbigpO1xuICB9IGZpbmFsbHkge1xuICAgIExpc3RlbmVyID0gbGlzdGVuZXI7XG4gIH1cbn1cbmZ1bmN0aW9uIG9uKGRlcHMsIGZuLCBvcHRpb25zKSB7XG4gIGNvbnN0IGlzQXJyYXkzID0gQXJyYXkuaXNBcnJheShkZXBzKTtcbiAgbGV0IHByZXZJbnB1dDtcbiAgbGV0IGRlZmVyID0gb3B0aW9ucyAmJiBvcHRpb25zLmRlZmVyO1xuICByZXR1cm4gKHByZXZWYWx1ZSkgPT4ge1xuICAgIGxldCBpbnB1dDtcbiAgICBpZiAoaXNBcnJheTMpIHtcbiAgICAgIGlucHV0ID0gQXJyYXkoZGVwcy5sZW5ndGgpO1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkZXBzLmxlbmd0aDsgaSsrKSBpbnB1dFtpXSA9IGRlcHNbaV0oKTtcbiAgICB9IGVsc2UgaW5wdXQgPSBkZXBzKCk7XG4gICAgaWYgKGRlZmVyKSB7XG4gICAgICBkZWZlciA9IGZhbHNlO1xuICAgICAgcmV0dXJuIHByZXZWYWx1ZTtcbiAgICB9XG4gICAgY29uc3QgcmVzdWx0ID0gdW50cmFjaygoKSA9PiBmbihpbnB1dCwgcHJldklucHV0LCBwcmV2VmFsdWUpKTtcbiAgICBwcmV2SW5wdXQgPSBpbnB1dDtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9O1xufVxuZnVuY3Rpb24gb25Nb3VudChmbikge1xuICBjcmVhdGVFZmZlY3QoKCkgPT4gdW50cmFjayhmbikpO1xufVxuZnVuY3Rpb24gb25DbGVhbnVwKGZuKSB7XG4gIGlmIChPd25lciA9PT0gbnVsbCkgO1xuICBlbHNlIGlmIChPd25lci5jbGVhbnVwcyA9PT0gbnVsbCkgT3duZXIuY2xlYW51cHMgPSBbZm5dO1xuICBlbHNlIE93bmVyLmNsZWFudXBzLnB1c2goZm4pO1xuICByZXR1cm4gZm47XG59XG5mdW5jdGlvbiBnZXRPd25lcigpIHtcbiAgcmV0dXJuIE93bmVyO1xufVxuZnVuY3Rpb24gcnVuV2l0aE93bmVyKG8sIGZuKSB7XG4gIGNvbnN0IHByZXYgPSBPd25lcjtcbiAgY29uc3QgcHJldkxpc3RlbmVyID0gTGlzdGVuZXI7XG4gIE93bmVyID0gbztcbiAgTGlzdGVuZXIgPSBudWxsO1xuICB0cnkge1xuICAgIHJldHVybiBydW5VcGRhdGVzKGZuLCB0cnVlKTtcbiAgfSBjYXRjaCAoZXJyKSB7XG4gICAgaGFuZGxlRXJyb3IoZXJyKTtcbiAgfSBmaW5hbGx5IHtcbiAgICBPd25lciA9IHByZXY7XG4gICAgTGlzdGVuZXIgPSBwcmV2TGlzdGVuZXI7XG4gIH1cbn1cbmZ1bmN0aW9uIHN0YXJ0VHJhbnNpdGlvbihmbikge1xuICBpZiAoVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcpIHtcbiAgICBmbigpO1xuICAgIHJldHVybiBUcmFuc2l0aW9uLmRvbmU7XG4gIH1cbiAgY29uc3QgbCA9IExpc3RlbmVyO1xuICBjb25zdCBvID0gT3duZXI7XG4gIHJldHVybiBQcm9taXNlLnJlc29sdmUoKS50aGVuKCgpID0+IHtcbiAgICBMaXN0ZW5lciA9IGw7XG4gICAgT3duZXIgPSBvO1xuICAgIGxldCB0O1xuICAgIGlmIChTY2hlZHVsZXIgfHwgU3VzcGVuc2VDb250ZXh0KSB7XG4gICAgICB0ID0gVHJhbnNpdGlvbiB8fCAoVHJhbnNpdGlvbiA9IHtcbiAgICAgICAgc291cmNlczogLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKSxcbiAgICAgICAgZWZmZWN0czogW10sXG4gICAgICAgIHByb21pc2VzOiAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpLFxuICAgICAgICBkaXNwb3NlZDogLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKSxcbiAgICAgICAgcXVldWU6IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCksXG4gICAgICAgIHJ1bm5pbmc6IHRydWVcbiAgICAgIH0pO1xuICAgICAgdC5kb25lIHx8ICh0LmRvbmUgPSBuZXcgUHJvbWlzZSgocmVzKSA9PiB0LnJlc29sdmUgPSByZXMpKTtcbiAgICAgIHQucnVubmluZyA9IHRydWU7XG4gICAgfVxuICAgIHJ1blVwZGF0ZXMoZm4sIGZhbHNlKTtcbiAgICBMaXN0ZW5lciA9IE93bmVyID0gbnVsbDtcbiAgICByZXR1cm4gdCA/IHQuZG9uZSA6IHZvaWQgMDtcbiAgfSk7XG59XG52YXIgW3RyYW5zUGVuZGluZywgc2V0VHJhbnNQZW5kaW5nXSA9IC8qIEBfX1BVUkVfXyAqLyBjcmVhdGVTaWduYWwoZmFsc2UpO1xuZnVuY3Rpb24gdXNlVHJhbnNpdGlvbigpIHtcbiAgcmV0dXJuIFt0cmFuc1BlbmRpbmcsIHN0YXJ0VHJhbnNpdGlvbl07XG59XG5mdW5jdGlvbiBjcmVhdGVDb250ZXh0KGRlZmF1bHRWYWx1ZSwgb3B0aW9ucykge1xuICBjb25zdCBpZCA9IFN5bWJvbChcImNvbnRleHRcIik7XG4gIHJldHVybiB7XG4gICAgaWQsXG4gICAgUHJvdmlkZXI6IGNyZWF0ZVByb3ZpZGVyKGlkKSxcbiAgICBkZWZhdWx0VmFsdWVcbiAgfTtcbn1cbmZ1bmN0aW9uIHVzZUNvbnRleHQoY29udGV4dCkge1xuICBsZXQgdmFsdWU7XG4gIHJldHVybiBPd25lciAmJiBPd25lci5jb250ZXh0ICYmICh2YWx1ZSA9IE93bmVyLmNvbnRleHRbY29udGV4dC5pZF0pICE9PSB2b2lkIDAgPyB2YWx1ZSA6IGNvbnRleHQuZGVmYXVsdFZhbHVlO1xufVxuZnVuY3Rpb24gY2hpbGRyZW4oZm4pIHtcbiAgY29uc3QgY2hpbGRyZW4yID0gY3JlYXRlTWVtbyhmbik7XG4gIGNvbnN0IG1lbW8gPSBjcmVhdGVNZW1vKCgpID0+IHJlc29sdmVDaGlsZHJlbihjaGlsZHJlbjIoKSkpO1xuICBtZW1vLnRvQXJyYXkgPSAoKSA9PiB7XG4gICAgY29uc3QgYyA9IG1lbW8oKTtcbiAgICByZXR1cm4gQXJyYXkuaXNBcnJheShjKSA/IGMgOiBjICE9IG51bGwgPyBbY10gOiBbXTtcbiAgfTtcbiAgcmV0dXJuIG1lbW87XG59XG52YXIgU3VzcGVuc2VDb250ZXh0O1xuZnVuY3Rpb24gcmVhZFNpZ25hbCgpIHtcbiAgY29uc3QgcnVubmluZ1RyYW5zaXRpb24gPSBUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZztcbiAgaWYgKHRoaXMuc291cmNlcyAmJiAocnVubmluZ1RyYW5zaXRpb24gPyB0aGlzLnRTdGF0ZSA6IHRoaXMuc3RhdGUpKSB7XG4gICAgaWYgKChydW5uaW5nVHJhbnNpdGlvbiA/IHRoaXMudFN0YXRlIDogdGhpcy5zdGF0ZSkgPT09IFNUQUxFKSB1cGRhdGVDb21wdXRhdGlvbih0aGlzKTtcbiAgICBlbHNlIHtcbiAgICAgIGNvbnN0IHVwZGF0ZXMgPSBVcGRhdGVzO1xuICAgICAgVXBkYXRlcyA9IG51bGw7XG4gICAgICBydW5VcGRhdGVzKCgpID0+IGxvb2tVcHN0cmVhbSh0aGlzKSwgZmFsc2UpO1xuICAgICAgVXBkYXRlcyA9IHVwZGF0ZXM7XG4gICAgfVxuICB9XG4gIGlmIChMaXN0ZW5lcikge1xuICAgIGNvbnN0IHNTbG90ID0gdGhpcy5vYnNlcnZlcnMgPyB0aGlzLm9ic2VydmVycy5sZW5ndGggOiAwO1xuICAgIGlmICghTGlzdGVuZXIuc291cmNlcykge1xuICAgICAgTGlzdGVuZXIuc291cmNlcyA9IFt0aGlzXTtcbiAgICAgIExpc3RlbmVyLnNvdXJjZVNsb3RzID0gW3NTbG90XTtcbiAgICB9IGVsc2Uge1xuICAgICAgTGlzdGVuZXIuc291cmNlcy5wdXNoKHRoaXMpO1xuICAgICAgTGlzdGVuZXIuc291cmNlU2xvdHMucHVzaChzU2xvdCk7XG4gICAgfVxuICAgIGlmICghdGhpcy5vYnNlcnZlcnMpIHtcbiAgICAgIHRoaXMub2JzZXJ2ZXJzID0gW0xpc3RlbmVyXTtcbiAgICAgIHRoaXMub2JzZXJ2ZXJTbG90cyA9IFtMaXN0ZW5lci5zb3VyY2VzLmxlbmd0aCAtIDFdO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLm9ic2VydmVycy5wdXNoKExpc3RlbmVyKTtcbiAgICAgIHRoaXMub2JzZXJ2ZXJTbG90cy5wdXNoKExpc3RlbmVyLnNvdXJjZXMubGVuZ3RoIC0gMSk7XG4gICAgfVxuICB9XG4gIGlmIChydW5uaW5nVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnNvdXJjZXMuaGFzKHRoaXMpKSByZXR1cm4gdGhpcy50VmFsdWU7XG4gIHJldHVybiB0aGlzLnZhbHVlO1xufVxuZnVuY3Rpb24gd3JpdGVTaWduYWwobm9kZSwgdmFsdWUsIGlzQ29tcCkge1xuICBsZXQgY3VycmVudCA9IFRyYW5zaXRpb24gJiYgVHJhbnNpdGlvbi5ydW5uaW5nICYmIFRyYW5zaXRpb24uc291cmNlcy5oYXMobm9kZSkgPyBub2RlLnRWYWx1ZSA6IG5vZGUudmFsdWU7XG4gIGlmICghbm9kZS5jb21wYXJhdG9yIHx8ICFub2RlLmNvbXBhcmF0b3IoY3VycmVudCwgdmFsdWUpKSB7XG4gICAgaWYgKFRyYW5zaXRpb24pIHtcbiAgICAgIGNvbnN0IFRyYW5zaXRpb25SdW5uaW5nID0gVHJhbnNpdGlvbi5ydW5uaW5nO1xuICAgICAgaWYgKFRyYW5zaXRpb25SdW5uaW5nIHx8ICFpc0NvbXAgJiYgVHJhbnNpdGlvbi5zb3VyY2VzLmhhcyhub2RlKSkge1xuICAgICAgICBUcmFuc2l0aW9uLnNvdXJjZXMuYWRkKG5vZGUpO1xuICAgICAgICBub2RlLnRWYWx1ZSA9IHZhbHVlO1xuICAgICAgfVxuICAgICAgaWYgKCFUcmFuc2l0aW9uUnVubmluZykgbm9kZS52YWx1ZSA9IHZhbHVlO1xuICAgIH0gZWxzZSBub2RlLnZhbHVlID0gdmFsdWU7XG4gICAgaWYgKG5vZGUub2JzZXJ2ZXJzICYmIG5vZGUub2JzZXJ2ZXJzLmxlbmd0aCkge1xuICAgICAgcnVuVXBkYXRlcygoKSA9PiB7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbm9kZS5vYnNlcnZlcnMubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgICBjb25zdCBvID0gbm9kZS5vYnNlcnZlcnNbaV07XG4gICAgICAgICAgY29uc3QgVHJhbnNpdGlvblJ1bm5pbmcgPSBUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZztcbiAgICAgICAgICBpZiAoVHJhbnNpdGlvblJ1bm5pbmcgJiYgVHJhbnNpdGlvbi5kaXNwb3NlZC5oYXMobykpIGNvbnRpbnVlO1xuICAgICAgICAgIGlmIChUcmFuc2l0aW9uUnVubmluZyA/ICFvLnRTdGF0ZSA6ICFvLnN0YXRlKSB7XG4gICAgICAgICAgICBpZiAoby5wdXJlKSBVcGRhdGVzLnB1c2gobyk7XG4gICAgICAgICAgICBlbHNlIEVmZmVjdHMucHVzaChvKTtcbiAgICAgICAgICAgIGlmIChvLm9ic2VydmVycykgbWFya0Rvd25zdHJlYW0obyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmICghVHJhbnNpdGlvblJ1bm5pbmcpIG8uc3RhdGUgPSBTVEFMRTtcbiAgICAgICAgICBlbHNlIG8udFN0YXRlID0gU1RBTEU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKFVwZGF0ZXMubGVuZ3RoID4gMWU2KSB7XG4gICAgICAgICAgVXBkYXRlcyA9IFtdO1xuICAgICAgICAgIGlmIChJU19ERVYpIDtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoKTtcbiAgICAgICAgfVxuICAgICAgfSwgZmFsc2UpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdmFsdWU7XG59XG5mdW5jdGlvbiB1cGRhdGVDb21wdXRhdGlvbihub2RlKSB7XG4gIGlmICghbm9kZS5mbikgcmV0dXJuO1xuICBjbGVhbk5vZGUobm9kZSk7XG4gIGNvbnN0IHRpbWUgPSBFeGVjQ291bnQ7XG4gIHJ1bkNvbXB1dGF0aW9uKFxuICAgIG5vZGUsXG4gICAgVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcgJiYgVHJhbnNpdGlvbi5zb3VyY2VzLmhhcyhub2RlKSA/IG5vZGUudFZhbHVlIDogbm9kZS52YWx1ZSxcbiAgICB0aW1lXG4gICk7XG4gIGlmIChUcmFuc2l0aW9uICYmICFUcmFuc2l0aW9uLnJ1bm5pbmcgJiYgVHJhbnNpdGlvbi5zb3VyY2VzLmhhcyhub2RlKSkge1xuICAgIHF1ZXVlTWljcm90YXNrKCgpID0+IHtcbiAgICAgIHJ1blVwZGF0ZXMoKCkgPT4ge1xuICAgICAgICBUcmFuc2l0aW9uICYmIChUcmFuc2l0aW9uLnJ1bm5pbmcgPSB0cnVlKTtcbiAgICAgICAgTGlzdGVuZXIgPSBPd25lciA9IG5vZGU7XG4gICAgICAgIHJ1bkNvbXB1dGF0aW9uKG5vZGUsIG5vZGUudFZhbHVlLCB0aW1lKTtcbiAgICAgICAgTGlzdGVuZXIgPSBPd25lciA9IG51bGw7XG4gICAgICB9LCBmYWxzZSk7XG4gICAgfSk7XG4gIH1cbn1cbmZ1bmN0aW9uIHJ1bkNvbXB1dGF0aW9uKG5vZGUsIHZhbHVlLCB0aW1lKSB7XG4gIGxldCBuZXh0VmFsdWU7XG4gIGNvbnN0IG93bmVyID0gT3duZXIsIGxpc3RlbmVyID0gTGlzdGVuZXI7XG4gIExpc3RlbmVyID0gT3duZXIgPSBub2RlO1xuICB0cnkge1xuICAgIG5leHRWYWx1ZSA9IG5vZGUuZm4odmFsdWUpO1xuICB9IGNhdGNoIChlcnIpIHtcbiAgICBpZiAobm9kZS5wdXJlKSB7XG4gICAgICBpZiAoVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcpIHtcbiAgICAgICAgbm9kZS50U3RhdGUgPSBTVEFMRTtcbiAgICAgICAgbm9kZS50T3duZWQgJiYgbm9kZS50T3duZWQuZm9yRWFjaChjbGVhbk5vZGUpO1xuICAgICAgICBub2RlLnRPd25lZCA9IHZvaWQgMDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG5vZGUuc3RhdGUgPSBTVEFMRTtcbiAgICAgICAgbm9kZS5vd25lZCAmJiBub2RlLm93bmVkLmZvckVhY2goY2xlYW5Ob2RlKTtcbiAgICAgICAgbm9kZS5vd25lZCA9IG51bGw7XG4gICAgICB9XG4gICAgfVxuICAgIG5vZGUudXBkYXRlZEF0ID0gdGltZSArIDE7XG4gICAgcmV0dXJuIGhhbmRsZUVycm9yKGVycik7XG4gIH0gZmluYWxseSB7XG4gICAgTGlzdGVuZXIgPSBsaXN0ZW5lcjtcbiAgICBPd25lciA9IG93bmVyO1xuICB9XG4gIGlmICghbm9kZS51cGRhdGVkQXQgfHwgbm9kZS51cGRhdGVkQXQgPD0gdGltZSkge1xuICAgIGlmIChub2RlLnVwZGF0ZWRBdCAhPSBudWxsICYmIFwib2JzZXJ2ZXJzXCIgaW4gbm9kZSkge1xuICAgICAgd3JpdGVTaWduYWwobm9kZSwgbmV4dFZhbHVlLCB0cnVlKTtcbiAgICB9IGVsc2UgaWYgKFRyYW5zaXRpb24gJiYgVHJhbnNpdGlvbi5ydW5uaW5nICYmIG5vZGUucHVyZSkge1xuICAgICAgVHJhbnNpdGlvbi5zb3VyY2VzLmFkZChub2RlKTtcbiAgICAgIG5vZGUudFZhbHVlID0gbmV4dFZhbHVlO1xuICAgIH0gZWxzZSBub2RlLnZhbHVlID0gbmV4dFZhbHVlO1xuICAgIG5vZGUudXBkYXRlZEF0ID0gdGltZTtcbiAgfVxufVxuZnVuY3Rpb24gY3JlYXRlQ29tcHV0YXRpb24oZm4sIGluaXQsIHB1cmUsIHN0YXRlID0gU1RBTEUsIG9wdGlvbnMpIHtcbiAgY29uc3QgYyA9IHtcbiAgICBmbixcbiAgICBzdGF0ZSxcbiAgICB1cGRhdGVkQXQ6IG51bGwsXG4gICAgb3duZWQ6IG51bGwsXG4gICAgc291cmNlczogbnVsbCxcbiAgICBzb3VyY2VTbG90czogbnVsbCxcbiAgICBjbGVhbnVwczogbnVsbCxcbiAgICB2YWx1ZTogaW5pdCxcbiAgICBvd25lcjogT3duZXIsXG4gICAgY29udGV4dDogT3duZXIgPyBPd25lci5jb250ZXh0IDogbnVsbCxcbiAgICBwdXJlXG4gIH07XG4gIGlmIChUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZykge1xuICAgIGMuc3RhdGUgPSAwO1xuICAgIGMudFN0YXRlID0gc3RhdGU7XG4gIH1cbiAgaWYgKE93bmVyID09PSBudWxsKSA7XG4gIGVsc2UgaWYgKE93bmVyICE9PSBVTk9XTkVEKSB7XG4gICAgaWYgKFRyYW5zaXRpb24gJiYgVHJhbnNpdGlvbi5ydW5uaW5nICYmIE93bmVyLnB1cmUpIHtcbiAgICAgIGlmICghT3duZXIudE93bmVkKSBPd25lci50T3duZWQgPSBbY107XG4gICAgICBlbHNlIE93bmVyLnRPd25lZC5wdXNoKGMpO1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAoIU93bmVyLm93bmVkKSBPd25lci5vd25lZCA9IFtjXTtcbiAgICAgIGVsc2UgT3duZXIub3duZWQucHVzaChjKTtcbiAgICB9XG4gIH1cbiAgaWYgKEV4dGVybmFsU291cmNlQ29uZmlnICYmIGMuZm4pIHtcbiAgICBjb25zdCBbdHJhY2ssIHRyaWdnZXJdID0gY3JlYXRlU2lnbmFsKHZvaWQgMCwge1xuICAgICAgZXF1YWxzOiBmYWxzZVxuICAgIH0pO1xuICAgIGNvbnN0IG9yZGluYXJ5ID0gRXh0ZXJuYWxTb3VyY2VDb25maWcuZmFjdG9yeShjLmZuLCB0cmlnZ2VyKTtcbiAgICBvbkNsZWFudXAoKCkgPT4gb3JkaW5hcnkuZGlzcG9zZSgpKTtcbiAgICBjb25zdCB0cmlnZ2VySW5UcmFuc2l0aW9uID0gKCkgPT4gc3RhcnRUcmFuc2l0aW9uKHRyaWdnZXIpLnRoZW4oKCkgPT4gaW5UcmFuc2l0aW9uLmRpc3Bvc2UoKSk7XG4gICAgY29uc3QgaW5UcmFuc2l0aW9uID0gRXh0ZXJuYWxTb3VyY2VDb25maWcuZmFjdG9yeShjLmZuLCB0cmlnZ2VySW5UcmFuc2l0aW9uKTtcbiAgICBjLmZuID0gKHgpID0+IHtcbiAgICAgIHRyYWNrKCk7XG4gICAgICByZXR1cm4gVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcgPyBpblRyYW5zaXRpb24udHJhY2soeCkgOiBvcmRpbmFyeS50cmFjayh4KTtcbiAgICB9O1xuICB9XG4gIHJldHVybiBjO1xufVxuZnVuY3Rpb24gcnVuVG9wKG5vZGUpIHtcbiAgY29uc3QgcnVubmluZ1RyYW5zaXRpb24gPSBUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZztcbiAgaWYgKChydW5uaW5nVHJhbnNpdGlvbiA/IG5vZGUudFN0YXRlIDogbm9kZS5zdGF0ZSkgPT09IDApIHJldHVybjtcbiAgaWYgKChydW5uaW5nVHJhbnNpdGlvbiA/IG5vZGUudFN0YXRlIDogbm9kZS5zdGF0ZSkgPT09IFBFTkRJTkcpIHJldHVybiBsb29rVXBzdHJlYW0obm9kZSk7XG4gIGlmIChub2RlLnN1c3BlbnNlICYmIHVudHJhY2sobm9kZS5zdXNwZW5zZS5pbkZhbGxiYWNrKSkgcmV0dXJuIG5vZGUuc3VzcGVuc2UuZWZmZWN0cy5wdXNoKG5vZGUpO1xuICBjb25zdCBhbmNlc3RvcnMgPSBbbm9kZV07XG4gIHdoaWxlICgobm9kZSA9IG5vZGUub3duZXIpICYmICghbm9kZS51cGRhdGVkQXQgfHwgbm9kZS51cGRhdGVkQXQgPCBFeGVjQ291bnQpKSB7XG4gICAgaWYgKHJ1bm5pbmdUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24uZGlzcG9zZWQuaGFzKG5vZGUpKSByZXR1cm47XG4gICAgaWYgKHJ1bm5pbmdUcmFuc2l0aW9uID8gbm9kZS50U3RhdGUgOiBub2RlLnN0YXRlKSBhbmNlc3RvcnMucHVzaChub2RlKTtcbiAgfVxuICBmb3IgKGxldCBpID0gYW5jZXN0b3JzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgbm9kZSA9IGFuY2VzdG9yc1tpXTtcbiAgICBpZiAocnVubmluZ1RyYW5zaXRpb24pIHtcbiAgICAgIGxldCB0b3AgPSBub2RlLCBwcmV2ID0gYW5jZXN0b3JzW2kgKyAxXTtcbiAgICAgIHdoaWxlICgodG9wID0gdG9wLm93bmVyKSAmJiB0b3AgIT09IHByZXYpIHtcbiAgICAgICAgaWYgKFRyYW5zaXRpb24uZGlzcG9zZWQuaGFzKHRvcCkpIHJldHVybjtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKChydW5uaW5nVHJhbnNpdGlvbiA/IG5vZGUudFN0YXRlIDogbm9kZS5zdGF0ZSkgPT09IFNUQUxFKSB7XG4gICAgICB1cGRhdGVDb21wdXRhdGlvbihub2RlKTtcbiAgICB9IGVsc2UgaWYgKChydW5uaW5nVHJhbnNpdGlvbiA/IG5vZGUudFN0YXRlIDogbm9kZS5zdGF0ZSkgPT09IFBFTkRJTkcpIHtcbiAgICAgIGNvbnN0IHVwZGF0ZXMgPSBVcGRhdGVzO1xuICAgICAgVXBkYXRlcyA9IG51bGw7XG4gICAgICBydW5VcGRhdGVzKCgpID0+IGxvb2tVcHN0cmVhbShub2RlLCBhbmNlc3RvcnNbMF0pLCBmYWxzZSk7XG4gICAgICBVcGRhdGVzID0gdXBkYXRlcztcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIHJ1blVwZGF0ZXMoZm4sIGluaXQpIHtcbiAgaWYgKFVwZGF0ZXMpIHJldHVybiBmbigpO1xuICBsZXQgd2FpdCA9IGZhbHNlO1xuICBpZiAoIWluaXQpIFVwZGF0ZXMgPSBbXTtcbiAgaWYgKEVmZmVjdHMpIHdhaXQgPSB0cnVlO1xuICBlbHNlIEVmZmVjdHMgPSBbXTtcbiAgRXhlY0NvdW50Kys7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzID0gZm4oKTtcbiAgICBjb21wbGV0ZVVwZGF0ZXMod2FpdCk7XG4gICAgcmV0dXJuIHJlcztcbiAgfSBjYXRjaCAoZXJyKSB7XG4gICAgaWYgKCF3YWl0KSBFZmZlY3RzID0gbnVsbDtcbiAgICBVcGRhdGVzID0gbnVsbDtcbiAgICBoYW5kbGVFcnJvcihlcnIpO1xuICB9XG59XG5mdW5jdGlvbiBjb21wbGV0ZVVwZGF0ZXMod2FpdCkge1xuICBpZiAoVXBkYXRlcykge1xuICAgIGlmIChTY2hlZHVsZXIgJiYgVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcpIHNjaGVkdWxlUXVldWUoVXBkYXRlcyk7XG4gICAgZWxzZSBydW5RdWV1ZShVcGRhdGVzKTtcbiAgICBVcGRhdGVzID0gbnVsbDtcbiAgfVxuICBpZiAod2FpdCkgcmV0dXJuO1xuICBsZXQgcmVzO1xuICBpZiAoVHJhbnNpdGlvbikge1xuICAgIGlmICghVHJhbnNpdGlvbi5wcm9taXNlcy5zaXplICYmICFUcmFuc2l0aW9uLnF1ZXVlLnNpemUpIHtcbiAgICAgIGNvbnN0IHNvdXJjZXMgPSBUcmFuc2l0aW9uLnNvdXJjZXM7XG4gICAgICBjb25zdCBkaXNwb3NlZCA9IFRyYW5zaXRpb24uZGlzcG9zZWQ7XG4gICAgICBFZmZlY3RzLnB1c2guYXBwbHkoRWZmZWN0cywgVHJhbnNpdGlvbi5lZmZlY3RzKTtcbiAgICAgIHJlcyA9IFRyYW5zaXRpb24ucmVzb2x2ZTtcbiAgICAgIGZvciAoY29uc3QgZTIgb2YgRWZmZWN0cykge1xuICAgICAgICBcInRTdGF0ZVwiIGluIGUyICYmIChlMi5zdGF0ZSA9IGUyLnRTdGF0ZSk7XG4gICAgICAgIGRlbGV0ZSBlMi50U3RhdGU7XG4gICAgICB9XG4gICAgICBUcmFuc2l0aW9uID0gbnVsbDtcbiAgICAgIHJ1blVwZGF0ZXMoKCkgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IGQgb2YgZGlzcG9zZWQpIGNsZWFuTm9kZShkKTtcbiAgICAgICAgZm9yIChjb25zdCB2IG9mIHNvdXJjZXMpIHtcbiAgICAgICAgICB2LnZhbHVlID0gdi50VmFsdWU7XG4gICAgICAgICAgaWYgKHYub3duZWQpIHtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwLCBsZW4gPSB2Lm93bmVkLmxlbmd0aDsgaSA8IGxlbjsgaSsrKSBjbGVhbk5vZGUodi5vd25lZFtpXSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmICh2LnRPd25lZCkgdi5vd25lZCA9IHYudE93bmVkO1xuICAgICAgICAgIGRlbGV0ZSB2LnRWYWx1ZTtcbiAgICAgICAgICBkZWxldGUgdi50T3duZWQ7XG4gICAgICAgICAgdi50U3RhdGUgPSAwO1xuICAgICAgICB9XG4gICAgICAgIHNldFRyYW5zUGVuZGluZyhmYWxzZSk7XG4gICAgICB9LCBmYWxzZSk7XG4gICAgfSBlbHNlIGlmIChUcmFuc2l0aW9uLnJ1bm5pbmcpIHtcbiAgICAgIFRyYW5zaXRpb24ucnVubmluZyA9IGZhbHNlO1xuICAgICAgVHJhbnNpdGlvbi5lZmZlY3RzLnB1c2guYXBwbHkoVHJhbnNpdGlvbi5lZmZlY3RzLCBFZmZlY3RzKTtcbiAgICAgIEVmZmVjdHMgPSBudWxsO1xuICAgICAgc2V0VHJhbnNQZW5kaW5nKHRydWUpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgfVxuICBjb25zdCBlID0gRWZmZWN0cztcbiAgRWZmZWN0cyA9IG51bGw7XG4gIGlmIChlLmxlbmd0aCkgcnVuVXBkYXRlcygoKSA9PiBydW5FZmZlY3RzKGUpLCBmYWxzZSk7XG4gIGlmIChyZXMpIHJlcygpO1xufVxuZnVuY3Rpb24gcnVuUXVldWUocXVldWUpIHtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBxdWV1ZS5sZW5ndGg7IGkrKykgcnVuVG9wKHF1ZXVlW2ldKTtcbn1cbmZ1bmN0aW9uIHNjaGVkdWxlUXVldWUocXVldWUpIHtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBxdWV1ZS5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGl0ZW0gPSBxdWV1ZVtpXTtcbiAgICBjb25zdCB0YXNrcyA9IFRyYW5zaXRpb24ucXVldWU7XG4gICAgaWYgKCF0YXNrcy5oYXMoaXRlbSkpIHtcbiAgICAgIHRhc2tzLmFkZChpdGVtKTtcbiAgICAgIFNjaGVkdWxlcigoKSA9PiB7XG4gICAgICAgIHRhc2tzLmRlbGV0ZShpdGVtKTtcbiAgICAgICAgcnVuVXBkYXRlcygoKSA9PiB7XG4gICAgICAgICAgVHJhbnNpdGlvbi5ydW5uaW5nID0gdHJ1ZTtcbiAgICAgICAgICBydW5Ub3AoaXRlbSk7XG4gICAgICAgIH0sIGZhbHNlKTtcbiAgICAgICAgVHJhbnNpdGlvbiAmJiAoVHJhbnNpdGlvbi5ydW5uaW5nID0gZmFsc2UpO1xuICAgICAgfSk7XG4gICAgfVxuICB9XG59XG5mdW5jdGlvbiBydW5Vc2VyRWZmZWN0cyhxdWV1ZSkge1xuICBsZXQgaSwgdXNlckxlbmd0aCA9IDA7XG4gIGZvciAoaSA9IDA7IGkgPCBxdWV1ZS5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGUgPSBxdWV1ZVtpXTtcbiAgICBpZiAoIWUudXNlcikgcnVuVG9wKGUpO1xuICAgIGVsc2UgcXVldWVbdXNlckxlbmd0aCsrXSA9IGU7XG4gIH1cbiAgaWYgKHNoYXJlZENvbmZpZy5jb250ZXh0KSB7XG4gICAgaWYgKHNoYXJlZENvbmZpZy5jb3VudCkge1xuICAgICAgc2hhcmVkQ29uZmlnLmVmZmVjdHMgfHwgKHNoYXJlZENvbmZpZy5lZmZlY3RzID0gW10pO1xuICAgICAgc2hhcmVkQ29uZmlnLmVmZmVjdHMucHVzaCguLi5xdWV1ZS5zbGljZSgwLCB1c2VyTGVuZ3RoKSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHNldEh5ZHJhdGVDb250ZXh0KCk7XG4gIH1cbiAgaWYgKHNoYXJlZENvbmZpZy5lZmZlY3RzICYmIChzaGFyZWRDb25maWcuZG9uZSB8fCAhc2hhcmVkQ29uZmlnLmNvdW50KSkge1xuICAgIHF1ZXVlID0gWy4uLnNoYXJlZENvbmZpZy5lZmZlY3RzLCAuLi5xdWV1ZV07XG4gICAgdXNlckxlbmd0aCArPSBzaGFyZWRDb25maWcuZWZmZWN0cy5sZW5ndGg7XG4gICAgZGVsZXRlIHNoYXJlZENvbmZpZy5lZmZlY3RzO1xuICB9XG4gIGZvciAoaSA9IDA7IGkgPCB1c2VyTGVuZ3RoOyBpKyspIHJ1blRvcChxdWV1ZVtpXSk7XG59XG5mdW5jdGlvbiBsb29rVXBzdHJlYW0obm9kZSwgaWdub3JlKSB7XG4gIGNvbnN0IHJ1bm5pbmdUcmFuc2l0aW9uID0gVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmc7XG4gIGlmIChydW5uaW5nVHJhbnNpdGlvbikgbm9kZS50U3RhdGUgPSAwO1xuICBlbHNlIG5vZGUuc3RhdGUgPSAwO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IG5vZGUuc291cmNlcy5sZW5ndGg7IGkgKz0gMSkge1xuICAgIGNvbnN0IHNvdXJjZSA9IG5vZGUuc291cmNlc1tpXTtcbiAgICBpZiAoc291cmNlLnNvdXJjZXMpIHtcbiAgICAgIGNvbnN0IHN0YXRlID0gcnVubmluZ1RyYW5zaXRpb24gPyBzb3VyY2UudFN0YXRlIDogc291cmNlLnN0YXRlO1xuICAgICAgaWYgKHN0YXRlID09PSBTVEFMRSkge1xuICAgICAgICBpZiAoc291cmNlICE9PSBpZ25vcmUgJiYgKCFzb3VyY2UudXBkYXRlZEF0IHx8IHNvdXJjZS51cGRhdGVkQXQgPCBFeGVjQ291bnQpKVxuICAgICAgICAgIHJ1blRvcChzb3VyY2UpO1xuICAgICAgfSBlbHNlIGlmIChzdGF0ZSA9PT0gUEVORElORykgbG9va1Vwc3RyZWFtKHNvdXJjZSwgaWdub3JlKTtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIG1hcmtEb3duc3RyZWFtKG5vZGUpIHtcbiAgY29uc3QgcnVubmluZ1RyYW5zaXRpb24gPSBUcmFuc2l0aW9uICYmIFRyYW5zaXRpb24ucnVubmluZztcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBub2RlLm9ic2VydmVycy5sZW5ndGg7IGkgKz0gMSkge1xuICAgIGNvbnN0IG8gPSBub2RlLm9ic2VydmVyc1tpXTtcbiAgICBpZiAocnVubmluZ1RyYW5zaXRpb24gPyAhby50U3RhdGUgOiAhby5zdGF0ZSkge1xuICAgICAgaWYgKHJ1bm5pbmdUcmFuc2l0aW9uKSBvLnRTdGF0ZSA9IFBFTkRJTkc7XG4gICAgICBlbHNlIG8uc3RhdGUgPSBQRU5ESU5HO1xuICAgICAgaWYgKG8ucHVyZSkgVXBkYXRlcy5wdXNoKG8pO1xuICAgICAgZWxzZSBFZmZlY3RzLnB1c2gobyk7XG4gICAgICBvLm9ic2VydmVycyAmJiBtYXJrRG93bnN0cmVhbShvKTtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIGNsZWFuTm9kZShub2RlKSB7XG4gIGxldCBpO1xuICBpZiAobm9kZS5zb3VyY2VzKSB7XG4gICAgd2hpbGUgKG5vZGUuc291cmNlcy5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IHNvdXJjZSA9IG5vZGUuc291cmNlcy5wb3AoKSwgaW5kZXggPSBub2RlLnNvdXJjZVNsb3RzLnBvcCgpLCBvYnMgPSBzb3VyY2Uub2JzZXJ2ZXJzO1xuICAgICAgaWYgKG9icyAmJiBvYnMubGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IG4gPSBvYnMucG9wKCksIHMgPSBzb3VyY2Uub2JzZXJ2ZXJTbG90cy5wb3AoKTtcbiAgICAgICAgaWYgKGluZGV4IDwgb2JzLmxlbmd0aCkge1xuICAgICAgICAgIG4uc291cmNlU2xvdHNbc10gPSBpbmRleDtcbiAgICAgICAgICBvYnNbaW5kZXhdID0gbjtcbiAgICAgICAgICBzb3VyY2Uub2JzZXJ2ZXJTbG90c1tpbmRleF0gPSBzO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGlmIChub2RlLnRPd25lZCkge1xuICAgIGZvciAoaSA9IG5vZGUudE93bmVkLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSBjbGVhbk5vZGUobm9kZS50T3duZWRbaV0pO1xuICAgIGRlbGV0ZSBub2RlLnRPd25lZDtcbiAgfVxuICBpZiAoVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcgJiYgbm9kZS5wdXJlKSB7XG4gICAgcmVzZXQobm9kZSwgdHJ1ZSk7XG4gIH0gZWxzZSBpZiAobm9kZS5vd25lZCkge1xuICAgIGZvciAoaSA9IG5vZGUub3duZWQubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIGNsZWFuTm9kZShub2RlLm93bmVkW2ldKTtcbiAgICBub2RlLm93bmVkID0gbnVsbDtcbiAgfVxuICBpZiAobm9kZS5jbGVhbnVwcykge1xuICAgIGZvciAoaSA9IG5vZGUuY2xlYW51cHMubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIG5vZGUuY2xlYW51cHNbaV0oKTtcbiAgICBub2RlLmNsZWFudXBzID0gbnVsbDtcbiAgfVxuICBpZiAoVHJhbnNpdGlvbiAmJiBUcmFuc2l0aW9uLnJ1bm5pbmcpIG5vZGUudFN0YXRlID0gMDtcbiAgZWxzZSBub2RlLnN0YXRlID0gMDtcbn1cbmZ1bmN0aW9uIHJlc2V0KG5vZGUsIHRvcCkge1xuICBpZiAoIXRvcCkge1xuICAgIG5vZGUudFN0YXRlID0gMDtcbiAgICBUcmFuc2l0aW9uLmRpc3Bvc2VkLmFkZChub2RlKTtcbiAgfVxuICBpZiAobm9kZS5vd25lZCkge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbm9kZS5vd25lZC5sZW5ndGg7IGkrKykgcmVzZXQobm9kZS5vd25lZFtpXSk7XG4gIH1cbn1cbmZ1bmN0aW9uIGNhc3RFcnJvcihlcnIpIHtcbiAgaWYgKGVyciBpbnN0YW5jZW9mIEVycm9yKSByZXR1cm4gZXJyO1xuICByZXR1cm4gbmV3IEVycm9yKHR5cGVvZiBlcnIgPT09IFwic3RyaW5nXCIgPyBlcnIgOiBcIlVua25vd24gZXJyb3JcIiwge1xuICAgIGNhdXNlOiBlcnJcbiAgfSk7XG59XG5mdW5jdGlvbiBydW5FcnJvcnMoZXJyLCBmbnMsIG93bmVyKSB7XG4gIHRyeSB7XG4gICAgZm9yIChjb25zdCBmIG9mIGZucykgZihlcnIpO1xuICB9IGNhdGNoIChlKSB7XG4gICAgaGFuZGxlRXJyb3IoZSwgb3duZXIgJiYgb3duZXIub3duZXIgfHwgbnVsbCk7XG4gIH1cbn1cbmZ1bmN0aW9uIGhhbmRsZUVycm9yKGVyciwgb3duZXIgPSBPd25lcikge1xuICBjb25zdCBmbnMgPSBFUlJPUiAmJiBvd25lciAmJiBvd25lci5jb250ZXh0ICYmIG93bmVyLmNvbnRleHRbRVJST1JdO1xuICBjb25zdCBlcnJvciA9IGNhc3RFcnJvcihlcnIpO1xuICBpZiAoIWZucykgdGhyb3cgZXJyb3I7XG4gIGlmIChFZmZlY3RzKVxuICAgIEVmZmVjdHMucHVzaCh7XG4gICAgICBmbigpIHtcbiAgICAgICAgcnVuRXJyb3JzKGVycm9yLCBmbnMsIG93bmVyKTtcbiAgICAgIH0sXG4gICAgICBzdGF0ZTogU1RBTEVcbiAgICB9KTtcbiAgZWxzZSBydW5FcnJvcnMoZXJyb3IsIGZucywgb3duZXIpO1xufVxuZnVuY3Rpb24gcmVzb2x2ZUNoaWxkcmVuKGNoaWxkcmVuMikge1xuICBpZiAodHlwZW9mIGNoaWxkcmVuMiA9PT0gXCJmdW5jdGlvblwiICYmICFjaGlsZHJlbjIubGVuZ3RoKSByZXR1cm4gcmVzb2x2ZUNoaWxkcmVuKGNoaWxkcmVuMigpKTtcbiAgaWYgKEFycmF5LmlzQXJyYXkoY2hpbGRyZW4yKSkge1xuICAgIGNvbnN0IHJlc3VsdHMgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNoaWxkcmVuMi5sZW5ndGg7IGkrKykge1xuICAgICAgY29uc3QgcmVzdWx0ID0gcmVzb2x2ZUNoaWxkcmVuKGNoaWxkcmVuMltpXSk7XG4gICAgICBBcnJheS5pc0FycmF5KHJlc3VsdCkgPyByZXN1bHRzLnB1c2guYXBwbHkocmVzdWx0cywgcmVzdWx0KSA6IHJlc3VsdHMucHVzaChyZXN1bHQpO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0cztcbiAgfVxuICByZXR1cm4gY2hpbGRyZW4yO1xufVxuZnVuY3Rpb24gY3JlYXRlUHJvdmlkZXIoaWQsIG9wdGlvbnMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIHByb3ZpZGVyKHByb3BzKSB7XG4gICAgbGV0IHJlcztcbiAgICBjcmVhdGVSZW5kZXJFZmZlY3QoXG4gICAgICAoKSA9PiByZXMgPSB1bnRyYWNrKCgpID0+IHtcbiAgICAgICAgT3duZXIuY29udGV4dCA9IHtcbiAgICAgICAgICAuLi5Pd25lci5jb250ZXh0LFxuICAgICAgICAgIFtpZF06IHByb3BzLnZhbHVlXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBjaGlsZHJlbigoKSA9PiBwcm9wcy5jaGlsZHJlbik7XG4gICAgICB9KSxcbiAgICAgIHZvaWQgMFxuICAgICk7XG4gICAgcmV0dXJuIHJlcztcbiAgfTtcbn1cbnZhciBGQUxMQkFDSyA9IFN5bWJvbChcImZhbGxiYWNrXCIpO1xuZnVuY3Rpb24gZGlzcG9zZShkKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgZC5sZW5ndGg7IGkrKykgZFtpXSgpO1xufVxuZnVuY3Rpb24gbWFwQXJyYXkobGlzdCwgbWFwRm4sIG9wdGlvbnMgPSB7fSkge1xuICBsZXQgaXRlbXMgPSBbXSwgbWFwcGVkID0gW10sIGRpc3Bvc2VycyA9IFtdLCBsZW4gPSAwLCBpbmRleGVzID0gbWFwRm4ubGVuZ3RoID4gMSA/IFtdIDogbnVsbDtcbiAgb25DbGVhbnVwKCgpID0+IGRpc3Bvc2UoZGlzcG9zZXJzKSk7XG4gIHJldHVybiAoKSA9PiB7XG4gICAgbGV0IG5ld0l0ZW1zID0gbGlzdCgpIHx8IFtdLCBuZXdMZW4gPSBuZXdJdGVtcy5sZW5ndGgsIGksIGo7XG4gICAgbmV3SXRlbXNbJFRSQUNLXTtcbiAgICByZXR1cm4gdW50cmFjaygoKSA9PiB7XG4gICAgICBsZXQgbmV3SW5kaWNlcywgbmV3SW5kaWNlc05leHQsIHRlbXAsIHRlbXBkaXNwb3NlcnMsIHRlbXBJbmRleGVzLCBzdGFydCwgZW5kLCBuZXdFbmQsIGl0ZW07XG4gICAgICBpZiAobmV3TGVuID09PSAwKSB7XG4gICAgICAgIGlmIChsZW4gIT09IDApIHtcbiAgICAgICAgICBkaXNwb3NlKGRpc3Bvc2Vycyk7XG4gICAgICAgICAgZGlzcG9zZXJzID0gW107XG4gICAgICAgICAgaXRlbXMgPSBbXTtcbiAgICAgICAgICBtYXBwZWQgPSBbXTtcbiAgICAgICAgICBsZW4gPSAwO1xuICAgICAgICAgIGluZGV4ZXMgJiYgKGluZGV4ZXMgPSBbXSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMuZmFsbGJhY2spIHtcbiAgICAgICAgICBpdGVtcyA9IFtGQUxMQkFDS107XG4gICAgICAgICAgbWFwcGVkWzBdID0gY3JlYXRlUm9vdCgoZGlzcG9zZXIpID0+IHtcbiAgICAgICAgICAgIGRpc3Bvc2Vyc1swXSA9IGRpc3Bvc2VyO1xuICAgICAgICAgICAgcmV0dXJuIG9wdGlvbnMuZmFsbGJhY2soKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBsZW4gPSAxO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKGxlbiA9PT0gMCkge1xuICAgICAgICBtYXBwZWQgPSBuZXcgQXJyYXkobmV3TGVuKTtcbiAgICAgICAgZm9yIChqID0gMDsgaiA8IG5ld0xlbjsgaisrKSB7XG4gICAgICAgICAgaXRlbXNbal0gPSBuZXdJdGVtc1tqXTtcbiAgICAgICAgICBtYXBwZWRbal0gPSBjcmVhdGVSb290KG1hcHBlcik7XG4gICAgICAgIH1cbiAgICAgICAgbGVuID0gbmV3TGVuO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGVtcCA9IG5ldyBBcnJheShuZXdMZW4pO1xuICAgICAgICB0ZW1wZGlzcG9zZXJzID0gbmV3IEFycmF5KG5ld0xlbik7XG4gICAgICAgIGluZGV4ZXMgJiYgKHRlbXBJbmRleGVzID0gbmV3IEFycmF5KG5ld0xlbikpO1xuICAgICAgICBmb3IgKHN0YXJ0ID0gMCwgZW5kID0gTWF0aC5taW4obGVuLCBuZXdMZW4pOyBzdGFydCA8IGVuZCAmJiBpdGVtc1tzdGFydF0gPT09IG5ld0l0ZW1zW3N0YXJ0XTsgc3RhcnQrKykgO1xuICAgICAgICBmb3IgKGVuZCA9IGxlbiAtIDEsIG5ld0VuZCA9IG5ld0xlbiAtIDE7IGVuZCA+PSBzdGFydCAmJiBuZXdFbmQgPj0gc3RhcnQgJiYgaXRlbXNbZW5kXSA9PT0gbmV3SXRlbXNbbmV3RW5kXTsgZW5kLS0sIG5ld0VuZC0tKSB7XG4gICAgICAgICAgdGVtcFtuZXdFbmRdID0gbWFwcGVkW2VuZF07XG4gICAgICAgICAgdGVtcGRpc3Bvc2Vyc1tuZXdFbmRdID0gZGlzcG9zZXJzW2VuZF07XG4gICAgICAgICAgaW5kZXhlcyAmJiAodGVtcEluZGV4ZXNbbmV3RW5kXSA9IGluZGV4ZXNbZW5kXSk7XG4gICAgICAgIH1cbiAgICAgICAgbmV3SW5kaWNlcyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG4gICAgICAgIG5ld0luZGljZXNOZXh0ID0gbmV3IEFycmF5KG5ld0VuZCArIDEpO1xuICAgICAgICBmb3IgKGogPSBuZXdFbmQ7IGogPj0gc3RhcnQ7IGotLSkge1xuICAgICAgICAgIGl0ZW0gPSBuZXdJdGVtc1tqXTtcbiAgICAgICAgICBpID0gbmV3SW5kaWNlcy5nZXQoaXRlbSk7XG4gICAgICAgICAgbmV3SW5kaWNlc05leHRbal0gPSBpID09PSB2b2lkIDAgPyAtMSA6IGk7XG4gICAgICAgICAgbmV3SW5kaWNlcy5zZXQoaXRlbSwgaik7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChpID0gc3RhcnQ7IGkgPD0gZW5kOyBpKyspIHtcbiAgICAgICAgICBpdGVtID0gaXRlbXNbaV07XG4gICAgICAgICAgaiA9IG5ld0luZGljZXMuZ2V0KGl0ZW0pO1xuICAgICAgICAgIGlmIChqICE9PSB2b2lkIDAgJiYgaiAhPT0gLTEpIHtcbiAgICAgICAgICAgIHRlbXBbal0gPSBtYXBwZWRbaV07XG4gICAgICAgICAgICB0ZW1wZGlzcG9zZXJzW2pdID0gZGlzcG9zZXJzW2ldO1xuICAgICAgICAgICAgaW5kZXhlcyAmJiAodGVtcEluZGV4ZXNbal0gPSBpbmRleGVzW2ldKTtcbiAgICAgICAgICAgIGogPSBuZXdJbmRpY2VzTmV4dFtqXTtcbiAgICAgICAgICAgIG5ld0luZGljZXMuc2V0KGl0ZW0sIGopO1xuICAgICAgICAgIH0gZWxzZSBkaXNwb3NlcnNbaV0oKTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGogPSBzdGFydDsgaiA8IG5ld0xlbjsgaisrKSB7XG4gICAgICAgICAgaWYgKGogaW4gdGVtcCkge1xuICAgICAgICAgICAgbWFwcGVkW2pdID0gdGVtcFtqXTtcbiAgICAgICAgICAgIGRpc3Bvc2Vyc1tqXSA9IHRlbXBkaXNwb3NlcnNbal07XG4gICAgICAgICAgICBpZiAoaW5kZXhlcykge1xuICAgICAgICAgICAgICBpbmRleGVzW2pdID0gdGVtcEluZGV4ZXNbal07XG4gICAgICAgICAgICAgIGluZGV4ZXNbal0oaik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIG1hcHBlZFtqXSA9IGNyZWF0ZVJvb3QobWFwcGVyKTtcbiAgICAgICAgfVxuICAgICAgICBtYXBwZWQgPSBtYXBwZWQuc2xpY2UoMCwgbGVuID0gbmV3TGVuKTtcbiAgICAgICAgaXRlbXMgPSBuZXdJdGVtcy5zbGljZSgwKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBtYXBwZWQ7XG4gICAgfSk7XG4gICAgZnVuY3Rpb24gbWFwcGVyKGRpc3Bvc2VyKSB7XG4gICAgICBkaXNwb3NlcnNbal0gPSBkaXNwb3NlcjtcbiAgICAgIGlmIChpbmRleGVzKSB7XG4gICAgICAgIGNvbnN0IFtzLCBzZXRdID0gY3JlYXRlU2lnbmFsKGopO1xuICAgICAgICBpbmRleGVzW2pdID0gc2V0O1xuICAgICAgICByZXR1cm4gbWFwRm4obmV3SXRlbXNbal0sIHMpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG1hcEZuKG5ld0l0ZW1zW2pdKTtcbiAgICB9XG4gIH07XG59XG5mdW5jdGlvbiBpbmRleEFycmF5KGxpc3QsIG1hcEZuLCBvcHRpb25zID0ge30pIHtcbiAgbGV0IGl0ZW1zID0gW10sIG1hcHBlZCA9IFtdLCBkaXNwb3NlcnMgPSBbXSwgc2lnbmFscyA9IFtdLCBsZW4gPSAwLCBpO1xuICBvbkNsZWFudXAoKCkgPT4gZGlzcG9zZShkaXNwb3NlcnMpKTtcbiAgcmV0dXJuICgpID0+IHtcbiAgICBjb25zdCBuZXdJdGVtcyA9IGxpc3QoKSB8fCBbXSwgbmV3TGVuID0gbmV3SXRlbXMubGVuZ3RoO1xuICAgIG5ld0l0ZW1zWyRUUkFDS107XG4gICAgcmV0dXJuIHVudHJhY2soKCkgPT4ge1xuICAgICAgaWYgKG5ld0xlbiA9PT0gMCkge1xuICAgICAgICBpZiAobGVuICE9PSAwKSB7XG4gICAgICAgICAgZGlzcG9zZShkaXNwb3NlcnMpO1xuICAgICAgICAgIGRpc3Bvc2VycyA9IFtdO1xuICAgICAgICAgIGl0ZW1zID0gW107XG4gICAgICAgICAgbWFwcGVkID0gW107XG4gICAgICAgICAgbGVuID0gMDtcbiAgICAgICAgICBzaWduYWxzID0gW107XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMuZmFsbGJhY2spIHtcbiAgICAgICAgICBpdGVtcyA9IFtGQUxMQkFDS107XG4gICAgICAgICAgbWFwcGVkWzBdID0gY3JlYXRlUm9vdCgoZGlzcG9zZXIpID0+IHtcbiAgICAgICAgICAgIGRpc3Bvc2Vyc1swXSA9IGRpc3Bvc2VyO1xuICAgICAgICAgICAgcmV0dXJuIG9wdGlvbnMuZmFsbGJhY2soKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBsZW4gPSAxO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBtYXBwZWQ7XG4gICAgICB9XG4gICAgICBpZiAoaXRlbXNbMF0gPT09IEZBTExCQUNLKSB7XG4gICAgICAgIGRpc3Bvc2Vyc1swXSgpO1xuICAgICAgICBkaXNwb3NlcnMgPSBbXTtcbiAgICAgICAgaXRlbXMgPSBbXTtcbiAgICAgICAgbWFwcGVkID0gW107XG4gICAgICAgIGxlbiA9IDA7XG4gICAgICB9XG4gICAgICBmb3IgKGkgPSAwOyBpIDwgbmV3TGVuOyBpKyspIHtcbiAgICAgICAgaWYgKGkgPCBpdGVtcy5sZW5ndGggJiYgaXRlbXNbaV0gIT09IG5ld0l0ZW1zW2ldKSB7XG4gICAgICAgICAgc2lnbmFsc1tpXSgoKSA9PiBuZXdJdGVtc1tpXSk7XG4gICAgICAgIH0gZWxzZSBpZiAoaSA+PSBpdGVtcy5sZW5ndGgpIHtcbiAgICAgICAgICBtYXBwZWRbaV0gPSBjcmVhdGVSb290KG1hcHBlcik7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGZvciAoOyBpIDwgaXRlbXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgZGlzcG9zZXJzW2ldKCk7XG4gICAgICB9XG4gICAgICBsZW4gPSBzaWduYWxzLmxlbmd0aCA9IGRpc3Bvc2Vycy5sZW5ndGggPSBuZXdMZW47XG4gICAgICBpdGVtcyA9IG5ld0l0ZW1zLnNsaWNlKDApO1xuICAgICAgcmV0dXJuIG1hcHBlZCA9IG1hcHBlZC5zbGljZSgwLCBsZW4pO1xuICAgIH0pO1xuICAgIGZ1bmN0aW9uIG1hcHBlcihkaXNwb3Nlcikge1xuICAgICAgZGlzcG9zZXJzW2ldID0gZGlzcG9zZXI7XG4gICAgICBjb25zdCBbcywgc2V0XSA9IGNyZWF0ZVNpZ25hbChuZXdJdGVtc1tpXSk7XG4gICAgICBzaWduYWxzW2ldID0gc2V0O1xuICAgICAgcmV0dXJuIG1hcEZuKHMsIGkpO1xuICAgIH1cbiAgfTtcbn1cbnZhciBoeWRyYXRpb25FbmFibGVkID0gZmFsc2U7XG5mdW5jdGlvbiBjcmVhdGVDb21wb25lbnQoQ29tcCwgcHJvcHMpIHtcbiAgaWYgKGh5ZHJhdGlvbkVuYWJsZWQpIHtcbiAgICBpZiAoc2hhcmVkQ29uZmlnLmNvbnRleHQpIHtcbiAgICAgIGNvbnN0IGMgPSBzaGFyZWRDb25maWcuY29udGV4dDtcbiAgICAgIHNldEh5ZHJhdGVDb250ZXh0KG5leHRIeWRyYXRlQ29udGV4dCgpKTtcbiAgICAgIGNvbnN0IHIgPSB1bnRyYWNrKCgpID0+IENvbXAocHJvcHMgfHwge30pKTtcbiAgICAgIHNldEh5ZHJhdGVDb250ZXh0KGMpO1xuICAgICAgcmV0dXJuIHI7XG4gICAgfVxuICB9XG4gIHJldHVybiB1bnRyYWNrKCgpID0+IENvbXAocHJvcHMgfHwge30pKTtcbn1cbmZ1bmN0aW9uIHRydWVGbigpIHtcbiAgcmV0dXJuIHRydWU7XG59XG52YXIgcHJvcFRyYXBzID0ge1xuICBnZXQoXywgcHJvcGVydHksIHJlY2VpdmVyKSB7XG4gICAgaWYgKHByb3BlcnR5ID09PSAkUFJPWFkpIHJldHVybiByZWNlaXZlcjtcbiAgICByZXR1cm4gXy5nZXQocHJvcGVydHkpO1xuICB9LFxuICBoYXMoXywgcHJvcGVydHkpIHtcbiAgICBpZiAocHJvcGVydHkgPT09ICRQUk9YWSkgcmV0dXJuIHRydWU7XG4gICAgcmV0dXJuIF8uaGFzKHByb3BlcnR5KTtcbiAgfSxcbiAgc2V0OiB0cnVlRm4sXG4gIGRlbGV0ZVByb3BlcnR5OiB0cnVlRm4sXG4gIGdldE93blByb3BlcnR5RGVzY3JpcHRvcihfLCBwcm9wZXJ0eSkge1xuICAgIHJldHVybiB7XG4gICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgZ2V0KCkge1xuICAgICAgICByZXR1cm4gXy5nZXQocHJvcGVydHkpO1xuICAgICAgfSxcbiAgICAgIHNldDogdHJ1ZUZuLFxuICAgICAgZGVsZXRlUHJvcGVydHk6IHRydWVGblxuICAgIH07XG4gIH0sXG4gIG93bktleXMoXykge1xuICAgIHJldHVybiBfLmtleXMoKTtcbiAgfVxufTtcbmZ1bmN0aW9uIHJlc29sdmVTb3VyY2Uocykge1xuICByZXR1cm4gIShzID0gdHlwZW9mIHMgPT09IFwiZnVuY3Rpb25cIiA/IHMoKSA6IHMpID8ge30gOiBzO1xufVxuZnVuY3Rpb24gcmVzb2x2ZVNvdXJjZXMoKSB7XG4gIGZvciAobGV0IGkgPSAwLCBsZW5ndGggPSB0aGlzLmxlbmd0aDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgY29uc3QgdiA9IHRoaXNbaV0oKTtcbiAgICBpZiAodiAhPT0gdm9pZCAwKSByZXR1cm4gdjtcbiAgfVxufVxuZnVuY3Rpb24gbWVyZ2VQcm9wcyguLi5zb3VyY2VzKSB7XG4gIGxldCBwcm94eSA9IGZhbHNlO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHNvdXJjZXMubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBzID0gc291cmNlc1tpXTtcbiAgICBwcm94eSA9IHByb3h5IHx8ICEhcyAmJiAkUFJPWFkgaW4gcztcbiAgICBzb3VyY2VzW2ldID0gdHlwZW9mIHMgPT09IFwiZnVuY3Rpb25cIiA/IChwcm94eSA9IHRydWUsIGNyZWF0ZU1lbW8ocykpIDogcztcbiAgfVxuICBpZiAoU1VQUE9SVFNfUFJPWFkgJiYgcHJveHkpIHtcbiAgICByZXR1cm4gbmV3IFByb3h5KFxuICAgICAge1xuICAgICAgICBnZXQocHJvcGVydHkpIHtcbiAgICAgICAgICBmb3IgKGxldCBpID0gc291cmNlcy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgICAgICAgY29uc3QgdiA9IHJlc29sdmVTb3VyY2Uoc291cmNlc1tpXSlbcHJvcGVydHldO1xuICAgICAgICAgICAgaWYgKHYgIT09IHZvaWQgMCkgcmV0dXJuIHY7XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBoYXMocHJvcGVydHkpIHtcbiAgICAgICAgICBmb3IgKGxldCBpID0gc291cmNlcy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgICAgICAgaWYgKHByb3BlcnR5IGluIHJlc29sdmVTb3VyY2Uoc291cmNlc1tpXSkpIHJldHVybiB0cnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0sXG4gICAgICAgIGtleXMoKSB7XG4gICAgICAgICAgY29uc3Qga2V5cyA9IFtdO1xuICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc291cmNlcy5sZW5ndGg7IGkrKylcbiAgICAgICAgICAgIGtleXMucHVzaCguLi5PYmplY3Qua2V5cyhyZXNvbHZlU291cmNlKHNvdXJjZXNbaV0pKSk7XG4gICAgICAgICAgcmV0dXJuIFsuLi5uZXcgU2V0KGtleXMpXTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHByb3BUcmFwc1xuICAgICk7XG4gIH1cbiAgY29uc3Qgc291cmNlc01hcCA9IHt9O1xuICBjb25zdCBkZWZpbmVkID0gLyogQF9fUFVSRV9fICovIE9iamVjdC5jcmVhdGUobnVsbCk7XG4gIGZvciAobGV0IGkgPSBzb3VyY2VzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgY29uc3Qgc291cmNlID0gc291cmNlc1tpXTtcbiAgICBpZiAoIXNvdXJjZSkgY29udGludWU7XG4gICAgY29uc3Qgc291cmNlS2V5cyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKHNvdXJjZSk7XG4gICAgZm9yIChsZXQgaTIgPSBzb3VyY2VLZXlzLmxlbmd0aCAtIDE7IGkyID49IDA7IGkyLS0pIHtcbiAgICAgIGNvbnN0IGtleSA9IHNvdXJjZUtleXNbaTJdO1xuICAgICAgaWYgKGtleSA9PT0gXCJfX3Byb3RvX19cIiB8fCBrZXkgPT09IFwiY29uc3RydWN0b3JcIikgY29udGludWU7XG4gICAgICBjb25zdCBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihzb3VyY2UsIGtleSk7XG4gICAgICBpZiAoIWRlZmluZWRba2V5XSkge1xuICAgICAgICBkZWZpbmVkW2tleV0gPSBkZXNjLmdldCA/IHtcbiAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICBnZXQ6IHJlc29sdmVTb3VyY2VzLmJpbmQoc291cmNlc01hcFtrZXldID0gW2Rlc2MuZ2V0LmJpbmQoc291cmNlKV0pXG4gICAgICAgIH0gOiBkZXNjLnZhbHVlICE9PSB2b2lkIDAgPyBkZXNjIDogdm9pZCAwO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3Qgc291cmNlczIgPSBzb3VyY2VzTWFwW2tleV07XG4gICAgICAgIGlmIChzb3VyY2VzMikge1xuICAgICAgICAgIGlmIChkZXNjLmdldCkgc291cmNlczIucHVzaChkZXNjLmdldC5iaW5kKHNvdXJjZSkpO1xuICAgICAgICAgIGVsc2UgaWYgKGRlc2MudmFsdWUgIT09IHZvaWQgMCkgc291cmNlczIucHVzaCgoKSA9PiBkZXNjLnZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICBjb25zdCB0YXJnZXQgPSB7fTtcbiAgY29uc3QgZGVmaW5lZEtleXMgPSBPYmplY3Qua2V5cyhkZWZpbmVkKTtcbiAgZm9yIChsZXQgaSA9IGRlZmluZWRLZXlzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgY29uc3Qga2V5ID0gZGVmaW5lZEtleXNbaV0sIGRlc2MgPSBkZWZpbmVkW2tleV07XG4gICAgaWYgKGRlc2MgJiYgZGVzYy5nZXQpIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgZGVzYyk7XG4gICAgZWxzZSB0YXJnZXRba2V5XSA9IGRlc2MgPyBkZXNjLnZhbHVlIDogdm9pZCAwO1xuICB9XG4gIHJldHVybiB0YXJnZXQ7XG59XG5mdW5jdGlvbiBzcGxpdFByb3BzKHByb3BzLCAuLi5rZXlzKSB7XG4gIGlmIChTVVBQT1JUU19QUk9YWSAmJiAkUFJPWFkgaW4gcHJvcHMpIHtcbiAgICBjb25zdCBibG9ja2VkID0gbmV3IFNldChrZXlzLmxlbmd0aCA+IDEgPyBrZXlzLmZsYXQoKSA6IGtleXNbMF0pO1xuICAgIGNvbnN0IHJlcyA9IGtleXMubWFwKChrKSA9PiB7XG4gICAgICByZXR1cm4gbmV3IFByb3h5KFxuICAgICAgICB7XG4gICAgICAgICAgZ2V0KHByb3BlcnR5KSB7XG4gICAgICAgICAgICByZXR1cm4gay5pbmNsdWRlcyhwcm9wZXJ0eSkgPyBwcm9wc1twcm9wZXJ0eV0gOiB2b2lkIDA7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBoYXMocHJvcGVydHkpIHtcbiAgICAgICAgICAgIHJldHVybiBrLmluY2x1ZGVzKHByb3BlcnR5KSAmJiBwcm9wZXJ0eSBpbiBwcm9wcztcbiAgICAgICAgICB9LFxuICAgICAgICAgIGtleXMoKSB7XG4gICAgICAgICAgICByZXR1cm4gay5maWx0ZXIoKHByb3BlcnR5KSA9PiBwcm9wZXJ0eSBpbiBwcm9wcyk7XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBwcm9wVHJhcHNcbiAgICAgICk7XG4gICAgfSk7XG4gICAgcmVzLnB1c2goXG4gICAgICBuZXcgUHJveHkoXG4gICAgICAgIHtcbiAgICAgICAgICBnZXQocHJvcGVydHkpIHtcbiAgICAgICAgICAgIHJldHVybiBibG9ja2VkLmhhcyhwcm9wZXJ0eSkgPyB2b2lkIDAgOiBwcm9wc1twcm9wZXJ0eV07XG4gICAgICAgICAgfSxcbiAgICAgICAgICBoYXMocHJvcGVydHkpIHtcbiAgICAgICAgICAgIHJldHVybiBibG9ja2VkLmhhcyhwcm9wZXJ0eSkgPyBmYWxzZSA6IHByb3BlcnR5IGluIHByb3BzO1xuICAgICAgICAgIH0sXG4gICAgICAgICAga2V5cygpIHtcbiAgICAgICAgICAgIHJldHVybiBPYmplY3Qua2V5cyhwcm9wcykuZmlsdGVyKChrKSA9PiAhYmxvY2tlZC5oYXMoaykpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgcHJvcFRyYXBzXG4gICAgICApXG4gICAgKTtcbiAgICByZXR1cm4gcmVzO1xuICB9XG4gIGNvbnN0IG90aGVyT2JqZWN0ID0ge307XG4gIGNvbnN0IG9iamVjdHMgPSBrZXlzLm1hcCgoKSA9PiAoe30pKTtcbiAgZm9yIChjb25zdCBwcm9wTmFtZSBvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyhwcm9wcykpIHtcbiAgICBjb25zdCBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihwcm9wcywgcHJvcE5hbWUpO1xuICAgIGNvbnN0IGlzRGVmYXVsdERlc2MgPSAhZGVzYy5nZXQgJiYgIWRlc2Muc2V0ICYmIGRlc2MuZW51bWVyYWJsZSAmJiBkZXNjLndyaXRhYmxlICYmIGRlc2MuY29uZmlndXJhYmxlO1xuICAgIGxldCBibG9ja2VkID0gZmFsc2U7XG4gICAgbGV0IG9iamVjdEluZGV4ID0gMDtcbiAgICBmb3IgKGNvbnN0IGsgb2Yga2V5cykge1xuICAgICAgaWYgKGsuaW5jbHVkZXMocHJvcE5hbWUpKSB7XG4gICAgICAgIGJsb2NrZWQgPSB0cnVlO1xuICAgICAgICBpc0RlZmF1bHREZXNjID8gb2JqZWN0c1tvYmplY3RJbmRleF1bcHJvcE5hbWVdID0gZGVzYy52YWx1ZSA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvYmplY3RzW29iamVjdEluZGV4XSwgcHJvcE5hbWUsIGRlc2MpO1xuICAgICAgfVxuICAgICAgKytvYmplY3RJbmRleDtcbiAgICB9XG4gICAgaWYgKCFibG9ja2VkKSB7XG4gICAgICBpc0RlZmF1bHREZXNjID8gb3RoZXJPYmplY3RbcHJvcE5hbWVdID0gZGVzYy52YWx1ZSA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvdGhlck9iamVjdCwgcHJvcE5hbWUsIGRlc2MpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gWy4uLm9iamVjdHMsIG90aGVyT2JqZWN0XTtcbn1cbmZ1bmN0aW9uIGxhenkoZm4pIHtcbiAgbGV0IGNvbXA7XG4gIGxldCBwO1xuICBjb25zdCB3cmFwID0gKHByb3BzKSA9PiB7XG4gICAgY29uc3QgY3R4ID0gc2hhcmVkQ29uZmlnLmNvbnRleHQ7XG4gICAgaWYgKGN0eCkge1xuICAgICAgY29uc3QgW3MsIHNldF0gPSBjcmVhdGVTaWduYWwoKTtcbiAgICAgIHNoYXJlZENvbmZpZy5jb3VudCB8fCAoc2hhcmVkQ29uZmlnLmNvdW50ID0gMCk7XG4gICAgICBzaGFyZWRDb25maWcuY291bnQrKztcbiAgICAgIChwIHx8IChwID0gZm4oKSkpLnRoZW4oKG1vZCkgPT4ge1xuICAgICAgICAhc2hhcmVkQ29uZmlnLmRvbmUgJiYgc2V0SHlkcmF0ZUNvbnRleHQoY3R4KTtcbiAgICAgICAgc2hhcmVkQ29uZmlnLmNvdW50LS07XG4gICAgICAgIHNldCgoKSA9PiBtb2QuZGVmYXVsdCk7XG4gICAgICAgIHNldEh5ZHJhdGVDb250ZXh0KCk7XG4gICAgICB9KTtcbiAgICAgIGNvbXAgPSBzO1xuICAgIH0gZWxzZSBpZiAoIWNvbXApIHtcbiAgICAgIGNvbnN0IFtzXSA9IGNyZWF0ZVJlc291cmNlKCgpID0+IChwIHx8IChwID0gZm4oKSkpLnRoZW4oKG1vZCkgPT4gbW9kLmRlZmF1bHQpKTtcbiAgICAgIGNvbXAgPSBzO1xuICAgIH1cbiAgICBsZXQgQ29tcDtcbiAgICByZXR1cm4gY3JlYXRlTWVtbyhcbiAgICAgICgpID0+IChDb21wID0gY29tcCgpKSA/IHVudHJhY2soKCkgPT4ge1xuICAgICAgICBpZiAoSVNfREVWKSA7XG4gICAgICAgIGlmICghY3R4IHx8IHNoYXJlZENvbmZpZy5kb25lKSByZXR1cm4gQ29tcChwcm9wcyk7XG4gICAgICAgIGNvbnN0IGMgPSBzaGFyZWRDb25maWcuY29udGV4dDtcbiAgICAgICAgc2V0SHlkcmF0ZUNvbnRleHQoY3R4KTtcbiAgICAgICAgY29uc3QgciA9IENvbXAocHJvcHMpO1xuICAgICAgICBzZXRIeWRyYXRlQ29udGV4dChjKTtcbiAgICAgICAgcmV0dXJuIHI7XG4gICAgICB9KSA6IFwiXCJcbiAgICApO1xuICB9O1xuICB3cmFwLnByZWxvYWQgPSAoKSA9PiBwIHx8ICgocCA9IGZuKCkpLnRoZW4oKG1vZCkgPT4gY29tcCA9ICgpID0+IG1vZC5kZWZhdWx0KSwgcCk7XG4gIHJldHVybiB3cmFwO1xufVxudmFyIGNvdW50ZXIgPSAwO1xuZnVuY3Rpb24gY3JlYXRlVW5pcXVlSWQoKSB7XG4gIGNvbnN0IGN0eCA9IHNoYXJlZENvbmZpZy5jb250ZXh0O1xuICByZXR1cm4gY3R4ID8gc2hhcmVkQ29uZmlnLmdldE5leHRDb250ZXh0SWQoKSA6IGBjbC0ke2NvdW50ZXIrK31gO1xufVxudmFyIG5hcnJvd2VkRXJyb3IgPSAobmFtZSkgPT4gYFN0YWxlIHJlYWQgZnJvbSA8JHtuYW1lfT4uYDtcbmZ1bmN0aW9uIEZvcihwcm9wcykge1xuICBjb25zdCBmYWxsYmFjayA9IFwiZmFsbGJhY2tcIiBpbiBwcm9wcyAmJiB7XG4gICAgZmFsbGJhY2s6ICgpID0+IHByb3BzLmZhbGxiYWNrXG4gIH07XG4gIHJldHVybiBjcmVhdGVNZW1vKG1hcEFycmF5KCgpID0+IHByb3BzLmVhY2gsIHByb3BzLmNoaWxkcmVuLCBmYWxsYmFjayB8fCB2b2lkIDApKTtcbn1cbmZ1bmN0aW9uIEluZGV4KHByb3BzKSB7XG4gIGNvbnN0IGZhbGxiYWNrID0gXCJmYWxsYmFja1wiIGluIHByb3BzICYmIHtcbiAgICBmYWxsYmFjazogKCkgPT4gcHJvcHMuZmFsbGJhY2tcbiAgfTtcbiAgcmV0dXJuIGNyZWF0ZU1lbW8oaW5kZXhBcnJheSgoKSA9PiBwcm9wcy5lYWNoLCBwcm9wcy5jaGlsZHJlbiwgZmFsbGJhY2sgfHwgdm9pZCAwKSk7XG59XG5mdW5jdGlvbiBTaG93KHByb3BzKSB7XG4gIGNvbnN0IGtleWVkID0gcHJvcHMua2V5ZWQ7XG4gIGNvbnN0IGNvbmRpdGlvblZhbHVlID0gY3JlYXRlTWVtbygoKSA9PiBwcm9wcy53aGVuLCB2b2lkIDAsIHZvaWQgMCk7XG4gIGNvbnN0IGNvbmRpdGlvbiA9IGtleWVkID8gY29uZGl0aW9uVmFsdWUgOiBjcmVhdGVNZW1vKGNvbmRpdGlvblZhbHVlLCB2b2lkIDAsIHtcbiAgICBlcXVhbHM6IChhLCBiKSA9PiAhYSA9PT0gIWJcbiAgfSk7XG4gIHJldHVybiBjcmVhdGVNZW1vKFxuICAgICgpID0+IHtcbiAgICAgIGNvbnN0IGMgPSBjb25kaXRpb24oKTtcbiAgICAgIGlmIChjKSB7XG4gICAgICAgIGNvbnN0IGNoaWxkID0gcHJvcHMuY2hpbGRyZW47XG4gICAgICAgIGNvbnN0IGZuID0gdHlwZW9mIGNoaWxkID09PSBcImZ1bmN0aW9uXCIgJiYgY2hpbGQubGVuZ3RoID4gMDtcbiAgICAgICAgcmV0dXJuIGZuID8gdW50cmFjayhcbiAgICAgICAgICAoKSA9PiBjaGlsZChcbiAgICAgICAgICAgIGtleWVkID8gYyA6ICgpID0+IHtcbiAgICAgICAgICAgICAgaWYgKCF1bnRyYWNrKGNvbmRpdGlvbikpIHRocm93IG5hcnJvd2VkRXJyb3IoXCJTaG93XCIpO1xuICAgICAgICAgICAgICByZXR1cm4gY29uZGl0aW9uVmFsdWUoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICApXG4gICAgICAgICkgOiBjaGlsZDtcbiAgICAgIH1cbiAgICAgIHJldHVybiBwcm9wcy5mYWxsYmFjaztcbiAgICB9LFxuICAgIHZvaWQgMCxcbiAgICB2b2lkIDBcbiAgKTtcbn1cbmZ1bmN0aW9uIFN3aXRjaChwcm9wcykge1xuICBjb25zdCBjaHMgPSBjaGlsZHJlbigoKSA9PiBwcm9wcy5jaGlsZHJlbik7XG4gIGNvbnN0IHN3aXRjaEZ1bmMgPSBjcmVhdGVNZW1vKCgpID0+IHtcbiAgICBjb25zdCBjaCA9IGNocygpO1xuICAgIGNvbnN0IG1wcyA9IEFycmF5LmlzQXJyYXkoY2gpID8gY2ggOiBbY2hdO1xuICAgIGxldCBmdW5jID0gKCkgPT4gdm9pZCAwO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbXBzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBpbmRleCA9IGk7XG4gICAgICBjb25zdCBtcCA9IG1wc1tpXTtcbiAgICAgIGNvbnN0IHByZXZGdW5jID0gZnVuYztcbiAgICAgIGNvbnN0IGNvbmRpdGlvblZhbHVlID0gY3JlYXRlTWVtbyhcbiAgICAgICAgKCkgPT4gcHJldkZ1bmMoKSA/IHZvaWQgMCA6IG1wLndoZW4sXG4gICAgICAgIHZvaWQgMCxcbiAgICAgICAgdm9pZCAwXG4gICAgICApO1xuICAgICAgY29uc3QgY29uZGl0aW9uID0gbXAua2V5ZWQgPyBjb25kaXRpb25WYWx1ZSA6IGNyZWF0ZU1lbW8oY29uZGl0aW9uVmFsdWUsIHZvaWQgMCwge1xuICAgICAgICBlcXVhbHM6IChhLCBiKSA9PiAhYSA9PT0gIWJcbiAgICAgIH0pO1xuICAgICAgZnVuYyA9ICgpID0+IHByZXZGdW5jKCkgfHwgKGNvbmRpdGlvbigpID8gW2luZGV4LCBjb25kaXRpb25WYWx1ZSwgbXBdIDogdm9pZCAwKTtcbiAgICB9XG4gICAgcmV0dXJuIGZ1bmM7XG4gIH0pO1xuICByZXR1cm4gY3JlYXRlTWVtbyhcbiAgICAoKSA9PiB7XG4gICAgICBjb25zdCBzZWwgPSBzd2l0Y2hGdW5jKCkoKTtcbiAgICAgIGlmICghc2VsKSByZXR1cm4gcHJvcHMuZmFsbGJhY2s7XG4gICAgICBjb25zdCBbaW5kZXgsIGNvbmRpdGlvblZhbHVlLCBtcF0gPSBzZWw7XG4gICAgICBjb25zdCBjaGlsZCA9IG1wLmNoaWxkcmVuO1xuICAgICAgY29uc3QgZm4gPSB0eXBlb2YgY2hpbGQgPT09IFwiZnVuY3Rpb25cIiAmJiBjaGlsZC5sZW5ndGggPiAwO1xuICAgICAgcmV0dXJuIGZuID8gdW50cmFjayhcbiAgICAgICAgKCkgPT4gY2hpbGQoXG4gICAgICAgICAgbXAua2V5ZWQgPyBjb25kaXRpb25WYWx1ZSgpIDogKCkgPT4ge1xuICAgICAgICAgICAgaWYgKHVudHJhY2soc3dpdGNoRnVuYykoKT8uWzBdICE9PSBpbmRleCkgdGhyb3cgbmFycm93ZWRFcnJvcihcIk1hdGNoXCIpO1xuICAgICAgICAgICAgcmV0dXJuIGNvbmRpdGlvblZhbHVlKCk7XG4gICAgICAgICAgfVxuICAgICAgICApXG4gICAgICApIDogY2hpbGQ7XG4gICAgfSxcbiAgICB2b2lkIDAsXG4gICAgdm9pZCAwXG4gICk7XG59XG5mdW5jdGlvbiBNYXRjaChwcm9wcykge1xuICByZXR1cm4gcHJvcHM7XG59XG52YXIgREVWID0gdm9pZCAwO1xuXG4vLyAuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vc29saWQtanNAMS45LjUvbm9kZV9tb2R1bGVzL3NvbGlkLWpzL3dlYi9kaXN0L3dlYi5qc1xudmFyIGJvb2xlYW5zID0gW1xuICBcImFsbG93ZnVsbHNjcmVlblwiLFxuICBcImFzeW5jXCIsXG4gIFwiYXV0b2ZvY3VzXCIsXG4gIFwiYXV0b3BsYXlcIixcbiAgXCJjaGVja2VkXCIsXG4gIFwiY29udHJvbHNcIixcbiAgXCJkZWZhdWx0XCIsXG4gIFwiZGlzYWJsZWRcIixcbiAgXCJmb3Jtbm92YWxpZGF0ZVwiLFxuICBcImhpZGRlblwiLFxuICBcImluZGV0ZXJtaW5hdGVcIixcbiAgXCJpbmVydFwiLFxuICBcImlzbWFwXCIsXG4gIFwibG9vcFwiLFxuICBcIm11bHRpcGxlXCIsXG4gIFwibXV0ZWRcIixcbiAgXCJub21vZHVsZVwiLFxuICBcIm5vdmFsaWRhdGVcIixcbiAgXCJvcGVuXCIsXG4gIFwicGxheXNpbmxpbmVcIixcbiAgXCJyZWFkb25seVwiLFxuICBcInJlcXVpcmVkXCIsXG4gIFwicmV2ZXJzZWRcIixcbiAgXCJzZWFtbGVzc1wiLFxuICBcInNlbGVjdGVkXCJcbl07XG52YXIgUHJvcGVydGllcyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KFtcbiAgXCJjbGFzc05hbWVcIixcbiAgXCJ2YWx1ZVwiLFxuICBcInJlYWRPbmx5XCIsXG4gIFwiZm9ybU5vVmFsaWRhdGVcIixcbiAgXCJpc01hcFwiLFxuICBcIm5vTW9kdWxlXCIsXG4gIFwicGxheXNJbmxpbmVcIixcbiAgLi4uYm9vbGVhbnNcbl0pO1xudmFyIENoaWxkUHJvcGVydGllcyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KFtcbiAgXCJpbm5lckhUTUxcIixcbiAgXCJ0ZXh0Q29udGVudFwiLFxuICBcImlubmVyVGV4dFwiLFxuICBcImNoaWxkcmVuXCJcbl0pO1xudmFyIEFsaWFzZXMgPSAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmFzc2lnbigvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKSwge1xuICBjbGFzc05hbWU6IFwiY2xhc3NcIixcbiAgaHRtbEZvcjogXCJmb3JcIlxufSk7XG52YXIgUHJvcEFsaWFzZXMgPSAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmFzc2lnbigvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKSwge1xuICBjbGFzczogXCJjbGFzc05hbWVcIixcbiAgZm9ybW5vdmFsaWRhdGU6IHtcbiAgICAkOiBcImZvcm1Ob1ZhbGlkYXRlXCIsXG4gICAgQlVUVE9OOiAxLFxuICAgIElOUFVUOiAxXG4gIH0sXG4gIGlzbWFwOiB7XG4gICAgJDogXCJpc01hcFwiLFxuICAgIElNRzogMVxuICB9LFxuICBub21vZHVsZToge1xuICAgICQ6IFwibm9Nb2R1bGVcIixcbiAgICBTQ1JJUFQ6IDFcbiAgfSxcbiAgcGxheXNpbmxpbmU6IHtcbiAgICAkOiBcInBsYXlzSW5saW5lXCIsXG4gICAgVklERU86IDFcbiAgfSxcbiAgcmVhZG9ubHk6IHtcbiAgICAkOiBcInJlYWRPbmx5XCIsXG4gICAgSU5QVVQ6IDEsXG4gICAgVEVYVEFSRUE6IDFcbiAgfVxufSk7XG5mdW5jdGlvbiBnZXRQcm9wQWxpYXMocHJvcCwgdGFnTmFtZSkge1xuICBjb25zdCBhID0gUHJvcEFsaWFzZXNbcHJvcF07XG4gIHJldHVybiB0eXBlb2YgYSA9PT0gXCJvYmplY3RcIiA/IGFbdGFnTmFtZV0gPyBhW1wiJFwiXSA6IHZvaWQgMCA6IGE7XG59XG52YXIgRGVsZWdhdGVkRXZlbnRzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoW1xuICBcImJlZm9yZWlucHV0XCIsXG4gIFwiY2xpY2tcIixcbiAgXCJkYmxjbGlja1wiLFxuICBcImNvbnRleHRtZW51XCIsXG4gIFwiZm9jdXNpblwiLFxuICBcImZvY3Vzb3V0XCIsXG4gIFwiaW5wdXRcIixcbiAgXCJrZXlkb3duXCIsXG4gIFwia2V5dXBcIixcbiAgXCJtb3VzZWRvd25cIixcbiAgXCJtb3VzZW1vdmVcIixcbiAgXCJtb3VzZW91dFwiLFxuICBcIm1vdXNlb3ZlclwiLFxuICBcIm1vdXNldXBcIixcbiAgXCJwb2ludGVyZG93blwiLFxuICBcInBvaW50ZXJtb3ZlXCIsXG4gIFwicG9pbnRlcm91dFwiLFxuICBcInBvaW50ZXJvdmVyXCIsXG4gIFwicG9pbnRlcnVwXCIsXG4gIFwidG91Y2hlbmRcIixcbiAgXCJ0b3VjaG1vdmVcIixcbiAgXCJ0b3VjaHN0YXJ0XCJcbl0pO1xudmFyIFNWR0VsZW1lbnRzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoW1xuICBcImFsdEdseXBoXCIsXG4gIFwiYWx0R2x5cGhEZWZcIixcbiAgXCJhbHRHbHlwaEl0ZW1cIixcbiAgXCJhbmltYXRlXCIsXG4gIFwiYW5pbWF0ZUNvbG9yXCIsXG4gIFwiYW5pbWF0ZU1vdGlvblwiLFxuICBcImFuaW1hdGVUcmFuc2Zvcm1cIixcbiAgXCJjaXJjbGVcIixcbiAgXCJjbGlwUGF0aFwiLFxuICBcImNvbG9yLXByb2ZpbGVcIixcbiAgXCJjdXJzb3JcIixcbiAgXCJkZWZzXCIsXG4gIFwiZGVzY1wiLFxuICBcImVsbGlwc2VcIixcbiAgXCJmZUJsZW5kXCIsXG4gIFwiZmVDb2xvck1hdHJpeFwiLFxuICBcImZlQ29tcG9uZW50VHJhbnNmZXJcIixcbiAgXCJmZUNvbXBvc2l0ZVwiLFxuICBcImZlQ29udm9sdmVNYXRyaXhcIixcbiAgXCJmZURpZmZ1c2VMaWdodGluZ1wiLFxuICBcImZlRGlzcGxhY2VtZW50TWFwXCIsXG4gIFwiZmVEaXN0YW50TGlnaHRcIixcbiAgXCJmZURyb3BTaGFkb3dcIixcbiAgXCJmZUZsb29kXCIsXG4gIFwiZmVGdW5jQVwiLFxuICBcImZlRnVuY0JcIixcbiAgXCJmZUZ1bmNHXCIsXG4gIFwiZmVGdW5jUlwiLFxuICBcImZlR2F1c3NpYW5CbHVyXCIsXG4gIFwiZmVJbWFnZVwiLFxuICBcImZlTWVyZ2VcIixcbiAgXCJmZU1lcmdlTm9kZVwiLFxuICBcImZlTW9ycGhvbG9neVwiLFxuICBcImZlT2Zmc2V0XCIsXG4gIFwiZmVQb2ludExpZ2h0XCIsXG4gIFwiZmVTcGVjdWxhckxpZ2h0aW5nXCIsXG4gIFwiZmVTcG90TGlnaHRcIixcbiAgXCJmZVRpbGVcIixcbiAgXCJmZVR1cmJ1bGVuY2VcIixcbiAgXCJmaWx0ZXJcIixcbiAgXCJmb250XCIsXG4gIFwiZm9udC1mYWNlXCIsXG4gIFwiZm9udC1mYWNlLWZvcm1hdFwiLFxuICBcImZvbnQtZmFjZS1uYW1lXCIsXG4gIFwiZm9udC1mYWNlLXNyY1wiLFxuICBcImZvbnQtZmFjZS11cmlcIixcbiAgXCJmb3JlaWduT2JqZWN0XCIsXG4gIFwiZ1wiLFxuICBcImdseXBoXCIsXG4gIFwiZ2x5cGhSZWZcIixcbiAgXCJoa2VyblwiLFxuICBcImltYWdlXCIsXG4gIFwibGluZVwiLFxuICBcImxpbmVhckdyYWRpZW50XCIsXG4gIFwibWFya2VyXCIsXG4gIFwibWFza1wiLFxuICBcIm1ldGFkYXRhXCIsXG4gIFwibWlzc2luZy1nbHlwaFwiLFxuICBcIm1wYXRoXCIsXG4gIFwicGF0aFwiLFxuICBcInBhdHRlcm5cIixcbiAgXCJwb2x5Z29uXCIsXG4gIFwicG9seWxpbmVcIixcbiAgXCJyYWRpYWxHcmFkaWVudFwiLFxuICBcInJlY3RcIixcbiAgXCJzZXRcIixcbiAgXCJzdG9wXCIsXG4gIFwic3ZnXCIsXG4gIFwic3dpdGNoXCIsXG4gIFwic3ltYm9sXCIsXG4gIFwidGV4dFwiLFxuICBcInRleHRQYXRoXCIsXG4gIFwidHJlZlwiLFxuICBcInRzcGFuXCIsXG4gIFwidXNlXCIsXG4gIFwidmlld1wiLFxuICBcInZrZXJuXCJcbl0pO1xudmFyIFNWR05hbWVzcGFjZSA9IHtcbiAgeGxpbms6IFwiaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGlua1wiLFxuICB4bWw6IFwiaHR0cDovL3d3dy53My5vcmcvWE1MLzE5OTgvbmFtZXNwYWNlXCJcbn07XG5mdW5jdGlvbiByZWNvbmNpbGVBcnJheXMocGFyZW50Tm9kZSwgYSwgYikge1xuICBsZXQgYkxlbmd0aCA9IGIubGVuZ3RoLCBhRW5kID0gYS5sZW5ndGgsIGJFbmQgPSBiTGVuZ3RoLCBhU3RhcnQgPSAwLCBiU3RhcnQgPSAwLCBhZnRlciA9IGFbYUVuZCAtIDFdLm5leHRTaWJsaW5nLCBtYXAgPSBudWxsO1xuICB3aGlsZSAoYVN0YXJ0IDwgYUVuZCB8fCBiU3RhcnQgPCBiRW5kKSB7XG4gICAgaWYgKGFbYVN0YXJ0XSA9PT0gYltiU3RhcnRdKSB7XG4gICAgICBhU3RhcnQrKztcbiAgICAgIGJTdGFydCsrO1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIHdoaWxlIChhW2FFbmQgLSAxXSA9PT0gYltiRW5kIC0gMV0pIHtcbiAgICAgIGFFbmQtLTtcbiAgICAgIGJFbmQtLTtcbiAgICB9XG4gICAgaWYgKGFFbmQgPT09IGFTdGFydCkge1xuICAgICAgY29uc3Qgbm9kZSA9IGJFbmQgPCBiTGVuZ3RoID8gYlN0YXJ0ID8gYltiU3RhcnQgLSAxXS5uZXh0U2libGluZyA6IGJbYkVuZCAtIGJTdGFydF0gOiBhZnRlcjtcbiAgICAgIHdoaWxlIChiU3RhcnQgPCBiRW5kKSBwYXJlbnROb2RlLmluc2VydEJlZm9yZShiW2JTdGFydCsrXSwgbm9kZSk7XG4gICAgfSBlbHNlIGlmIChiRW5kID09PSBiU3RhcnQpIHtcbiAgICAgIHdoaWxlIChhU3RhcnQgPCBhRW5kKSB7XG4gICAgICAgIGlmICghbWFwIHx8ICFtYXAuaGFzKGFbYVN0YXJ0XSkpIGFbYVN0YXJ0XS5yZW1vdmUoKTtcbiAgICAgICAgYVN0YXJ0Kys7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChhW2FTdGFydF0gPT09IGJbYkVuZCAtIDFdICYmIGJbYlN0YXJ0XSA9PT0gYVthRW5kIC0gMV0pIHtcbiAgICAgIGNvbnN0IG5vZGUgPSBhWy0tYUVuZF0ubmV4dFNpYmxpbmc7XG4gICAgICBwYXJlbnROb2RlLmluc2VydEJlZm9yZShiW2JTdGFydCsrXSwgYVthU3RhcnQrK10ubmV4dFNpYmxpbmcpO1xuICAgICAgcGFyZW50Tm9kZS5pbnNlcnRCZWZvcmUoYlstLWJFbmRdLCBub2RlKTtcbiAgICAgIGFbYUVuZF0gPSBiW2JFbmRdO1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAoIW1hcCkge1xuICAgICAgICBtYXAgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgICAgICBsZXQgaSA9IGJTdGFydDtcbiAgICAgICAgd2hpbGUgKGkgPCBiRW5kKSBtYXAuc2V0KGJbaV0sIGkrKyk7XG4gICAgICB9XG4gICAgICBjb25zdCBpbmRleCA9IG1hcC5nZXQoYVthU3RhcnRdKTtcbiAgICAgIGlmIChpbmRleCAhPSBudWxsKSB7XG4gICAgICAgIGlmIChiU3RhcnQgPCBpbmRleCAmJiBpbmRleCA8IGJFbmQpIHtcbiAgICAgICAgICBsZXQgaSA9IGFTdGFydCwgc2VxdWVuY2UgPSAxLCB0O1xuICAgICAgICAgIHdoaWxlICgrK2kgPCBhRW5kICYmIGkgPCBiRW5kKSB7XG4gICAgICAgICAgICBpZiAoKHQgPSBtYXAuZ2V0KGFbaV0pKSA9PSBudWxsIHx8IHQgIT09IGluZGV4ICsgc2VxdWVuY2UpIGJyZWFrO1xuICAgICAgICAgICAgc2VxdWVuY2UrKztcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKHNlcXVlbmNlID4gaW5kZXggLSBiU3RhcnQpIHtcbiAgICAgICAgICAgIGNvbnN0IG5vZGUgPSBhW2FTdGFydF07XG4gICAgICAgICAgICB3aGlsZSAoYlN0YXJ0IDwgaW5kZXgpIHBhcmVudE5vZGUuaW5zZXJ0QmVmb3JlKGJbYlN0YXJ0KytdLCBub2RlKTtcbiAgICAgICAgICB9IGVsc2UgcGFyZW50Tm9kZS5yZXBsYWNlQ2hpbGQoYltiU3RhcnQrK10sIGFbYVN0YXJ0KytdKTtcbiAgICAgICAgfSBlbHNlIGFTdGFydCsrO1xuICAgICAgfSBlbHNlIGFbYVN0YXJ0KytdLnJlbW92ZSgpO1xuICAgIH1cbiAgfVxufVxudmFyICQkRVZFTlRTID0gXCJfJERYX0RFTEVHQVRFXCI7XG5mdW5jdGlvbiByZW5kZXIoY29kZSwgZWxlbWVudCwgaW5pdCwgb3B0aW9ucyA9IHt9KSB7XG4gIGxldCBkaXNwb3NlcjtcbiAgY3JlYXRlUm9vdCgoZGlzcG9zZTIpID0+IHtcbiAgICBkaXNwb3NlciA9IGRpc3Bvc2UyO1xuICAgIGVsZW1lbnQgPT09IGRvY3VtZW50ID8gY29kZSgpIDogaW5zZXJ0KGVsZW1lbnQsIGNvZGUoKSwgZWxlbWVudC5maXJzdENoaWxkID8gbnVsbCA6IHZvaWQgMCwgaW5pdCk7XG4gIH0sIG9wdGlvbnMub3duZXIpO1xuICByZXR1cm4gKCkgPT4ge1xuICAgIGRpc3Bvc2VyKCk7XG4gICAgZWxlbWVudC50ZXh0Q29udGVudCA9IFwiXCI7XG4gIH07XG59XG5mdW5jdGlvbiB0ZW1wbGF0ZShodG1sLCBpc0ltcG9ydE5vZGUsIGlzU1ZHLCBpc01hdGhNTCkge1xuICBsZXQgbm9kZTtcbiAgY29uc3QgY3JlYXRlID0gKCkgPT4ge1xuICAgIGNvbnN0IHQgPSBpc01hdGhNTCA/IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnROUyhcImh0dHA6Ly93d3cudzMub3JnLzE5OTgvTWF0aC9NYXRoTUxcIiwgXCJ0ZW1wbGF0ZVwiKSA6IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJ0ZW1wbGF0ZVwiKTtcbiAgICB0LmlubmVySFRNTCA9IGh0bWw7XG4gICAgcmV0dXJuIGlzU1ZHID8gdC5jb250ZW50LmZpcnN0Q2hpbGQuZmlyc3RDaGlsZCA6IGlzTWF0aE1MID8gdC5maXJzdENoaWxkIDogdC5jb250ZW50LmZpcnN0Q2hpbGQ7XG4gIH07XG4gIGNvbnN0IGZuID0gaXNJbXBvcnROb2RlID8gKCkgPT4gdW50cmFjaygoKSA9PiBkb2N1bWVudC5pbXBvcnROb2RlKG5vZGUgfHwgKG5vZGUgPSBjcmVhdGUoKSksIHRydWUpKSA6ICgpID0+IChub2RlIHx8IChub2RlID0gY3JlYXRlKCkpKS5jbG9uZU5vZGUodHJ1ZSk7XG4gIGZuLmNsb25lTm9kZSA9IGZuO1xuICByZXR1cm4gZm47XG59XG5mdW5jdGlvbiBkZWxlZ2F0ZUV2ZW50cyhldmVudE5hbWVzLCBkb2N1bWVudDIgPSB3aW5kb3cuZG9jdW1lbnQpIHtcbiAgY29uc3QgZSA9IGRvY3VtZW50MlskJEVWRU5UU10gfHwgKGRvY3VtZW50MlskJEVWRU5UU10gPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpKTtcbiAgZm9yIChsZXQgaSA9IDAsIGwgPSBldmVudE5hbWVzLmxlbmd0aDsgaSA8IGw7IGkrKykge1xuICAgIGNvbnN0IG5hbWUgPSBldmVudE5hbWVzW2ldO1xuICAgIGlmICghZS5oYXMobmFtZSkpIHtcbiAgICAgIGUuYWRkKG5hbWUpO1xuICAgICAgZG9jdW1lbnQyLmFkZEV2ZW50TGlzdGVuZXIobmFtZSwgZXZlbnRIYW5kbGVyKTtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIGNsZWFyRGVsZWdhdGVkRXZlbnRzKGRvY3VtZW50MiA9IHdpbmRvdy5kb2N1bWVudCkge1xuICBpZiAoZG9jdW1lbnQyWyQkRVZFTlRTXSkge1xuICAgIGZvciAobGV0IG5hbWUgb2YgZG9jdW1lbnQyWyQkRVZFTlRTXS5rZXlzKCkpIGRvY3VtZW50Mi5yZW1vdmVFdmVudExpc3RlbmVyKG5hbWUsIGV2ZW50SGFuZGxlcik7XG4gICAgZGVsZXRlIGRvY3VtZW50MlskJEVWRU5UU107XG4gIH1cbn1cbmZ1bmN0aW9uIHNldEF0dHJpYnV0ZShub2RlLCBuYW1lLCB2YWx1ZSkge1xuICBpZiAoaXNIeWRyYXRpbmcobm9kZSkpIHJldHVybjtcbiAgaWYgKHZhbHVlID09IG51bGwpIG5vZGUucmVtb3ZlQXR0cmlidXRlKG5hbWUpO1xuICBlbHNlIG5vZGUuc2V0QXR0cmlidXRlKG5hbWUsIHZhbHVlKTtcbn1cbmZ1bmN0aW9uIHNldEF0dHJpYnV0ZU5TKG5vZGUsIG5hbWVzcGFjZSwgbmFtZSwgdmFsdWUpIHtcbiAgaWYgKGlzSHlkcmF0aW5nKG5vZGUpKSByZXR1cm47XG4gIGlmICh2YWx1ZSA9PSBudWxsKSBub2RlLnJlbW92ZUF0dHJpYnV0ZU5TKG5hbWVzcGFjZSwgbmFtZSk7XG4gIGVsc2Ugbm9kZS5zZXRBdHRyaWJ1dGVOUyhuYW1lc3BhY2UsIG5hbWUsIHZhbHVlKTtcbn1cbmZ1bmN0aW9uIHNldEJvb2xBdHRyaWJ1dGUobm9kZSwgbmFtZSwgdmFsdWUpIHtcbiAgaWYgKGlzSHlkcmF0aW5nKG5vZGUpKSByZXR1cm47XG4gIHZhbHVlID8gbm9kZS5zZXRBdHRyaWJ1dGUobmFtZSwgXCJcIikgOiBub2RlLnJlbW92ZUF0dHJpYnV0ZShuYW1lKTtcbn1cbmZ1bmN0aW9uIGNsYXNzTmFtZShub2RlLCB2YWx1ZSkge1xuICBpZiAoaXNIeWRyYXRpbmcobm9kZSkpIHJldHVybjtcbiAgaWYgKHZhbHVlID09IG51bGwpIG5vZGUucmVtb3ZlQXR0cmlidXRlKFwiY2xhc3NcIik7XG4gIGVsc2Ugbm9kZS5jbGFzc05hbWUgPSB2YWx1ZTtcbn1cbmZ1bmN0aW9uIGFkZEV2ZW50TGlzdGVuZXIobm9kZSwgbmFtZSwgaGFuZGxlciwgZGVsZWdhdGUpIHtcbiAgaWYgKGRlbGVnYXRlKSB7XG4gICAgaWYgKEFycmF5LmlzQXJyYXkoaGFuZGxlcikpIHtcbiAgICAgIG5vZGVbYCQkJHtuYW1lfWBdID0gaGFuZGxlclswXTtcbiAgICAgIG5vZGVbYCQkJHtuYW1lfURhdGFgXSA9IGhhbmRsZXJbMV07XG4gICAgfSBlbHNlIG5vZGVbYCQkJHtuYW1lfWBdID0gaGFuZGxlcjtcbiAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KGhhbmRsZXIpKSB7XG4gICAgY29uc3QgaGFuZGxlckZuID0gaGFuZGxlclswXTtcbiAgICBub2RlLmFkZEV2ZW50TGlzdGVuZXIobmFtZSwgaGFuZGxlclswXSA9IChlKSA9PiBoYW5kbGVyRm4uY2FsbChub2RlLCBoYW5kbGVyWzFdLCBlKSk7XG4gIH0gZWxzZSBub2RlLmFkZEV2ZW50TGlzdGVuZXIobmFtZSwgaGFuZGxlciwgdHlwZW9mIGhhbmRsZXIgIT09IFwiZnVuY3Rpb25cIiAmJiBoYW5kbGVyKTtcbn1cbmZ1bmN0aW9uIGNsYXNzTGlzdChub2RlLCB2YWx1ZSwgcHJldiA9IHt9KSB7XG4gIGNvbnN0IGNsYXNzS2V5cyA9IE9iamVjdC5rZXlzKHZhbHVlIHx8IHt9KSwgcHJldktleXMgPSBPYmplY3Qua2V5cyhwcmV2KTtcbiAgbGV0IGksIGxlbjtcbiAgZm9yIChpID0gMCwgbGVuID0gcHJldktleXMubGVuZ3RoOyBpIDwgbGVuOyBpKyspIHtcbiAgICBjb25zdCBrZXkgPSBwcmV2S2V5c1tpXTtcbiAgICBpZiAoIWtleSB8fCBrZXkgPT09IFwidW5kZWZpbmVkXCIgfHwgdmFsdWVba2V5XSkgY29udGludWU7XG4gICAgdG9nZ2xlQ2xhc3NLZXkobm9kZSwga2V5LCBmYWxzZSk7XG4gICAgZGVsZXRlIHByZXZba2V5XTtcbiAgfVxuICBmb3IgKGkgPSAwLCBsZW4gPSBjbGFzc0tleXMubGVuZ3RoOyBpIDwgbGVuOyBpKyspIHtcbiAgICBjb25zdCBrZXkgPSBjbGFzc0tleXNbaV0sIGNsYXNzVmFsdWUgPSAhIXZhbHVlW2tleV07XG4gICAgaWYgKCFrZXkgfHwga2V5ID09PSBcInVuZGVmaW5lZFwiIHx8IHByZXZba2V5XSA9PT0gY2xhc3NWYWx1ZSB8fCAhY2xhc3NWYWx1ZSkgY29udGludWU7XG4gICAgdG9nZ2xlQ2xhc3NLZXkobm9kZSwga2V5LCB0cnVlKTtcbiAgICBwcmV2W2tleV0gPSBjbGFzc1ZhbHVlO1xuICB9XG4gIHJldHVybiBwcmV2O1xufVxuZnVuY3Rpb24gc3R5bGUobm9kZSwgdmFsdWUsIHByZXYpIHtcbiAgaWYgKCF2YWx1ZSkgcmV0dXJuIHByZXYgPyBzZXRBdHRyaWJ1dGUobm9kZSwgXCJzdHlsZVwiKSA6IHZhbHVlO1xuICBjb25zdCBub2RlU3R5bGUgPSBub2RlLnN0eWxlO1xuICBpZiAodHlwZW9mIHZhbHVlID09PSBcInN0cmluZ1wiKSByZXR1cm4gbm9kZVN0eWxlLmNzc1RleHQgPSB2YWx1ZTtcbiAgdHlwZW9mIHByZXYgPT09IFwic3RyaW5nXCIgJiYgKG5vZGVTdHlsZS5jc3NUZXh0ID0gcHJldiA9IHZvaWQgMCk7XG4gIHByZXYgfHwgKHByZXYgPSB7fSk7XG4gIHZhbHVlIHx8ICh2YWx1ZSA9IHt9KTtcbiAgbGV0IHYsIHM7XG4gIGZvciAocyBpbiBwcmV2KSB7XG4gICAgdmFsdWVbc10gPT0gbnVsbCAmJiBub2RlU3R5bGUucmVtb3ZlUHJvcGVydHkocyk7XG4gICAgZGVsZXRlIHByZXZbc107XG4gIH1cbiAgZm9yIChzIGluIHZhbHVlKSB7XG4gICAgdiA9IHZhbHVlW3NdO1xuICAgIGlmICh2ICE9PSBwcmV2W3NdKSB7XG4gICAgICBub2RlU3R5bGUuc2V0UHJvcGVydHkocywgdik7XG4gICAgICBwcmV2W3NdID0gdjtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHByZXY7XG59XG5mdW5jdGlvbiBzcHJlYWQobm9kZSwgcHJvcHMgPSB7fSwgaXNTVkcsIHNraXBDaGlsZHJlbikge1xuICBjb25zdCBwcmV2UHJvcHMgPSB7fTtcbiAgaWYgKCFza2lwQ2hpbGRyZW4pIHtcbiAgICBjcmVhdGVSZW5kZXJFZmZlY3QoXG4gICAgICAoKSA9PiBwcmV2UHJvcHMuY2hpbGRyZW4gPSBpbnNlcnRFeHByZXNzaW9uKG5vZGUsIHByb3BzLmNoaWxkcmVuLCBwcmV2UHJvcHMuY2hpbGRyZW4pXG4gICAgKTtcbiAgfVxuICBjcmVhdGVSZW5kZXJFZmZlY3QoKCkgPT4gdHlwZW9mIHByb3BzLnJlZiA9PT0gXCJmdW5jdGlvblwiICYmIHVzZShwcm9wcy5yZWYsIG5vZGUpKTtcbiAgY3JlYXRlUmVuZGVyRWZmZWN0KCgpID0+IGFzc2lnbihub2RlLCBwcm9wcywgaXNTVkcsIHRydWUsIHByZXZQcm9wcywgdHJ1ZSkpO1xuICByZXR1cm4gcHJldlByb3BzO1xufVxuZnVuY3Rpb24gdXNlKGZuLCBlbGVtZW50LCBhcmcpIHtcbiAgcmV0dXJuIHVudHJhY2soKCkgPT4gZm4oZWxlbWVudCwgYXJnKSk7XG59XG5mdW5jdGlvbiBpbnNlcnQocGFyZW50LCBhY2Nlc3NvciwgbWFya2VyLCBpbml0aWFsKSB7XG4gIGlmIChtYXJrZXIgIT09IHZvaWQgMCAmJiAhaW5pdGlhbCkgaW5pdGlhbCA9IFtdO1xuICBpZiAodHlwZW9mIGFjY2Vzc29yICE9PSBcImZ1bmN0aW9uXCIpIHJldHVybiBpbnNlcnRFeHByZXNzaW9uKHBhcmVudCwgYWNjZXNzb3IsIGluaXRpYWwsIG1hcmtlcik7XG4gIGNyZWF0ZVJlbmRlckVmZmVjdCgoY3VycmVudCkgPT4gaW5zZXJ0RXhwcmVzc2lvbihwYXJlbnQsIGFjY2Vzc29yKCksIGN1cnJlbnQsIG1hcmtlciksIGluaXRpYWwpO1xufVxuZnVuY3Rpb24gYXNzaWduKG5vZGUsIHByb3BzLCBpc1NWRywgc2tpcENoaWxkcmVuLCBwcmV2UHJvcHMgPSB7fSwgc2tpcFJlZiA9IGZhbHNlKSB7XG4gIHByb3BzIHx8IChwcm9wcyA9IHt9KTtcbiAgZm9yIChjb25zdCBwcm9wIGluIHByZXZQcm9wcykge1xuICAgIGlmICghKHByb3AgaW4gcHJvcHMpKSB7XG4gICAgICBpZiAocHJvcCA9PT0gXCJjaGlsZHJlblwiKSBjb250aW51ZTtcbiAgICAgIHByZXZQcm9wc1twcm9wXSA9IGFzc2lnblByb3Aobm9kZSwgcHJvcCwgbnVsbCwgcHJldlByb3BzW3Byb3BdLCBpc1NWRywgc2tpcFJlZiwgcHJvcHMpO1xuICAgIH1cbiAgfVxuICBmb3IgKGNvbnN0IHByb3AgaW4gcHJvcHMpIHtcbiAgICBpZiAocHJvcCA9PT0gXCJjaGlsZHJlblwiKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgY29uc3QgdmFsdWUgPSBwcm9wc1twcm9wXTtcbiAgICBwcmV2UHJvcHNbcHJvcF0gPSBhc3NpZ25Qcm9wKG5vZGUsIHByb3AsIHZhbHVlLCBwcmV2UHJvcHNbcHJvcF0sIGlzU1ZHLCBza2lwUmVmLCBwcm9wcyk7XG4gIH1cbn1cbmZ1bmN0aW9uIGdldE5leHRFbGVtZW50KHRlbXBsYXRlMikge1xuICBsZXQgbm9kZSwga2V5LCBoeWRyYXRpbmcgPSBpc0h5ZHJhdGluZygpO1xuICBpZiAoIWh5ZHJhdGluZyB8fCAhKG5vZGUgPSBzaGFyZWRDb25maWcucmVnaXN0cnkuZ2V0KGtleSA9IGdldEh5ZHJhdGlvbktleSgpKSkpIHtcbiAgICByZXR1cm4gdGVtcGxhdGUyKCk7XG4gIH1cbiAgaWYgKHNoYXJlZENvbmZpZy5jb21wbGV0ZWQpIHNoYXJlZENvbmZpZy5jb21wbGV0ZWQuYWRkKG5vZGUpO1xuICBzaGFyZWRDb25maWcucmVnaXN0cnkuZGVsZXRlKGtleSk7XG4gIHJldHVybiBub2RlO1xufVxuZnVuY3Rpb24gaXNIeWRyYXRpbmcobm9kZSkge1xuICByZXR1cm4gISFzaGFyZWRDb25maWcuY29udGV4dCAmJiAhc2hhcmVkQ29uZmlnLmRvbmUgJiYgKCFub2RlIHx8IG5vZGUuaXNDb25uZWN0ZWQpO1xufVxuZnVuY3Rpb24gdG9Qcm9wZXJ0eU5hbWUobmFtZSkge1xuICByZXR1cm4gbmFtZS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoLy0oW2Etel0pL2csIChfLCB3KSA9PiB3LnRvVXBwZXJDYXNlKCkpO1xufVxuZnVuY3Rpb24gdG9nZ2xlQ2xhc3NLZXkobm9kZSwga2V5LCB2YWx1ZSkge1xuICBjb25zdCBjbGFzc05hbWVzID0ga2V5LnRyaW0oKS5zcGxpdCgvXFxzKy8pO1xuICBmb3IgKGxldCBpID0gMCwgbmFtZUxlbiA9IGNsYXNzTmFtZXMubGVuZ3RoOyBpIDwgbmFtZUxlbjsgaSsrKVxuICAgIG5vZGUuY2xhc3NMaXN0LnRvZ2dsZShjbGFzc05hbWVzW2ldLCB2YWx1ZSk7XG59XG5mdW5jdGlvbiBhc3NpZ25Qcm9wKG5vZGUsIHByb3AsIHZhbHVlLCBwcmV2LCBpc1NWRywgc2tpcFJlZiwgcHJvcHMpIHtcbiAgbGV0IGlzQ0UsIGlzUHJvcCwgaXNDaGlsZFByb3AsIHByb3BBbGlhcywgZm9yY2VQcm9wO1xuICBpZiAocHJvcCA9PT0gXCJzdHlsZVwiKSByZXR1cm4gc3R5bGUobm9kZSwgdmFsdWUsIHByZXYpO1xuICBpZiAocHJvcCA9PT0gXCJjbGFzc0xpc3RcIikgcmV0dXJuIGNsYXNzTGlzdChub2RlLCB2YWx1ZSwgcHJldik7XG4gIGlmICh2YWx1ZSA9PT0gcHJldikgcmV0dXJuIHByZXY7XG4gIGlmIChwcm9wID09PSBcInJlZlwiKSB7XG4gICAgaWYgKCFza2lwUmVmKSB2YWx1ZShub2RlKTtcbiAgfSBlbHNlIGlmIChwcm9wLnNsaWNlKDAsIDMpID09PSBcIm9uOlwiKSB7XG4gICAgY29uc3QgZSA9IHByb3Auc2xpY2UoMyk7XG4gICAgcHJldiAmJiBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoZSwgcHJldiwgdHlwZW9mIHByZXYgIT09IFwiZnVuY3Rpb25cIiAmJiBwcmV2KTtcbiAgICB2YWx1ZSAmJiBub2RlLmFkZEV2ZW50TGlzdGVuZXIoZSwgdmFsdWUsIHR5cGVvZiB2YWx1ZSAhPT0gXCJmdW5jdGlvblwiICYmIHZhbHVlKTtcbiAgfSBlbHNlIGlmIChwcm9wLnNsaWNlKDAsIDEwKSA9PT0gXCJvbmNhcHR1cmU6XCIpIHtcbiAgICBjb25zdCBlID0gcHJvcC5zbGljZSgxMCk7XG4gICAgcHJldiAmJiBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoZSwgcHJldiwgdHJ1ZSk7XG4gICAgdmFsdWUgJiYgbm9kZS5hZGRFdmVudExpc3RlbmVyKGUsIHZhbHVlLCB0cnVlKTtcbiAgfSBlbHNlIGlmIChwcm9wLnNsaWNlKDAsIDIpID09PSBcIm9uXCIpIHtcbiAgICBjb25zdCBuYW1lID0gcHJvcC5zbGljZSgyKS50b0xvd2VyQ2FzZSgpO1xuICAgIGNvbnN0IGRlbGVnYXRlID0gRGVsZWdhdGVkRXZlbnRzLmhhcyhuYW1lKTtcbiAgICBpZiAoIWRlbGVnYXRlICYmIHByZXYpIHtcbiAgICAgIGNvbnN0IGggPSBBcnJheS5pc0FycmF5KHByZXYpID8gcHJldlswXSA6IHByZXY7XG4gICAgICBub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIobmFtZSwgaCk7XG4gICAgfVxuICAgIGlmIChkZWxlZ2F0ZSB8fCB2YWx1ZSkge1xuICAgICAgYWRkRXZlbnRMaXN0ZW5lcihub2RlLCBuYW1lLCB2YWx1ZSwgZGVsZWdhdGUpO1xuICAgICAgZGVsZWdhdGUgJiYgZGVsZWdhdGVFdmVudHMoW25hbWVdKTtcbiAgICB9XG4gIH0gZWxzZSBpZiAocHJvcC5zbGljZSgwLCA1KSA9PT0gXCJhdHRyOlwiKSB7XG4gICAgc2V0QXR0cmlidXRlKG5vZGUsIHByb3Auc2xpY2UoNSksIHZhbHVlKTtcbiAgfSBlbHNlIGlmIChwcm9wLnNsaWNlKDAsIDUpID09PSBcImJvb2w6XCIpIHtcbiAgICBzZXRCb29sQXR0cmlidXRlKG5vZGUsIHByb3Auc2xpY2UoNSksIHZhbHVlKTtcbiAgfSBlbHNlIGlmICgoZm9yY2VQcm9wID0gcHJvcC5zbGljZSgwLCA1KSA9PT0gXCJwcm9wOlwiKSB8fCAoaXNDaGlsZFByb3AgPSBDaGlsZFByb3BlcnRpZXMuaGFzKHByb3ApKSB8fCAhaXNTVkcgJiYgKChwcm9wQWxpYXMgPSBnZXRQcm9wQWxpYXMocHJvcCwgbm9kZS50YWdOYW1lKSkgfHwgKGlzUHJvcCA9IFByb3BlcnRpZXMuaGFzKHByb3ApKSkgfHwgKGlzQ0UgPSBub2RlLm5vZGVOYW1lLmluY2x1ZGVzKFwiLVwiKSB8fCBcImlzXCIgaW4gcHJvcHMpKSB7XG4gICAgaWYgKGZvcmNlUHJvcCkge1xuICAgICAgcHJvcCA9IHByb3Auc2xpY2UoNSk7XG4gICAgICBpc1Byb3AgPSB0cnVlO1xuICAgIH0gZWxzZSBpZiAoaXNIeWRyYXRpbmcobm9kZSkpIHJldHVybiB2YWx1ZTtcbiAgICBpZiAocHJvcCA9PT0gXCJjbGFzc1wiIHx8IHByb3AgPT09IFwiY2xhc3NOYW1lXCIpIGNsYXNzTmFtZShub2RlLCB2YWx1ZSk7XG4gICAgZWxzZSBpZiAoaXNDRSAmJiAhaXNQcm9wICYmICFpc0NoaWxkUHJvcCkgbm9kZVt0b1Byb3BlcnR5TmFtZShwcm9wKV0gPSB2YWx1ZTtcbiAgICBlbHNlIG5vZGVbcHJvcEFsaWFzIHx8IHByb3BdID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgY29uc3QgbnMgPSBpc1NWRyAmJiBwcm9wLmluZGV4T2YoXCI6XCIpID4gLTEgJiYgU1ZHTmFtZXNwYWNlW3Byb3Auc3BsaXQoXCI6XCIpWzBdXTtcbiAgICBpZiAobnMpIHNldEF0dHJpYnV0ZU5TKG5vZGUsIG5zLCBwcm9wLCB2YWx1ZSk7XG4gICAgZWxzZSBzZXRBdHRyaWJ1dGUobm9kZSwgQWxpYXNlc1twcm9wXSB8fCBwcm9wLCB2YWx1ZSk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuZnVuY3Rpb24gZXZlbnRIYW5kbGVyKGUpIHtcbiAgaWYgKHNoYXJlZENvbmZpZy5yZWdpc3RyeSAmJiBzaGFyZWRDb25maWcuZXZlbnRzKSB7XG4gICAgaWYgKHNoYXJlZENvbmZpZy5ldmVudHMuZmluZCgoW2VsLCBldl0pID0+IGV2ID09PSBlKSkgcmV0dXJuO1xuICB9XG4gIGxldCBub2RlID0gZS50YXJnZXQ7XG4gIGNvbnN0IGtleSA9IGAkJCR7ZS50eXBlfWA7XG4gIGNvbnN0IG9yaVRhcmdldCA9IGUudGFyZ2V0O1xuICBjb25zdCBvcmlDdXJyZW50VGFyZ2V0ID0gZS5jdXJyZW50VGFyZ2V0O1xuICBjb25zdCByZXRhcmdldCA9ICh2YWx1ZSkgPT4gT2JqZWN0LmRlZmluZVByb3BlcnR5KGUsIFwidGFyZ2V0XCIsIHtcbiAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgdmFsdWVcbiAgfSk7XG4gIGNvbnN0IGhhbmRsZU5vZGUgPSAoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlciA9IG5vZGVba2V5XTtcbiAgICBpZiAoaGFuZGxlciAmJiAhbm9kZS5kaXNhYmxlZCkge1xuICAgICAgY29uc3QgZGF0YSA9IG5vZGVbYCR7a2V5fURhdGFgXTtcbiAgICAgIGRhdGEgIT09IHZvaWQgMCA/IGhhbmRsZXIuY2FsbChub2RlLCBkYXRhLCBlKSA6IGhhbmRsZXIuY2FsbChub2RlLCBlKTtcbiAgICAgIGlmIChlLmNhbmNlbEJ1YmJsZSkgcmV0dXJuO1xuICAgIH1cbiAgICBub2RlLmhvc3QgJiYgdHlwZW9mIG5vZGUuaG9zdCAhPT0gXCJzdHJpbmdcIiAmJiAhbm9kZS5ob3N0Ll8kaG9zdCAmJiBub2RlLmNvbnRhaW5zKGUudGFyZ2V0KSAmJiByZXRhcmdldChub2RlLmhvc3QpO1xuICAgIHJldHVybiB0cnVlO1xuICB9O1xuICBjb25zdCB3YWxrVXBUcmVlID0gKCkgPT4ge1xuICAgIHdoaWxlIChoYW5kbGVOb2RlKCkgJiYgKG5vZGUgPSBub2RlLl8kaG9zdCB8fCBub2RlLnBhcmVudE5vZGUgfHwgbm9kZS5ob3N0KSkgO1xuICB9O1xuICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZSwgXCJjdXJyZW50VGFyZ2V0XCIsIHtcbiAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgZ2V0KCkge1xuICAgICAgcmV0dXJuIG5vZGUgfHwgZG9jdW1lbnQ7XG4gICAgfVxuICB9KTtcbiAgaWYgKHNoYXJlZENvbmZpZy5yZWdpc3RyeSAmJiAhc2hhcmVkQ29uZmlnLmRvbmUpIHNoYXJlZENvbmZpZy5kb25lID0gXyRIWS5kb25lID0gdHJ1ZTtcbiAgaWYgKGUuY29tcG9zZWRQYXRoKSB7XG4gICAgY29uc3QgcGF0aCA9IGUuY29tcG9zZWRQYXRoKCk7XG4gICAgcmV0YXJnZXQocGF0aFswXSk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBwYXRoLmxlbmd0aCAtIDI7IGkrKykge1xuICAgICAgbm9kZSA9IHBhdGhbaV07XG4gICAgICBpZiAoIWhhbmRsZU5vZGUoKSkgYnJlYWs7XG4gICAgICBpZiAobm9kZS5fJGhvc3QpIHtcbiAgICAgICAgbm9kZSA9IG5vZGUuXyRob3N0O1xuICAgICAgICB3YWxrVXBUcmVlKCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgaWYgKG5vZGUucGFyZW50Tm9kZSA9PT0gb3JpQ3VycmVudFRhcmdldCkge1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG4gIH0gZWxzZSB3YWxrVXBUcmVlKCk7XG4gIHJldGFyZ2V0KG9yaVRhcmdldCk7XG59XG5mdW5jdGlvbiBpbnNlcnRFeHByZXNzaW9uKHBhcmVudCwgdmFsdWUsIGN1cnJlbnQsIG1hcmtlciwgdW53cmFwQXJyYXkpIHtcbiAgY29uc3QgaHlkcmF0aW5nID0gaXNIeWRyYXRpbmcocGFyZW50KTtcbiAgaWYgKGh5ZHJhdGluZykge1xuICAgICFjdXJyZW50ICYmIChjdXJyZW50ID0gWy4uLnBhcmVudC5jaGlsZE5vZGVzXSk7XG4gICAgbGV0IGNsZWFuZWQgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGN1cnJlbnQubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IG5vZGUgPSBjdXJyZW50W2ldO1xuICAgICAgaWYgKG5vZGUubm9kZVR5cGUgPT09IDggJiYgbm9kZS5kYXRhLnNsaWNlKDAsIDIpID09PSBcIiEkXCIpIG5vZGUucmVtb3ZlKCk7XG4gICAgICBlbHNlIGNsZWFuZWQucHVzaChub2RlKTtcbiAgICB9XG4gICAgY3VycmVudCA9IGNsZWFuZWQ7XG4gIH1cbiAgd2hpbGUgKHR5cGVvZiBjdXJyZW50ID09PSBcImZ1bmN0aW9uXCIpIGN1cnJlbnQgPSBjdXJyZW50KCk7XG4gIGlmICh2YWx1ZSA9PT0gY3VycmVudCkgcmV0dXJuIGN1cnJlbnQ7XG4gIGNvbnN0IHQgPSB0eXBlb2YgdmFsdWUsIG11bHRpID0gbWFya2VyICE9PSB2b2lkIDA7XG4gIHBhcmVudCA9IG11bHRpICYmIGN1cnJlbnRbMF0gJiYgY3VycmVudFswXS5wYXJlbnROb2RlIHx8IHBhcmVudDtcbiAgaWYgKHQgPT09IFwic3RyaW5nXCIgfHwgdCA9PT0gXCJudW1iZXJcIikge1xuICAgIGlmIChoeWRyYXRpbmcpIHJldHVybiBjdXJyZW50O1xuICAgIGlmICh0ID09PSBcIm51bWJlclwiKSB7XG4gICAgICB2YWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICBpZiAodmFsdWUgPT09IGN1cnJlbnQpIHJldHVybiBjdXJyZW50O1xuICAgIH1cbiAgICBpZiAobXVsdGkpIHtcbiAgICAgIGxldCBub2RlID0gY3VycmVudFswXTtcbiAgICAgIGlmIChub2RlICYmIG5vZGUubm9kZVR5cGUgPT09IDMpIHtcbiAgICAgICAgbm9kZS5kYXRhICE9PSB2YWx1ZSAmJiAobm9kZS5kYXRhID0gdmFsdWUpO1xuICAgICAgfSBlbHNlIG5vZGUgPSBkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZSh2YWx1ZSk7XG4gICAgICBjdXJyZW50ID0gY2xlYW5DaGlsZHJlbihwYXJlbnQsIGN1cnJlbnQsIG1hcmtlciwgbm9kZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmIChjdXJyZW50ICE9PSBcIlwiICYmIHR5cGVvZiBjdXJyZW50ID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIGN1cnJlbnQgPSBwYXJlbnQuZmlyc3RDaGlsZC5kYXRhID0gdmFsdWU7XG4gICAgICB9IGVsc2UgY3VycmVudCA9IHBhcmVudC50ZXh0Q29udGVudCA9IHZhbHVlO1xuICAgIH1cbiAgfSBlbHNlIGlmICh2YWx1ZSA9PSBudWxsIHx8IHQgPT09IFwiYm9vbGVhblwiKSB7XG4gICAgaWYgKGh5ZHJhdGluZykgcmV0dXJuIGN1cnJlbnQ7XG4gICAgY3VycmVudCA9IGNsZWFuQ2hpbGRyZW4ocGFyZW50LCBjdXJyZW50LCBtYXJrZXIpO1xuICB9IGVsc2UgaWYgKHQgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIGNyZWF0ZVJlbmRlckVmZmVjdCgoKSA9PiB7XG4gICAgICBsZXQgdiA9IHZhbHVlKCk7XG4gICAgICB3aGlsZSAodHlwZW9mIHYgPT09IFwiZnVuY3Rpb25cIikgdiA9IHYoKTtcbiAgICAgIGN1cnJlbnQgPSBpbnNlcnRFeHByZXNzaW9uKHBhcmVudCwgdiwgY3VycmVudCwgbWFya2VyKTtcbiAgICB9KTtcbiAgICByZXR1cm4gKCkgPT4gY3VycmVudDtcbiAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgIGNvbnN0IGFycmF5ID0gW107XG4gICAgY29uc3QgY3VycmVudEFycmF5ID0gY3VycmVudCAmJiBBcnJheS5pc0FycmF5KGN1cnJlbnQpO1xuICAgIGlmIChub3JtYWxpemVJbmNvbWluZ0FycmF5KGFycmF5LCB2YWx1ZSwgY3VycmVudCwgdW53cmFwQXJyYXkpKSB7XG4gICAgICBjcmVhdGVSZW5kZXJFZmZlY3QoKCkgPT4gY3VycmVudCA9IGluc2VydEV4cHJlc3Npb24ocGFyZW50LCBhcnJheSwgY3VycmVudCwgbWFya2VyLCB0cnVlKSk7XG4gICAgICByZXR1cm4gKCkgPT4gY3VycmVudDtcbiAgICB9XG4gICAgaWYgKGh5ZHJhdGluZykge1xuICAgICAgaWYgKCFhcnJheS5sZW5ndGgpIHJldHVybiBjdXJyZW50O1xuICAgICAgaWYgKG1hcmtlciA9PT0gdm9pZCAwKSByZXR1cm4gY3VycmVudCA9IFsuLi5wYXJlbnQuY2hpbGROb2Rlc107XG4gICAgICBsZXQgbm9kZSA9IGFycmF5WzBdO1xuICAgICAgaWYgKG5vZGUucGFyZW50Tm9kZSAhPT0gcGFyZW50KSByZXR1cm4gY3VycmVudDtcbiAgICAgIGNvbnN0IG5vZGVzID0gW25vZGVdO1xuICAgICAgd2hpbGUgKChub2RlID0gbm9kZS5uZXh0U2libGluZykgIT09IG1hcmtlcikgbm9kZXMucHVzaChub2RlKTtcbiAgICAgIHJldHVybiBjdXJyZW50ID0gbm9kZXM7XG4gICAgfVxuICAgIGlmIChhcnJheS5sZW5ndGggPT09IDApIHtcbiAgICAgIGN1cnJlbnQgPSBjbGVhbkNoaWxkcmVuKHBhcmVudCwgY3VycmVudCwgbWFya2VyKTtcbiAgICAgIGlmIChtdWx0aSkgcmV0dXJuIGN1cnJlbnQ7XG4gICAgfSBlbHNlIGlmIChjdXJyZW50QXJyYXkpIHtcbiAgICAgIGlmIChjdXJyZW50Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBhcHBlbmROb2RlcyhwYXJlbnQsIGFycmF5LCBtYXJrZXIpO1xuICAgICAgfSBlbHNlIHJlY29uY2lsZUFycmF5cyhwYXJlbnQsIGN1cnJlbnQsIGFycmF5KTtcbiAgICB9IGVsc2Uge1xuICAgICAgY3VycmVudCAmJiBjbGVhbkNoaWxkcmVuKHBhcmVudCk7XG4gICAgICBhcHBlbmROb2RlcyhwYXJlbnQsIGFycmF5KTtcbiAgICB9XG4gICAgY3VycmVudCA9IGFycmF5O1xuICB9IGVsc2UgaWYgKHZhbHVlLm5vZGVUeXBlKSB7XG4gICAgaWYgKGh5ZHJhdGluZyAmJiB2YWx1ZS5wYXJlbnROb2RlKSByZXR1cm4gY3VycmVudCA9IG11bHRpID8gW3ZhbHVlXSA6IHZhbHVlO1xuICAgIGlmIChBcnJheS5pc0FycmF5KGN1cnJlbnQpKSB7XG4gICAgICBpZiAobXVsdGkpIHJldHVybiBjdXJyZW50ID0gY2xlYW5DaGlsZHJlbihwYXJlbnQsIGN1cnJlbnQsIG1hcmtlciwgdmFsdWUpO1xuICAgICAgY2xlYW5DaGlsZHJlbihwYXJlbnQsIGN1cnJlbnQsIG51bGwsIHZhbHVlKTtcbiAgICB9IGVsc2UgaWYgKGN1cnJlbnQgPT0gbnVsbCB8fCBjdXJyZW50ID09PSBcIlwiIHx8ICFwYXJlbnQuZmlyc3RDaGlsZCkge1xuICAgICAgcGFyZW50LmFwcGVuZENoaWxkKHZhbHVlKTtcbiAgICB9IGVsc2UgcGFyZW50LnJlcGxhY2VDaGlsZCh2YWx1ZSwgcGFyZW50LmZpcnN0Q2hpbGQpO1xuICAgIGN1cnJlbnQgPSB2YWx1ZTtcbiAgfSBlbHNlIDtcbiAgcmV0dXJuIGN1cnJlbnQ7XG59XG5mdW5jdGlvbiBub3JtYWxpemVJbmNvbWluZ0FycmF5KG5vcm1hbGl6ZWQsIGFycmF5LCBjdXJyZW50LCB1bndyYXApIHtcbiAgbGV0IGR5bmFtaWMgPSBmYWxzZTtcbiAgZm9yIChsZXQgaSA9IDAsIGxlbiA9IGFycmF5Lmxlbmd0aDsgaSA8IGxlbjsgaSsrKSB7XG4gICAgbGV0IGl0ZW0gPSBhcnJheVtpXSwgcHJldiA9IGN1cnJlbnQgJiYgY3VycmVudFtub3JtYWxpemVkLmxlbmd0aF0sIHQ7XG4gICAgaWYgKGl0ZW0gPT0gbnVsbCB8fCBpdGVtID09PSB0cnVlIHx8IGl0ZW0gPT09IGZhbHNlKSA7XG4gICAgZWxzZSBpZiAoKHQgPSB0eXBlb2YgaXRlbSkgPT09IFwib2JqZWN0XCIgJiYgaXRlbS5ub2RlVHlwZSkge1xuICAgICAgbm9ybWFsaXplZC5wdXNoKGl0ZW0pO1xuICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShpdGVtKSkge1xuICAgICAgZHluYW1pYyA9IG5vcm1hbGl6ZUluY29taW5nQXJyYXkobm9ybWFsaXplZCwgaXRlbSwgcHJldikgfHwgZHluYW1pYztcbiAgICB9IGVsc2UgaWYgKHQgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgaWYgKHVud3JhcCkge1xuICAgICAgICB3aGlsZSAodHlwZW9mIGl0ZW0gPT09IFwiZnVuY3Rpb25cIikgaXRlbSA9IGl0ZW0oKTtcbiAgICAgICAgZHluYW1pYyA9IG5vcm1hbGl6ZUluY29taW5nQXJyYXkoXG4gICAgICAgICAgbm9ybWFsaXplZCxcbiAgICAgICAgICBBcnJheS5pc0FycmF5KGl0ZW0pID8gaXRlbSA6IFtpdGVtXSxcbiAgICAgICAgICBBcnJheS5pc0FycmF5KHByZXYpID8gcHJldiA6IFtwcmV2XVxuICAgICAgICApIHx8IGR5bmFtaWM7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBub3JtYWxpemVkLnB1c2goaXRlbSk7XG4gICAgICAgIGR5bmFtaWMgPSB0cnVlO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCB2YWx1ZSA9IFN0cmluZyhpdGVtKTtcbiAgICAgIGlmIChwcmV2ICYmIHByZXYubm9kZVR5cGUgPT09IDMgJiYgcHJldi5kYXRhID09PSB2YWx1ZSkgbm9ybWFsaXplZC5wdXNoKHByZXYpO1xuICAgICAgZWxzZSBub3JtYWxpemVkLnB1c2goZG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUodmFsdWUpKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGR5bmFtaWM7XG59XG5mdW5jdGlvbiBhcHBlbmROb2RlcyhwYXJlbnQsIGFycmF5LCBtYXJrZXIgPSBudWxsKSB7XG4gIGZvciAobGV0IGkgPSAwLCBsZW4gPSBhcnJheS5sZW5ndGg7IGkgPCBsZW47IGkrKykgcGFyZW50Lmluc2VydEJlZm9yZShhcnJheVtpXSwgbWFya2VyKTtcbn1cbmZ1bmN0aW9uIGNsZWFuQ2hpbGRyZW4ocGFyZW50LCBjdXJyZW50LCBtYXJrZXIsIHJlcGxhY2VtZW50KSB7XG4gIGlmIChtYXJrZXIgPT09IHZvaWQgMCkgcmV0dXJuIHBhcmVudC50ZXh0Q29udGVudCA9IFwiXCI7XG4gIGNvbnN0IG5vZGUgPSByZXBsYWNlbWVudCB8fCBkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShcIlwiKTtcbiAgaWYgKGN1cnJlbnQubGVuZ3RoKSB7XG4gICAgbGV0IGluc2VydGVkID0gZmFsc2U7XG4gICAgZm9yIChsZXQgaSA9IGN1cnJlbnQubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHtcbiAgICAgIGNvbnN0IGVsID0gY3VycmVudFtpXTtcbiAgICAgIGlmIChub2RlICE9PSBlbCkge1xuICAgICAgICBjb25zdCBpc1BhcmVudCA9IGVsLnBhcmVudE5vZGUgPT09IHBhcmVudDtcbiAgICAgICAgaWYgKCFpbnNlcnRlZCAmJiAhaSlcbiAgICAgICAgICBpc1BhcmVudCA/IHBhcmVudC5yZXBsYWNlQ2hpbGQobm9kZSwgZWwpIDogcGFyZW50Lmluc2VydEJlZm9yZShub2RlLCBtYXJrZXIpO1xuICAgICAgICBlbHNlIGlzUGFyZW50ICYmIGVsLnJlbW92ZSgpO1xuICAgICAgfSBlbHNlIGluc2VydGVkID0gdHJ1ZTtcbiAgICB9XG4gIH0gZWxzZSBwYXJlbnQuaW5zZXJ0QmVmb3JlKG5vZGUsIG1hcmtlcik7XG4gIHJldHVybiBbbm9kZV07XG59XG5mdW5jdGlvbiBnZXRIeWRyYXRpb25LZXkoKSB7XG4gIHJldHVybiBzaGFyZWRDb25maWcuZ2V0TmV4dENvbnRleHRJZCgpO1xufVxudmFyIGlzU2VydmVyID0gZmFsc2U7XG52YXIgU1ZHX05BTUVTUEFDRSA9IFwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIjtcbmZ1bmN0aW9uIGNyZWF0ZUVsZW1lbnQodGFnTmFtZSwgaXNTVkcgPSBmYWxzZSkge1xuICByZXR1cm4gaXNTVkcgPyBkb2N1bWVudC5jcmVhdGVFbGVtZW50TlMoU1ZHX05BTUVTUEFDRSwgdGFnTmFtZSkgOiBkb2N1bWVudC5jcmVhdGVFbGVtZW50KHRhZ05hbWUpO1xufVxuZnVuY3Rpb24gUG9ydGFsKHByb3BzKSB7XG4gIGNvbnN0IHsgdXNlU2hhZG93IH0gPSBwcm9wcywgbWFya2VyID0gZG9jdW1lbnQuY3JlYXRlVGV4dE5vZGUoXCJcIiksIG1vdW50ID0gKCkgPT4gcHJvcHMubW91bnQgfHwgZG9jdW1lbnQuYm9keSwgb3duZXIgPSBnZXRPd25lcigpO1xuICBsZXQgY29udGVudDtcbiAgbGV0IGh5ZHJhdGluZyA9ICEhc2hhcmVkQ29uZmlnLmNvbnRleHQ7XG4gIGNyZWF0ZUVmZmVjdChcbiAgICAoKSA9PiB7XG4gICAgICBpZiAoaHlkcmF0aW5nKSBnZXRPd25lcigpLnVzZXIgPSBoeWRyYXRpbmcgPSBmYWxzZTtcbiAgICAgIGNvbnRlbnQgfHwgKGNvbnRlbnQgPSBydW5XaXRoT3duZXIob3duZXIsICgpID0+IGNyZWF0ZU1lbW8oKCkgPT4gcHJvcHMuY2hpbGRyZW4pKSk7XG4gICAgICBjb25zdCBlbCA9IG1vdW50KCk7XG4gICAgICBpZiAoZWwgaW5zdGFuY2VvZiBIVE1MSGVhZEVsZW1lbnQpIHtcbiAgICAgICAgY29uc3QgW2NsZWFuLCBzZXRDbGVhbl0gPSBjcmVhdGVTaWduYWwoZmFsc2UpO1xuICAgICAgICBjb25zdCBjbGVhbnVwID0gKCkgPT4gc2V0Q2xlYW4odHJ1ZSk7XG4gICAgICAgIGNyZWF0ZVJvb3QoKGRpc3Bvc2UyKSA9PiBpbnNlcnQoZWwsICgpID0+ICFjbGVhbigpID8gY29udGVudCgpIDogZGlzcG9zZTIoKSwgbnVsbCkpO1xuICAgICAgICBvbkNsZWFudXAoY2xlYW51cCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBjb250YWluZXIgPSBjcmVhdGVFbGVtZW50KHByb3BzLmlzU1ZHID8gXCJnXCIgOiBcImRpdlwiLCBwcm9wcy5pc1NWRyksIHJlbmRlclJvb3QgPSB1c2VTaGFkb3cgJiYgY29udGFpbmVyLmF0dGFjaFNoYWRvdyA/IGNvbnRhaW5lci5hdHRhY2hTaGFkb3coe1xuICAgICAgICAgIG1vZGU6IFwib3BlblwiXG4gICAgICAgIH0pIDogY29udGFpbmVyO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoY29udGFpbmVyLCBcIl8kaG9zdFwiLCB7XG4gICAgICAgICAgZ2V0KCkge1xuICAgICAgICAgICAgcmV0dXJuIG1hcmtlci5wYXJlbnROb2RlO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgIH0pO1xuICAgICAgICBpbnNlcnQocmVuZGVyUm9vdCwgY29udGVudCk7XG4gICAgICAgIGVsLmFwcGVuZENoaWxkKGNvbnRhaW5lcik7XG4gICAgICAgIHByb3BzLnJlZiAmJiBwcm9wcy5yZWYoY29udGFpbmVyKTtcbiAgICAgICAgb25DbGVhbnVwKCgpID0+IGVsLnJlbW92ZUNoaWxkKGNvbnRhaW5lcikpO1xuICAgICAgfVxuICAgIH0sXG4gICAgdm9pZCAwLFxuICAgIHtcbiAgICAgIHJlbmRlcjogIWh5ZHJhdGluZ1xuICAgIH1cbiAgKTtcbiAgcmV0dXJuIG1hcmtlcjtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUR5bmFtaWMoY29tcG9uZW50LCBwcm9wcykge1xuICBjb25zdCBjYWNoZWQgPSBjcmVhdGVNZW1vKGNvbXBvbmVudCk7XG4gIHJldHVybiBjcmVhdGVNZW1vKCgpID0+IHtcbiAgICBjb25zdCBjb21wb25lbnQyID0gY2FjaGVkKCk7XG4gICAgc3dpdGNoICh0eXBlb2YgY29tcG9uZW50Mikge1xuICAgICAgY2FzZSBcImZ1bmN0aW9uXCI6XG4gICAgICAgIHJldHVybiB1bnRyYWNrKCgpID0+IGNvbXBvbmVudDIocHJvcHMpKTtcbiAgICAgIGNhc2UgXCJzdHJpbmdcIjpcbiAgICAgICAgY29uc3QgaXNTdmcgPSBTVkdFbGVtZW50cy5oYXMoY29tcG9uZW50Mik7XG4gICAgICAgIGNvbnN0IGVsID0gc2hhcmVkQ29uZmlnLmNvbnRleHQgPyBnZXROZXh0RWxlbWVudCgpIDogY3JlYXRlRWxlbWVudChjb21wb25lbnQyLCBpc1N2Zyk7XG4gICAgICAgIHNwcmVhZChlbCwgcHJvcHMsIGlzU3ZnKTtcbiAgICAgICAgcmV0dXJuIGVsO1xuICAgIH1cbiAgfSk7XG59XG5mdW5jdGlvbiBEeW5hbWljKHByb3BzKSB7XG4gIGNvbnN0IFssIG90aGVyc10gPSBzcGxpdFByb3BzKHByb3BzLCBbXCJjb21wb25lbnRcIl0pO1xuICByZXR1cm4gY3JlYXRlRHluYW1pYygoKSA9PiBwcm9wcy5jb21wb25lbnQsIG90aGVycyk7XG59XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2RvdWJsZS1pbmRleGVkLWt2LmpzXG52YXIgRG91YmxlSW5kZXhlZEtWID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmtleVRvVmFsdWUgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIHRoaXMudmFsdWVUb0tleSA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG4gIH1cbiAgc2V0KGtleSwgdmFsdWUpIHtcbiAgICB0aGlzLmtleVRvVmFsdWUuc2V0KGtleSwgdmFsdWUpO1xuICAgIHRoaXMudmFsdWVUb0tleS5zZXQodmFsdWUsIGtleSk7XG4gIH1cbiAgZ2V0QnlLZXkoa2V5KSB7XG4gICAgcmV0dXJuIHRoaXMua2V5VG9WYWx1ZS5nZXQoa2V5KTtcbiAgfVxuICBnZXRCeVZhbHVlKHZhbHVlKSB7XG4gICAgcmV0dXJuIHRoaXMudmFsdWVUb0tleS5nZXQodmFsdWUpO1xuICB9XG4gIGNsZWFyKCkge1xuICAgIHRoaXMua2V5VG9WYWx1ZS5jbGVhcigpO1xuICAgIHRoaXMudmFsdWVUb0tleS5jbGVhcigpO1xuICB9XG59O1xuXG4vLyAuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vc3VwZXJqc29uQDIuMi4xL25vZGVfbW9kdWxlcy9zdXBlcmpzb24vZGlzdC9yZWdpc3RyeS5qc1xudmFyIFJlZ2lzdHJ5ID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcihnZW5lcmF0ZUlkZW50aWZpZXIpIHtcbiAgICB0aGlzLmdlbmVyYXRlSWRlbnRpZmllciA9IGdlbmVyYXRlSWRlbnRpZmllcjtcbiAgICB0aGlzLmt2ID0gbmV3IERvdWJsZUluZGV4ZWRLVigpO1xuICB9XG4gIHJlZ2lzdGVyKHZhbHVlLCBpZGVudGlmaWVyKSB7XG4gICAgaWYgKHRoaXMua3YuZ2V0QnlWYWx1ZSh2YWx1ZSkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKCFpZGVudGlmaWVyKSB7XG4gICAgICBpZGVudGlmaWVyID0gdGhpcy5nZW5lcmF0ZUlkZW50aWZpZXIodmFsdWUpO1xuICAgIH1cbiAgICB0aGlzLmt2LnNldChpZGVudGlmaWVyLCB2YWx1ZSk7XG4gIH1cbiAgY2xlYXIoKSB7XG4gICAgdGhpcy5rdi5jbGVhcigpO1xuICB9XG4gIGdldElkZW50aWZpZXIodmFsdWUpIHtcbiAgICByZXR1cm4gdGhpcy5rdi5nZXRCeVZhbHVlKHZhbHVlKTtcbiAgfVxuICBnZXRWYWx1ZShpZGVudGlmaWVyKSB7XG4gICAgcmV0dXJuIHRoaXMua3YuZ2V0QnlLZXkoaWRlbnRpZmllcik7XG4gIH1cbn07XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2NsYXNzLXJlZ2lzdHJ5LmpzXG52YXIgQ2xhc3NSZWdpc3RyeSA9IGNsYXNzIGV4dGVuZHMgUmVnaXN0cnkge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICBzdXBlcigoYykgPT4gYy5uYW1lKTtcbiAgICB0aGlzLmNsYXNzVG9BbGxvd2VkUHJvcHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICB9XG4gIHJlZ2lzdGVyKHZhbHVlLCBvcHRpb25zKSB7XG4gICAgaWYgKHR5cGVvZiBvcHRpb25zID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBpZiAob3B0aW9ucy5hbGxvd1Byb3BzKSB7XG4gICAgICAgIHRoaXMuY2xhc3NUb0FsbG93ZWRQcm9wcy5zZXQodmFsdWUsIG9wdGlvbnMuYWxsb3dQcm9wcyk7XG4gICAgICB9XG4gICAgICBzdXBlci5yZWdpc3Rlcih2YWx1ZSwgb3B0aW9ucy5pZGVudGlmaWVyKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc3VwZXIucmVnaXN0ZXIodmFsdWUsIG9wdGlvbnMpO1xuICAgIH1cbiAgfVxuICBnZXRBbGxvd2VkUHJvcHModmFsdWUpIHtcbiAgICByZXR1cm4gdGhpcy5jbGFzc1RvQWxsb3dlZFByb3BzLmdldCh2YWx1ZSk7XG4gIH1cbn07XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L3V0aWwuanNcbmZ1bmN0aW9uIHZhbHVlc09mT2JqKHJlY29yZCkge1xuICBpZiAoXCJ2YWx1ZXNcIiBpbiBPYmplY3QpIHtcbiAgICByZXR1cm4gT2JqZWN0LnZhbHVlcyhyZWNvcmQpO1xuICB9XG4gIGNvbnN0IHZhbHVlcyA9IFtdO1xuICBmb3IgKGNvbnN0IGtleSBpbiByZWNvcmQpIHtcbiAgICBpZiAocmVjb3JkLmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgIHZhbHVlcy5wdXNoKHJlY29yZFtrZXldKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlcztcbn1cbmZ1bmN0aW9uIGZpbmQocmVjb3JkLCBwcmVkaWNhdGUpIHtcbiAgY29uc3QgdmFsdWVzID0gdmFsdWVzT2ZPYmoocmVjb3JkKTtcbiAgaWYgKFwiZmluZFwiIGluIHZhbHVlcykge1xuICAgIHJldHVybiB2YWx1ZXMuZmluZChwcmVkaWNhdGUpO1xuICB9XG4gIGNvbnN0IHZhbHVlc05vdE5ldmVyID0gdmFsdWVzO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHZhbHVlc05vdE5ldmVyLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgdmFsdWUgPSB2YWx1ZXNOb3ROZXZlcltpXTtcbiAgICBpZiAocHJlZGljYXRlKHZhbHVlKSkge1xuICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdm9pZCAwO1xufVxuZnVuY3Rpb24gZm9yRWFjaChyZWNvcmQsIHJ1bikge1xuICBPYmplY3QuZW50cmllcyhyZWNvcmQpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gcnVuKHZhbHVlLCBrZXkpKTtcbn1cbmZ1bmN0aW9uIGluY2x1ZGVzKGFyciwgdmFsdWUpIHtcbiAgcmV0dXJuIGFyci5pbmRleE9mKHZhbHVlKSAhPT0gLTE7XG59XG5mdW5jdGlvbiBmaW5kQXJyKHJlY29yZCwgcHJlZGljYXRlKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgcmVjb3JkLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgdmFsdWUgPSByZWNvcmRbaV07XG4gICAgaWYgKHByZWRpY2F0ZSh2YWx1ZSkpIHtcbiAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZvaWQgMDtcbn1cblxuLy8gLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3N1cGVyanNvbkAyLjIuMS9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvY3VzdG9tLXRyYW5zZm9ybWVyLXJlZ2lzdHJ5LmpzXG52YXIgQ3VzdG9tVHJhbnNmb3JtZXJSZWdpc3RyeSA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy50cmFuc2ZvbWVycyA9IHt9O1xuICB9XG4gIHJlZ2lzdGVyKHRyYW5zZm9ybWVyKSB7XG4gICAgdGhpcy50cmFuc2ZvbWVyc1t0cmFuc2Zvcm1lci5uYW1lXSA9IHRyYW5zZm9ybWVyO1xuICB9XG4gIGZpbmRBcHBsaWNhYmxlKHYpIHtcbiAgICByZXR1cm4gZmluZCh0aGlzLnRyYW5zZm9tZXJzLCAodHJhbnNmb3JtZXIpID0+IHRyYW5zZm9ybWVyLmlzQXBwbGljYWJsZSh2KSk7XG4gIH1cbiAgZmluZEJ5TmFtZShuYW1lKSB7XG4gICAgcmV0dXJuIHRoaXMudHJhbnNmb21lcnNbbmFtZV07XG4gIH1cbn07XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2lzLmpzXG52YXIgZ2V0VHlwZSA9IChwYXlsb2FkKSA9PiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwocGF5bG9hZCkuc2xpY2UoOCwgLTEpO1xudmFyIGlzVW5kZWZpbmVkID0gKHBheWxvYWQpID0+IHR5cGVvZiBwYXlsb2FkID09PSBcInVuZGVmaW5lZFwiO1xudmFyIGlzTnVsbCA9IChwYXlsb2FkKSA9PiBwYXlsb2FkID09PSBudWxsO1xudmFyIGlzUGxhaW5PYmplY3QgPSAocGF5bG9hZCkgPT4ge1xuICBpZiAodHlwZW9mIHBheWxvYWQgIT09IFwib2JqZWN0XCIgfHwgcGF5bG9hZCA9PT0gbnVsbClcbiAgICByZXR1cm4gZmFsc2U7XG4gIGlmIChwYXlsb2FkID09PSBPYmplY3QucHJvdG90eXBlKVxuICAgIHJldHVybiBmYWxzZTtcbiAgaWYgKE9iamVjdC5nZXRQcm90b3R5cGVPZihwYXlsb2FkKSA9PT0gbnVsbClcbiAgICByZXR1cm4gdHJ1ZTtcbiAgcmV0dXJuIE9iamVjdC5nZXRQcm90b3R5cGVPZihwYXlsb2FkKSA9PT0gT2JqZWN0LnByb3RvdHlwZTtcbn07XG52YXIgaXNFbXB0eU9iamVjdCA9IChwYXlsb2FkKSA9PiBpc1BsYWluT2JqZWN0KHBheWxvYWQpICYmIE9iamVjdC5rZXlzKHBheWxvYWQpLmxlbmd0aCA9PT0gMDtcbnZhciBpc0FycmF5ID0gKHBheWxvYWQpID0+IEFycmF5LmlzQXJyYXkocGF5bG9hZCk7XG52YXIgaXNTdHJpbmcgPSAocGF5bG9hZCkgPT4gdHlwZW9mIHBheWxvYWQgPT09IFwic3RyaW5nXCI7XG52YXIgaXNOdW1iZXIgPSAocGF5bG9hZCkgPT4gdHlwZW9mIHBheWxvYWQgPT09IFwibnVtYmVyXCIgJiYgIWlzTmFOKHBheWxvYWQpO1xudmFyIGlzQm9vbGVhbiA9IChwYXlsb2FkKSA9PiB0eXBlb2YgcGF5bG9hZCA9PT0gXCJib29sZWFuXCI7XG52YXIgaXNSZWdFeHAgPSAocGF5bG9hZCkgPT4gcGF5bG9hZCBpbnN0YW5jZW9mIFJlZ0V4cDtcbnZhciBpc01hcCA9IChwYXlsb2FkKSA9PiBwYXlsb2FkIGluc3RhbmNlb2YgTWFwO1xudmFyIGlzU2V0ID0gKHBheWxvYWQpID0+IHBheWxvYWQgaW5zdGFuY2VvZiBTZXQ7XG52YXIgaXNTeW1ib2wgPSAocGF5bG9hZCkgPT4gZ2V0VHlwZShwYXlsb2FkKSA9PT0gXCJTeW1ib2xcIjtcbnZhciBpc0RhdGUgPSAocGF5bG9hZCkgPT4gcGF5bG9hZCBpbnN0YW5jZW9mIERhdGUgJiYgIWlzTmFOKHBheWxvYWQudmFsdWVPZigpKTtcbnZhciBpc0Vycm9yID0gKHBheWxvYWQpID0+IHBheWxvYWQgaW5zdGFuY2VvZiBFcnJvcjtcbnZhciBpc05hTlZhbHVlID0gKHBheWxvYWQpID0+IHR5cGVvZiBwYXlsb2FkID09PSBcIm51bWJlclwiICYmIGlzTmFOKHBheWxvYWQpO1xudmFyIGlzUHJpbWl0aXZlID0gKHBheWxvYWQpID0+IGlzQm9vbGVhbihwYXlsb2FkKSB8fCBpc051bGwocGF5bG9hZCkgfHwgaXNVbmRlZmluZWQocGF5bG9hZCkgfHwgaXNOdW1iZXIocGF5bG9hZCkgfHwgaXNTdHJpbmcocGF5bG9hZCkgfHwgaXNTeW1ib2wocGF5bG9hZCk7XG52YXIgaXNCaWdpbnQgPSAocGF5bG9hZCkgPT4gdHlwZW9mIHBheWxvYWQgPT09IFwiYmlnaW50XCI7XG52YXIgaXNJbmZpbml0ZSA9IChwYXlsb2FkKSA9PiBwYXlsb2FkID09PSBJbmZpbml0eSB8fCBwYXlsb2FkID09PSAtSW5maW5pdHk7XG52YXIgaXNUeXBlZEFycmF5ID0gKHBheWxvYWQpID0+IEFycmF5QnVmZmVyLmlzVmlldyhwYXlsb2FkKSAmJiAhKHBheWxvYWQgaW5zdGFuY2VvZiBEYXRhVmlldyk7XG52YXIgaXNVUkwgPSAocGF5bG9hZCkgPT4gcGF5bG9hZCBpbnN0YW5jZW9mIFVSTDtcblxuLy8gLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3N1cGVyanNvbkAyLjIuMS9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcGF0aHN0cmluZ2lmaWVyLmpzXG52YXIgZXNjYXBlS2V5ID0gKGtleSkgPT4ga2V5LnJlcGxhY2UoL1xcLi9nLCBcIlxcXFwuXCIpO1xudmFyIHN0cmluZ2lmeVBhdGggPSAocGF0aCkgPT4gcGF0aC5tYXAoU3RyaW5nKS5tYXAoZXNjYXBlS2V5KS5qb2luKFwiLlwiKTtcbnZhciBwYXJzZVBhdGggPSAoc3RyaW5nKSA9PiB7XG4gIGNvbnN0IHJlc3VsdCA9IFtdO1xuICBsZXQgc2VnbWVudCA9IFwiXCI7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyaW5nLmxlbmd0aDsgaSsrKSB7XG4gICAgbGV0IGNoYXIgPSBzdHJpbmcuY2hhckF0KGkpO1xuICAgIGNvbnN0IGlzRXNjYXBlZERvdCA9IGNoYXIgPT09IFwiXFxcXFwiICYmIHN0cmluZy5jaGFyQXQoaSArIDEpID09PSBcIi5cIjtcbiAgICBpZiAoaXNFc2NhcGVkRG90KSB7XG4gICAgICBzZWdtZW50ICs9IFwiLlwiO1xuICAgICAgaSsrO1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IGlzRW5kT2ZTZWdtZW50ID0gY2hhciA9PT0gXCIuXCI7XG4gICAgaWYgKGlzRW5kT2ZTZWdtZW50KSB7XG4gICAgICByZXN1bHQucHVzaChzZWdtZW50KTtcbiAgICAgIHNlZ21lbnQgPSBcIlwiO1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIHNlZ21lbnQgKz0gY2hhcjtcbiAgfVxuICBjb25zdCBsYXN0U2VnbWVudCA9IHNlZ21lbnQ7XG4gIHJlc3VsdC5wdXNoKGxhc3RTZWdtZW50KTtcbiAgcmV0dXJuIHJlc3VsdDtcbn07XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L3RyYW5zZm9ybWVyLmpzXG5mdW5jdGlvbiBzaW1wbGVUcmFuc2Zvcm1hdGlvbihpc0FwcGxpY2FibGUsIGFubm90YXRpb24sIHRyYW5zZm9ybSwgdW50cmFuc2Zvcm0pIHtcbiAgcmV0dXJuIHtcbiAgICBpc0FwcGxpY2FibGUsXG4gICAgYW5ub3RhdGlvbixcbiAgICB0cmFuc2Zvcm0sXG4gICAgdW50cmFuc2Zvcm1cbiAgfTtcbn1cbnZhciBzaW1wbGVSdWxlcyA9IFtcbiAgc2ltcGxlVHJhbnNmb3JtYXRpb24oaXNVbmRlZmluZWQsIFwidW5kZWZpbmVkXCIsICgpID0+IG51bGwsICgpID0+IHZvaWQgMCksXG4gIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzQmlnaW50LCBcImJpZ2ludFwiLCAodikgPT4gdi50b1N0cmluZygpLCAodikgPT4ge1xuICAgIGlmICh0eXBlb2YgQmlnSW50ICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICByZXR1cm4gQmlnSW50KHYpO1xuICAgIH1cbiAgICBjb25zb2xlLmVycm9yKFwiUGxlYXNlIGFkZCBhIEJpZ0ludCBwb2x5ZmlsbC5cIik7XG4gICAgcmV0dXJuIHY7XG4gIH0pLFxuICBzaW1wbGVUcmFuc2Zvcm1hdGlvbihpc0RhdGUsIFwiRGF0ZVwiLCAodikgPT4gdi50b0lTT1N0cmluZygpLCAodikgPT4gbmV3IERhdGUodikpLFxuICBzaW1wbGVUcmFuc2Zvcm1hdGlvbihpc0Vycm9yLCBcIkVycm9yXCIsICh2LCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCBiYXNlRXJyb3IgPSB7XG4gICAgICBuYW1lOiB2Lm5hbWUsXG4gICAgICBtZXNzYWdlOiB2Lm1lc3NhZ2VcbiAgICB9O1xuICAgIHN1cGVySnNvbi5hbGxvd2VkRXJyb3JQcm9wcy5mb3JFYWNoKChwcm9wKSA9PiB7XG4gICAgICBiYXNlRXJyb3JbcHJvcF0gPSB2W3Byb3BdO1xuICAgIH0pO1xuICAgIHJldHVybiBiYXNlRXJyb3I7XG4gIH0sICh2LCBzdXBlckpzb24pID0+IHtcbiAgICBjb25zdCBlID0gbmV3IEVycm9yKHYubWVzc2FnZSk7XG4gICAgZS5uYW1lID0gdi5uYW1lO1xuICAgIGUuc3RhY2sgPSB2LnN0YWNrO1xuICAgIHN1cGVySnNvbi5hbGxvd2VkRXJyb3JQcm9wcy5mb3JFYWNoKChwcm9wKSA9PiB7XG4gICAgICBlW3Byb3BdID0gdltwcm9wXTtcbiAgICB9KTtcbiAgICByZXR1cm4gZTtcbiAgfSksXG4gIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzUmVnRXhwLCBcInJlZ2V4cFwiLCAodikgPT4gXCJcIiArIHYsIChyZWdleCkgPT4ge1xuICAgIGNvbnN0IGJvZHkgPSByZWdleC5zbGljZSgxLCByZWdleC5sYXN0SW5kZXhPZihcIi9cIikpO1xuICAgIGNvbnN0IGZsYWdzID0gcmVnZXguc2xpY2UocmVnZXgubGFzdEluZGV4T2YoXCIvXCIpICsgMSk7XG4gICAgcmV0dXJuIG5ldyBSZWdFeHAoYm9keSwgZmxhZ3MpO1xuICB9KSxcbiAgc2ltcGxlVHJhbnNmb3JtYXRpb24oXG4gICAgaXNTZXQsXG4gICAgXCJzZXRcIixcbiAgICAvLyAoc2V0cyBvbmx5IGV4aXN0IGluIGVzNispXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGVzNS9uby1lczYtbWV0aG9kc1xuICAgICh2KSA9PiBbLi4udi52YWx1ZXMoKV0sXG4gICAgKHYpID0+IG5ldyBTZXQodilcbiAgKSxcbiAgc2ltcGxlVHJhbnNmb3JtYXRpb24oaXNNYXAsIFwibWFwXCIsICh2KSA9PiBbLi4udi5lbnRyaWVzKCldLCAodikgPT4gbmV3IE1hcCh2KSksXG4gIHNpbXBsZVRyYW5zZm9ybWF0aW9uKCh2KSA9PiBpc05hTlZhbHVlKHYpIHx8IGlzSW5maW5pdGUodiksIFwibnVtYmVyXCIsICh2KSA9PiB7XG4gICAgaWYgKGlzTmFOVmFsdWUodikpIHtcbiAgICAgIHJldHVybiBcIk5hTlwiO1xuICAgIH1cbiAgICBpZiAodiA+IDApIHtcbiAgICAgIHJldHVybiBcIkluZmluaXR5XCI7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBcIi1JbmZpbml0eVwiO1xuICAgIH1cbiAgfSwgTnVtYmVyKSxcbiAgc2ltcGxlVHJhbnNmb3JtYXRpb24oKHYpID0+IHYgPT09IDAgJiYgMSAvIHYgPT09IC1JbmZpbml0eSwgXCJudW1iZXJcIiwgKCkgPT4ge1xuICAgIHJldHVybiBcIi0wXCI7XG4gIH0sIE51bWJlciksXG4gIHNpbXBsZVRyYW5zZm9ybWF0aW9uKGlzVVJMLCBcIlVSTFwiLCAodikgPT4gdi50b1N0cmluZygpLCAodikgPT4gbmV3IFVSTCh2KSlcbl07XG5mdW5jdGlvbiBjb21wb3NpdGVUcmFuc2Zvcm1hdGlvbihpc0FwcGxpY2FibGUsIGFubm90YXRpb24sIHRyYW5zZm9ybSwgdW50cmFuc2Zvcm0pIHtcbiAgcmV0dXJuIHtcbiAgICBpc0FwcGxpY2FibGUsXG4gICAgYW5ub3RhdGlvbixcbiAgICB0cmFuc2Zvcm0sXG4gICAgdW50cmFuc2Zvcm1cbiAgfTtcbn1cbnZhciBzeW1ib2xSdWxlID0gY29tcG9zaXRlVHJhbnNmb3JtYXRpb24oKHMsIHN1cGVySnNvbikgPT4ge1xuICBpZiAoaXNTeW1ib2wocykpIHtcbiAgICBjb25zdCBpc1JlZ2lzdGVyZWQgPSAhIXN1cGVySnNvbi5zeW1ib2xSZWdpc3RyeS5nZXRJZGVudGlmaWVyKHMpO1xuICAgIHJldHVybiBpc1JlZ2lzdGVyZWQ7XG4gIH1cbiAgcmV0dXJuIGZhbHNlO1xufSwgKHMsIHN1cGVySnNvbikgPT4ge1xuICBjb25zdCBpZGVudGlmaWVyID0gc3VwZXJKc29uLnN5bWJvbFJlZ2lzdHJ5LmdldElkZW50aWZpZXIocyk7XG4gIHJldHVybiBbXCJzeW1ib2xcIiwgaWRlbnRpZmllcl07XG59LCAodikgPT4gdi5kZXNjcmlwdGlvbiwgKF8sIGEsIHN1cGVySnNvbikgPT4ge1xuICBjb25zdCB2YWx1ZSA9IHN1cGVySnNvbi5zeW1ib2xSZWdpc3RyeS5nZXRWYWx1ZShhWzFdKTtcbiAgaWYgKCF2YWx1ZSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIlRyeWluZyB0byBkZXNlcmlhbGl6ZSB1bmtub3duIHN5bWJvbFwiKTtcbiAgfVxuICByZXR1cm4gdmFsdWU7XG59KTtcbnZhciBjb25zdHJ1Y3RvclRvTmFtZSA9IFtcbiAgSW50OEFycmF5LFxuICBVaW50OEFycmF5LFxuICBJbnQxNkFycmF5LFxuICBVaW50MTZBcnJheSxcbiAgSW50MzJBcnJheSxcbiAgVWludDMyQXJyYXksXG4gIEZsb2F0MzJBcnJheSxcbiAgRmxvYXQ2NEFycmF5LFxuICBVaW50OENsYW1wZWRBcnJheVxuXS5yZWR1Y2UoKG9iaiwgY3RvcikgPT4ge1xuICBvYmpbY3Rvci5uYW1lXSA9IGN0b3I7XG4gIHJldHVybiBvYmo7XG59LCB7fSk7XG52YXIgdHlwZWRBcnJheVJ1bGUgPSBjb21wb3NpdGVUcmFuc2Zvcm1hdGlvbihpc1R5cGVkQXJyYXksICh2KSA9PiBbXCJ0eXBlZC1hcnJheVwiLCB2LmNvbnN0cnVjdG9yLm5hbWVdLCAodikgPT4gWy4uLnZdLCAodiwgYSkgPT4ge1xuICBjb25zdCBjdG9yID0gY29uc3RydWN0b3JUb05hbWVbYVsxXV07XG4gIGlmICghY3Rvcikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIlRyeWluZyB0byBkZXNlcmlhbGl6ZSB1bmtub3duIHR5cGVkIGFycmF5XCIpO1xuICB9XG4gIHJldHVybiBuZXcgY3Rvcih2KTtcbn0pO1xuZnVuY3Rpb24gaXNJbnN0YW5jZU9mUmVnaXN0ZXJlZENsYXNzKHBvdGVudGlhbENsYXNzLCBzdXBlckpzb24pIHtcbiAgaWYgKHBvdGVudGlhbENsYXNzPy5jb25zdHJ1Y3Rvcikge1xuICAgIGNvbnN0IGlzUmVnaXN0ZXJlZCA9ICEhc3VwZXJKc29uLmNsYXNzUmVnaXN0cnkuZ2V0SWRlbnRpZmllcihwb3RlbnRpYWxDbGFzcy5jb25zdHJ1Y3Rvcik7XG4gICAgcmV0dXJuIGlzUmVnaXN0ZXJlZDtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG52YXIgY2xhc3NSdWxlID0gY29tcG9zaXRlVHJhbnNmb3JtYXRpb24oaXNJbnN0YW5jZU9mUmVnaXN0ZXJlZENsYXNzLCAoY2xhenosIHN1cGVySnNvbikgPT4ge1xuICBjb25zdCBpZGVudGlmaWVyID0gc3VwZXJKc29uLmNsYXNzUmVnaXN0cnkuZ2V0SWRlbnRpZmllcihjbGF6ei5jb25zdHJ1Y3Rvcik7XG4gIHJldHVybiBbXCJjbGFzc1wiLCBpZGVudGlmaWVyXTtcbn0sIChjbGF6eiwgc3VwZXJKc29uKSA9PiB7XG4gIGNvbnN0IGFsbG93ZWRQcm9wcyA9IHN1cGVySnNvbi5jbGFzc1JlZ2lzdHJ5LmdldEFsbG93ZWRQcm9wcyhjbGF6ei5jb25zdHJ1Y3Rvcik7XG4gIGlmICghYWxsb3dlZFByb3BzKSB7XG4gICAgcmV0dXJuIHsgLi4uY2xhenogfTtcbiAgfVxuICBjb25zdCByZXN1bHQgPSB7fTtcbiAgYWxsb3dlZFByb3BzLmZvckVhY2goKHByb3ApID0+IHtcbiAgICByZXN1bHRbcHJvcF0gPSBjbGF6eltwcm9wXTtcbiAgfSk7XG4gIHJldHVybiByZXN1bHQ7XG59LCAodiwgYSwgc3VwZXJKc29uKSA9PiB7XG4gIGNvbnN0IGNsYXp6ID0gc3VwZXJKc29uLmNsYXNzUmVnaXN0cnkuZ2V0VmFsdWUoYVsxXSk7XG4gIGlmICghY2xhenopIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJUcnlpbmcgdG8gZGVzZXJpYWxpemUgdW5rbm93biBjbGFzcyAtIGNoZWNrIGh0dHBzOi8vZ2l0aHViLmNvbS9ibGl0ei1qcy9zdXBlcmpzb24vaXNzdWVzLzExNiNpc3N1ZWNvbW1lbnQtNzczOTk2NTY0XCIpO1xuICB9XG4gIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5jcmVhdGUoY2xhenoucHJvdG90eXBlKSwgdik7XG59KTtcbnZhciBjdXN0b21SdWxlID0gY29tcG9zaXRlVHJhbnNmb3JtYXRpb24oKHZhbHVlLCBzdXBlckpzb24pID0+IHtcbiAgcmV0dXJuICEhc3VwZXJKc29uLmN1c3RvbVRyYW5zZm9ybWVyUmVnaXN0cnkuZmluZEFwcGxpY2FibGUodmFsdWUpO1xufSwgKHZhbHVlLCBzdXBlckpzb24pID0+IHtcbiAgY29uc3QgdHJhbnNmb3JtZXIgPSBzdXBlckpzb24uY3VzdG9tVHJhbnNmb3JtZXJSZWdpc3RyeS5maW5kQXBwbGljYWJsZSh2YWx1ZSk7XG4gIHJldHVybiBbXCJjdXN0b21cIiwgdHJhbnNmb3JtZXIubmFtZV07XG59LCAodmFsdWUsIHN1cGVySnNvbikgPT4ge1xuICBjb25zdCB0cmFuc2Zvcm1lciA9IHN1cGVySnNvbi5jdXN0b21UcmFuc2Zvcm1lclJlZ2lzdHJ5LmZpbmRBcHBsaWNhYmxlKHZhbHVlKTtcbiAgcmV0dXJuIHRyYW5zZm9ybWVyLnNlcmlhbGl6ZSh2YWx1ZSk7XG59LCAodiwgYSwgc3VwZXJKc29uKSA9PiB7XG4gIGNvbnN0IHRyYW5zZm9ybWVyID0gc3VwZXJKc29uLmN1c3RvbVRyYW5zZm9ybWVyUmVnaXN0cnkuZmluZEJ5TmFtZShhWzFdKTtcbiAgaWYgKCF0cmFuc2Zvcm1lcikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIlRyeWluZyB0byBkZXNlcmlhbGl6ZSB1bmtub3duIGN1c3RvbSB2YWx1ZVwiKTtcbiAgfVxuICByZXR1cm4gdHJhbnNmb3JtZXIuZGVzZXJpYWxpemUodik7XG59KTtcbnZhciBjb21wb3NpdGVSdWxlcyA9IFtjbGFzc1J1bGUsIHN5bWJvbFJ1bGUsIGN1c3RvbVJ1bGUsIHR5cGVkQXJyYXlSdWxlXTtcbnZhciB0cmFuc2Zvcm1WYWx1ZSA9ICh2YWx1ZSwgc3VwZXJKc29uKSA9PiB7XG4gIGNvbnN0IGFwcGxpY2FibGVDb21wb3NpdGVSdWxlID0gZmluZEFycihjb21wb3NpdGVSdWxlcywgKHJ1bGUpID0+IHJ1bGUuaXNBcHBsaWNhYmxlKHZhbHVlLCBzdXBlckpzb24pKTtcbiAgaWYgKGFwcGxpY2FibGVDb21wb3NpdGVSdWxlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHZhbHVlOiBhcHBsaWNhYmxlQ29tcG9zaXRlUnVsZS50cmFuc2Zvcm0odmFsdWUsIHN1cGVySnNvbiksXG4gICAgICB0eXBlOiBhcHBsaWNhYmxlQ29tcG9zaXRlUnVsZS5hbm5vdGF0aW9uKHZhbHVlLCBzdXBlckpzb24pXG4gICAgfTtcbiAgfVxuICBjb25zdCBhcHBsaWNhYmxlU2ltcGxlUnVsZSA9IGZpbmRBcnIoc2ltcGxlUnVsZXMsIChydWxlKSA9PiBydWxlLmlzQXBwbGljYWJsZSh2YWx1ZSwgc3VwZXJKc29uKSk7XG4gIGlmIChhcHBsaWNhYmxlU2ltcGxlUnVsZSkge1xuICAgIHJldHVybiB7XG4gICAgICB2YWx1ZTogYXBwbGljYWJsZVNpbXBsZVJ1bGUudHJhbnNmb3JtKHZhbHVlLCBzdXBlckpzb24pLFxuICAgICAgdHlwZTogYXBwbGljYWJsZVNpbXBsZVJ1bGUuYW5ub3RhdGlvblxuICAgIH07XG4gIH1cbiAgcmV0dXJuIHZvaWQgMDtcbn07XG52YXIgc2ltcGxlUnVsZXNCeUFubm90YXRpb24gPSB7fTtcbnNpbXBsZVJ1bGVzLmZvckVhY2goKHJ1bGUpID0+IHtcbiAgc2ltcGxlUnVsZXNCeUFubm90YXRpb25bcnVsZS5hbm5vdGF0aW9uXSA9IHJ1bGU7XG59KTtcbnZhciB1bnRyYW5zZm9ybVZhbHVlID0gKGpzb24sIHR5cGUsIHN1cGVySnNvbikgPT4ge1xuICBpZiAoaXNBcnJheSh0eXBlKSkge1xuICAgIHN3aXRjaCAodHlwZVswXSkge1xuICAgICAgY2FzZSBcInN5bWJvbFwiOlxuICAgICAgICByZXR1cm4gc3ltYm9sUnVsZS51bnRyYW5zZm9ybShqc29uLCB0eXBlLCBzdXBlckpzb24pO1xuICAgICAgY2FzZSBcImNsYXNzXCI6XG4gICAgICAgIHJldHVybiBjbGFzc1J1bGUudW50cmFuc2Zvcm0oanNvbiwgdHlwZSwgc3VwZXJKc29uKTtcbiAgICAgIGNhc2UgXCJjdXN0b21cIjpcbiAgICAgICAgcmV0dXJuIGN1c3RvbVJ1bGUudW50cmFuc2Zvcm0oanNvbiwgdHlwZSwgc3VwZXJKc29uKTtcbiAgICAgIGNhc2UgXCJ0eXBlZC1hcnJheVwiOlxuICAgICAgICByZXR1cm4gdHlwZWRBcnJheVJ1bGUudW50cmFuc2Zvcm0oanNvbiwgdHlwZSwgc3VwZXJKc29uKTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlVua25vd24gdHJhbnNmb3JtYXRpb246IFwiICsgdHlwZSk7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGNvbnN0IHRyYW5zZm9ybWF0aW9uID0gc2ltcGxlUnVsZXNCeUFubm90YXRpb25bdHlwZV07XG4gICAgaWYgKCF0cmFuc2Zvcm1hdGlvbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVW5rbm93biB0cmFuc2Zvcm1hdGlvbjogXCIgKyB0eXBlKTtcbiAgICB9XG4gICAgcmV0dXJuIHRyYW5zZm9ybWF0aW9uLnVudHJhbnNmb3JtKGpzb24sIHN1cGVySnNvbik7XG4gIH1cbn07XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2FjY2Vzc0RlZXAuanNcbnZhciBnZXROdGhLZXkgPSAodmFsdWUsIG4pID0+IHtcbiAgY29uc3Qga2V5cyA9IHZhbHVlLmtleXMoKTtcbiAgd2hpbGUgKG4gPiAwKSB7XG4gICAga2V5cy5uZXh0KCk7XG4gICAgbi0tO1xuICB9XG4gIHJldHVybiBrZXlzLm5leHQoKS52YWx1ZTtcbn07XG5mdW5jdGlvbiB2YWxpZGF0ZVBhdGgocGF0aCkge1xuICBpZiAoaW5jbHVkZXMocGF0aCwgXCJfX3Byb3RvX19cIikpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJfX3Byb3RvX18gaXMgbm90IGFsbG93ZWQgYXMgYSBwcm9wZXJ0eVwiKTtcbiAgfVxuICBpZiAoaW5jbHVkZXMocGF0aCwgXCJwcm90b3R5cGVcIikpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJwcm90b3R5cGUgaXMgbm90IGFsbG93ZWQgYXMgYSBwcm9wZXJ0eVwiKTtcbiAgfVxuICBpZiAoaW5jbHVkZXMocGF0aCwgXCJjb25zdHJ1Y3RvclwiKSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcImNvbnN0cnVjdG9yIGlzIG5vdCBhbGxvd2VkIGFzIGEgcHJvcGVydHlcIik7XG4gIH1cbn1cbnZhciBnZXREZWVwID0gKG9iamVjdCwgcGF0aCkgPT4ge1xuICB2YWxpZGF0ZVBhdGgocGF0aCk7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgcGF0aC5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGtleSA9IHBhdGhbaV07XG4gICAgaWYgKGlzU2V0KG9iamVjdCkpIHtcbiAgICAgIG9iamVjdCA9IGdldE50aEtleShvYmplY3QsICtrZXkpO1xuICAgIH0gZWxzZSBpZiAoaXNNYXAob2JqZWN0KSkge1xuICAgICAgY29uc3Qgcm93ID0gK2tleTtcbiAgICAgIGNvbnN0IHR5cGUgPSArcGF0aFsrK2ldID09PSAwID8gXCJrZXlcIiA6IFwidmFsdWVcIjtcbiAgICAgIGNvbnN0IGtleU9mUm93ID0gZ2V0TnRoS2V5KG9iamVjdCwgcm93KTtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFwia2V5XCI6XG4gICAgICAgICAgb2JqZWN0ID0ga2V5T2ZSb3c7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJ2YWx1ZVwiOlxuICAgICAgICAgIG9iamVjdCA9IG9iamVjdC5nZXQoa2V5T2ZSb3cpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBvYmplY3QgPSBvYmplY3Rba2V5XTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9iamVjdDtcbn07XG52YXIgc2V0RGVlcCA9IChvYmplY3QsIHBhdGgsIG1hcHBlcikgPT4ge1xuICB2YWxpZGF0ZVBhdGgocGF0aCk7XG4gIGlmIChwYXRoLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBtYXBwZXIob2JqZWN0KTtcbiAgfVxuICBsZXQgcGFyZW50ID0gb2JqZWN0O1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHBhdGgubGVuZ3RoIC0gMTsgaSsrKSB7XG4gICAgY29uc3Qga2V5ID0gcGF0aFtpXTtcbiAgICBpZiAoaXNBcnJheShwYXJlbnQpKSB7XG4gICAgICBjb25zdCBpbmRleCA9ICtrZXk7XG4gICAgICBwYXJlbnQgPSBwYXJlbnRbaW5kZXhdO1xuICAgIH0gZWxzZSBpZiAoaXNQbGFpbk9iamVjdChwYXJlbnQpKSB7XG4gICAgICBwYXJlbnQgPSBwYXJlbnRba2V5XTtcbiAgICB9IGVsc2UgaWYgKGlzU2V0KHBhcmVudCkpIHtcbiAgICAgIGNvbnN0IHJvdyA9ICtrZXk7XG4gICAgICBwYXJlbnQgPSBnZXROdGhLZXkocGFyZW50LCByb3cpO1xuICAgIH0gZWxzZSBpZiAoaXNNYXAocGFyZW50KSkge1xuICAgICAgY29uc3QgaXNFbmQgPSBpID09PSBwYXRoLmxlbmd0aCAtIDI7XG4gICAgICBpZiAoaXNFbmQpIHtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBjb25zdCByb3cgPSAra2V5O1xuICAgICAgY29uc3QgdHlwZSA9ICtwYXRoWysraV0gPT09IDAgPyBcImtleVwiIDogXCJ2YWx1ZVwiO1xuICAgICAgY29uc3Qga2V5T2ZSb3cgPSBnZXROdGhLZXkocGFyZW50LCByb3cpO1xuICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgIGNhc2UgXCJrZXlcIjpcbiAgICAgICAgICBwYXJlbnQgPSBrZXlPZlJvdztcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBcInZhbHVlXCI6XG4gICAgICAgICAgcGFyZW50ID0gcGFyZW50LmdldChrZXlPZlJvdyk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGNvbnN0IGxhc3RLZXkgPSBwYXRoW3BhdGgubGVuZ3RoIC0gMV07XG4gIGlmIChpc0FycmF5KHBhcmVudCkpIHtcbiAgICBwYXJlbnRbK2xhc3RLZXldID0gbWFwcGVyKHBhcmVudFsrbGFzdEtleV0pO1xuICB9IGVsc2UgaWYgKGlzUGxhaW5PYmplY3QocGFyZW50KSkge1xuICAgIHBhcmVudFtsYXN0S2V5XSA9IG1hcHBlcihwYXJlbnRbbGFzdEtleV0pO1xuICB9XG4gIGlmIChpc1NldChwYXJlbnQpKSB7XG4gICAgY29uc3Qgb2xkVmFsdWUgPSBnZXROdGhLZXkocGFyZW50LCArbGFzdEtleSk7XG4gICAgY29uc3QgbmV3VmFsdWUgPSBtYXBwZXIob2xkVmFsdWUpO1xuICAgIGlmIChvbGRWYWx1ZSAhPT0gbmV3VmFsdWUpIHtcbiAgICAgIHBhcmVudC5kZWxldGUob2xkVmFsdWUpO1xuICAgICAgcGFyZW50LmFkZChuZXdWYWx1ZSk7XG4gICAgfVxuICB9XG4gIGlmIChpc01hcChwYXJlbnQpKSB7XG4gICAgY29uc3Qgcm93ID0gK3BhdGhbcGF0aC5sZW5ndGggLSAyXTtcbiAgICBjb25zdCBrZXlUb1JvdyA9IGdldE50aEtleShwYXJlbnQsIHJvdyk7XG4gICAgY29uc3QgdHlwZSA9ICtsYXN0S2V5ID09PSAwID8gXCJrZXlcIiA6IFwidmFsdWVcIjtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgXCJrZXlcIjoge1xuICAgICAgICBjb25zdCBuZXdLZXkgPSBtYXBwZXIoa2V5VG9Sb3cpO1xuICAgICAgICBwYXJlbnQuc2V0KG5ld0tleSwgcGFyZW50LmdldChrZXlUb1JvdykpO1xuICAgICAgICBpZiAobmV3S2V5ICE9PSBrZXlUb1Jvdykge1xuICAgICAgICAgIHBhcmVudC5kZWxldGUoa2V5VG9Sb3cpO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSBcInZhbHVlXCI6IHtcbiAgICAgICAgcGFyZW50LnNldChrZXlUb1JvdywgbWFwcGVyKHBhcmVudC5nZXQoa2V5VG9Sb3cpKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gb2JqZWN0O1xufTtcblxuLy8gLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3N1cGVyanNvbkAyLjIuMS9ub2RlX21vZHVsZXMvc3VwZXJqc29uL2Rpc3QvcGxhaW5lci5qc1xuZnVuY3Rpb24gdHJhdmVyc2UodHJlZSwgd2Fsa2VyMiwgb3JpZ2luID0gW10pIHtcbiAgaWYgKCF0cmVlKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGlmICghaXNBcnJheSh0cmVlKSkge1xuICAgIGZvckVhY2godHJlZSwgKHN1YnRyZWUsIGtleSkgPT4gdHJhdmVyc2Uoc3VidHJlZSwgd2Fsa2VyMiwgWy4uLm9yaWdpbiwgLi4ucGFyc2VQYXRoKGtleSldKSk7XG4gICAgcmV0dXJuO1xuICB9XG4gIGNvbnN0IFtub2RlVmFsdWUsIGNoaWxkcmVuMl0gPSB0cmVlO1xuICBpZiAoY2hpbGRyZW4yKSB7XG4gICAgZm9yRWFjaChjaGlsZHJlbjIsIChjaGlsZCwga2V5KSA9PiB7XG4gICAgICB0cmF2ZXJzZShjaGlsZCwgd2Fsa2VyMiwgWy4uLm9yaWdpbiwgLi4ucGFyc2VQYXRoKGtleSldKTtcbiAgICB9KTtcbiAgfVxuICB3YWxrZXIyKG5vZGVWYWx1ZSwgb3JpZ2luKTtcbn1cbmZ1bmN0aW9uIGFwcGx5VmFsdWVBbm5vdGF0aW9ucyhwbGFpbiwgYW5ub3RhdGlvbnMsIHN1cGVySnNvbikge1xuICB0cmF2ZXJzZShhbm5vdGF0aW9ucywgKHR5cGUsIHBhdGgpID0+IHtcbiAgICBwbGFpbiA9IHNldERlZXAocGxhaW4sIHBhdGgsICh2KSA9PiB1bnRyYW5zZm9ybVZhbHVlKHYsIHR5cGUsIHN1cGVySnNvbikpO1xuICB9KTtcbiAgcmV0dXJuIHBsYWluO1xufVxuZnVuY3Rpb24gYXBwbHlSZWZlcmVudGlhbEVxdWFsaXR5QW5ub3RhdGlvbnMocGxhaW4sIGFubm90YXRpb25zKSB7XG4gIGZ1bmN0aW9uIGFwcGx5KGlkZW50aWNhbFBhdGhzLCBwYXRoKSB7XG4gICAgY29uc3Qgb2JqZWN0ID0gZ2V0RGVlcChwbGFpbiwgcGFyc2VQYXRoKHBhdGgpKTtcbiAgICBpZGVudGljYWxQYXRocy5tYXAocGFyc2VQYXRoKS5mb3JFYWNoKChpZGVudGljYWxPYmplY3RQYXRoKSA9PiB7XG4gICAgICBwbGFpbiA9IHNldERlZXAocGxhaW4sIGlkZW50aWNhbE9iamVjdFBhdGgsICgpID0+IG9iamVjdCk7XG4gICAgfSk7XG4gIH1cbiAgaWYgKGlzQXJyYXkoYW5ub3RhdGlvbnMpKSB7XG4gICAgY29uc3QgW3Jvb3QsIG90aGVyXSA9IGFubm90YXRpb25zO1xuICAgIHJvb3QuZm9yRWFjaCgoaWRlbnRpY2FsUGF0aCkgPT4ge1xuICAgICAgcGxhaW4gPSBzZXREZWVwKHBsYWluLCBwYXJzZVBhdGgoaWRlbnRpY2FsUGF0aCksICgpID0+IHBsYWluKTtcbiAgICB9KTtcbiAgICBpZiAob3RoZXIpIHtcbiAgICAgIGZvckVhY2gob3RoZXIsIGFwcGx5KTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgZm9yRWFjaChhbm5vdGF0aW9ucywgYXBwbHkpO1xuICB9XG4gIHJldHVybiBwbGFpbjtcbn1cbnZhciBpc0RlZXAgPSAob2JqZWN0LCBzdXBlckpzb24pID0+IGlzUGxhaW5PYmplY3Qob2JqZWN0KSB8fCBpc0FycmF5KG9iamVjdCkgfHwgaXNNYXAob2JqZWN0KSB8fCBpc1NldChvYmplY3QpIHx8IGlzSW5zdGFuY2VPZlJlZ2lzdGVyZWRDbGFzcyhvYmplY3QsIHN1cGVySnNvbik7XG5mdW5jdGlvbiBhZGRJZGVudGl0eShvYmplY3QsIHBhdGgsIGlkZW50aXRpZXMpIHtcbiAgY29uc3QgZXhpc3RpbmdTZXQgPSBpZGVudGl0aWVzLmdldChvYmplY3QpO1xuICBpZiAoZXhpc3RpbmdTZXQpIHtcbiAgICBleGlzdGluZ1NldC5wdXNoKHBhdGgpO1xuICB9IGVsc2Uge1xuICAgIGlkZW50aXRpZXMuc2V0KG9iamVjdCwgW3BhdGhdKTtcbiAgfVxufVxuZnVuY3Rpb24gZ2VuZXJhdGVSZWZlcmVudGlhbEVxdWFsaXR5QW5ub3RhdGlvbnMoaWRlbnRpdGl0ZXMsIGRlZHVwZSkge1xuICBjb25zdCByZXN1bHQgPSB7fTtcbiAgbGV0IHJvb3RFcXVhbGl0eVBhdGhzID0gdm9pZCAwO1xuICBpZGVudGl0aXRlcy5mb3JFYWNoKChwYXRocykgPT4ge1xuICAgIGlmIChwYXRocy5sZW5ndGggPD0gMSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoIWRlZHVwZSkge1xuICAgICAgcGF0aHMgPSBwYXRocy5tYXAoKHBhdGgpID0+IHBhdGgubWFwKFN0cmluZykpLnNvcnQoKGEsIGIpID0+IGEubGVuZ3RoIC0gYi5sZW5ndGgpO1xuICAgIH1cbiAgICBjb25zdCBbcmVwcmVzZW50YXRpdmVQYXRoLCAuLi5pZGVudGljYWxQYXRoc10gPSBwYXRocztcbiAgICBpZiAocmVwcmVzZW50YXRpdmVQYXRoLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcm9vdEVxdWFsaXR5UGF0aHMgPSBpZGVudGljYWxQYXRocy5tYXAoc3RyaW5naWZ5UGF0aCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJlc3VsdFtzdHJpbmdpZnlQYXRoKHJlcHJlc2VudGF0aXZlUGF0aCldID0gaWRlbnRpY2FsUGF0aHMubWFwKHN0cmluZ2lmeVBhdGgpO1xuICAgIH1cbiAgfSk7XG4gIGlmIChyb290RXF1YWxpdHlQYXRocykge1xuICAgIGlmIChpc0VtcHR5T2JqZWN0KHJlc3VsdCkpIHtcbiAgICAgIHJldHVybiBbcm9vdEVxdWFsaXR5UGF0aHNdO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gW3Jvb3RFcXVhbGl0eVBhdGhzLCByZXN1bHRdO1xuICAgIH1cbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gaXNFbXB0eU9iamVjdChyZXN1bHQpID8gdm9pZCAwIDogcmVzdWx0O1xuICB9XG59XG52YXIgd2Fsa2VyID0gKG9iamVjdCwgaWRlbnRpdGllcywgc3VwZXJKc29uLCBkZWR1cGUsIHBhdGggPSBbXSwgb2JqZWN0c0luVGhpc1BhdGggPSBbXSwgc2Vlbk9iamVjdHMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpKSA9PiB7XG4gIGNvbnN0IHByaW1pdGl2ZSA9IGlzUHJpbWl0aXZlKG9iamVjdCk7XG4gIGlmICghcHJpbWl0aXZlKSB7XG4gICAgYWRkSWRlbnRpdHkob2JqZWN0LCBwYXRoLCBpZGVudGl0aWVzKTtcbiAgICBjb25zdCBzZWVuID0gc2Vlbk9iamVjdHMuZ2V0KG9iamVjdCk7XG4gICAgaWYgKHNlZW4pIHtcbiAgICAgIHJldHVybiBkZWR1cGUgPyB7XG4gICAgICAgIHRyYW5zZm9ybWVkVmFsdWU6IG51bGxcbiAgICAgIH0gOiBzZWVuO1xuICAgIH1cbiAgfVxuICBpZiAoIWlzRGVlcChvYmplY3QsIHN1cGVySnNvbikpIHtcbiAgICBjb25zdCB0cmFuc2Zvcm1lZDIgPSB0cmFuc2Zvcm1WYWx1ZShvYmplY3QsIHN1cGVySnNvbik7XG4gICAgY29uc3QgcmVzdWx0MiA9IHRyYW5zZm9ybWVkMiA/IHtcbiAgICAgIHRyYW5zZm9ybWVkVmFsdWU6IHRyYW5zZm9ybWVkMi52YWx1ZSxcbiAgICAgIGFubm90YXRpb25zOiBbdHJhbnNmb3JtZWQyLnR5cGVdXG4gICAgfSA6IHtcbiAgICAgIHRyYW5zZm9ybWVkVmFsdWU6IG9iamVjdFxuICAgIH07XG4gICAgaWYgKCFwcmltaXRpdmUpIHtcbiAgICAgIHNlZW5PYmplY3RzLnNldChvYmplY3QsIHJlc3VsdDIpO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0MjtcbiAgfVxuICBpZiAoaW5jbHVkZXMob2JqZWN0c0luVGhpc1BhdGgsIG9iamVjdCkpIHtcbiAgICByZXR1cm4ge1xuICAgICAgdHJhbnNmb3JtZWRWYWx1ZTogbnVsbFxuICAgIH07XG4gIH1cbiAgY29uc3QgdHJhbnNmb3JtYXRpb25SZXN1bHQgPSB0cmFuc2Zvcm1WYWx1ZShvYmplY3QsIHN1cGVySnNvbik7XG4gIGNvbnN0IHRyYW5zZm9ybWVkID0gdHJhbnNmb3JtYXRpb25SZXN1bHQ/LnZhbHVlID8/IG9iamVjdDtcbiAgY29uc3QgdHJhbnNmb3JtZWRWYWx1ZSA9IGlzQXJyYXkodHJhbnNmb3JtZWQpID8gW10gOiB7fTtcbiAgY29uc3QgaW5uZXJBbm5vdGF0aW9ucyA9IHt9O1xuICBmb3JFYWNoKHRyYW5zZm9ybWVkLCAodmFsdWUsIGluZGV4KSA9PiB7XG4gICAgaWYgKGluZGV4ID09PSBcIl9fcHJvdG9fX1wiIHx8IGluZGV4ID09PSBcImNvbnN0cnVjdG9yXCIgfHwgaW5kZXggPT09IFwicHJvdG90eXBlXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRGV0ZWN0ZWQgcHJvcGVydHkgJHtpbmRleH0uIFRoaXMgaXMgYSBwcm90b3R5cGUgcG9sbHV0aW9uIHJpc2ssIHBsZWFzZSByZW1vdmUgaXQgZnJvbSB5b3VyIG9iamVjdC5gKTtcbiAgICB9XG4gICAgY29uc3QgcmVjdXJzaXZlUmVzdWx0ID0gd2Fsa2VyKHZhbHVlLCBpZGVudGl0aWVzLCBzdXBlckpzb24sIGRlZHVwZSwgWy4uLnBhdGgsIGluZGV4XSwgWy4uLm9iamVjdHNJblRoaXNQYXRoLCBvYmplY3RdLCBzZWVuT2JqZWN0cyk7XG4gICAgdHJhbnNmb3JtZWRWYWx1ZVtpbmRleF0gPSByZWN1cnNpdmVSZXN1bHQudHJhbnNmb3JtZWRWYWx1ZTtcbiAgICBpZiAoaXNBcnJheShyZWN1cnNpdmVSZXN1bHQuYW5ub3RhdGlvbnMpKSB7XG4gICAgICBpbm5lckFubm90YXRpb25zW2luZGV4XSA9IHJlY3Vyc2l2ZVJlc3VsdC5hbm5vdGF0aW9ucztcbiAgICB9IGVsc2UgaWYgKGlzUGxhaW5PYmplY3QocmVjdXJzaXZlUmVzdWx0LmFubm90YXRpb25zKSkge1xuICAgICAgZm9yRWFjaChyZWN1cnNpdmVSZXN1bHQuYW5ub3RhdGlvbnMsICh0cmVlLCBrZXkpID0+IHtcbiAgICAgICAgaW5uZXJBbm5vdGF0aW9uc1tlc2NhcGVLZXkoaW5kZXgpICsgXCIuXCIgKyBrZXldID0gdHJlZTtcbiAgICAgIH0pO1xuICAgIH1cbiAgfSk7XG4gIGNvbnN0IHJlc3VsdCA9IGlzRW1wdHlPYmplY3QoaW5uZXJBbm5vdGF0aW9ucykgPyB7XG4gICAgdHJhbnNmb3JtZWRWYWx1ZSxcbiAgICBhbm5vdGF0aW9uczogISF0cmFuc2Zvcm1hdGlvblJlc3VsdCA/IFt0cmFuc2Zvcm1hdGlvblJlc3VsdC50eXBlXSA6IHZvaWQgMFxuICB9IDoge1xuICAgIHRyYW5zZm9ybWVkVmFsdWUsXG4gICAgYW5ub3RhdGlvbnM6ICEhdHJhbnNmb3JtYXRpb25SZXN1bHQgPyBbdHJhbnNmb3JtYXRpb25SZXN1bHQudHlwZSwgaW5uZXJBbm5vdGF0aW9uc10gOiBpbm5lckFubm90YXRpb25zXG4gIH07XG4gIGlmICghcHJpbWl0aXZlKSB7XG4gICAgc2Vlbk9iamVjdHMuc2V0KG9iamVjdCwgcmVzdWx0KTtcbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufTtcblxuLy8gLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2lzLXdoYXRANC4xLjE2L25vZGVfbW9kdWxlcy9pcy13aGF0L2Rpc3QvaW5kZXguanNcbmZ1bmN0aW9uIGdldFR5cGUyKHBheWxvYWQpIHtcbiAgcmV0dXJuIE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChwYXlsb2FkKS5zbGljZSg4LCAtMSk7XG59XG5mdW5jdGlvbiBpc0FycmF5MihwYXlsb2FkKSB7XG4gIHJldHVybiBnZXRUeXBlMihwYXlsb2FkKSA9PT0gXCJBcnJheVwiO1xufVxuZnVuY3Rpb24gaXNQbGFpbk9iamVjdDIocGF5bG9hZCkge1xuICBpZiAoZ2V0VHlwZTIocGF5bG9hZCkgIT09IFwiT2JqZWN0XCIpXG4gICAgcmV0dXJuIGZhbHNlO1xuICBjb25zdCBwcm90b3R5cGUgPSBPYmplY3QuZ2V0UHJvdG90eXBlT2YocGF5bG9hZCk7XG4gIHJldHVybiAhIXByb3RvdHlwZSAmJiBwcm90b3R5cGUuY29uc3RydWN0b3IgPT09IE9iamVjdCAmJiBwcm90b3R5cGUgPT09IE9iamVjdC5wcm90b3R5cGU7XG59XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9jb3B5LWFueXRoaW5nQDMuMC41L25vZGVfbW9kdWxlcy9jb3B5LWFueXRoaW5nL2Rpc3QvaW5kZXguanNcbmZ1bmN0aW9uIGFzc2lnblByb3AyKGNhcnJ5LCBrZXksIG5ld1ZhbCwgb3JpZ2luYWxPYmplY3QsIGluY2x1ZGVOb25lbnVtZXJhYmxlKSB7XG4gIGNvbnN0IHByb3BUeXBlID0ge30ucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChvcmlnaW5hbE9iamVjdCwga2V5KSA/IFwiZW51bWVyYWJsZVwiIDogXCJub25lbnVtZXJhYmxlXCI7XG4gIGlmIChwcm9wVHlwZSA9PT0gXCJlbnVtZXJhYmxlXCIpXG4gICAgY2Fycnlba2V5XSA9IG5ld1ZhbDtcbiAgaWYgKGluY2x1ZGVOb25lbnVtZXJhYmxlICYmIHByb3BUeXBlID09PSBcIm5vbmVudW1lcmFibGVcIikge1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShjYXJyeSwga2V5LCB7XG4gICAgICB2YWx1ZTogbmV3VmFsLFxuICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgIH0pO1xuICB9XG59XG5mdW5jdGlvbiBjb3B5KHRhcmdldCwgb3B0aW9ucyA9IHt9KSB7XG4gIGlmIChpc0FycmF5Mih0YXJnZXQpKSB7XG4gICAgcmV0dXJuIHRhcmdldC5tYXAoKGl0ZW0pID0+IGNvcHkoaXRlbSwgb3B0aW9ucykpO1xuICB9XG4gIGlmICghaXNQbGFpbk9iamVjdDIodGFyZ2V0KSkge1xuICAgIHJldHVybiB0YXJnZXQ7XG4gIH1cbiAgY29uc3QgcHJvcHMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyh0YXJnZXQpO1xuICBjb25zdCBzeW1ib2xzID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyh0YXJnZXQpO1xuICByZXR1cm4gWy4uLnByb3BzLCAuLi5zeW1ib2xzXS5yZWR1Y2UoKGNhcnJ5LCBrZXkpID0+IHtcbiAgICBpZiAoaXNBcnJheTIob3B0aW9ucy5wcm9wcykgJiYgIW9wdGlvbnMucHJvcHMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgcmV0dXJuIGNhcnJ5O1xuICAgIH1cbiAgICBjb25zdCB2YWwgPSB0YXJnZXRba2V5XTtcbiAgICBjb25zdCBuZXdWYWwgPSBjb3B5KHZhbCwgb3B0aW9ucyk7XG4gICAgYXNzaWduUHJvcDIoY2FycnksIGtleSwgbmV3VmFsLCB0YXJnZXQsIG9wdGlvbnMubm9uZW51bWVyYWJsZSk7XG4gICAgcmV0dXJuIGNhcnJ5O1xuICB9LCB7fSk7XG59XG5cbi8vIC4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9zdXBlcmpzb25AMi4yLjEvbm9kZV9tb2R1bGVzL3N1cGVyanNvbi9kaXN0L2luZGV4LmpzXG52YXIgU3VwZXJKU09OID0gY2xhc3Mge1xuICAvKipcbiAgICogQHBhcmFtIGRlZHVwZVJlZmVyZW50aWFsRXF1YWxpdGllcyAgSWYgdHJ1ZSwgU3VwZXJKU09OIHdpbGwgbWFrZSBzdXJlIG9ubHkgb25lIGluc3RhbmNlIG9mIHJlZmVyZW50aWFsbHkgZXF1YWwgb2JqZWN0cyBhcmUgc2VyaWFsaXplZCBhbmQgdGhlIHJlc3QgYXJlIHJlcGxhY2VkIHdpdGggYG51bGxgLlxuICAgKi9cbiAgY29uc3RydWN0b3IoeyBkZWR1cGUgPSBmYWxzZSB9ID0ge30pIHtcbiAgICB0aGlzLmNsYXNzUmVnaXN0cnkgPSBuZXcgQ2xhc3NSZWdpc3RyeSgpO1xuICAgIHRoaXMuc3ltYm9sUmVnaXN0cnkgPSBuZXcgUmVnaXN0cnkoKHMpID0+IHMuZGVzY3JpcHRpb24gPz8gXCJcIik7XG4gICAgdGhpcy5jdXN0b21UcmFuc2Zvcm1lclJlZ2lzdHJ5ID0gbmV3IEN1c3RvbVRyYW5zZm9ybWVyUmVnaXN0cnkoKTtcbiAgICB0aGlzLmFsbG93ZWRFcnJvclByb3BzID0gW107XG4gICAgdGhpcy5kZWR1cGUgPSBkZWR1cGU7XG4gIH1cbiAgc2VyaWFsaXplKG9iamVjdCkge1xuICAgIGNvbnN0IGlkZW50aXRpZXMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuICAgIGNvbnN0IG91dHB1dCA9IHdhbGtlcihvYmplY3QsIGlkZW50aXRpZXMsIHRoaXMsIHRoaXMuZGVkdXBlKTtcbiAgICBjb25zdCByZXMgPSB7XG4gICAgICBqc29uOiBvdXRwdXQudHJhbnNmb3JtZWRWYWx1ZVxuICAgIH07XG4gICAgaWYgKG91dHB1dC5hbm5vdGF0aW9ucykge1xuICAgICAgcmVzLm1ldGEgPSB7XG4gICAgICAgIC4uLnJlcy5tZXRhLFxuICAgICAgICB2YWx1ZXM6IG91dHB1dC5hbm5vdGF0aW9uc1xuICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgZXF1YWxpdHlBbm5vdGF0aW9ucyA9IGdlbmVyYXRlUmVmZXJlbnRpYWxFcXVhbGl0eUFubm90YXRpb25zKGlkZW50aXRpZXMsIHRoaXMuZGVkdXBlKTtcbiAgICBpZiAoZXF1YWxpdHlBbm5vdGF0aW9ucykge1xuICAgICAgcmVzLm1ldGEgPSB7XG4gICAgICAgIC4uLnJlcy5tZXRhLFxuICAgICAgICByZWZlcmVudGlhbEVxdWFsaXRpZXM6IGVxdWFsaXR5QW5ub3RhdGlvbnNcbiAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiByZXM7XG4gIH1cbiAgZGVzZXJpYWxpemUocGF5bG9hZCkge1xuICAgIGNvbnN0IHsganNvbiwgbWV0YSB9ID0gcGF5bG9hZDtcbiAgICBsZXQgcmVzdWx0ID0gY29weShqc29uKTtcbiAgICBpZiAobWV0YT8udmFsdWVzKSB7XG4gICAgICByZXN1bHQgPSBhcHBseVZhbHVlQW5ub3RhdGlvbnMocmVzdWx0LCBtZXRhLnZhbHVlcywgdGhpcyk7XG4gICAgfVxuICAgIGlmIChtZXRhPy5yZWZlcmVudGlhbEVxdWFsaXRpZXMpIHtcbiAgICAgIHJlc3VsdCA9IGFwcGx5UmVmZXJlbnRpYWxFcXVhbGl0eUFubm90YXRpb25zKHJlc3VsdCwgbWV0YS5yZWZlcmVudGlhbEVxdWFsaXRpZXMpO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIHN0cmluZ2lmeShvYmplY3QpIHtcbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkodGhpcy5zZXJpYWxpemUob2JqZWN0KSk7XG4gIH1cbiAgcGFyc2Uoc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMuZGVzZXJpYWxpemUoSlNPTi5wYXJzZShzdHJpbmcpKTtcbiAgfVxuICByZWdpc3RlckNsYXNzKHYsIG9wdGlvbnMpIHtcbiAgICB0aGlzLmNsYXNzUmVnaXN0cnkucmVnaXN0ZXIodiwgb3B0aW9ucyk7XG4gIH1cbiAgcmVnaXN0ZXJTeW1ib2wodiwgaWRlbnRpZmllcikge1xuICAgIHRoaXMuc3ltYm9sUmVnaXN0cnkucmVnaXN0ZXIodiwgaWRlbnRpZmllcik7XG4gIH1cbiAgcmVnaXN0ZXJDdXN0b20odHJhbnNmb3JtZXIsIG5hbWUpIHtcbiAgICB0aGlzLmN1c3RvbVRyYW5zZm9ybWVyUmVnaXN0cnkucmVnaXN0ZXIoe1xuICAgICAgbmFtZSxcbiAgICAgIC4uLnRyYW5zZm9ybWVyXG4gICAgfSk7XG4gIH1cbiAgYWxsb3dFcnJvclByb3BzKC4uLnByb3BzKSB7XG4gICAgdGhpcy5hbGxvd2VkRXJyb3JQcm9wcy5wdXNoKC4uLnByb3BzKTtcbiAgfVxufTtcblN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UgPSBuZXcgU3VwZXJKU09OKCk7XG5TdXBlckpTT04uc2VyaWFsaXplID0gU3VwZXJKU09OLmRlZmF1bHRJbnN0YW5jZS5zZXJpYWxpemUuYmluZChTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlKTtcblN1cGVySlNPTi5kZXNlcmlhbGl6ZSA9IFN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UuZGVzZXJpYWxpemUuYmluZChTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlKTtcblN1cGVySlNPTi5zdHJpbmdpZnkgPSBTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlLnN0cmluZ2lmeS5iaW5kKFN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UpO1xuU3VwZXJKU09OLnBhcnNlID0gU3VwZXJKU09OLmRlZmF1bHRJbnN0YW5jZS5wYXJzZS5iaW5kKFN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UpO1xuU3VwZXJKU09OLnJlZ2lzdGVyQ2xhc3MgPSBTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlLnJlZ2lzdGVyQ2xhc3MuYmluZChTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlKTtcblN1cGVySlNPTi5yZWdpc3RlclN5bWJvbCA9IFN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UucmVnaXN0ZXJTeW1ib2wuYmluZChTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlKTtcblN1cGVySlNPTi5yZWdpc3RlckN1c3RvbSA9IFN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UucmVnaXN0ZXJDdXN0b20uYmluZChTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlKTtcblN1cGVySlNPTi5hbGxvd0Vycm9yUHJvcHMgPSBTdXBlckpTT04uZGVmYXVsdEluc3RhbmNlLmFsbG93RXJyb3JQcm9wcy5iaW5kKFN1cGVySlNPTi5kZWZhdWx0SW5zdGFuY2UpO1xudmFyIHNlcmlhbGl6ZSA9IFN1cGVySlNPTi5zZXJpYWxpemU7XG5TdXBlckpTT04uZGVzZXJpYWxpemU7XG52YXIgc3RyaW5naWZ5ID0gU3VwZXJKU09OLnN0cmluZ2lmeTtcblN1cGVySlNPTi5wYXJzZTtcblN1cGVySlNPTi5yZWdpc3RlckNsYXNzO1xuU3VwZXJKU09OLnJlZ2lzdGVyQ3VzdG9tO1xuU3VwZXJKU09OLnJlZ2lzdGVyU3ltYm9sO1xuU3VwZXJKU09OLmFsbG93RXJyb3JQcm9wcztcblxuLy8gc3JjL3V0aWxzLnRzeFxuZnVuY3Rpb24gZ2V0UXVlcnlTdGF0dXNMYWJlbChxdWVyeSkge1xuICByZXR1cm4gcXVlcnkuc3RhdGUuZmV0Y2hTdGF0dXMgPT09IFwiZmV0Y2hpbmdcIiA/IFwiZmV0Y2hpbmdcIiA6ICFxdWVyeS5nZXRPYnNlcnZlcnNDb3VudCgpID8gXCJpbmFjdGl2ZVwiIDogcXVlcnkuc3RhdGUuZmV0Y2hTdGF0dXMgPT09IFwicGF1c2VkXCIgPyBcInBhdXNlZFwiIDogcXVlcnkuaXNTdGFsZSgpID8gXCJzdGFsZVwiIDogXCJmcmVzaFwiO1xufVxuZnVuY3Rpb24gZ2V0U2lkZWRQcm9wKHByb3AsIHNpZGUpIHtcbiAgcmV0dXJuIGAke3Byb3B9JHtzaWRlLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgc2lkZS5zbGljZSgxKX1gO1xufVxuZnVuY3Rpb24gZ2V0UXVlcnlTdGF0dXNDb2xvcih7XG4gIHF1ZXJ5U3RhdGUsXG4gIG9ic2VydmVyQ291bnQsXG4gIGlzU3RhbGVcbn0pIHtcbiAgcmV0dXJuIHF1ZXJ5U3RhdGUuZmV0Y2hTdGF0dXMgPT09IFwiZmV0Y2hpbmdcIiA/IFwiYmx1ZVwiIDogIW9ic2VydmVyQ291bnQgPyBcImdyYXlcIiA6IHF1ZXJ5U3RhdGUuZmV0Y2hTdGF0dXMgPT09IFwicGF1c2VkXCIgPyBcInB1cnBsZVwiIDogaXNTdGFsZSA/IFwieWVsbG93XCIgOiBcImdyZWVuXCI7XG59XG5mdW5jdGlvbiBnZXRNdXRhdGlvblN0YXR1c0NvbG9yKHtcbiAgc3RhdHVzLFxuICBpc1BhdXNlZFxufSkge1xuICByZXR1cm4gaXNQYXVzZWQgPyBcInB1cnBsZVwiIDogc3RhdHVzID09PSBcImVycm9yXCIgPyBcInJlZFwiIDogc3RhdHVzID09PSBcInBlbmRpbmdcIiA/IFwieWVsbG93XCIgOiBzdGF0dXMgPT09IFwic3VjY2Vzc1wiID8gXCJncmVlblwiIDogXCJncmF5XCI7XG59XG5mdW5jdGlvbiBnZXRRdWVyeVN0YXR1c0NvbG9yQnlMYWJlbChsYWJlbCkge1xuICByZXR1cm4gbGFiZWwgPT09IFwiZnJlc2hcIiA/IFwiZ3JlZW5cIiA6IGxhYmVsID09PSBcInN0YWxlXCIgPyBcInllbGxvd1wiIDogbGFiZWwgPT09IFwicGF1c2VkXCIgPyBcInB1cnBsZVwiIDogbGFiZWwgPT09IFwiaW5hY3RpdmVcIiA/IFwiZ3JheVwiIDogXCJibHVlXCI7XG59XG52YXIgZGlzcGxheVZhbHVlID0gKHZhbHVlLCBiZWF1dGlmeSA9IGZhbHNlKSA9PiB7XG4gIGNvbnN0IHtcbiAgICBqc29uXG4gIH0gPSBzZXJpYWxpemUodmFsdWUpO1xuICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoanNvbiwgbnVsbCwgYmVhdXRpZnkgPyAyIDogdm9pZCAwKTtcbn07XG52YXIgZ2V0U3RhdHVzUmFuayA9IChxKSA9PiBxLnN0YXRlLmZldGNoU3RhdHVzICE9PSBcImlkbGVcIiA/IDAgOiAhcS5nZXRPYnNlcnZlcnNDb3VudCgpID8gMyA6IHEuaXNTdGFsZSgpID8gMiA6IDE7XG52YXIgcXVlcnlIYXNoU29ydCA9IChhLCBiKSA9PiBhLnF1ZXJ5SGFzaC5sb2NhbGVDb21wYXJlKGIucXVlcnlIYXNoKTtcbnZhciBkYXRlU29ydCA9IChhLCBiKSA9PiBhLnN0YXRlLmRhdGFVcGRhdGVkQXQgPCBiLnN0YXRlLmRhdGFVcGRhdGVkQXQgPyAxIDogLTE7XG52YXIgc3RhdHVzQW5kRGF0ZVNvcnQgPSAoYSwgYikgPT4ge1xuICBpZiAoZ2V0U3RhdHVzUmFuayhhKSA9PT0gZ2V0U3RhdHVzUmFuayhiKSkge1xuICAgIHJldHVybiBkYXRlU29ydChhLCBiKTtcbiAgfVxuICByZXR1cm4gZ2V0U3RhdHVzUmFuayhhKSA+IGdldFN0YXR1c1JhbmsoYikgPyAxIDogLTE7XG59O1xudmFyIHNvcnRGbnMgPSB7XG4gIHN0YXR1czogc3RhdHVzQW5kRGF0ZVNvcnQsXG4gIFwicXVlcnkgaGFzaFwiOiBxdWVyeUhhc2hTb3J0LFxuICBcImxhc3QgdXBkYXRlZFwiOiBkYXRlU29ydFxufTtcbnZhciBnZXRNdXRhdGlvblN0YXR1c1JhbmsgPSAobSkgPT4gbS5zdGF0ZS5pc1BhdXNlZCA/IDAgOiBtLnN0YXRlLnN0YXR1cyA9PT0gXCJlcnJvclwiID8gMiA6IG0uc3RhdGUuc3RhdHVzID09PSBcInBlbmRpbmdcIiA/IDEgOiAzO1xudmFyIG11dGF0aW9uRGF0ZVNvcnQgPSAoYSwgYikgPT4gYS5zdGF0ZS5zdWJtaXR0ZWRBdCA8IGIuc3RhdGUuc3VibWl0dGVkQXQgPyAxIDogLTE7XG52YXIgbXV0YXRpb25TdGF0dXNTb3J0ID0gKGEsIGIpID0+IHtcbiAgaWYgKGdldE11dGF0aW9uU3RhdHVzUmFuayhhKSA9PT0gZ2V0TXV0YXRpb25TdGF0dXNSYW5rKGIpKSB7XG4gICAgcmV0dXJuIG11dGF0aW9uRGF0ZVNvcnQoYSwgYik7XG4gIH1cbiAgcmV0dXJuIGdldE11dGF0aW9uU3RhdHVzUmFuayhhKSA+IGdldE11dGF0aW9uU3RhdHVzUmFuayhiKSA/IDEgOiAtMTtcbn07XG52YXIgbXV0YXRpb25Tb3J0Rm5zID0ge1xuICBzdGF0dXM6IG11dGF0aW9uU3RhdHVzU29ydCxcbiAgXCJsYXN0IHVwZGF0ZWRcIjogbXV0YXRpb25EYXRlU29ydFxufTtcbnZhciBjb252ZXJ0UmVtVG9QaXhlbHMgPSAocmVtKSA9PiB7XG4gIHJldHVybiByZW0gKiBwYXJzZUZsb2F0KGdldENvbXB1dGVkU3R5bGUoZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50KS5mb250U2l6ZSk7XG59O1xudmFyIGdldFByZWZlcnJlZENvbG9yU2NoZW1lID0gKCkgPT4ge1xuICBjb25zdCBbY29sb3JTY2hlbWUsIHNldENvbG9yU2NoZW1lXSA9IGNyZWF0ZVNpZ25hbChcImRhcmtcIik7XG4gIG9uTW91bnQoKCkgPT4ge1xuICAgIGNvbnN0IHF1ZXJ5ID0gd2luZG93Lm1hdGNoTWVkaWEoXCIocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspXCIpO1xuICAgIHNldENvbG9yU2NoZW1lKHF1ZXJ5Lm1hdGNoZXMgPyBcImRhcmtcIiA6IFwibGlnaHRcIik7XG4gICAgY29uc3QgbGlzdGVuZXIgPSAoZSkgPT4ge1xuICAgICAgc2V0Q29sb3JTY2hlbWUoZS5tYXRjaGVzID8gXCJkYXJrXCIgOiBcImxpZ2h0XCIpO1xuICAgIH07XG4gICAgcXVlcnkuYWRkRXZlbnRMaXN0ZW5lcihcImNoYW5nZVwiLCBsaXN0ZW5lcik7XG4gICAgb25DbGVhbnVwKCgpID0+IHF1ZXJ5LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgbGlzdGVuZXIpKTtcbiAgfSk7XG4gIHJldHVybiBjb2xvclNjaGVtZTtcbn07XG52YXIgdXBkYXRlTmVzdGVkRGF0YUJ5UGF0aCA9IChvbGREYXRhLCB1cGRhdGVQYXRoLCB2YWx1ZSkgPT4ge1xuICBpZiAodXBkYXRlUGF0aC5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cbiAgaWYgKG9sZERhdGEgaW5zdGFuY2VvZiBNYXApIHtcbiAgICBjb25zdCBuZXdEYXRhID0gbmV3IE1hcChvbGREYXRhKTtcbiAgICBpZiAodXBkYXRlUGF0aC5sZW5ndGggPT09IDEpIHtcbiAgICAgIG5ld0RhdGEuc2V0KHVwZGF0ZVBhdGhbMF0sIHZhbHVlKTtcbiAgICAgIHJldHVybiBuZXdEYXRhO1xuICAgIH1cbiAgICBjb25zdCBbaGVhZCwgLi4udGFpbF0gPSB1cGRhdGVQYXRoO1xuICAgIG5ld0RhdGEuc2V0KGhlYWQsIHVwZGF0ZU5lc3RlZERhdGFCeVBhdGgobmV3RGF0YS5nZXQoaGVhZCksIHRhaWwsIHZhbHVlKSk7XG4gICAgcmV0dXJuIG5ld0RhdGE7XG4gIH1cbiAgaWYgKG9sZERhdGEgaW5zdGFuY2VvZiBTZXQpIHtcbiAgICBjb25zdCBzZXRBc0FycmF5ID0gdXBkYXRlTmVzdGVkRGF0YUJ5UGF0aChBcnJheS5mcm9tKG9sZERhdGEpLCB1cGRhdGVQYXRoLCB2YWx1ZSk7XG4gICAgcmV0dXJuIG5ldyBTZXQoc2V0QXNBcnJheSk7XG4gIH1cbiAgaWYgKEFycmF5LmlzQXJyYXkob2xkRGF0YSkpIHtcbiAgICBjb25zdCBuZXdEYXRhID0gWy4uLm9sZERhdGFdO1xuICAgIGlmICh1cGRhdGVQYXRoLmxlbmd0aCA9PT0gMSkge1xuICAgICAgbmV3RGF0YVt1cGRhdGVQYXRoWzBdXSA9IHZhbHVlO1xuICAgICAgcmV0dXJuIG5ld0RhdGE7XG4gICAgfVxuICAgIGNvbnN0IFtoZWFkLCAuLi50YWlsXSA9IHVwZGF0ZVBhdGg7XG4gICAgbmV3RGF0YVtoZWFkXSA9IHVwZGF0ZU5lc3RlZERhdGFCeVBhdGgobmV3RGF0YVtoZWFkXSwgdGFpbCwgdmFsdWUpO1xuICAgIHJldHVybiBuZXdEYXRhO1xuICB9XG4gIGlmIChvbGREYXRhIGluc3RhbmNlb2YgT2JqZWN0KSB7XG4gICAgY29uc3QgbmV3RGF0YSA9IHtcbiAgICAgIC4uLm9sZERhdGFcbiAgICB9O1xuICAgIGlmICh1cGRhdGVQYXRoLmxlbmd0aCA9PT0gMSkge1xuICAgICAgbmV3RGF0YVt1cGRhdGVQYXRoWzBdXSA9IHZhbHVlO1xuICAgICAgcmV0dXJuIG5ld0RhdGE7XG4gICAgfVxuICAgIGNvbnN0IFtoZWFkLCAuLi50YWlsXSA9IHVwZGF0ZVBhdGg7XG4gICAgbmV3RGF0YVtoZWFkXSA9IHVwZGF0ZU5lc3RlZERhdGFCeVBhdGgobmV3RGF0YVtoZWFkXSwgdGFpbCwgdmFsdWUpO1xuICAgIHJldHVybiBuZXdEYXRhO1xuICB9XG4gIHJldHVybiBvbGREYXRhO1xufTtcbnZhciBkZWxldGVOZXN0ZWREYXRhQnlQYXRoID0gKG9sZERhdGEsIGRlbGV0ZVBhdGgpID0+IHtcbiAgaWYgKG9sZERhdGEgaW5zdGFuY2VvZiBNYXApIHtcbiAgICBjb25zdCBuZXdEYXRhID0gbmV3IE1hcChvbGREYXRhKTtcbiAgICBpZiAoZGVsZXRlUGF0aC5sZW5ndGggPT09IDEpIHtcbiAgICAgIG5ld0RhdGEuZGVsZXRlKGRlbGV0ZVBhdGhbMF0pO1xuICAgICAgcmV0dXJuIG5ld0RhdGE7XG4gICAgfVxuICAgIGNvbnN0IFtoZWFkLCAuLi50YWlsXSA9IGRlbGV0ZVBhdGg7XG4gICAgbmV3RGF0YS5zZXQoaGVhZCwgZGVsZXRlTmVzdGVkRGF0YUJ5UGF0aChuZXdEYXRhLmdldChoZWFkKSwgdGFpbCkpO1xuICAgIHJldHVybiBuZXdEYXRhO1xuICB9XG4gIGlmIChvbGREYXRhIGluc3RhbmNlb2YgU2V0KSB7XG4gICAgY29uc3Qgc2V0QXNBcnJheSA9IGRlbGV0ZU5lc3RlZERhdGFCeVBhdGgoQXJyYXkuZnJvbShvbGREYXRhKSwgZGVsZXRlUGF0aCk7XG4gICAgcmV0dXJuIG5ldyBTZXQoc2V0QXNBcnJheSk7XG4gIH1cbiAgaWYgKEFycmF5LmlzQXJyYXkob2xkRGF0YSkpIHtcbiAgICBjb25zdCBuZXdEYXRhID0gWy4uLm9sZERhdGFdO1xuICAgIGlmIChkZWxldGVQYXRoLmxlbmd0aCA9PT0gMSkge1xuICAgICAgcmV0dXJuIG5ld0RhdGEuZmlsdGVyKChfLCBpZHgpID0+IGlkeC50b1N0cmluZygpICE9PSBkZWxldGVQYXRoWzBdKTtcbiAgICB9XG4gICAgY29uc3QgW2hlYWQsIC4uLnRhaWxdID0gZGVsZXRlUGF0aDtcbiAgICBuZXdEYXRhW2hlYWRdID0gZGVsZXRlTmVzdGVkRGF0YUJ5UGF0aChuZXdEYXRhW2hlYWRdLCB0YWlsKTtcbiAgICByZXR1cm4gbmV3RGF0YTtcbiAgfVxuICBpZiAob2xkRGF0YSBpbnN0YW5jZW9mIE9iamVjdCkge1xuICAgIGNvbnN0IG5ld0RhdGEgPSB7XG4gICAgICAuLi5vbGREYXRhXG4gICAgfTtcbiAgICBpZiAoZGVsZXRlUGF0aC5sZW5ndGggPT09IDEpIHtcbiAgICAgIGRlbGV0ZSBuZXdEYXRhW2RlbGV0ZVBhdGhbMF1dO1xuICAgICAgcmV0dXJuIG5ld0RhdGE7XG4gICAgfVxuICAgIGNvbnN0IFtoZWFkLCAuLi50YWlsXSA9IGRlbGV0ZVBhdGg7XG4gICAgbmV3RGF0YVtoZWFkXSA9IGRlbGV0ZU5lc3RlZERhdGFCeVBhdGgobmV3RGF0YVtoZWFkXSwgdGFpbCk7XG4gICAgcmV0dXJuIG5ld0RhdGE7XG4gIH1cbiAgcmV0dXJuIG9sZERhdGE7XG59O1xudmFyIHNldHVwU3R5bGVTaGVldCA9IChub25jZSwgdGFyZ2V0KSA9PiB7XG4gIGlmICghbm9uY2UpIHJldHVybjtcbiAgY29uc3Qgc3R5bGVFeGlzdHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKFwiI19nb29iZXJcIikgfHwgdGFyZ2V0Py5xdWVyeVNlbGVjdG9yKFwiI19nb29iZXJcIik7XG4gIGlmIChzdHlsZUV4aXN0cykgcmV0dXJuO1xuICBjb25zdCBzdHlsZVRhZyA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzdHlsZVwiKTtcbiAgY29uc3QgdGV4dE5vZGUgPSBkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShcIlwiKTtcbiAgc3R5bGVUYWcuYXBwZW5kQ2hpbGQodGV4dE5vZGUpO1xuICBzdHlsZVRhZy5pZCA9IFwiX2dvb2JlclwiO1xuICBzdHlsZVRhZy5zZXRBdHRyaWJ1dGUoXCJub25jZVwiLCBub25jZSk7XG4gIGlmICh0YXJnZXQpIHtcbiAgICB0YXJnZXQuYXBwZW5kQ2hpbGQoc3R5bGVUYWcpO1xuICB9IGVsc2Uge1xuICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoc3R5bGVUYWcpO1xuICB9XG59O1xuXG5leHBvcnQgeyAkVFJBQ0ssIERFViwgRHluYW1pYywgRm9yLCBJbmRleCwgTWF0Y2gsIFBvcnRhbCwgU2hvdywgU3dpdGNoLCBhZGRFdmVudExpc3RlbmVyLCBiYXRjaCwgY2xhc3NOYW1lLCBjbGVhckRlbGVnYXRlZEV2ZW50cywgY29udmVydFJlbVRvUGl4ZWxzLCBjcmVhdGVDb21wb25lbnQsIGNyZWF0ZUNvbXB1dGVkLCBjcmVhdGVDb250ZXh0LCBjcmVhdGVFZmZlY3QsIGNyZWF0ZU1lbW8sIGNyZWF0ZVJlbmRlckVmZmVjdCwgY3JlYXRlUm9vdCwgY3JlYXRlU2lnbmFsLCBjcmVhdGVVbmlxdWVJZCwgZGVsZWdhdGVFdmVudHMsIGRlbGV0ZU5lc3RlZERhdGFCeVBhdGgsIGRpc3BsYXlWYWx1ZSwgZ2V0TXV0YXRpb25TdGF0dXNDb2xvciwgZ2V0T3duZXIsIGdldFByZWZlcnJlZENvbG9yU2NoZW1lLCBnZXRRdWVyeVN0YXR1c0NvbG9yLCBnZXRRdWVyeVN0YXR1c0NvbG9yQnlMYWJlbCwgZ2V0UXVlcnlTdGF0dXNMYWJlbCwgZ2V0U2lkZWRQcm9wLCBpbnNlcnQsIGlzU2VydmVyLCBsYXp5LCBtZXJnZVByb3BzLCBtdXRhdGlvblNvcnRGbnMsIG9uLCBvbkNsZWFudXAsIG9uTW91bnQsIHJlbmRlciwgc2VyaWFsaXplLCBzZXRBdHRyaWJ1dGUsIHNldHVwU3R5bGVTaGVldCwgc29ydEZucywgc3BsaXRQcm9wcywgc3ByZWFkLCBzdHJpbmdpZnksIHRlbXBsYXRlLCB1bnRyYWNrLCB1cGRhdGVOZXN0ZWREYXRhQnlQYXRoLCB1c2UsIHVzZUNvbnRleHQsIHVzZVRyYW5zaXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/chunk/V5T5VJKG.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/dev.js":
/*!************************************************************!*\
  !*** ./node_modules/@tanstack/query-devtools/build/dev.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TanstackQueryDevtools: () => (/* binding */ TanstackQueryDevtools),\n/* harmony export */   TanstackQueryDevtoolsPanel: () => (/* binding */ TanstackQueryDevtoolsPanel)\n/* harmony export */ });\n/* harmony import */ var _chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk/V5T5VJKG.js */ \"(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/chunk/V5T5VJKG.js\");\n\n\n// src/TanstackQueryDevtools.tsx\nvar TanstackQueryDevtools = class {\n  #client;\n  #onlineManager;\n  #queryFlavor;\n  #version;\n  #isMounted = false;\n  #styleNonce;\n  #shadowDOMTarget;\n  #buttonPosition;\n  #position;\n  #initialIsOpen;\n  #errorTypes;\n  #Component;\n  #dispose;\n  constructor(config) {\n    const {\n      client,\n      queryFlavor,\n      version,\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget\n    } = config;\n    this.#client = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(client);\n    this.#queryFlavor = queryFlavor;\n    this.#version = version;\n    this.#onlineManager = onlineManager;\n    this.#styleNonce = styleNonce;\n    this.#shadowDOMTarget = shadowDOMTarget;\n    this.#buttonPosition = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(buttonPosition);\n    this.#position = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(position);\n    this.#initialIsOpen = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(initialIsOpen);\n    this.#errorTypes = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(errorTypes);\n  }\n  setButtonPosition(position) {\n    this.#buttonPosition[1](position);\n  }\n  setPosition(position) {\n    this.#position[1](position);\n  }\n  setInitialIsOpen(isOpen) {\n    this.#initialIsOpen[1](isOpen);\n  }\n  setErrorTypes(errorTypes) {\n    this.#errorTypes[1](errorTypes);\n  }\n  setClient(client) {\n    this.#client[1](client);\n  }\n  mount(el) {\n    if (this.#isMounted) {\n      throw new Error(\"Devtools is already mounted\");\n    }\n    const dispose = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.render)(() => {\n      const _self$ = this;\n      const [btnPosition] = this.#buttonPosition;\n      const [pos] = this.#position;\n      const [isOpen] = this.#initialIsOpen;\n      const [errors] = this.#errorTypes;\n      const [queryClient] = this.#client;\n      let Devtools;\n      if (this.#Component) {\n        Devtools = this.#Component;\n      } else {\n        Devtools = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.lazy)(() => __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_js\").then(__webpack_require__.bind(__webpack_require__, /*! ./DevtoolsComponent/HH7B3BHX.js */ \"(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js\")));\n        this.#Component = Devtools;\n      }\n      (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.setupStyleSheet)(this.#styleNonce, this.#shadowDOMTarget);\n      return (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createComponent)(Devtools, (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.mergeProps)({\n        get queryFlavor() {\n          return _self$.#queryFlavor;\n        },\n        get version() {\n          return _self$.#version;\n        },\n        get onlineManager() {\n          return _self$.#onlineManager;\n        },\n        get shadowDOMTarget() {\n          return _self$.#shadowDOMTarget;\n        }\n      }, {\n        get client() {\n          return queryClient();\n        },\n        get buttonPosition() {\n          return btnPosition();\n        },\n        get position() {\n          return pos();\n        },\n        get initialIsOpen() {\n          return isOpen();\n        },\n        get errorTypes() {\n          return errors();\n        }\n      }));\n    }, el);\n    this.#isMounted = true;\n    this.#dispose = dispose;\n  }\n  unmount() {\n    if (!this.#isMounted) {\n      throw new Error(\"Devtools is not mounted\");\n    }\n    this.#dispose?.();\n    this.#isMounted = false;\n  }\n};\n\n// src/TanstackQueryDevtoolsPanel.tsx\nvar TanstackQueryDevtoolsPanel = class {\n  #client;\n  #onlineManager;\n  #queryFlavor;\n  #version;\n  #isMounted = false;\n  #styleNonce;\n  #shadowDOMTarget;\n  #buttonPosition;\n  #position;\n  #initialIsOpen;\n  #errorTypes;\n  #onClose;\n  #Component;\n  #dispose;\n  constructor(config) {\n    const {\n      client,\n      queryFlavor,\n      version,\n      onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose\n    } = config;\n    this.#client = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(client);\n    this.#queryFlavor = queryFlavor;\n    this.#version = version;\n    this.#onlineManager = onlineManager;\n    this.#styleNonce = styleNonce;\n    this.#shadowDOMTarget = shadowDOMTarget;\n    this.#buttonPosition = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(buttonPosition);\n    this.#position = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(position);\n    this.#initialIsOpen = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(initialIsOpen);\n    this.#errorTypes = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(errorTypes);\n    this.#onClose = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createSignal)(onClose);\n  }\n  setButtonPosition(position) {\n    this.#buttonPosition[1](position);\n  }\n  setPosition(position) {\n    this.#position[1](position);\n  }\n  setInitialIsOpen(isOpen) {\n    this.#initialIsOpen[1](isOpen);\n  }\n  setErrorTypes(errorTypes) {\n    this.#errorTypes[1](errorTypes);\n  }\n  setClient(client) {\n    this.#client[1](client);\n  }\n  setOnClose(onClose) {\n    this.#onClose[1](() => onClose);\n  }\n  mount(el) {\n    if (this.#isMounted) {\n      throw new Error(\"Devtools is already mounted\");\n    }\n    const dispose = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.render)(() => {\n      const _self$ = this;\n      const [btnPosition] = this.#buttonPosition;\n      const [pos] = this.#position;\n      const [isOpen] = this.#initialIsOpen;\n      const [errors] = this.#errorTypes;\n      const [queryClient] = this.#client;\n      const [onClose] = this.#onClose;\n      let Devtools;\n      if (this.#Component) {\n        Devtools = this.#Component;\n      } else {\n        Devtools = (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.lazy)(() => __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_js\").then(__webpack_require__.bind(__webpack_require__, /*! ./DevtoolsPanelComponent/JZI2RDCT.js */ \"(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js\")));\n        this.#Component = Devtools;\n      }\n      (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.setupStyleSheet)(this.#styleNonce, this.#shadowDOMTarget);\n      return (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.createComponent)(Devtools, (0,_chunk_V5T5VJKG_js__WEBPACK_IMPORTED_MODULE_0__.mergeProps)({\n        get queryFlavor() {\n          return _self$.#queryFlavor;\n        },\n        get version() {\n          return _self$.#version;\n        },\n        get onlineManager() {\n          return _self$.#onlineManager;\n        },\n        get shadowDOMTarget() {\n          return _self$.#shadowDOMTarget;\n        }\n      }, {\n        get client() {\n          return queryClient();\n        },\n        get buttonPosition() {\n          return btnPosition();\n        },\n        get position() {\n          return pos();\n        },\n        get initialIsOpen() {\n          return isOpen();\n        },\n        get errorTypes() {\n          return errors();\n        },\n        get onClose() {\n          return onClose();\n        }\n      }));\n    }, el);\n    this.#isMounted = true;\n    this.#dispose = dispose;\n  }\n  unmount() {\n    if (!this.#isMounted) {\n      throw new Error(\"Devtools is not mounted\");\n    }\n    this.#dispose?.();\n    this.#isMounted = false;\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/dev.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(pages-dir-browser)/./node_modules/@tanstack/react-query/build/modern/index.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\n\"use client\";\n\n// src/ReactQueryDevtools.tsx\n\n\n\n\nfunction ReactQueryDevtools(props) {\n  const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const {\n    buttonPosition,\n    position,\n    initialIsOpen,\n    errorTypes,\n    styleNonce,\n    shadowDOMTarget\n  } = props;\n  const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n    new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtools({\n      client: queryClient,\n      queryFlavor: \"React Query\",\n      version: \"5\",\n      onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.onlineManager,\n      buttonPosition,\n      position,\n      initialIsOpen,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget\n    })\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    devtools.setClient(queryClient);\n  }, [queryClient, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (buttonPosition) {\n      devtools.setButtonPosition(buttonPosition);\n    }\n  }, [buttonPosition, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (position) {\n      devtools.setPosition(position);\n    }\n  }, [position, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    devtools.setInitialIsOpen(initialIsOpen || false);\n  }, [initialIsOpen, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || []);\n  }, [errorTypes, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current);\n    }\n    return () => {\n      devtools.unmount();\n    };\n  }, [devtools]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", { dir: \"ltr\", className: \"tsqd-parent-container\", ref });\n}\n\n//# sourceMappingURL=ReactQueryDevtools.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(pages-dir-browser)/./node_modules/@tanstack/react-query/build/modern/index.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(pages-dir-browser)/./node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\n\"use client\";\n\n// src/ReactQueryDevtoolsPanel.tsx\n\n\n\n\nfunction ReactQueryDevtoolsPanel(props) {\n  const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const { errorTypes, styleNonce, shadowDOMTarget } = props;\n  const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n    new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtoolsPanel({\n      client: queryClient,\n      queryFlavor: \"React Query\",\n      version: \"5\",\n      onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.onlineManager,\n      buttonPosition: \"bottom-left\",\n      position: \"bottom\",\n      initialIsOpen: true,\n      errorTypes,\n      styleNonce,\n      shadowDOMTarget,\n      onClose: props.onClose\n    })\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    devtools.setClient(queryClient);\n  }, [queryClient, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    devtools.setOnClose(props.onClose ?? (() => {\n    }));\n  }, [props.onClose, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    devtools.setErrorTypes(errorTypes || []);\n  }, [errorTypes, devtools]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (ref.current) {\n      devtools.mount(ref.current);\n    }\n    return () => {\n      devtools.unmount();\n    };\n  }, [devtools]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    \"div\",\n    {\n      style: { height: \"500px\", ...props.style },\n      className: \"tsqd-parent-container\",\n      ref\n    }\n  );\n}\n\n//# sourceMappingURL=ReactQueryDevtoolsPanel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query-devtools/build/modern/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools2),\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel2)\n/* harmony export */ });\n/* harmony import */ var _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ReactQueryDevtools.js */ \"(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\");\n/* harmony import */ var _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReactQueryDevtoolsPanel.js */ \"(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\");\n\"use client\";\n\n// src/index.ts\n\n\nvar ReactQueryDevtools2 =  false ? 0 : _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 =  false ? 0 : _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__.ReactQueryDevtoolsPanel;\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMvYnVpbGQvbW9kZXJuL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7QUFFQTtBQUNvRDtBQUNVO0FBQzlELDBCQUEwQixNQUFzQyxHQUFHLENBRWxFLENBQUMsRUFBRSxzRUFBMkI7QUFDL0IsK0JBQStCLE1BQXNDLEdBQUcsQ0FFdkUsQ0FBQyxFQUFFLGdGQUFxQztBQUl2QztBQUNGIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcbm9kZV9tb2R1bGVzXFxAdGFuc3RhY2tcXHJlYWN0LXF1ZXJ5LWRldnRvb2xzXFxidWlsZFxcbW9kZXJuXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL2luZGV4LnRzXG5pbXBvcnQgKiBhcyBEZXZ0b29scyBmcm9tIFwiLi9SZWFjdFF1ZXJ5RGV2dG9vbHMuanNcIjtcbmltcG9ydCAqIGFzIERldnRvb2xzUGFuZWwgZnJvbSBcIi4vUmVhY3RRdWVyeURldnRvb2xzUGFuZWwuanNcIjtcbnZhciBSZWFjdFF1ZXJ5RGV2dG9vbHMyID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwiZGV2ZWxvcG1lbnRcIiA/IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gbnVsbDtcbn0gOiBEZXZ0b29scy5SZWFjdFF1ZXJ5RGV2dG9vbHM7XG52YXIgUmVhY3RRdWVyeURldnRvb2xzUGFuZWwyID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwiZGV2ZWxvcG1lbnRcIiA/IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gbnVsbDtcbn0gOiBEZXZ0b29sc1BhbmVsLlJlYWN0UXVlcnlEZXZ0b29sc1BhbmVsO1xuZXhwb3J0IHtcbiAgUmVhY3RRdWVyeURldnRvb2xzMiBhcyBSZWFjdFF1ZXJ5RGV2dG9vbHMsXG4gIFJlYWN0UXVlcnlEZXZ0b29sc1BhbmVsMiBhcyBSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\n"));

/***/ })

}]);