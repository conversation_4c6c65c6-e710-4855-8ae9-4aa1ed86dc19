import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Card,
  CardMedia,
  Typography,
  IconButton,
  Chip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Avatar,
  Badge,
  Dialog,
  DialogContent,
  Button,
  Paper,
  Fab,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Close,
  Star,
  LocationOn,
  FilterList,
  Refresh,
  Verified,
  Chat,
  Info
} from '@mui/icons-material';
import { FaHeart, FaMapMarkerAlt, FaFilter, FaShoppingCart } from 'react-icons/fa';
import Layout from '../components/Layout/Layout';
import { useAuth } from '../src/providers/AuthProvider';



interface User {
  id: string;
  name: string;
  age: number;
  avatar: string;
  photos: string[];
  bio: string;
  location: string;
  distance: number;
  interests: string[];
  verified: boolean;
  online: boolean;
  job?: string;
  education?: string;
  shoppingCenter?: string;
}

interface ShoppingCenter {
  id: string;
  name: string;
  address: string;
  userCount: number;
}

const DiscoverPage: React.FC = () => {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [likedUsers, setLikedUsers] = useState<Set<string>>(new Set());
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const [showProfile, setShowProfile] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showShoppingCenters, setShowShoppingCenters] = useState(false);

  // Проверка авторизации
  React.useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return (
      <>
        <Head>
          <title>Загрузка - Likes Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Layout>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
            <Typography>Загрузка...</Typography>
          </Box>
        </Layout>
      </>
    );
  }

  if (!user) {
    return null;
  }

  // Фильтры
  const [ageRange, setAgeRange] = useState<number[]>([18, 35]);
  const [maxDistance, setMaxDistance] = useState(50);
  const [selectedShoppingCenter, setSelectedShoppingCenter] = useState('all');

  const shoppingCenters: ShoppingCenter[] = [
    { id: 'all', name: 'Все торговые центры', address: '', userCount: 156 },
    { id: 'afimall', name: 'Афимолл Сити', address: 'Пресненская наб., 2', userCount: 23 },
    { id: 'evropeisky', name: 'Европейский', address: 'пл. Киевского Вокзала, 2', userCount: 18 },
    { id: 'vegas', name: 'Vegas Крокус Сити', address: 'МКАД, 65-66 км', userCount: 31 },
    { id: 'avia-park', name: 'Авиапарк', address: 'Ходынский бул., 4', userCount: 27 },
    { id: 'metropolis', name: 'Метрополис', address: 'Ленинградское ш., 16А', userCount: 19 },
    { id: 'columbus', name: 'Колумбус', address: 'Кировоградская ул., 13А', userCount: 15 }
  ];

  // Тестовые пользователи
  const testUsers: User[] = [
    {
      id: '1',
      name: 'Анна Петрова',
      age: 25,
      avatar: 'https://picsum.photos/200/200?random=1',
      photos: [
        'https://picsum.photos/400/600?random=11',
        'https://picsum.photos/400/600?random=12',
        'https://picsum.photos/400/600?random=13'
      ],
      bio: 'Люблю путешествия, фотографию и хорошую компанию. Ищу серьезные отношения.',
      location: 'Москва, Россия',
      distance: 2.5,
      interests: ['Путешествия', 'Фотография', 'Кино', 'Спорт'],
      verified: true,
      online: true,
      job: 'Дизайнер',
      education: 'МГУ',
      shoppingCenter: 'afimall'
    },
    {
      id: '2',
      name: 'Мария Сидорова',
      age: 28,
      avatar: 'https://picsum.photos/200/200?random=2',
      photos: [
        'https://picsum.photos/400/600?random=21',
        'https://picsum.photos/400/600?random=22'
      ],
      bio: 'Дизайнер, йога-инструктор. Ценю искренность и чувство юмора.',
      location: 'Санкт-Петербург, Россия',
      distance: 5.2,
      interests: ['Йога', 'Дизайн', 'Медитация', 'Искусство'],
      verified: true,
      online: false,
      job: 'Йога-инструктор',
      shoppingCenter: 'evropeisky'
    },
    {
      id: '3',
      name: 'Елена Козлова',
      age: 23,
      avatar: 'https://picsum.photos/200/200?random=3',
      photos: [
        'https://picsum.photos/400/600?random=31',
        'https://picsum.photos/400/600?random=32'
      ],
      bio: 'Студентка, изучаю психологию. Люблю книги, музыку и долгие прогулки.',
      location: 'Екатеринбург, Россия',
      distance: 1.8,
      interests: ['Психология', 'Книги', 'Музыка', 'Прогулки'],
      verified: false,
      online: true,
      education: 'УрФУ',
      shoppingCenter: 'vegas'
    }
  ];

  useEffect(() => {
    // Фильтрация пользователей
    const filteredUsers = testUsers.filter(user => {
      const ageMatch = user.age >= ageRange[0] && user.age <= ageRange[1];
      const distanceMatch = user.distance <= maxDistance;
      const shoppingCenterMatch = selectedShoppingCenter === 'all' || user.shoppingCenter === selectedShoppingCenter;

      return ageMatch && distanceMatch && shoppingCenterMatch;
    });

    setUsers(filteredUsers);
    setCurrentUserIndex(0);
  }, [ageRange, maxDistance, selectedShoppingCenter]);

  const currentUser = users[currentUserIndex];

  const handleLike = (userId: string) => {
    setLikedUsers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const handleSwipe = (direction: 'like' | 'pass') => {
    if (!currentUser) return;

    if (direction === 'like') {
      handleLike(currentUser.id);
    }

    // Переход к следующему пользователю
    setTimeout(() => {
      setCurrentUserIndex(prev => prev + 1);
    }, 300);
  };

  const handleShowProfile = (user: User) => {
    setSelectedUser(user);
    setShowProfile(true);
  };

  return (
    <>
      <Head>
        <title>Знакомства - Likes Love</title>
        <meta name="description" content="Найдите свою вторую половинку среди тысяч пользователей" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <Layout>
        <Container maxWidth="sm" sx={{ py: 2, minHeight: '100vh' }}>
        {/* Заголовок с фильтрами */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" sx={{
            background: 'linear-gradient(45deg, #ff6b9d, #c44569)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 'bold'
          }}>
            Знакомства
          </Typography>
          <Box display="flex" gap={1}>
            <IconButton
              onClick={() => setShowShoppingCenters(true)}
              color="primary"
            >
              <FaShoppingCart />
            </IconButton>
            <IconButton
              onClick={() => setShowFilters(true)}
              color="primary"
            >
              <FilterList />
            </IconButton>
          </Box>
        </Box>

        {/* Статистика */}
        <Box sx={{ mb: 3, display: 'flex', gap: 1, justifyContent: 'center' }}>
          <Chip
            label={`${users.length} анкет`}
            color="primary"
            icon={<FaHeart />}
          />
          <Chip
            label={`${likedUsers.size} лайков`}
            color="secondary"
            icon={<Star />}
          />
          <Chip
            label={`До ${maxDistance} км`}
            color="info"
            icon={<LocationOn />}
          />
        </Box>

        {/* Карточка пользователя */}
        {currentUser ? (
          <Card
            sx={{
              height: '70vh',
              position: 'relative',
              overflow: 'hidden',
              cursor: 'pointer',
              mb: 3,
              borderRadius: 3,
              boxShadow: '0 8px 32px rgba(255, 107, 157, 0.2)',
              background: 'linear-gradient(135deg, rgba(255, 107, 157, 0.05) 0%, rgba(196, 69, 105, 0.05) 100%)',
              border: '1px solid rgba(255, 107, 157, 0.2)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: '0 12px 40px rgba(255, 107, 157, 0.3)'
              }
            }}
            onClick={() => handleShowProfile(currentUser)}
          >
            <CardMedia
              component="img"
              height="100%"
              image={currentUser.photos[0]}
              alt={currentUser.name}
              sx={{ objectFit: 'cover' }}
            />

            {/* Градиент снизу */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: '50%',
                background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-end',
                p: 3
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Badge
                  overlap="circular"
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  variant="dot"
                  color={currentUser.online ? 'success' : 'default'}
                >
                  <Avatar
                    src={currentUser.avatar}
                    sx={{ width: 40, height: 40, mr: 2 }}
                  />
                </Badge>

                <Box sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {currentUser.name}, {currentUser.age}
                    </Typography>
                    {currentUser.verified && (
                      <Verified sx={{ color: 'primary.main', fontSize: '1.5rem' }} />
                    )}
                  </Box>
                </Box>
              </Box>

              <Box display="flex" alignItems="center" mb={1}>
                <LocationOn sx={{ color: 'white', mr: 1, fontSize: 20 }} />
                <Typography variant="body1" color="white">
                  {currentUser.distance} км • {currentUser.location}
                </Typography>
              </Box>

              {currentUser.job && (
                <Typography variant="body2" color="white" sx={{ opacity: 0.9, mb: 1 }}>
                  {currentUser.job}
                </Typography>
              )}

              <Typography variant="body2" color="white" sx={{ opacity: 0.9, mb: 2 }}>
                {currentUser.bio.length > 100
                  ? `${currentUser.bio.substring(0, 100)}...`
                  : currentUser.bio
                }
              </Typography>

              {/* Интересы */}
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {currentUser.interests.slice(0, 3).map((interest, index) => (
                  <Chip
                    key={index}
                    label={interest}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      border: '1px solid rgba(255,255,255,0.3)'
                    }}
                  />
                ))}
                {currentUser.interests.length > 3 && (
                  <Chip
                    label={`+${currentUser.interests.length - 3}`}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      border: '1px solid rgba(255,255,255,0.3)'
                    }}
                  />
                )}
              </Box>
            </Box>
          </Card>
        ) : (
          <Paper sx={{ p: 4, textAlign: 'center', mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Анкеты закончились
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Попробуйте изменить фильтры поиска
            </Typography>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={() => {
                setCurrentUserIndex(0);
                setUsers(testUsers);
              }}
            >
              Начать сначала
            </Button>
          </Paper>
        )}

        {/* Кнопки действий */}
        {currentUser && (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            gap={3}
            sx={{ mb: 3 }}
          >
            <IconButton
              onClick={() => handleSwipe('pass')}
              sx={{
                backgroundColor: 'error.main',
                color: 'white',
                width: 56,
                height: 56,
                '&:hover': { backgroundColor: 'error.dark' }
              }}
            >
              <Close />
            </IconButton>

            <IconButton
              onClick={() => handleSwipe('like')}
              sx={{
                backgroundColor: likedUsers.has(currentUser.id) ? 'success.dark' : 'success.main',
                color: 'white',
                width: 64,
                height: 64,
                '&:hover': { backgroundColor: 'success.dark' }
              }}
            >
              {likedUsers.has(currentUser.id) ? <Favorite /> : <FavoriteBorder />}
            </IconButton>

            <IconButton
              onClick={() => handleShowProfile(currentUser)}
              sx={{
                backgroundColor: 'info.main',
                color: 'white',
                width: 48,
                height: 48,
                '&:hover': { backgroundColor: 'info.dark' }
              }}
            >
              <Info />
            </IconButton>
          </Box>
        )}

        {/* Диалог профиля */}
        <Dialog
          open={showProfile}
          onClose={() => setShowProfile(false)}
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: { borderRadius: 3, maxHeight: '90vh' }
          }}
        >
          <DialogContent sx={{ p: 0 }}>
            {selectedUser && (
              <Box>
                {/* Фотографии */}
                <Box sx={{ height: 400, position: 'relative' }}>
                  <CardMedia
                    component="img"
                    height="100%"
                    image={selectedUser.photos[0]}
                    alt={selectedUser.name}
                    sx={{ objectFit: 'cover' }}
                  />
                  <IconButton
                    onClick={() => setShowProfile(false)}
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      color: 'white'
                    }}
                  >
                    <Close />
                  </IconButton>
                </Box>

                {/* Информация */}
                <Box sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Badge
                      overlap="circular"
                      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      variant="dot"
                      color={selectedUser.online ? 'success' : 'default'}
                    >
                      <Avatar
                        src={selectedUser.avatar}
                        sx={{ width: 60, height: 60, mr: 2 }}
                      />
                    </Badge>

                    <Box sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h4" fontWeight="bold">
                          {selectedUser.name}, {selectedUser.age}
                        </Typography>
                        {selectedUser.verified && (
                          <Verified color="primary" sx={{ fontSize: '1.5rem' }} />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        <FaMapMarkerAlt style={{ marginRight: 4 }} />
                        {selectedUser.distance} км • {selectedUser.location}
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="body1" sx={{ mb: 3 }}>
                    {selectedUser.bio}
                  </Typography>

                  {/* Детали */}
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    {selectedUser.job && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Работа
                        </Typography>
                        <Typography variant="body1">
                          {selectedUser.job}
                        </Typography>
                      </Grid>
                    )}

                    {selectedUser.education && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Образование
                        </Typography>
                        <Typography variant="body1">
                          {selectedUser.education}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>

                  {/* Интересы */}
                  <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                    Интересы
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 3 }}>
                    {selectedUser.interests.map((interest, index) => (
                      <Chip
                        key={index}
                        label={interest}
                        variant="outlined"
                        color="primary"
                        size="small"
                      />
                    ))}
                  </Box>

                  {/* Действия */}
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                      variant={likedUsers.has(selectedUser.id) ? 'contained' : 'outlined'}
                      fullWidth
                      startIcon={likedUsers.has(selectedUser.id) ? <Favorite /> : <FavoriteBorder />}
                      onClick={() => handleLike(selectedUser.id)}
                      sx={{
                        background: likedUsers.has(selectedUser.id)
                          ? 'linear-gradient(45deg, #ff6b9d, #c44569)'
                          : 'transparent',
                        color: likedUsers.has(selectedUser.id) ? 'white' : 'primary.main',
                        border: likedUsers.has(selectedUser.id) ? 'none' : '1px solid',
                        borderColor: 'primary.main',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #ff6b9d, #c44569)',
                          color: 'white'
                        }
                      }}
                    >
                      {likedUsers.has(selectedUser.id) ? 'Нравится' : 'Лайк'}
                    </Button>

                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<Chat />}
                      href={`/chats/${selectedUser.id}`}
                    >
                      Написать
                    </Button>
                  </Box>
                </Box>
              </Box>
            )}
          </DialogContent>
        </Dialog>

        {/* Диалог фильтров */}
        <Drawer
          anchor="bottom"
          open={showFilters}
          onClose={() => setShowFilters(false)}
          PaperProps={{
            sx: { borderTopLeftRadius: 16, borderTopRightRadius: 16 }
          }}
        >
          <Box sx={{ p: 3, minHeight: 300 }}>
            <Typography variant="h6" gutterBottom>
              Фильтры поиска
            </Typography>

            {/* Возраст */}
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Возраст: {ageRange[0]} - {ageRange[1]} лет
            </Typography>
            <Slider
              value={ageRange}
              onChange={(_, newValue) => setAgeRange(newValue as number[])}
              valueLabelDisplay="auto"
              min={18}
              max={60}
              sx={{ mb: 3 }}
            />

            {/* Расстояние */}
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Максимальное расстояние: {maxDistance} км
            </Typography>
            <Slider
              value={maxDistance}
              onChange={(_, newValue) => setMaxDistance(newValue as number)}
              valueLabelDisplay="auto"
              min={1}
              max={100}
              sx={{ mb: 3 }}
            />

            {/* Торговый центр */}
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Торговый центр</InputLabel>
              <Select
                value={selectedShoppingCenter}
                onChange={(e) => setSelectedShoppingCenter(e.target.value)}
                label="Торговый центр"
              >
                {shoppingCenters.map((center) => (
                  <MenuItem key={center.id} value={center.id}>
                    {center.name} {center.id !== 'all' && `(${center.userCount})`}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Button
              variant="contained"
              fullWidth
              onClick={() => setShowFilters(false)}
            >
              Применить фильтры
            </Button>
          </Box>
        </Drawer>

        {/* Диалог торговых центров */}
        <Drawer
          anchor="bottom"
          open={showShoppingCenters}
          onClose={() => setShowShoppingCenters(false)}
          PaperProps={{
            sx: { borderTopLeftRadius: 16, borderTopRightRadius: 16 }
          }}
        >
          <Box sx={{ p: 3, maxHeight: '70vh', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Поиск по торговым центрам
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Найдите людей, которые часто посещают те же места
            </Typography>

            <List>
              {shoppingCenters.map((center, index) => (
                <React.Fragment key={center.id}>
                  <ListItem
                    button
                    onClick={() => {
                      setSelectedShoppingCenter(center.id);
                      setShowShoppingCenters(false);
                    }}
                    selected={selectedShoppingCenter === center.id}
                  >
                    <Box sx={{
                      minWidth: 40,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: selectedShoppingCenter === center.id ? '#ff6b9d' : 'inherit',
                      fontSize: '1.2rem'
                    }}>
                      🛍️
                    </Box>
                    <ListItemText
                      primary={center.name}
                      secondary={center.address || `${center.userCount} пользователей`}
                    />
                    {center.id !== 'all' && (
                      <Chip
                        label={center.userCount}
                        size="small"
                        color="primary"
                      />
                    )}
                  </ListItem>
                  {index < shoppingCenters.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Box>
        </Drawer>

          {/* Floating Action Button */}
          <Fab
            color="primary"
            sx={{
              position: 'fixed',
              bottom: 24,
              right: 24
            }}
            onClick={() => window.location.href = '/matches'}
          >
            <FaHeart />
          </Fab>
        </Container>
      </Layout>
    </>
  );
};

export default DiscoverPage;
