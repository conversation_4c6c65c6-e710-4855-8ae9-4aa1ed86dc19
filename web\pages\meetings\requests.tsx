import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Badge,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Check as AcceptIcon,
  Close as DeclineIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Message as MessageIcon,
  Refresh as RefreshIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getMeetingRequests,
  getMeetingInvitations,
  respondToMeetingRequest,
  respondToInvitation
} from '../../src/services/meetingsService';
import { 
  MeetingRequest,
  MeetingInvitation 
} from '../../src/types/meetings.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`requests-tabpanel-${index}`}
      aria-labelledby={`requests-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const MeetingRequestsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [requests, setRequests] = useState<MeetingRequest[]>([]);
  const [invitations, setInvitations] = useState<MeetingInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Dialog states
  const [responseDialog, setResponseDialog] = useState<{
    open: boolean;
    type: 'request' | 'invitation';
    item: MeetingRequest | MeetingInvitation | null;
    action: 'approve' | 'decline' | 'accept' | 'decline';
  }>({
    open: false,
    type: 'request',
    item: null,
    action: 'approve'
  });
  const [responseMessage, setResponseMessage] = useState('');

  const tabs = [
    { label: 'Запросы на участие', key: 'requests' },
    { label: 'Приглашения', key: 'invitations' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadData();
  }, [user, router, activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (activeTab === 0) {
        const requestsData = await getMeetingRequests('received');
        setRequests(requestsData.data);
      } else {
        const invitationsData = await getMeetingInvitations('received');
        setInvitations(invitationsData.data);
      }
    } catch (err: any) {
      setError('Ошибка загрузки данных');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenResponseDialog = (
    type: 'request' | 'invitation',
    item: MeetingRequest | MeetingInvitation,
    action: 'approve' | 'decline' | 'accept' | 'decline'
  ) => {
    setResponseDialog({ open: true, type, item, action });
    setResponseMessage('');
  };

  const handleCloseResponseDialog = () => {
    setResponseDialog({ open: false, type: 'request', item: null, action: 'approve' });
    setResponseMessage('');
  };

  const handleRespondToRequest = async () => {
    const { item, action } = responseDialog;
    if (!item || responseDialog.type !== 'request') return;
    
    try {
      setActionLoading(item.id);
      setError(null);

      await respondToMeetingRequest(
        item.id,
        action as 'approved' | 'declined',
        responseMessage.trim() || undefined
      );
      
      setSuccess(`Запрос ${action === 'approve' ? 'одобрен' : 'отклонен'}`);
      handleCloseResponseDialog();
      loadData(); // Reload data
    } catch (err: any) {
      setError('Ошибка обработки запроса');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRespondToInvitation = async () => {
    const { item, action } = responseDialog;
    if (!item || responseDialog.type !== 'invitation') return;
    
    try {
      setActionLoading(item.id);
      setError(null);

      await respondToInvitation(
        item.id,
        action as 'accepted' | 'declined',
        responseMessage.trim() || undefined
      );
      
      setSuccess(`Приглашение ${action === 'accept' ? 'принято' : 'отклонено'}`);
      handleCloseResponseDialog();
      loadData(); // Reload data
    } catch (err: any) {
      setError('Ошибка обработки приглашения');
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
      case 'accepted':
        return 'success';
      case 'declined':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Ожидает ответа';
      case 'approved':
        return 'Одобрено';
      case 'accepted':
        return 'Принято';
      case 'declined':
        return 'Отклонено';
      default:
        return status;
    }
  };

  const pendingRequestsCount = requests.filter(r => r.status === 'pending').length;
  const pendingInvitationsCount = invitations.filter(i => i.status === 'pending').length;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Запросы и приглашения - Likes & Love</title>
        <meta 
          name="description" 
          content="Управляйте запросами на участие и приглашениями на встречи" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <NotificationsIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Запросы и приглашения
              </Typography>
              <IconButton onClick={loadData} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Paper elevation={3} sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
              {/* Tabs */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={activeTab}
                  onChange={(_, newValue) => setActiveTab(newValue)}
                  variant={isMobile ? "scrollable" : "fullWidth"}
                  scrollButtons="auto"
                >
                  <Tab 
                    label={
                      <Badge badgeContent={pendingRequestsCount} color="error">
                        {tabs[0].label}
                      </Badge>
                    } 
                  />
                  <Tab 
                    label={
                      <Badge badgeContent={pendingInvitationsCount} color="error">
                        {tabs[1].label}
                      </Badge>
                    } 
                  />
                </Tabs>
              </Box>

              {/* Content */}
              <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
                <TabPanel value={activeTab} index={0}>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress size={60} />
                    </Box>
                  ) : requests.length === 0 ? (
                    <Box sx={{ textAlign: 'center', py: 8 }}>
                      <NotificationsIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Нет запросов на участие
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Здесь будут отображаться запросы на участие в ваших встречах
                      </Typography>
                    </Box>
                  ) : (
                    <Fade in timeout={600}>
                      <List sx={{ height: '100%', overflow: 'auto', py: 0 }}>
                        {requests.map((request) => (
                          <ListItem
                            key={request.id}
                            sx={{
                              borderBottom: 1,
                              borderColor: 'divider',
                              py: 2
                            }}
                          >
                            <ListItemAvatar>
                              <Avatar src={request.requester.avatarUrl}>
                                {request.requester.firstName[0]}
                              </Avatar>
                            </ListItemAvatar>

                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                  <Typography variant="subtitle1">
                                    {request.requester.firstName} {request.requester.lastName}
                                  </Typography>
                                  <Chip
                                    label={getStatusLabel(request.status)}
                                    size="small"
                                    color={getStatusColor(request.status) as any}
                                    variant="outlined"
                                  />
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="body2" gutterBottom>
                                    Хочет присоединиться к: <strong>{request.meeting.title}</strong>
                                  </Typography>
                                  
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption" color="text.secondary">
                                        {formatDate(request.meeting.scheduledAt)}
                                      </Typography>
                                    </Box>
                                    
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <LocationIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption" color="text.secondary">
                                        {request.meeting.location.name}
                                      </Typography>
                                    </Box>
                                  </Box>

                                  {request.message && (
                                    <Typography variant="body2" sx={{ 
                                      fontStyle: 'italic', 
                                      backgroundColor: 'grey.100', 
                                      p: 1, 
                                      borderRadius: 1,
                                      mb: 1
                                    }}>
                                      "{request.message}"
                                    </Typography>
                                  )}

                                  <Typography variant="caption" color="text.secondary">
                                    Запрос отправлен: {formatDate(request.createdAt)}
                                  </Typography>
                                </Box>
                              }
                            />

                            {request.status === 'pending' && (
                              <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
                                <IconButton
                                  color="success"
                                  onClick={() => handleOpenResponseDialog('request', request, 'approve')}
                                  disabled={actionLoading === request.id}
                                >
                                  <AcceptIcon />
                                </IconButton>
                                <IconButton
                                  color="error"
                                  onClick={() => handleOpenResponseDialog('request', request, 'decline')}
                                  disabled={actionLoading === request.id}
                                >
                                  <DeclineIcon />
                                </IconButton>
                                <IconButton
                                  onClick={() => router.push(`/chat/new?userId=${request.requesterId}`)}
                                >
                                  <MessageIcon />
                                </IconButton>
                              </Box>
                            )}
                          </ListItem>
                        ))}
                      </List>
                    </Fade>
                  )}
                </TabPanel>

                <TabPanel value={activeTab} index={1}>
                  {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress size={60} />
                    </Box>
                  ) : invitations.length === 0 ? (
                    <Box sx={{ textAlign: 'center', py: 8 }}>
                      <NotificationsIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Нет приглашений
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Здесь будут отображаться приглашения на встречи
                      </Typography>
                    </Box>
                  ) : (
                    <Fade in timeout={600}>
                      <List sx={{ height: '100%', overflow: 'auto', py: 0 }}>
                        {invitations.map((invitation) => (
                          <ListItem
                            key={invitation.id}
                            sx={{
                              borderBottom: 1,
                              borderColor: 'divider',
                              py: 2
                            }}
                          >
                            <ListItemAvatar>
                              <Avatar src={invitation.inviter.avatarUrl}>
                                {invitation.inviter.firstName[0]}
                              </Avatar>
                            </ListItemAvatar>

                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                  <Typography variant="subtitle1">
                                    {invitation.inviter.firstName} {invitation.inviter.lastName}
                                  </Typography>
                                  <Chip
                                    label={getStatusLabel(invitation.status)}
                                    size="small"
                                    color={getStatusColor(invitation.status) as any}
                                    variant="outlined"
                                  />
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="body2" gutterBottom>
                                    Приглашает на: <strong>{invitation.meeting.title}</strong>
                                  </Typography>
                                  
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption" color="text.secondary">
                                        {formatDate(invitation.meeting.scheduledAt)}
                                      </Typography>
                                    </Box>
                                    
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <LocationIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption" color="text.secondary">
                                        {invitation.meeting.location.name}
                                      </Typography>
                                    </Box>

                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                      <PeopleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                                      <Typography variant="caption" color="text.secondary">
                                        {invitation.meeting.participants.length} участников
                                      </Typography>
                                    </Box>
                                  </Box>

                                  {invitation.message && (
                                    <Typography variant="body2" sx={{ 
                                      fontStyle: 'italic', 
                                      backgroundColor: 'grey.100', 
                                      p: 1, 
                                      borderRadius: 1,
                                      mb: 1
                                    }}>
                                      "{invitation.message}"
                                    </Typography>
                                  )}

                                  <Typography variant="caption" color="text.secondary">
                                    Приглашение отправлено: {formatDate(invitation.createdAt)}
                                  </Typography>
                                </Box>
                              }
                            />

                            {invitation.status === 'pending' && (
                              <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
                                <IconButton
                                  color="success"
                                  onClick={() => handleOpenResponseDialog('invitation', invitation, 'accept')}
                                  disabled={actionLoading === invitation.id}
                                >
                                  <AcceptIcon />
                                </IconButton>
                                <IconButton
                                  color="error"
                                  onClick={() => handleOpenResponseDialog('invitation', invitation, 'decline')}
                                  disabled={actionLoading === invitation.id}
                                >
                                  <DeclineIcon />
                                </IconButton>
                                <IconButton
                                  onClick={() => router.push(`/meetings/${invitation.meetingId}`)}
                                >
                                  <MessageIcon />
                                </IconButton>
                              </Box>
                            )}
                          </ListItem>
                        ))}
                      </List>
                    </Fade>
                  )}
                </TabPanel>
              </Box>
            </Paper>
          </Box>
        </Container>

        {/* Response Dialog */}
        <Dialog 
          open={responseDialog.open} 
          onClose={handleCloseResponseDialog}
          maxWidth="sm" 
          fullWidth
        >
          <DialogTitle>
            {responseDialog.action === 'approve' || responseDialog.action === 'accept' 
              ? (responseDialog.type === 'request' ? 'Одобрить запрос' : 'Принять приглашение')
              : (responseDialog.type === 'request' ? 'Отклонить запрос' : 'Отклонить приглашение')
            }
          </DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Сообщение (необязательно)"
              value={responseMessage}
              onChange={(e) => setResponseMessage(e.target.value)}
              placeholder={
                responseDialog.action === 'approve' || responseDialog.action === 'accept'
                  ? "Добавьте приветственное сообщение..."
                  : "Объясните причину отказа..."
              }
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseResponseDialog}>
              Отмена
            </Button>
            <Button 
              variant="contained"
              color={responseDialog.action === 'approve' || responseDialog.action === 'accept' ? 'success' : 'error'}
              onClick={responseDialog.type === 'request' ? handleRespondToRequest : handleRespondToInvitation}
              disabled={!!actionLoading}
            >
              {actionLoading ? 'Обработка...' : 
               (responseDialog.action === 'approve' || responseDialog.action === 'accept' 
                ? (responseDialog.type === 'request' ? 'Одобрить' : 'Принять')
                : 'Отклонить'
               )
              }
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default MeetingRequestsPage;
