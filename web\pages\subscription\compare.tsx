import React, { useState } from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Switch,
  FormControlLabel,
  Alert,
  Paper,
} from '@mui/material';
import {
  Check as CheckIcon,
  Close as CloseIcon,
  Star as StarIcon,
  Diamond as DiamondIcon,
  ShoppingBag as ShoppingIcon,
  Search as SearchIcon,
  Notifications as NotificationsIcon,
  LocalOffer as DiscountIcon,
  People as PeopleIcon,
  Favorite as FavoriteIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../src/providers/AuthProvider';
import { useSubscription } from '../../src/providers/SubscriptionProvider';
import Layout from '../../components/Layout/Layout';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  period: 'month' | 'year';
  popular?: boolean;
  features: {
    category: string;
    items: {
      name: string;
      basic: boolean | string;
      premium: boolean | string;
      vip: boolean | string;
      icon?: React.ReactNode;
    }[];
  }[];
}

const SubscriptionComparePage: React.FC = () => {
  const { t } = useTranslation('common');
  const { user } = useAuth();
  const { subscription } = useSubscription();
  const router = useRouter();
  const [isYearly, setIsYearly] = useState(false);

  const plans: SubscriptionPlan[] = [
    {
      id: 'basic',
      name: 'Базовый',
      price: 0,
      period: 'month',
      features: []
    },
    {
      id: 'premium',
      name: 'Premium',
      price: isYearly ? 2390 : 299,
      period: isYearly ? 'year' : 'month',
      popular: true,
      features: []
    },
    {
      id: 'vip',
      name: 'VIP',
      price: isYearly ? 4790 : 599,
      period: isYearly ? 'year' : 'month',
      features: []
    }
  ];

  const features = [
    {
      category: 'Торговые центры',
      items: [
        {
          name: 'Любимые ТЦ',
          basic: '1 ТЦ',
          premium: '5 ТЦ',
          vip: '10 ТЦ',
          icon: <ShoppingIcon />
        },
        {
          name: 'Поиск людей по ТЦ',
          basic: false,
          premium: true,
          vip: true,
          icon: <SearchIcon />
        },
        {
          name: 'Уведомления о событиях',
          basic: 'Базовые',
          premium: 'Расширенные',
          vip: 'VIP уведомления',
          icon: <NotificationsIcon />
        },
        {
          name: 'Скидки в ТЦ',
          basic: false,
          premium: 'До 10%',
          vip: 'До 20%',
          icon: <DiscountIcon />
        }
      ]
    },
    {
      category: 'Поиск и знакомства',
      items: [
        {
          name: 'Лайки в день',
          basic: '10',
          premium: '50',
          vip: 'Безлимит',
          icon: <FavoriteIcon />
        },
        {
          name: 'Суперлайки в день',
          basic: '1',
          premium: '5',
          vip: '10',
          icon: <StarIcon />
        },
        {
          name: 'Просмотр кто лайкнул',
          basic: false,
          premium: true,
          vip: true,
          icon: <PeopleIcon />
        },
        {
          name: 'Приоритет в поиске',
          basic: false,
          premium: true,
          vip: 'VIP приоритет',
          icon: <SearchIcon />
        }
      ]
    },
    {
      category: 'Дополнительные возможности',
      items: [
        {
          name: 'Анонимный просмотр',
          basic: false,
          premium: true,
          vip: true,
          icon: <PeopleIcon />
        },
        {
          name: 'Отмена лайков',
          basic: false,
          premium: true,
          vip: true,
          icon: <FavoriteIcon />
        },
        {
          name: 'Расширенные фильтры',
          basic: false,
          premium: true,
          vip: true,
          icon: <SearchIcon />
        },
        {
          name: 'Техподдержка',
          basic: 'Стандартная',
          premium: 'Приоритетная',
          vip: 'VIP поддержка',
          icon: <NotificationsIcon />
        }
      ]
    }
  ];

  const handleSubscribe = (planId: string) => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    
    router.push(`/subscription/payment?plan=${planId}&period=${isYearly ? 'year' : 'month'}`);
  };

  const renderFeatureValue = (value: boolean | string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <CheckIcon color="success" />
      ) : (
        <CloseIcon color="disabled" />
      );
    }
    return (
      <Typography variant="body2" color="text.primary">
        {value}
      </Typography>
    );
  };

  const getPlanColor = (planId: string) => {
    switch (planId) {
      case 'premium':
        return 'primary';
      case 'vip':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'premium':
        return <StarIcon />;
      case 'vip':
        return <DiamondIcon />;
      default:
        return null;
    }
  };

  return (
    <>
      <Head>
        <title>Сравнение планов подписок - Likes Love</title>
        <meta 
          name="description" 
          content="Сравните возможности планов подписки Likes Love. Выберите подходящий план для эффективных знакомств." 
        />
        <meta name="robots" content="index, follow" />
      </Head>

      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" gutterBottom>
              Сравнение планов
            </Typography>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Выберите план, который подходит именно вам
            </Typography>
            
            {/* Yearly Toggle */}
            <Box sx={{ mt: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isYearly}
                    onChange={(e) => setIsYearly(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography>Годовая подписка</Typography>
                    <Chip label="Скидка 20%" color="success" size="small" />
                  </Box>
                }
              />
            </Box>
          </Box>

          {/* Current Subscription Alert */}
          {subscription && (
            <Alert severity="info" sx={{ mb: 4 }}>
              Ваш текущий план: <strong>{subscription.plan}</strong>
              {subscription.expiresAt && (
                <> до {new Date(subscription.expiresAt).toLocaleDateString('ru-RU')}</>
              )}
            </Alert>
          )}

          {/* Plans Cards */}
          <Grid container spacing={3} sx={{ mb: 6 }}>
            {plans.map((plan, index) => (
              <Grid item xs={12} md={4} key={plan.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      position: 'relative',
                      border: plan.popular ? 2 : 1,
                      borderColor: plan.popular ? 'primary.main' : 'divider',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4,
                      },
                      transition: 'all 0.2s ease-in-out',
                    }}
                  >
                    {plan.popular && (
                      <Chip
                        label="Популярный"
                        color="primary"
                        sx={{
                          position: 'absolute',
                          top: -12,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          zIndex: 1,
                        }}
                      />
                    )}

                    <CardHeader
                      title={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getPlanIcon(plan.id)}
                          {plan.name}
                        </Box>
                      }
                      subheader={
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="h4" color="primary">
                            {plan.price === 0 ? 'Бесплатно' : `${plan.price}₽`}
                          </Typography>
                          {plan.price > 0 && (
                            <Typography variant="body2" color="text.secondary">
                              /{plan.period === 'year' ? 'год' : 'месяц'}
                            </Typography>
                          )}
                          {isYearly && plan.price > 0 && (
                            <Typography variant="caption" color="success.main">
                              Экономия {Math.round(plan.price * 0.2)}₽
                            </Typography>
                          )}
                        </Box>
                      }
                      sx={{ textAlign: 'center', pb: 1 }}
                    />

                    <CardContent sx={{ pt: 0 }}>
                      <Button
                        variant={plan.popular ? 'contained' : 'outlined'}
                        color={getPlanColor(plan.id) as any}
                        fullWidth
                        size="large"
                        onClick={() => handleSubscribe(plan.id)}
                        disabled={subscription?.plan === plan.id}
                        sx={{ mb: 3 }}
                      >
                        {subscription?.plan === plan.id ? 'Текущий план' : 
                         plan.price === 0 ? 'Текущий план' : 'Выбрать план'}
                      </Button>

                      {/* Key Features */}
                      <Typography variant="subtitle2" gutterBottom>
                        Ключевые возможности:
                      </Typography>
                      <List dense>
                        <ListItem>
                          <ListItemIcon>
                            <ShoppingIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={`${plan.id === 'basic' ? '1' : plan.id === 'premium' ? '5' : '10'} любимых ТЦ`}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <FavoriteIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={`${plan.id === 'basic' ? '10' : plan.id === 'premium' ? '50' : 'Безлимит'} лайков/день`}
                          />
                        </ListItem>
                        {plan.id !== 'basic' && (
                          <ListItem>
                            <ListItemIcon>
                              <SearchIcon fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary="Поиск по ТЦ" />
                          </ListItem>
                        )}
                      </List>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Detailed Comparison Table */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Подробное сравнение возможностей
            </Typography>
            
            {features.map((category, categoryIndex) => (
              <Box key={category.category} sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom color="primary">
                  {category.category}
                </Typography>
                
                {category.items.map((feature, featureIndex) => (
                  <Box key={feature.name}>
                    <Grid container spacing={2} alignItems="center" sx={{ py: 2 }}>
                      <Grid item xs={12} md={4}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {feature.icon}
                          <Typography variant="body1">
                            {feature.name}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4} md={2} sx={{ textAlign: 'center' }}>
                        {renderFeatureValue(feature.basic)}
                      </Grid>
                      <Grid item xs={4} md={3} sx={{ textAlign: 'center' }}>
                        {renderFeatureValue(feature.premium)}
                      </Grid>
                      <Grid item xs={4} md={3} sx={{ textAlign: 'center' }}>
                        {renderFeatureValue(feature.vip)}
                      </Grid>
                    </Grid>
                    {featureIndex < category.items.length - 1 && <Divider />}
                  </Box>
                ))}
              </Box>
            ))}
          </Paper>

          {/* FAQ Section */}
          <Box sx={{ mt: 6, textAlign: 'center' }}>
            <Typography variant="h5" gutterBottom>
              Остались вопросы?
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              Ознакомьтесь с часто задаваемыми вопросами или свяжитесь с нашей поддержкой
            </Typography>
            <Box sx={{ mt: 2, display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="outlined"
                onClick={() => router.push('/help/faq')}
              >
                Частые вопросы
              </Button>
              <Button
                variant="contained"
                onClick={() => router.push('/help/contact')}
              >
                Связаться с поддержкой
              </Button>
            </Box>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
    },
  };
};

export default SubscriptionComparePage;
