import React from 'react';
import Head from 'next/head';
import { GetStaticProps } from 'next';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import {
  Box,
  Typography,
  Container,
  Grid,
  Card,
  CardContent,
  Avatar,
  Stack,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  Security as SecurityIcon,
  Groups as GroupsIcon,
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import Layout from '../components/Layout/Layout';

interface TeamMember {
  name: string;
  role: string;
  avatar: string;
  bio: string;
}

interface Statistic {
  value: string;
  label: string;
  icon: React.ReactNode;
}

export default function AboutPage() {
  const { t } = useTranslation('common');

  const teamMembers: TeamMember[] = [
    {
      name: 'Анна Петрова',
      role: '💕 CEO & Основатель',
      avatar: 'https://picsum.photos/150/150?random=1',
      bio: '🧠 Эксперт в области психологии отношений с 15-летним опытом. Создала уникальный алгоритм совместимости, который помог найти любовь миллионам пар!'
    },
    {
      name: 'Михаил Иванов',
      role: '⚡ CTO & Технический директор',
      avatar: 'https://picsum.photos/150/150?random=2',
      bio: '🚀 Ведущий разработчик мультиплатформенных решений. Обеспечивает безопасность и молниеносную скорость поиска вашей второй половинки.'
    },
    {
      name: 'Елена Сидорова',
      role: '🎨 Head of Design',
      avatar: 'https://picsum.photos/150/150?random=3',
      bio: '✨ Специалист по UX/UI дизайну. Создает соблазнительно красивые и интуитивно понятные интерфейсы, в которые влюбляются с первого взгляда.'
    },
    {
      name: 'Дмитрий Козлов',
      role: '📈 Head of Marketing',
      avatar: 'https://picsum.photos/150/150?random=4',
      bio: '💘 Эксперт по цифровому маркетингу. Помогает людям находить наше приложение и свою настоящую любовь. Мастер создания романтической атмосферы!'
    }
  ];

  const statistics: Statistic[] = [
    {
      value: '2M+',
      label: 'Активных пользователей по всему миру',
      icon: <GroupsIcon />
    },
    {
      value: '150K+',
      label: 'Счастливых пар создано',
      icon: <FavoriteIcon />
    },
    {
      value: '4.9',
      label: 'Рейтинг в App Store и Google Play',
      icon: <StarIcon />
    },
    {
      value: '99.9%',
      label: 'Время работы сервиса',
      icon: <TrendingUpIcon />
    }
  ];

  const values = [
    {
      title: 'Безопасность превыше всего',
      description: 'Ваши данные защищены современным шифрованием. Все профили проходят верификацию для максимальной безопасности.',
      icon: <SecurityIcon />
    },
    {
      title: 'Настоящая любовь',
      description: 'Наш ИИ-алгоритм анализирует совместимость по 50+ параметрам, чтобы найти именно вашего человека.',
      icon: <FavoriteIcon />
    },
    {
      title: 'Мультиплатформенность',
      description: 'Доступно везде: веб, iOS, Android. Синхронизация между устройствами и единый аккаунт.',
      icon: <GroupsIcon />
    }
  ];

  return (
    <>
      <Head>
        <title>О нас - Likes Love | История создания лучшего приложения для знакомств</title>
        <meta name="description" content="Узнайте историю создания Likes Love, познакомьтесь с нашей командой и ценностями. Революционное мультиплатформенное приложение для поиска настоящей любви." />
        <meta name="keywords" content="о нас, команда, история, ценности, миссия, likes love, знакомства" />
        <link rel="canonical" href="https://likes-love.com/about" />

        <meta property="og:title" content="О нас - Likes Love" />
        <meta property="og:description" content="Узнайте больше о создателях лучшего приложения для знакомств" />
        <meta property="og:url" content="https://likes-love.com/about" />
        <meta property="og:type" content="website" />
        
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "Likes Love",
              "url": "https://likes-love.com",
              "description": 'Лучшее мультиплатформенное приложение для знакомств',
              "foundingDate": "2020",
              "founder": {
                "@type": "Person",
                "name": "Анна Петрова"
              },
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "RU",
                "addressLocality": "Москва"
              }
            })
          }}
        />
      </Head>
      <Layout showHeader={true} showFooter={true}>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          {/* Hero Section */}
          <Box textAlign="center" sx={{ mb: 8 }}>
            <Typography
              variant="h2"
              component="h1"
              fontWeight="bold"
              gutterBottom
              sx={{
                background: (theme) => `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              💕 О нас - Likes Love
            </Typography>
            <Typography variant="h5" color="text.secondary" sx={{ mb: 4, maxWidth: 800, mx: 'auto' }}>
              🌟 Революционное мультиплатформенное приложение, которое помогает миллионам людей находить настоящую любовь.
              💖 Доступно на всех устройствах с единым аккаунтом и синхронизацией. Присоединяйтесь к самому романтичному сообществу!
            </Typography>
          </Box>

          {/* Mission Section */}
          <Paper sx={{ p: 6, mb: 8, textAlign: 'center', bgcolor: 'primary.light' }}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              💝 Наша миссия
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto' }}>
              🎯 Создать самое эффективное и безопасное мультиплатформенное приложение для знакомств,
              которое поможет каждому человеку найти свою настоящую любовь. 🧠 Мы используем передовые
              технологии ИИ и психологические исследования, чтобы соединять совместимые души по всему миру.
              ✨ Ваша идеальная пара ждет вас!
            </Typography>
          </Paper>

          {/* Statistics */}
          <Box sx={{ mb: 8 }}>
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              🏆 Наши достижения
            </Typography>
            <Grid container spacing={4} sx={{ mt: 2 }}>
              {statistics.map((stat, index) => (
                <Grid item xs={6} md={3} key={index}>
                  <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
                    <Box
                      sx={{
                        width: 64,
                        height: 64,
                        borderRadius: '50%',
                        bgcolor: 'primary.light',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                        color: 'primary.main'
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography variant="h3" fontWeight="bold" color="primary.main" gutterBottom>
                      {stat.value}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* Our Story */}
          <Box sx={{ mb: 8 }}>
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  📖 Наша история любви
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  История Likes Love началась в 2020 году с простой, но амбициозной идеи: создать приложение,
                  которое действительно помогает людям находить настоящую любовь, а не просто случайные знакомства.
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  Мы разработали уникальный мультиплатформенный подход, позволяющий пользователям общаться
                  с любого устройства - смартфона, планшета или компьютера. Наш ИИ-алгоритм анализирует
                  совместимость по психологическим, социальным и личностным параметрам.
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Сегодня более 2 миллионов пользователей доверяют нам поиск своей второй половинки.
                  Мы гордимся тем, что помогли создать более 150 тысяч счастливых пар по всему миру!
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box
                  component="img"
                  src="https://picsum.photos/500/400?random=story"
                  alt="История Likes Love"
                  sx={{
                    width: '100%',
                    height: 400,
                    objectFit: 'cover',
                    borderRadius: 3,
                    boxShadow: 4
                  }}
                />
              </Grid>
            </Grid>
          </Box>

          {/* Values */}
          <Box sx={{ mb: 8 }}>
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              💎 Наши ценности
            </Typography>
            <Grid container spacing={4} sx={{ mt: 2 }}>
              {values.map((value, index) => (
                <Grid item xs={12} md={4} key={index}>
                  <Card sx={{ p: 4, height: '100%', textAlign: 'center' }}>
                    <Box
                      sx={{
                        width: 64,
                        height: 64,
                        borderRadius: '50%',
                        bgcolor: 'secondary.light',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 3,
                        color: 'secondary.main'
                      }}
                    >
                      {value.icon}
                    </Box>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {value.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {value.description}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* Team */}
          <Box sx={{ mb: 8 }}>
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              👥 Наша команда мечты
            </Typography>
            <Typography variant="h6" color="text.secondary" textAlign="center" sx={{ mb: 4 }}>
              🌟 Профессионалы своего дела, создающие магию любви
            </Typography>
            <Grid container spacing={4}>
              {teamMembers.map((member, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
                    <Avatar
                      src={member.avatar}
                      alt={member.name}
                      sx={{
                        width: 100,
                        height: 100,
                        mx: 'auto',
                        mb: 2,
                        border: '4px solid',
                        borderColor: 'primary.light'
                      }}
                    />
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      {member.name}
                    </Typography>
                    <Typography variant="subtitle2" color="primary.main" gutterBottom>
                      {member.role}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {member.bio}
                    </Typography>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* Why Choose Us */}
          <Paper sx={{ p: 6, bgcolor: 'grey.50' }}>
            <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
              Почему выбирают Likes Love?
            </Typography>
            <Typography variant="h6" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
              Мы предлагаем уникальные возможности, которых нет у конкурентов
            </Typography>
            <Grid container spacing={4} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <List>
                  {[
                    'Мультиплатформенность - работает везде',
                    'ИИ-алгоритм совместимости по 50+ параметрам',
                    'Верификация всех профилей в течение 24 часов',
                    'Круглосуточная поддержка на 12 языках'
                  ].map((feature, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <List>
                  {[
                    'Синхронизация между всеми устройствами',
                    'HD видеозвонки и голосовые сообщения',
                    'Умная геолокация с настройками приватности',
                    'Премиум подписка с эксклюзивными функциями'
                  ].map((feature, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <CheckIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText primary={feature} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </Grid>

            {/* CTA для подписки */}
            <Box sx={{ textAlign: 'center', mt: 4, p: 4, bgcolor: 'primary.main', borderRadius: 2 }}>
              <Typography variant="h5" sx={{ color: 'white', mb: 2, fontWeight: 'bold' }}>
                🎉 Готовы найти свою любовь?
              </Typography>
              <Typography variant="body1" sx={{ color: 'white', mb: 3, opacity: 0.9 }}>
                Присоединяйтесь к 2 миллионам пользователей и получите доступ ко всем премиум функциям!
              </Typography>
              <Button
                variant="contained"
                size="large"
                sx={{
                  bgcolor: 'white',
                  color: 'primary.main',
                  fontWeight: 'bold',
                  px: 4,
                  py: 1.5,
                  '&:hover': { bgcolor: 'grey.100' }
                }}
              >
                Начать поиск любви бесплатно
              </Button>
            </Box>
          </Paper>
        </Container>
      </Layout>
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
    },
  };
};
