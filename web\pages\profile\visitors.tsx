import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Badge,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  MoreVert as MoreVertIcon,
  Favorite as FavoriteIcon,
  Block as BlockIcon,
  Message as MessageIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  FilterList as FilterIcon,
  MarkEmailRead as MarkReadIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getProfileVisitors, 
  markVisitorsAsRead, 
  markAllVisitorsAsRead 
} from '../../src/services/profileService';
import { ProfileVisitor, FilterOptions } from '../../src/types/profile.types';

const VisitorsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [visitors, setVisitors] = useState<ProfileVisitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedVisitor, setSelectedVisitor] = useState<ProfileVisitor | null>(null);
  const [markingAsRead, setMarkingAsRead] = useState(false);

  // Pagination and filters
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState<'visitedAt' | 'lastActiveAt'>('visitedAt');
  const [sortOrder, setSortOrder] = useState<'desc' | 'asc'>('desc');
  const [filterRead, setFilterRead] = useState<'all' | 'unread' | 'read'>('all');
  const [filterVerified, setFilterVerified] = useState<boolean | undefined>(undefined);

  const limit = 20;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadVisitors();
  }, [user, router, page, sortBy, sortOrder, filterRead, filterVerified]);

  const loadVisitors = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {
        page,
        limit,
        sortBy,
        sortOrder,
        verifiedOnly: filterVerified
      };

      if (filterRead !== 'all') {
        filters.isRead = filterRead === 'read';
      }

      const response = await getProfileVisitors(filters);
      setVisitors(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      setError('Ошибка загрузки посетителей');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, visitor: ProfileVisitor) => {
    setAnchorEl(event.currentTarget);
    setSelectedVisitor(visitor);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedVisitor(null);
  };

  const handleMarkAsRead = async (visitorIds: string[]) => {
    try {
      setMarkingAsRead(true);
      await markVisitorsAsRead(visitorIds);
      
      // Обновляем локальное состояние
      setVisitors(prev => prev.map(visitor => 
        visitorIds.includes(visitor.id) 
          ? { ...visitor, isRead: true }
          : visitor
      ));
    } catch (err: any) {
      setError('Ошибка отметки как прочитанное');
    } finally {
      setMarkingAsRead(false);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      setMarkingAsRead(true);
      await markAllVisitorsAsRead();
      
      // Обновляем локальное состояние
      setVisitors(prev => prev.map(visitor => ({ ...visitor, isRead: true })));
    } catch (err: any) {
      setError('Ошибка отметки всех как прочитанные');
    } finally {
      setMarkingAsRead(false);
    }
  };

  const handleViewProfile = (userId: string) => {
    router.push(`/users/${userId}`);
    handleMenuClose();
  };

  const handleSendMessage = (userId: string) => {
    router.push(`/chat?userId=${userId}`);
    handleMenuClose();
  };

  const handleLikeUser = (userId: string) => {
    // Логика лайка пользователя
    handleMenuClose();
  };

  const handleBlockUser = (userId: string) => {
    router.push(`/profile/blocked?blockUserId=${userId}`);
    handleMenuClose();
  };

  const formatVisitTime = (visitedAt: string) => {
    const now = new Date();
    const visitTime = new Date(visitedAt);
    const diffInHours = Math.floor((now.getTime() - visitTime.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getViewTypeText = (viewType: string) => {
    switch (viewType) {
      case 'profile':
        return 'Просмотр профиля';
      case 'photo':
        return 'Просмотр фото';
      case 'search':
        return 'Из поиска';
      default:
        return 'Просмотр';
    }
  };

  const unreadCount = visitors.filter(v => !v.isRead).length;

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Посетители профиля - Likes & Love</title>
        <meta 
          name="description" 
          content="Посмотрите, кто посещал ваш профиль в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/profile/visitors" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <Badge badgeContent={unreadCount} color="primary">
                      <VisibilityIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                    </Badge>
                    Посетители профиля
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {visitors.length > 0 ? `${visitors.length} посетителей` : 'Пока никто не посещал ваш профиль'}
                  </Typography>
                </Box>
                {unreadCount > 0 && (
                  <Button
                    variant="outlined"
                    startIcon={<MarkReadIcon />}
                    onClick={handleMarkAllAsRead}
                    disabled={markingAsRead}
                    size={isMobile ? "small" : "medium"}
                  >
                    Отметить все как прочитанные
                  </Button>
                )}
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {/* Filters */}
              <Box sx={{ mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Сортировка</InputLabel>
                      <Select
                        value={`${sortBy}-${sortOrder}`}
                        label="Сортировка"
                        onChange={(e) => {
                          const [newSortBy, newSortOrder] = e.target.value.split('-');
                          setSortBy(newSortBy as any);
                          setSortOrder(newSortOrder as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="visitedAt-desc">Сначала новые</MenuItem>
                        <MenuItem value="visitedAt-asc">Сначала старые</MenuItem>
                        <MenuItem value="lastActiveAt-desc">По активности</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Статус</InputLabel>
                      <Select
                        value={filterRead}
                        label="Статус"
                        onChange={(e) => {
                          setFilterRead(e.target.value as any);
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="unread">Непрочитанные</MenuItem>
                        <MenuItem value="read">Прочитанные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Верификация</InputLabel>
                      <Select
                        value={filterVerified === undefined ? 'all' : filterVerified ? 'verified' : 'unverified'}
                        label="Верификация"
                        onChange={(e) => {
                          const value = e.target.value;
                          setFilterVerified(
                            value === 'all' ? undefined : value === 'verified'
                          );
                          setPage(1);
                        }}
                      >
                        <MenuItem value="all">Все</MenuItem>
                        <MenuItem value="verified">Верифицированные</MenuItem>
                        <MenuItem value="unverified">Неверифицированные</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка посетителей...
                  </Typography>
                </Box>
              ) : visitors.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <VisibilityIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Пока никто не посещал ваш профиль
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Сделайте ваш профиль более привлекательным, чтобы получить больше посетителей
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => router.push('/profile/edit')}
                  >
                    Улучшить профиль
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Visitors grid */}
                    <Grid container spacing={2}>
                      {visitors.map((visitor) => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={visitor.id}>
                          <Card 
                            sx={{ 
                              position: 'relative',
                              border: !visitor.isRead ? `2px solid ${theme.palette.primary.main}` : 'none',
                              '&:hover': {
                                boxShadow: theme.shadows[4]
                              }
                            }}
                          >
                            {!visitor.isRead && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: 8,
                                  left: 8,
                                  width: 12,
                                  height: 12,
                                  borderRadius: '50%',
                                  backgroundColor: theme.palette.primary.main,
                                  zIndex: 1
                                }}
                              />
                            )}

                            <CardContent sx={{ pb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Badge
                                  overlap="circular"
                                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                                  badgeContent={
                                    visitor.user.isOnline ? (
                                      <Box
                                        sx={{
                                          width: 12,
                                          height: 12,
                                          borderRadius: '50%',
                                          backgroundColor: 'success.main',
                                          border: '2px solid white'
                                        }}
                                      />
                                    ) : null
                                  }
                                >
                                  <Avatar
                                    src={visitor.user.avatarUrl}
                                    sx={{ width: 56, height: 56 }}
                                  >
                                    {visitor.user.firstName[0]}
                                  </Avatar>
                                </Badge>
                                <Box sx={{ ml: 2, flexGrow: 1 }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Typography variant="subtitle2" noWrap>
                                      {visitor.user.firstName}
                                    </Typography>
                                    {(visitor.user.verificationStatus.phone || 
                                      visitor.user.verificationStatus.email || 
                                      visitor.user.verificationStatus.photo) && (
                                      <VerifiedIcon 
                                        sx={{ 
                                          ml: 0.5, 
                                          fontSize: 16, 
                                          color: 'primary.main' 
                                        }} 
                                      />
                                    )}
                                  </Box>
                                  <Typography variant="caption" color="text.secondary">
                                    {visitor.user.age} лет
                                  </Typography>
                                </Box>
                                <IconButton
                                  size="small"
                                  onClick={(e) => handleMenuOpen(e, visitor)}
                                >
                                  <MoreVertIcon />
                                </IconButton>
                              </Box>

                              <Box sx={{ mb: 1 }}>
                                <Chip
                                  label={getViewTypeText(visitor.viewType)}
                                  size="small"
                                  variant="outlined"
                                  sx={{ mr: 1 }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {formatVisitTime(visitor.visitedAt)}
                                </Typography>
                              </Box>

                              {visitor.user.isOnline ? (
                                <Typography variant="caption" color="success.main">
                                  Онлайн
                                </Typography>
                              ) : (
                                <Typography variant="caption" color="text.secondary">
                                  <ScheduleIcon sx={{ fontSize: 12, mr: 0.5, verticalAlign: 'middle' }} />
                                  {formatVisitTime(visitor.user.lastActiveAt)}
                                </Typography>
                              )}
                            </CardContent>

                            <CardActions sx={{ pt: 0, px: 2, pb: 2 }}>
                              <Button
                                size="small"
                                onClick={() => handleViewProfile(visitor.user.id)}
                                fullWidth
                              >
                                Посмотреть профиль
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Pagination
                          count={totalPages}
                          page={page}
                          onChange={(_, newPage) => setPage(newPage)}
                          color="primary"
                          size={isMobile ? "small" : "medium"}
                        />
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Context menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedVisitor && handleViewProfile(selectedVisitor.user.id)}>
            <VisibilityIcon sx={{ mr: 1 }} />
            Посмотреть профиль
          </MenuItem>
          <MenuItem onClick={() => selectedVisitor && handleSendMessage(selectedVisitor.user.id)}>
            <MessageIcon sx={{ mr: 1 }} />
            Написать сообщение
          </MenuItem>
          <MenuItem onClick={() => selectedVisitor && handleLikeUser(selectedVisitor.user.id)}>
            <FavoriteIcon sx={{ mr: 1 }} />
            Поставить лайк
          </MenuItem>
          {!selectedVisitor?.isRead && (
            <MenuItem 
              onClick={() => selectedVisitor && handleMarkAsRead([selectedVisitor.id])}
              disabled={markingAsRead}
            >
              <MarkReadIcon sx={{ mr: 1 }} />
              Отметить как прочитанное
            </MenuItem>
          )}
          <MenuItem onClick={() => selectedVisitor && handleBlockUser(selectedVisitor.user.id)}>
            <BlockIcon sx={{ mr: 1 }} />
            Заблокировать
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default VisitorsPage;
