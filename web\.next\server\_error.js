"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./components/common/Notification.tsx":
/*!********************************************!*\
  !*** ./components/common/Notification.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notification: () => (/* binding */ Notification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Snackbar!=!@mui/material */ \"__barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js\");\n\n\n\nconst Notification = ({ open, message, severity = 'success', onClose, autoHideDuration = 6000 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_2__.Snackbar, {\n        open: open,\n        autoHideDuration: autoHideDuration,\n        onClose: onClose,\n        anchorOrigin: {\n            vertical: 'bottom',\n            horizontal: 'center'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            onClose: onClose,\n            severity: severity,\n            children: message\n        }, void 0, false, {\n            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\components\\\\common\\\\Notification.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\components\\\\common\\\\Notification.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2NvbW1vbi9Ob3RpZmljYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBMEI7QUFDa0M7QUFVckQsTUFBTUcsZUFBNEMsQ0FBQyxFQUN4REMsSUFBSSxFQUNKQyxPQUFPLEVBQ1BDLFdBQVcsU0FBUyxFQUNwQkMsT0FBTyxFQUNQQyxtQkFBbUIsSUFBSSxFQUN4QjtJQUNDLHFCQUNFLDhEQUFDUCx3RkFBUUE7UUFDUEcsTUFBTUE7UUFDTkksa0JBQWtCQTtRQUNsQkQsU0FBU0E7UUFDVEUsY0FBYztZQUFFQyxVQUFVO1lBQVVDLFlBQVk7UUFBUztrQkFFekQsNEVBQUNULHFGQUFLQTtZQUFDSyxTQUFTQTtZQUFTRCxVQUFVQTtzQkFDaENEOzs7Ozs7Ozs7OztBQUlULEVBQUUiLCJzb3VyY2VzIjpbIkY6XFxDdXJzb3JcXGxsLmNvbVxcaW5zdGFsbFxcd2ViXFxjb21wb25lbnRzXFxjb21tb25cXE5vdGlmaWNhdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNuYWNrYmFyLCBBbGVydCwgQWxlcnRQcm9wcyB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuXG5pbnRlcmZhY2UgTm90aWZpY2F0aW9uUHJvcHMge1xuICBvcGVuOiBib29sZWFuO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHNldmVyaXR5PzogQWxlcnRQcm9wc1snc2V2ZXJpdHknXTtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgYXV0b0hpZGVEdXJhdGlvbj86IG51bWJlcjtcbn1cblxuZXhwb3J0IGNvbnN0IE5vdGlmaWNhdGlvbjogUmVhY3QuRkM8Tm90aWZpY2F0aW9uUHJvcHM+ID0gKHtcbiAgb3BlbixcbiAgbWVzc2FnZSxcbiAgc2V2ZXJpdHkgPSAnc3VjY2VzcycsXG4gIG9uQ2xvc2UsXG4gIGF1dG9IaWRlRHVyYXRpb24gPSA2MDAwLFxufSkgPT4ge1xuICByZXR1cm4gKFxuICAgIDxTbmFja2JhclxuICAgICAgb3Blbj17b3Blbn1cbiAgICAgIGF1dG9IaWRlRHVyYXRpb249e2F1dG9IaWRlRHVyYXRpb259XG4gICAgICBvbkNsb3NlPXtvbkNsb3NlfVxuICAgICAgYW5jaG9yT3JpZ2luPXt7IHZlcnRpY2FsOiAnYm90dG9tJywgaG9yaXpvbnRhbDogJ2NlbnRlcicgfX1cbiAgICA+XG4gICAgICA8QWxlcnQgb25DbG9zZT17b25DbG9zZX0gc2V2ZXJpdHk9e3NldmVyaXR5fT5cbiAgICAgICAge21lc3NhZ2V9XG4gICAgICA8L0FsZXJ0PlxuICAgIDwvU25hY2tiYXI+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU25hY2tiYXIiLCJBbGVydCIsIk5vdGlmaWNhdGlvbiIsIm9wZW4iLCJtZXNzYWdlIiwic2V2ZXJpdHkiLCJvbkNsb3NlIiwiYXV0b0hpZGVEdXJhdGlvbiIsImFuY2hvck9yaWdpbiIsInZlcnRpY2FsIiwiaG9yaXpvbnRhbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/common/Notification.tsx\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\n([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"./node_modules/@mui/material/node/CssBaseline/index.js\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/createEmotionCache */ \"./src/createEmotionCache.ts\");\n/* harmony import */ var _src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/providers/AppProviders */ \"./src/providers/AppProviders.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react__WEBPACK_IMPORTED_MODULE_1__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__, _src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__, _src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__]);\n([_emotion_react__WEBPACK_IMPORTED_MODULE_1__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__, _src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__, _src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n// Динамический импорт ReactQueryDevtools только для клиентской стороны\nconst ReactQueryDevtools = next_dynamic__WEBPACK_IMPORTED_MODULE_5___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @tanstack/react-query-devtools */ \"@tanstack/react-query-devtools\")).then((mod)=>mod.ReactQueryDevtools), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\_app.tsx -> \" + \"@tanstack/react-query-devtools\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n\n// i18n управляется через next-i18next\n\n// Client-side cache, shared for the whole session of the user in the browser.\nconst clientSideEmotionCache = (0,_src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n// Create a client\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 1000 * 60 * 5,\n            gcTime: 1000 * 60 * 10,\n            retry: 3,\n            refetchOnWindowFocus: false\n        }\n    }\n});\nfunction MyApp(props) {\n    const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_1__.CacheProvider, {\n        value: emotionCache,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Likes & Love - Найдите свою настоящую любовь\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Платформа для серьезных знакомств с проверенными профилями\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n                client: queryClient,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__.AppProviders, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQueryDevtools, {\n                        initialIsOpen: false\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\n/* harmony import */ var _src_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/utils/createEmotionCache */ \"./src/utils/createEmotionCache.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_3__, _src_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_4__]);\n([_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_3__, _src_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction MyDocument({ emotionStyleTags }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n        lang: \"ru\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#ff6b9d\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"shortcut icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"emotion-insertion-point\",\n                        content: \"\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    emotionStyleTags,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/icon?family=Material+Icons\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"Likes & Love\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.png\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#ff6b9d\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-config\",\n                        content: \"/browserconfig.xml\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n// `getInitialProps` belongs to `_document` (instead of `_app`),\n// it's compatible with static-site generation (SSG).\nMyDocument.getInitialProps = async (ctx)=>{\n    // Resolution order\n    //\n    // On the server:\n    // 1. app.getInitialProps\n    // 2. page.getInitialProps\n    // 3. document.getInitialProps\n    // 4. app.render\n    // 5. page.render\n    // 6. document.render\n    //\n    // On the server with error:\n    // 1. document.getInitialProps\n    // 2. app.render\n    // 3. page.render\n    // 4. document.render\n    //\n    // On the client\n    // 1. app.getInitialProps\n    // 2. page.getInitialProps\n    // 3. app.render\n    // 4. page.render\n    const originalRenderPage = ctx.renderPage;\n    // You can consider sharing the same emotion cache between all the SSR requests to speed up performance.\n    // However, be aware that it can have global side effects.\n    const cache = (0,_src_utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_4__.createEmotionCache)();\n    const { extractCriticalToChunks } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(cache);\n    ctx.renderPage = ()=>originalRenderPage({\n            enhanceApp: (App)=>function EnhanceApp(props) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                        emotionCache: cache,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 18\n                    }, this);\n                }\n        });\n    const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(ctx);\n    // This is important. It prevents emotion to render invalid HTML.\n    // See https://github.com/mui/material-ui/issues/26561#issuecomment-855286153\n    const emotionStyles = extractCriticalToChunks(initialProps.html);\n    const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n            \"data-emotion\": `${style.key} ${style.ids.join(' ')}`,\n            // eslint-disable-next-line react/no-danger\n            dangerouslySetInnerHTML: {\n                __html: style.css\n            }\n        }, style.key, false, {\n            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_document.tsx\",\n            lineNumber: 106,\n            columnNumber: 5\n        }, undefined));\n    return {\n        ...initialProps,\n        emotionStyleTags\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./src/config/design-system.ts":
/*!*************************************!*\
  !*** ./src/config/design-system.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CROSS_PLATFORM_THEMES: () => (/* binding */ CROSS_PLATFORM_THEMES),\n/* harmony export */   DESIGN_TOKENS: () => (/* binding */ DESIGN_TOKENS),\n/* harmony export */   exportTokensForPlatform: () => (/* binding */ exportTokensForPlatform),\n/* harmony export */   getThemeById: () => (/* binding */ getThemeById),\n/* harmony export */   syncThemeAcrossPlatforms: () => (/* binding */ syncThemeAcrossPlatforms)\n/* harmony export */ });\n/**\r\n * Кроссплатформенная система дизайна для likes-love.com\r\n * Используется во всех версиях приложения: Web, iOS, Android, Huawei\r\n */ // Глобальные токены дизайна\nconst DESIGN_TOKENS = [\n    // Цвета\n    {\n        id: 'color-likes-love-primary',\n        name: 'Likes Love Primary',\n        value: '#ff6b9d',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'color-likes-love-secondary',\n        name: 'Likes Love Secondary',\n        value: '#4ecdc4',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'color-romantic-primary',\n        name: 'Romantic Primary',\n        value: '#ff6b9d',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'color-romantic-secondary',\n        name: 'Romantic Secondary',\n        value: '#ff8e53',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    // Градиенты\n    {\n        id: 'gradient-likes-love-primary',\n        name: 'Likes Love Primary Gradient',\n        value: 'linear-gradient(135deg, #ff6b9d 0%, #4ecdc4 100%)',\n        category: 'gradient',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'gradient-romantic-primary',\n        name: 'Romantic Primary Gradient',\n        value: 'linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%)',\n        category: 'gradient',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    // Отступы (в пикселях)\n    {\n        id: 'spacing-xs',\n        name: 'Extra Small Spacing',\n        value: '4',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-sm',\n        name: 'Small Spacing',\n        value: '8',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-md',\n        name: 'Medium Spacing',\n        value: '16',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-lg',\n        name: 'Large Spacing',\n        value: '24',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-xl',\n        name: 'Extra Large Spacing',\n        value: '32',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    // Радиусы скругления\n    {\n        id: 'radius-sm',\n        name: 'Small Border Radius',\n        value: '8',\n        category: 'radius',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'radius-md',\n        name: 'Medium Border Radius',\n        value: '12',\n        category: 'radius',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'radius-lg',\n        name: 'Large Border Radius',\n        value: '16',\n        category: 'radius',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    }\n];\n// Конфигурации тем для всех платформ\nconst CROSS_PLATFORM_THEMES = [\n    {\n        id: 'likes-love',\n        name: 'likes-love',\n        displayName: 'Likes & Love',\n        primaryColor: '#ff6b9d',\n        secondaryColor: '#4ecdc4',\n        gradients: {\n            primary: 'linear-gradient(135deg, #ff6b9d 0%, #4ecdc4 100%)',\n            secondary: 'linear-gradient(135deg, #4ecdc4 0%, #45b7aa 100%)',\n            accent: 'linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#f8f9fa',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#ff6b9d',\n                inverse: '#ffffff'\n            },\n            border: '#e2e8f0',\n            shadow: 'rgba(0, 0, 0, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\n            md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1)',\n            lg: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'\n        }\n    },\n    {\n        id: 'romantic',\n        name: 'romantic',\n        displayName: 'Романтическая',\n        primaryColor: '#ff6b9d',\n        secondaryColor: '#ff8e53',\n        gradients: {\n            primary: 'linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%)',\n            secondary: 'linear-gradient(135deg, #ff8e53 0%, #ffb347 100%)',\n            accent: 'linear-gradient(135deg, #ff6b9d 0%, #ff7fab 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#fef7f7',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#ff6b9d',\n                inverse: '#ffffff'\n            },\n            border: '#f7e8e8',\n            shadow: 'rgba(255, 107, 157, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(255, 107, 157, 0.12), 0 1px 2px rgba(255, 107, 157, 0.24)',\n            md: '0 4px 6px rgba(255, 107, 157, 0.07), 0 1px 3px rgba(255, 107, 157, 0.1)',\n            lg: '0 10px 25px rgba(255, 107, 157, 0.1), 0 4px 6px rgba(255, 107, 157, 0.05)'\n        }\n    },\n    {\n        id: 'elegant',\n        name: 'elegant',\n        displayName: 'Элегантная',\n        primaryColor: '#9f7aea',\n        secondaryColor: '#667eea',\n        gradients: {\n            primary: 'linear-gradient(135deg, #9f7aea 0%, #667eea 100%)',\n            secondary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            accent: 'linear-gradient(135deg, #9f7aea 0%, #b794f6 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#faf5ff',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#9f7aea',\n                inverse: '#ffffff'\n            },\n            border: '#e9d8fd',\n            shadow: 'rgba(159, 122, 234, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',\n            fontSizes: {\n                xs: 12,\n                sm: 14,\n                md: 16,\n                lg: 18,\n                xl: 24,\n                xxl: 32\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(159, 122, 234, 0.12), 0 1px 2px rgba(159, 122, 234, 0.24)',\n            md: '0 4px 6px rgba(159, 122, 234, 0.07), 0 1px 3px rgba(159, 122, 234, 0.1)',\n            lg: '0 10px 25px rgba(159, 122, 234, 0.1), 0 4px 6px rgba(159, 122, 234, 0.05)'\n        }\n    },\n    {\n        id: 'modern',\n        name: 'modern',\n        displayName: 'Современная',\n        primaryColor: '#38b2ac',\n        secondaryColor: '#4fd1c7',\n        gradients: {\n            primary: 'linear-gradient(135deg, #38b2ac 0%, #4fd1c7 100%)',\n            secondary: 'linear-gradient(135deg, #4fd1c7 0%, #81e6d9 100%)',\n            accent: 'linear-gradient(135deg, #38b2ac 0%, #319795 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#f0fffe',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#38b2ac',\n                inverse: '#ffffff'\n            },\n            border: '#b2f5ea',\n            shadow: 'rgba(56, 178, 172, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(56, 178, 172, 0.12), 0 1px 2px rgba(56, 178, 172, 0.24)',\n            md: '0 4px 6px rgba(56, 178, 172, 0.07), 0 1px 3px rgba(56, 178, 172, 0.1)',\n            lg: '0 10px 25px rgba(56, 178, 172, 0.1), 0 4px 6px rgba(56, 178, 172, 0.05)'\n        }\n    },\n    {\n        id: 'dark',\n        name: 'dark',\n        displayName: 'Темная',\n        primaryColor: '#f56565',\n        secondaryColor: '#48bb78',\n        gradients: {\n            primary: 'linear-gradient(135deg, #f56565 0%, #48bb78 100%)',\n            secondary: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',\n            accent: 'linear-gradient(135deg, #f56565 0%, #fc8181 100%)'\n        },\n        colors: {\n            background: '#1a202c',\n            surface: '#2d3748',\n            text: {\n                primary: '#f7fafc',\n                secondary: '#a0aec0',\n                accent: '#f56565',\n                inverse: '#2d3748'\n            },\n            border: '#4a5568',\n            shadow: 'rgba(0, 0, 0, 0.3)',\n            overlay: 'rgba(0, 0, 0, 0.7)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\n            md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1)',\n            lg: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'\n        }\n    }\n];\n// Функция для получения темы по ID\nconst getThemeById = (themeId)=>{\n    return CROSS_PLATFORM_THEMES.find((theme)=>theme.id === themeId);\n};\n// Функция для экспорта токенов в различные форматы\nconst exportTokensForPlatform = (platform)=>{\n    const platformTokens = DESIGN_TOKENS.filter((token)=>token.platforms.includes(platform));\n    switch(platform){\n        case 'web':\n            return exportToCSSVariables(platformTokens);\n        case 'ios':\n            return exportToSwiftUITokens(platformTokens);\n        case 'android':\n            return exportToXMLColors(platformTokens);\n        case 'huawei':\n            return exportToHarmonyOSTokens(platformTokens);\n        default:\n            return platformTokens;\n    }\n};\n// Экспорт в CSS переменные для Web\nconst exportToCSSVariables = (tokens)=>{\n    const cssVars = tokens.map((token)=>{\n        const cssName = `--${token.id.replace(/[A-Z]/g, (letter)=>`-${letter.toLowerCase()}`)}`;\n        return `  ${cssName}: ${token.value};`;\n    }).join('\\n');\n    return `:root {\\n${cssVars}\\n}`;\n};\n// Экспорт для SwiftUI (iOS)\nconst exportToSwiftUITokens = (tokens)=>{\n    const swiftTokens = tokens.map((token)=>{\n        const swiftName = token.id.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());\n        if (token.category === 'color') {\n            return `  static let ${swiftName} = Color(hex: \"${token.value}\")`;\n        }\n        return `  static let ${swiftName} = ${token.value}`;\n    }).join('\\n');\n    return `struct DesignTokens {\\n${swiftTokens}\\n}`;\n};\n// Экспорт для Android XML\nconst exportToXMLColors = (tokens)=>{\n    const colorTokens = tokens.filter((token)=>token.category === 'color');\n    const xmlColors = colorTokens.map((token)=>{\n        const xmlName = token.id.replace(/-/g, '_');\n        return `  <color name=\"${xmlName}\">${token.value}</color>`;\n    }).join('\\n');\n    return `<resources>\\n${xmlColors}\\n</resources>`;\n};\n// Экспорт для HarmonyOS\nconst exportToHarmonyOSTokens = (tokens)=>{\n    const harmonyTokens = tokens.map((token)=>{\n        const harmonyName = token.id.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());\n        return `  ${harmonyName}: '${token.value}',`;\n    }).join('\\n');\n    return `export const DesignTokens = {\\n${harmonyTokens}\\n};`;\n};\nconst syncThemeAcrossPlatforms = async (preferences)=>{\n    // Логика синхронизации настроек темы между платформами\n    // Здесь будет интеграция с API для сохранения настроек пользователя\n    return {\n        success: true,\n        syncedAt: new Date().toISOString(),\n        platforms: preferences.syncedPlatforms\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/design-system.ts\n");

/***/ }),

/***/ "./src/createEmotionCache.ts":
/*!***********************************!*\
  !*** ./src/createEmotionCache.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction createEmotionCache() {\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: 'css',\n        prepend: true\n    });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY3JlYXRlRW1vdGlvbkNhY2hlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QztBQUVsQyxTQUFTQztJQUNkLE9BQU9ELDBEQUFXQSxDQUFDO1FBQUVFLEtBQUs7UUFBT0MsU0FBUztJQUFLO0FBQ2pEO0FBRUEsaUVBQWVGLGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsiRjpcXEN1cnNvclxcbGwuY29tXFxpbnN0YWxsXFx3ZWJcXHNyY1xcY3JlYXRlRW1vdGlvbkNhY2hlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVDYWNoZSBmcm9tICdAZW1vdGlvbi9jYWNoZSc7XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVFbW90aW9uQ2FjaGUoKSB7XG4gIHJldHVybiBjcmVhdGVDYWNoZSh7IGtleTogJ2NzcycsIHByZXBlbmQ6IHRydWUgfSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGNyZWF0ZUVtb3Rpb25DYWNoZTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDYWNoZSIsImNyZWF0ZUVtb3Rpb25DYWNoZSIsImtleSIsInByZXBlbmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/createEmotionCache.ts\n");

/***/ }),

/***/ "./src/hooks/useContacts.ts":
/*!**********************************!*\
  !*** ./src/hooks/useContacts.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContacts: () => (/* binding */ useContacts)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_contacts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/contacts */ \"./src/utils/contacts.ts\");\n\n\n\nconst useContacts = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const [contacts, setContacts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const loadContacts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[loadContacts]\": async ()=>{\n            try {\n                setLoading(true);\n                const storedContacts = await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.getStoredContacts)();\n                setContacts(storedContacts);\n                setError(null);\n            } catch (err) {\n                setError(t('contacts.error.loadFailed'));\n                console.error('Error loading contacts:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[loadContacts]\"], [\n        t\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useContacts.useEffect\": ()=>{\n            loadContacts();\n        }\n    }[\"useContacts.useEffect\"], [\n        loadContacts\n    ]);\n    const addContact = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[addContact]\": async (contact)=>{\n            try {\n                setLoading(true);\n                const hasPermission = await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.checkContactPermission)();\n                if (!hasPermission) {\n                    throw new Error(t('contacts.error.permissionDenied'));\n                }\n                const fullContact = {\n                    ...contact,\n                    id: crypto.randomUUID(),\n                    addedAt: new Date().toISOString()\n                };\n                const success = await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.addToContacts)(fullContact);\n                if (success) {\n                    await loadContacts();\n                    return {\n                        success: true\n                    };\n                } else {\n                    throw new Error(t('contacts.error.addFailed'));\n                }\n            } catch (err) {\n                const error = err instanceof Error ? err.message : t('contacts.error.default');\n                setError(error);\n                return {\n                    success: false,\n                    error\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[addContact]\"], [\n        t,\n        loadContacts\n    ]);\n    const removeContact = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[removeContact]\": async (contactId)=>{\n            try {\n                setLoading(true);\n                await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.removeContacts)([\n                    contactId\n                ]);\n                await loadContacts();\n                return {\n                    success: true\n                };\n            } catch (err) {\n                const error = t('contacts.error.removeFailed');\n                setError(error);\n                return {\n                    success: false,\n                    error\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[removeContact]\"], [\n        t,\n        loadContacts\n    ]);\n    const removeAllContacts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[removeAllContacts]\": async ()=>{\n            try {\n                setLoading(true);\n                await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.removeContacts)();\n                setContacts([]);\n                return {\n                    success: true\n                };\n            } catch (err) {\n                const error = t('contacts.error.removeAllFailed');\n                setError(error);\n                return {\n                    success: false,\n                    error\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[removeAllContacts]\"], [\n        t\n    ]);\n    return {\n        contacts,\n        loading,\n        error,\n        addContact,\n        removeContact,\n        removeAllContacts,\n        loadContacts\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useContacts.ts\n");

/***/ }),

/***/ "./src/hooks/useNotification.ts":
/*!**************************************!*\
  !*** ./src/hooks/useNotification.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useNotification = ()=>{\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        open: false,\n        message: '',\n        severity: 'success'\n    });\n    const showNotification = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotification.useCallback[showNotification]\": (message, severity = 'success')=>{\n            setNotification({\n                open: true,\n                message,\n                severity\n            });\n        }\n    }[\"useNotification.useCallback[showNotification]\"], []);\n    const hideNotification = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotification.useCallback[hideNotification]\": ()=>{\n            setNotification({\n                \"useNotification.useCallback[hideNotification]\": (prev)=>({\n                        ...prev,\n                        open: false\n                    })\n            }[\"useNotification.useCallback[hideNotification]\"]);\n        }\n    }[\"useNotification.useCallback[hideNotification]\"], []);\n    return {\n        notification,\n        showNotification,\n        hideNotification\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useNotification.ts\n");

/***/ }),

/***/ "./src/providers/AdminThemeProvider.tsx":
/*!**********************************************!*\
  !*** ./src/providers/AdminThemeProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminThemeProvider: () => (/* binding */ AdminThemeProvider),\n/* harmony export */   useAdminTheme: () => (/* binding */ useAdminTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery!=!@mui/material */ \"__barrel_optimize__?names=useMediaQuery!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _config_design_system__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/design-system */ \"./src/config/design-system.ts\");\n/* harmony import */ var _styles_theme_factory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/theme-factory */ \"./src/styles/theme-factory.ts\");\n\n\n\n\n\n\nconst AdminThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AdminThemeProvider = ({ children })=>{\n    const [currentTheme, setCurrentTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availableThemes, setAvailableThemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewTheme, setPreviewTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPreviewMode, setIsPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const prefersDarkMode = (0,_barrel_optimize_names_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__.useMediaQuery)('(prefers-color-scheme: dark)');\n    // Загрузка тем при инициализации\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminThemeProvider.useEffect\": ()=>{\n            loadThemesFromAdmin();\n        }\n    }[\"AdminThemeProvider.useEffect\"], []);\n    // Загрузка тем из админки\n    const loadThemesFromAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[loadThemesFromAdmin]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const response = await fetch('/api/admin/themes', {\n                    headers: {\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    }\n                });\n                if (response.ok) {\n                    const themesData = await response.json();\n                    // Проверяем, что получили массив тем\n                    const themes = Array.isArray(themesData) ? themesData : [];\n                    if (themes.length === 0) {\n                        // Если нет тем, создаем локальные\n                        const localThemes = createLocalThemes();\n                        setAvailableThemes(localThemes);\n                        setCurrentTheme(localThemes[0]);\n                    } else {\n                        setAvailableThemes(themes);\n                        // Находим активную тему или используем дефолтную\n                        const activeTheme = themes.find({\n                            \"AdminThemeProvider.useCallback[loadThemesFromAdmin]\": (t)=>t.isActive\n                        }[\"AdminThemeProvider.useCallback[loadThemesFromAdmin]\"]) || themes.find({\n                            \"AdminThemeProvider.useCallback[loadThemesFromAdmin]\": (t)=>t.isDefault\n                        }[\"AdminThemeProvider.useCallback[loadThemesFromAdmin]\"]) || createDefaultTheme();\n                        setCurrentTheme(activeTheme);\n                    }\n                } else {\n                    // Если API недоступно, используем локальные темы\n                    const localThemes = createLocalThemes();\n                    setAvailableThemes(localThemes);\n                    setCurrentTheme(localThemes[0]);\n                }\n            } catch (err) {\n                console.error('Ошибка загрузки тем:', err);\n                // Fallback к локальным темам\n                const localThemes = createLocalThemes();\n                setAvailableThemes(localThemes);\n                setCurrentTheme(localThemes[0]);\n                setError('Не удалось загрузить темы из админки, используются локальные');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[loadThemesFromAdmin]\"], []);\n    // Создание локальных тем на основе CROSS_PLATFORM_THEMES\n    const createLocalThemes = ()=>{\n        return _config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES.map((config, index)=>({\n                id: config.id,\n                name: config.name,\n                displayName: config.displayName,\n                isActive: index === 0,\n                isDefault: index === 0,\n                config,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            }));\n    };\n    // Создание дефолтной темы\n    const createDefaultTheme = ()=>{\n        const defaultConfig = _config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES[0];\n        return {\n            id: 'default',\n            name: 'default',\n            displayName: 'По умолчанию',\n            isActive: true,\n            isDefault: true,\n            config: defaultConfig,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n    };\n    // Установка темы\n    const setTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[setTheme]\": async (themeId)=>{\n            const theme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[setTheme].theme\": (t)=>t.id === themeId\n            }[\"AdminThemeProvider.useCallback[setTheme].theme\"]);\n            if (!theme) {\n                setError(`Тема с ID ${themeId} не найдена`);\n                return;\n            }\n            try {\n                // Сохраняем в админке\n                await fetch('/api/admin/themes/activate', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify({\n                        themeId\n                    })\n                });\n                // Обновляем локальное состояние\n                setAvailableThemes({\n                    \"AdminThemeProvider.useCallback[setTheme]\": (prev)=>prev.map({\n                            \"AdminThemeProvider.useCallback[setTheme]\": (t)=>({\n                                    ...t,\n                                    isActive: t.id === themeId\n                                })\n                        }[\"AdminThemeProvider.useCallback[setTheme]\"])\n                }[\"AdminThemeProvider.useCallback[setTheme]\"]);\n                setCurrentTheme(theme);\n                // Сохраняем в localStorage для быстрого доступа\n                localStorage.setItem('activeThemeId', themeId);\n            } catch (err) {\n                console.error('Ошибка установки темы:', err);\n                // Устанавливаем локально даже если API недоступно\n                setCurrentTheme(theme);\n                localStorage.setItem('activeThemeId', themeId);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[setTheme]\"], [\n        availableThemes\n    ]);\n    // Переключение темы (упрощенный метод для совместимости)\n    const switchTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[switchTheme]\": async (themeName)=>{\n            // Ищем тему по имени или создаем простое переключение light/dark\n            let targetTheme;\n            if (themeName === 'light') {\n                targetTheme = availableThemes.find({\n                    \"AdminThemeProvider.useCallback[switchTheme]\": (t)=>t.name.includes('light') || t.name === 'likes-love'\n                }[\"AdminThemeProvider.useCallback[switchTheme]\"]);\n            } else if (themeName === 'dark') {\n                targetTheme = availableThemes.find({\n                    \"AdminThemeProvider.useCallback[switchTheme]\": (t)=>t.name.includes('dark')\n                }[\"AdminThemeProvider.useCallback[switchTheme]\"]);\n            } else {\n                targetTheme = availableThemes.find({\n                    \"AdminThemeProvider.useCallback[switchTheme]\": (t)=>t.name === themeName\n                }[\"AdminThemeProvider.useCallback[switchTheme]\"]);\n            }\n            // Если не найдена, используем следующую доступную тему\n            if (!targetTheme) {\n                const currentIndex = availableThemes.findIndex({\n                    \"AdminThemeProvider.useCallback[switchTheme].currentIndex\": (t)=>t.id === currentTheme?.id\n                }[\"AdminThemeProvider.useCallback[switchTheme].currentIndex\"]);\n                const nextIndex = (currentIndex + 1) % availableThemes.length;\n                targetTheme = availableThemes[nextIndex];\n            }\n            if (targetTheme) {\n                await setTheme(targetTheme.id);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[switchTheme]\"], [\n        availableThemes,\n        currentTheme,\n        setTheme\n    ]);\n    // Создание кастомной темы\n    const createCustomTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[createCustomTheme]\": async (config)=>{\n            const newTheme = {\n                id: `custom-${Date.now()}`,\n                name: config.name || 'custom',\n                displayName: config.displayName || 'Кастомная тема',\n                isActive: false,\n                isDefault: false,\n                config: config.config || _config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES[0],\n                customizations: config.customizations,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            try {\n                const response = await fetch('/api/admin/themes', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify(newTheme)\n                });\n                if (response.ok) {\n                    const savedTheme = await response.json();\n                    setAvailableThemes({\n                        \"AdminThemeProvider.useCallback[createCustomTheme]\": (prev)=>[\n                                ...prev,\n                                savedTheme\n                            ]\n                    }[\"AdminThemeProvider.useCallback[createCustomTheme]\"]);\n                    return savedTheme;\n                }\n            } catch (err) {\n                console.error('Ошибка создания темы:', err);\n            }\n            // Добавляем локально если API недоступно\n            setAvailableThemes({\n                \"AdminThemeProvider.useCallback[createCustomTheme]\": (prev)=>[\n                        ...prev,\n                        newTheme\n                    ]\n            }[\"AdminThemeProvider.useCallback[createCustomTheme]\"]);\n            return newTheme;\n        }\n    }[\"AdminThemeProvider.useCallback[createCustomTheme]\"], []);\n    // Обновление темы\n    const updateTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[updateTheme]\": async (themeId, updates)=>{\n            try {\n                await fetch(`/api/admin/themes/${themeId}`, {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify(updates)\n                });\n                setAvailableThemes({\n                    \"AdminThemeProvider.useCallback[updateTheme]\": (prev)=>prev.map({\n                            \"AdminThemeProvider.useCallback[updateTheme]\": (theme)=>theme.id === themeId ? {\n                                    ...theme,\n                                    ...updates,\n                                    updatedAt: new Date().toISOString()\n                                } : theme\n                        }[\"AdminThemeProvider.useCallback[updateTheme]\"])\n                }[\"AdminThemeProvider.useCallback[updateTheme]\"]);\n                if (currentTheme?.id === themeId) {\n                    setCurrentTheme({\n                        \"AdminThemeProvider.useCallback[updateTheme]\": (prev)=>prev ? {\n                                ...prev,\n                                ...updates\n                            } : prev\n                    }[\"AdminThemeProvider.useCallback[updateTheme]\"]);\n                }\n            } catch (err) {\n                console.error('Ошибка обновления темы:', err);\n                setError('Не удалось обновить тему');\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[updateTheme]\"], [\n        currentTheme\n    ]);\n    // Удаление темы\n    const deleteTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[deleteTheme]\": async (themeId)=>{\n            const theme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[deleteTheme].theme\": (t)=>t.id === themeId\n            }[\"AdminThemeProvider.useCallback[deleteTheme].theme\"]);\n            if (theme?.isDefault) {\n                setError('Нельзя удалить дефолтную тему');\n                return;\n            }\n            try {\n                await fetch(`/api/admin/themes/${themeId}`, {\n                    method: 'DELETE',\n                    headers: {\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    }\n                });\n                setAvailableThemes({\n                    \"AdminThemeProvider.useCallback[deleteTheme]\": (prev)=>prev.filter({\n                            \"AdminThemeProvider.useCallback[deleteTheme]\": (t)=>t.id !== themeId\n                        }[\"AdminThemeProvider.useCallback[deleteTheme]\"])\n                }[\"AdminThemeProvider.useCallback[deleteTheme]\"]);\n                if (currentTheme?.id === themeId) {\n                    const defaultTheme = availableThemes.find({\n                        \"AdminThemeProvider.useCallback[deleteTheme].defaultTheme\": (t)=>t.isDefault\n                    }[\"AdminThemeProvider.useCallback[deleteTheme].defaultTheme\"]);\n                    if (defaultTheme) {\n                        await setTheme(defaultTheme.id);\n                    }\n                }\n            } catch (err) {\n                console.error('Ошибка удаления темы:', err);\n                setError('Не удалось удалить тему');\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[deleteTheme]\"], [\n        availableThemes,\n        currentTheme,\n        setTheme\n    ]);\n    // Сброс к дефолтной теме\n    const resetToDefault = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[resetToDefault]\": async ()=>{\n            const defaultTheme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[resetToDefault].defaultTheme\": (t)=>t.isDefault\n            }[\"AdminThemeProvider.useCallback[resetToDefault].defaultTheme\"]);\n            if (defaultTheme) {\n                await setTheme(defaultTheme.id);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[resetToDefault]\"], [\n        availableThemes,\n        setTheme\n    ]);\n    // Сохранение темы в админке\n    const saveThemeToAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[saveThemeToAdmin]\": async (theme)=>{\n            try {\n                await fetch('/api/admin/themes', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify(theme)\n                });\n            } catch (err) {\n                console.error('Ошибка сохранения темы:', err);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[saveThemeToAdmin]\"], []);\n    // Получение Material-UI темы\n    const getMuiTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[getMuiTheme]\": ()=>{\n            const activeTheme = previewTheme || currentTheme;\n            if (!activeTheme) {\n                return (0,_styles_theme_factory__WEBPACK_IMPORTED_MODULE_3__.createLikesLoveTheme)(_config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES[0]);\n            }\n            return (0,_styles_theme_factory__WEBPACK_IMPORTED_MODULE_3__.createLikesLoveTheme)(activeTheme.config, activeTheme.customizations);\n        }\n    }[\"AdminThemeProvider.useCallback[getMuiTheme]\"], [\n        currentTheme,\n        previewTheme\n    ]);\n    // Предварительный просмотр темы\n    const handlePreviewTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[handlePreviewTheme]\": (themeId)=>{\n            const theme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[handlePreviewTheme].theme\": (t)=>t.id === themeId\n            }[\"AdminThemeProvider.useCallback[handlePreviewTheme].theme\"]);\n            if (theme) {\n                setPreviewTheme(theme);\n                setIsPreviewMode(true);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[handlePreviewTheme]\"], [\n        availableThemes\n    ]);\n    // Остановка предварительного просмотра\n    const stopPreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[stopPreview]\": ()=>{\n            setPreviewTheme(null);\n            setIsPreviewMode(false);\n        }\n    }[\"AdminThemeProvider.useCallback[stopPreview]\"], []);\n    const contextValue = {\n        currentTheme: currentTheme || createDefaultTheme(),\n        availableThemes,\n        isLoading,\n        error,\n        setTheme,\n        switchTheme,\n        createCustomTheme,\n        updateTheme,\n        deleteTheme,\n        resetToDefault,\n        loadThemesFromAdmin,\n        saveThemeToAdmin,\n        getMuiTheme,\n        previewTheme: handlePreviewTheme,\n        stopPreview,\n        isPreviewMode\n    };\n    const muiTheme = getMuiTheme();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminThemeContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n            theme: muiTheme,\n            children: children\n        }, void 0, false, {\n            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AdminThemeProvider.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AdminThemeProvider.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAdminTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AdminThemeContext);\n    if (!context) {\n        throw new Error('useAdminTheme must be used within AdminThemeProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/AdminThemeProvider.tsx\n");

/***/ }),

/***/ "./src/providers/AppProviders.tsx":
/*!****************************************!*\
  !*** ./src/providers/AppProviders.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProviders: () => (/* binding */ AppProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CssBaseline!=!@mui/material */ \"__barrel_optimize__?names=CssBaseline!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthProvider */ \"./src/providers/AuthProvider.tsx\");\n/* harmony import */ var _NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationsProvider */ \"./src/providers/NotificationsProvider.tsx\");\n/* harmony import */ var _ContactsProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ContactsProvider */ \"./src/providers/ContactsProvider.tsx\");\n/* harmony import */ var _MatchProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MatchProvider */ \"./src/providers/MatchProvider.tsx\");\n/* harmony import */ var _ChatProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ChatProvider */ \"./src/providers/ChatProvider.tsx\");\n/* harmony import */ var _SettingsProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SettingsProvider */ \"./src/providers/SettingsProvider.tsx\");\n/* harmony import */ var _AdminThemeProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AdminThemeProvider */ \"./src/providers/AdminThemeProvider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__, _MatchProvider__WEBPACK_IMPORTED_MODULE_5__, _ChatProvider__WEBPACK_IMPORTED_MODULE_6__]);\n([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__, _MatchProvider__WEBPACK_IMPORTED_MODULE_5__, _ChatProvider__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst AppProviders = ({ children })=>{\n    return(// I18nextProvider уже в _app.tsx, убираем дублирование\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminThemeProvider__WEBPACK_IMPORTED_MODULE_8__.AdminThemeProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_9__.CssBaseline, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__.NotificationsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SettingsProvider__WEBPACK_IMPORTED_MODULE_7__.SettingsProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContactsProvider__WEBPACK_IMPORTED_MODULE_4__.ContactsProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MatchProvider__WEBPACK_IMPORTED_MODULE_5__.MatchProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatProvider__WEBPACK_IMPORTED_MODULE_6__.ChatProvider, {\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined));\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/AppProviders.tsx\n");

/***/ }),

/***/ "./src/providers/AuthProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/AuthProvider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isAuthenticated = !!user && !!token;\n    // Загрузка токена из localStorage при инициализации\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const savedToken = localStorage.getItem('token');\n            const savedUser = localStorage.getItem('user');\n            if (savedToken && savedUser) {\n                try {\n                    const parsedUser = JSON.parse(savedUser);\n                    setToken(savedToken);\n                    setUser(parsedUser);\n                    // Проверяем валидность токена\n                    validateToken(savedToken).then({\n                        \"AuthProvider.useEffect\": (isValid)=>{\n                            if (!isValid) {\n                                logout();\n                            }\n                        }\n                    }[\"AuthProvider.useEffect\"]);\n                } catch (error) {\n                    console.error('Error parsing saved user data:', error);\n                    logout();\n                }\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Проверка валидности токена\n    const validateToken = async (tokenToValidate)=>{\n        try {\n            const response = await fetch('/api/auth/validate', {\n                method: 'GET',\n                headers: {\n                    'Authorization': `Bearer ${tokenToValidate}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.error('Token validation error:', error);\n            return false;\n        }\n    };\n    // Вход в систему\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                return {\n                    success: false,\n                    error: data.message || 'Login failed'\n                };\n            }\n            const { user: userData, token: userToken } = data;\n            // Сохраняем данные\n            setUser(userData);\n            setToken(userToken);\n            localStorage.setItem('token', userToken);\n            localStorage.setItem('user', JSON.stringify(userData));\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Network error'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Регистрация\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/auth/register', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(userData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                return {\n                    success: false,\n                    error: data.message || 'Registration failed'\n                };\n            }\n            const { user: newUser, token: userToken } = data;\n            // Сохраняем данные\n            setUser(newUser);\n            setToken(userToken);\n            localStorage.setItem('token', userToken);\n            localStorage.setItem('user', JSON.stringify(newUser));\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Registration error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Network error'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Выход из системы\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n    };\n    // Обновление данных пользователя\n    const updateUser = (userData)=>{\n        if (user) {\n            const updatedUser = {\n                ...user,\n                ...userData\n            };\n            setUser(updatedUser);\n            localStorage.setItem('user', JSON.stringify(updatedUser));\n        }\n    };\n    // Обновление токена\n    const refreshToken = async ()=>{\n        if (!token) return false;\n        try {\n            const response = await fetch('/api/auth/refresh', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                logout();\n                return false;\n            }\n            const data = await response.json();\n            const { token: newToken, user: userData } = data;\n            setToken(newToken);\n            setUser(userData);\n            localStorage.setItem('token', newToken);\n            localStorage.setItem('user', JSON.stringify(userData));\n            return true;\n        } catch (error) {\n            console.error('Token refresh error:', error);\n            logout();\n            return false;\n        }\n    };\n    const value = {\n        user,\n        token,\n        isLoading,\n        isAuthenticated,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshToken\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "./src/providers/ChatProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/ChatProvider.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationsProvider */ \"./src/providers/NotificationsProvider.tsx\");\n/* harmony import */ var _MatchProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MatchProvider */ \"./src/providers/MatchProvider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__, _MatchProvider__WEBPACK_IMPORTED_MODULE_3__]);\n([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__, _MatchProvider__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ChatProvider = ({ children })=>{\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { showSuccess, showError } = (0,_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    const { hasActiveMatch } = (0,_MatchProvider__WEBPACK_IMPORTED_MODULE_3__.useMatch)();\n    // Load chats and messages from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            const loadData = {\n                \"ChatProvider.useEffect.loadData\": ()=>{\n                    try {\n                        const storedChats = localStorage.getItem('likes-love-chats');\n                        const storedMessages = localStorage.getItem('likes-love-messages');\n                        if (storedChats) {\n                            setChats(JSON.parse(storedChats));\n                        }\n                        if (storedMessages) {\n                            setMessages(JSON.parse(storedMessages));\n                        }\n                    } catch (error) {\n                        console.error('Error loading chat data:', error);\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"ChatProvider.useEffect\"], []);\n    // Save data to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            try {\n                localStorage.setItem('likes-love-chats', JSON.stringify(chats));\n            } catch (error) {\n                console.error('Error saving chats:', error);\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        chats\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            try {\n                localStorage.setItem('likes-love-messages', JSON.stringify(messages));\n            } catch (error) {\n                console.error('Error saving messages:', error);\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        messages\n    ]);\n    const createChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createChat]\": async (participantId)=>{\n            // Check if users have an active match\n            if (!hasActiveMatch(participantId)) {\n                showError('Вы можете общаться только с матчами');\n                return null;\n            }\n            // Check if chat already exists\n            const existingChat = chats.find({\n                \"ChatProvider.useCallback[createChat].existingChat\": (chat)=>chat.participantIds.includes(participantId) && chat.isActive\n            }[\"ChatProvider.useCallback[createChat].existingChat\"]);\n            if (existingChat) {\n                return existingChat.id;\n            }\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"ChatProvider.useCallback[createChat]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"ChatProvider.useCallback[createChat]\"]);\n                const newChat = {\n                    id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    participantIds: [\n                        'current-user',\n                        participantId\n                    ],\n                    unreadCount: 0,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isActive: true\n                };\n                setChats({\n                    \"ChatProvider.useCallback[createChat]\": (prev)=>[\n                            ...prev,\n                            newChat\n                        ]\n                }[\"ChatProvider.useCallback[createChat]\"]);\n                showSuccess('Чат создан');\n                return newChat.id;\n            } catch (error) {\n                console.error('Error creating chat:', error);\n                showError('Ошибка при создании чата');\n                return null;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"ChatProvider.useCallback[createChat]\"], [\n        chats,\n        hasActiveMatch,\n        showSuccess,\n        showError\n    ]);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": async (chatId, content, type = 'text')=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"ChatProvider.useCallback[sendMessage]\": (resolve)=>setTimeout(resolve, 300)\n                }[\"ChatProvider.useCallback[sendMessage]\"]);\n                const chat = chats.find({\n                    \"ChatProvider.useCallback[sendMessage].chat\": (c)=>c.id === chatId\n                }[\"ChatProvider.useCallback[sendMessage].chat\"]);\n                if (!chat) {\n                    throw new Error('Chat not found');\n                }\n                const receiverId = chat.participantIds.find({\n                    \"ChatProvider.useCallback[sendMessage].receiverId\": (id)=>id !== 'current-user'\n                }[\"ChatProvider.useCallback[sendMessage].receiverId\"]);\n                if (!receiverId) {\n                    throw new Error('Receiver not found');\n                }\n                const newMessage = {\n                    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    chatId,\n                    senderId: 'current-user',\n                    receiverId,\n                    content,\n                    type,\n                    timestamp: new Date().toISOString(),\n                    isRead: false\n                };\n                setMessages({\n                    \"ChatProvider.useCallback[sendMessage]\": (prev)=>[\n                            ...prev,\n                            newMessage\n                        ]\n                }[\"ChatProvider.useCallback[sendMessage]\"]);\n                // Update chat with last message\n                setChats({\n                    \"ChatProvider.useCallback[sendMessage]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[sendMessage]\": (chat)=>chat.id === chatId ? {\n                                    ...chat,\n                                    lastMessage: newMessage,\n                                    updatedAt: new Date().toISOString()\n                                } : chat\n                        }[\"ChatProvider.useCallback[sendMessage]\"])\n                }[\"ChatProvider.useCallback[sendMessage]\"]);\n                return true;\n            } catch (error) {\n                console.error('Error sending message:', error);\n                showError('Ошибка при отправке сообщения');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        chats,\n        showError\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markAsRead]\": async (chatId)=>{\n            try {\n                // Mark all messages in chat as read\n                setMessages({\n                    \"ChatProvider.useCallback[markAsRead]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[markAsRead]\": (message)=>message.chatId === chatId && message.receiverId === 'current-user' ? {\n                                    ...message,\n                                    isRead: true\n                                } : message\n                        }[\"ChatProvider.useCallback[markAsRead]\"])\n                }[\"ChatProvider.useCallback[markAsRead]\"]);\n                // Reset unread count for chat\n                setChats({\n                    \"ChatProvider.useCallback[markAsRead]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[markAsRead]\": (chat)=>chat.id === chatId ? {\n                                    ...chat,\n                                    unreadCount: 0\n                                } : chat\n                        }[\"ChatProvider.useCallback[markAsRead]\"])\n                }[\"ChatProvider.useCallback[markAsRead]\"]);\n            } catch (error) {\n                console.error('Error marking as read:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markAsRead]\"], []);\n    const getUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getUnreadCount]\": ()=>{\n            return messages.filter({\n                \"ChatProvider.useCallback[getUnreadCount]\": (message)=>message.receiverId === 'current-user' && !message.isRead\n            }[\"ChatProvider.useCallback[getUnreadCount]\"]).length;\n        }\n    }[\"ChatProvider.useCallback[getUnreadCount]\"], [\n        messages\n    ]);\n    const getChatMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getChatMessages]\": (chatId)=>{\n            return messages.filter({\n                \"ChatProvider.useCallback[getChatMessages]\": (message)=>message.chatId === chatId\n            }[\"ChatProvider.useCallback[getChatMessages]\"]).sort({\n                \"ChatProvider.useCallback[getChatMessages]\": (a, b)=>new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n            }[\"ChatProvider.useCallback[getChatMessages]\"]);\n        }\n    }[\"ChatProvider.useCallback[getChatMessages]\"], [\n        messages\n    ]);\n    const deleteChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteChat]\": async (chatId)=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"ChatProvider.useCallback[deleteChat]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"ChatProvider.useCallback[deleteChat]\"]);\n                setChats({\n                    \"ChatProvider.useCallback[deleteChat]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[deleteChat]\": (chat)=>chat.id === chatId ? {\n                                    ...chat,\n                                    isActive: false\n                                } : chat\n                        }[\"ChatProvider.useCallback[deleteChat]\"])\n                }[\"ChatProvider.useCallback[deleteChat]\"]);\n                showSuccess('Чат удален');\n                return true;\n            } catch (error) {\n                console.error('Error deleting chat:', error);\n                showError('Ошибка при удалении чата');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteChat]\"], [\n        showSuccess,\n        showError\n    ]);\n    const value = {\n        chats,\n        messages,\n        isLoading,\n        createChat,\n        sendMessage,\n        markAsRead,\n        getUnreadCount,\n        getChatMessages,\n        deleteChat\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\ChatProvider.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, undefined);\n};\nconst useChat = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (context === undefined) {\n        throw new Error('useChat must be used within a ChatProvider');\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/ChatProvider.tsx\n");

/***/ }),

/***/ "./src/providers/ContactsProvider.tsx":
/*!********************************************!*\
  !*** ./src/providers/ContactsProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactsProvider: () => (/* binding */ ContactsProvider),\n/* harmony export */   useContactsContext: () => (/* binding */ useContactsContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useContacts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useContacts */ \"./src/hooks/useContacts.ts\");\n/* harmony import */ var _hooks_useNotification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useNotification */ \"./src/hooks/useNotification.ts\");\n/* harmony import */ var _components_common_Notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/common/Notification */ \"./components/common/Notification.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nconst ContactsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ContactsProvider = ({ children })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const { contacts, loading, addContact: addContactToStore, removeContact: removeContactFromStore, removeAllContacts: removeAllContactsFromStore } = (0,_hooks_useContacts__WEBPACK_IMPORTED_MODULE_2__.useContacts)();\n    const { notification, showNotification, hideNotification } = (0,_hooks_useNotification__WEBPACK_IMPORTED_MODULE_3__.useNotification)();\n    const addContact = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContactsProvider.useCallback[addContact]\": async (contactData)=>{\n            try {\n                const result = await addContactToStore(contactData);\n                if (result.success) {\n                    showNotification(t('contacts.dialog.success'), 'success');\n                    return true;\n                } else {\n                    showNotification(result.error || t('contacts.dialog.error'), 'error');\n                    return false;\n                }\n            } catch (error) {\n                showNotification(t('contacts.error.default'), 'error');\n                return false;\n            }\n        }\n    }[\"ContactsProvider.useCallback[addContact]\"], [\n        addContactToStore,\n        showNotification,\n        t\n    ]);\n    const removeContact = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContactsProvider.useCallback[removeContact]\": async (contactId)=>{\n            try {\n                const result = await removeContactFromStore(contactId);\n                if (result.success) {\n                    showNotification(t('contacts.dialog.removeSuccess'), 'success');\n                    return true;\n                } else {\n                    showNotification(result.error || t('contacts.error.removeFailed'), 'error');\n                    return false;\n                }\n            } catch (error) {\n                showNotification(t('contacts.error.default'), 'error');\n                return false;\n            }\n        }\n    }[\"ContactsProvider.useCallback[removeContact]\"], [\n        removeContactFromStore,\n        showNotification,\n        t\n    ]);\n    const removeAllContacts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContactsProvider.useCallback[removeAllContacts]\": async ()=>{\n            try {\n                const result = await removeAllContactsFromStore();\n                if (result.success) {\n                    showNotification(t('contacts.dialog.removeAllSuccess'), 'success');\n                    return true;\n                } else {\n                    showNotification(result.error || t('contacts.error.removeAllFailed'), 'error');\n                    return false;\n                }\n            } catch (error) {\n                showNotification(t('contacts.error.default'), 'error');\n                return false;\n            }\n        }\n    }[\"ContactsProvider.useCallback[removeAllContacts]\"], [\n        removeAllContactsFromStore,\n        showNotification,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactsContext.Provider, {\n        value: {\n            addContact,\n            removeContact,\n            removeAllContacts,\n            contacts,\n            loading\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Notification__WEBPACK_IMPORTED_MODULE_4__.Notification, {\n                open: notification.open,\n                message: notification.message,\n                severity: notification.severity,\n                onClose: hideNotification\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\ContactsProvider.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\ContactsProvider.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\nconst useContactsContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ContactsContext);\n    if (context === undefined) {\n        throw new Error('useContactsContext must be used within a ContactsProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/ContactsProvider.tsx\n");

/***/ }),

/***/ "./src/providers/MatchProvider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/MatchProvider.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchProvider: () => (/* binding */ MatchProvider),\n/* harmony export */   useMatch: () => (/* binding */ useMatch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationsProvider */ \"./src/providers/NotificationsProvider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__]);\n_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst MatchContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst MatchProvider = ({ children })=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { showSuccess, showError } = (0,_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    // Load matches from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchProvider.useEffect\": ()=>{\n            const loadMatches = {\n                \"MatchProvider.useEffect.loadMatches\": ()=>{\n                    try {\n                        const storedMatches = localStorage.getItem('likes-love-matches');\n                        if (storedMatches) {\n                            setMatches(JSON.parse(storedMatches));\n                        }\n                    } catch (error) {\n                        console.error('Error loading matches:', error);\n                    }\n                }\n            }[\"MatchProvider.useEffect.loadMatches\"];\n            loadMatches();\n        }\n    }[\"MatchProvider.useEffect\"], []);\n    // Save matches to localStorage whenever matches change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchProvider.useEffect\": ()=>{\n            try {\n                localStorage.setItem('likes-love-matches', JSON.stringify(matches));\n            } catch (error) {\n                console.error('Error saving matches:', error);\n            }\n        }\n    }[\"MatchProvider.useEffect\"], [\n        matches\n    ]);\n    const createMatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[createMatch]\": async (userId)=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"MatchProvider.useCallback[createMatch]\": (resolve)=>setTimeout(resolve, 1000)\n                }[\"MatchProvider.useCallback[createMatch]\"]);\n                const newMatch = {\n                    id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    userId: 'current-user',\n                    matchedUserId: userId,\n                    createdAt: new Date().toISOString(),\n                    isActive: true,\n                    matchedUser: {\n                        id: userId,\n                        name: `User ${userId}`,\n                        photo: undefined,\n                        age: Math.floor(Math.random() * 20) + 18\n                    }\n                };\n                setMatches({\n                    \"MatchProvider.useCallback[createMatch]\": (prev)=>[\n                            ...prev,\n                            newMatch\n                        ]\n                }[\"MatchProvider.useCallback[createMatch]\"]);\n                showSuccess('Новый матч создан!');\n                return true;\n            } catch (error) {\n                console.error('Error creating match:', error);\n                showError('Ошибка при создании матча');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[createMatch]\"], [\n        showSuccess,\n        showError\n    ]);\n    const unmatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[unmatch]\": async (matchId)=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"MatchProvider.useCallback[unmatch]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"MatchProvider.useCallback[unmatch]\"]);\n                setMatches({\n                    \"MatchProvider.useCallback[unmatch]\": (prev)=>prev.map({\n                            \"MatchProvider.useCallback[unmatch]\": (match)=>match.id === matchId ? {\n                                    ...match,\n                                    isActive: false\n                                } : match\n                        }[\"MatchProvider.useCallback[unmatch]\"])\n                }[\"MatchProvider.useCallback[unmatch]\"]);\n                showSuccess('Матч отменен');\n                return true;\n            } catch (error) {\n                console.error('Error unmatching:', error);\n                showError('Ошибка при отмене матча');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[unmatch]\"], [\n        showSuccess,\n        showError\n    ]);\n    const getActiveMatches = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[getActiveMatches]\": async ()=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"MatchProvider.useCallback[getActiveMatches]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"MatchProvider.useCallback[getActiveMatches]\"]);\n                const activeMatches = matches.filter({\n                    \"MatchProvider.useCallback[getActiveMatches].activeMatches\": (match)=>match.isActive\n                }[\"MatchProvider.useCallback[getActiveMatches].activeMatches\"]);\n                return activeMatches;\n            } catch (error) {\n                console.error('Error getting active matches:', error);\n                showError('Ошибка при загрузке матчей');\n                return [];\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[getActiveMatches]\"], [\n        matches,\n        showError\n    ]);\n    const hasActiveMatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[hasActiveMatch]\": (userId)=>{\n            return matches.some({\n                \"MatchProvider.useCallback[hasActiveMatch]\": (match)=>match.matchedUserId === userId && match.isActive\n            }[\"MatchProvider.useCallback[hasActiveMatch]\"]);\n        }\n    }[\"MatchProvider.useCallback[hasActiveMatch]\"], [\n        matches\n    ]);\n    const getMatchById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[getMatchById]\": (matchId)=>{\n            return matches.find({\n                \"MatchProvider.useCallback[getMatchById]\": (match)=>match.id === matchId\n            }[\"MatchProvider.useCallback[getMatchById]\"]);\n        }\n    }[\"MatchProvider.useCallback[getMatchById]\"], [\n        matches\n    ]);\n    const refreshMatches = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[refreshMatches]\": async ()=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call to refresh matches\n                await new Promise({\n                    \"MatchProvider.useCallback[refreshMatches]\": (resolve)=>setTimeout(resolve, 1000)\n                }[\"MatchProvider.useCallback[refreshMatches]\"]);\n                // In real app, this would fetch from API\n                // For now, just update lastInteraction for existing matches\n                setMatches({\n                    \"MatchProvider.useCallback[refreshMatches]\": (prev)=>prev.map({\n                            \"MatchProvider.useCallback[refreshMatches]\": (match)=>({\n                                    ...match,\n                                    lastInteraction: new Date().toISOString()\n                                })\n                        }[\"MatchProvider.useCallback[refreshMatches]\"])\n                }[\"MatchProvider.useCallback[refreshMatches]\"]);\n            } catch (error) {\n                console.error('Error refreshing matches:', error);\n                showError('Ошибка при обновлении матчей');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[refreshMatches]\"], [\n        showError\n    ]);\n    const value = {\n        matches,\n        isLoading,\n        createMatch,\n        unmatch,\n        getActiveMatches,\n        hasActiveMatch,\n        getMatchById,\n        refreshMatches\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MatchContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\MatchProvider.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\nconst useMatch = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MatchContext);\n    if (context === undefined) {\n        throw new Error('useMatch must be used within a MatchProvider');\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/MatchProvider.tsx\n");

/***/ }),

/***/ "./src/providers/NotificationsProvider.tsx":
/*!*************************************************!*\
  !*** ./src/providers/NotificationsProvider.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationsProvider: () => (/* binding */ NotificationsProvider),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst NotificationsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst NotificationsProvider = ({ children })=>{\n    const showNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showNotification]\": (message, type = 'info', options)=>{\n            const toastOptions = {\n                duration: options?.duration || 4000,\n                position: options?.position || 'top-right'\n            };\n            switch(type){\n                case 'success':\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(message, toastOptions);\n                    break;\n                case 'error':\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(message, toastOptions);\n                    break;\n                case 'warning':\n                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(message, {\n                        ...toastOptions,\n                        icon: '⚠️'\n                    });\n                    break;\n                case 'info':\n                default:\n                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(message, {\n                        ...toastOptions,\n                        icon: 'ℹ️'\n                    });\n                    break;\n            }\n        }\n    }[\"NotificationsProvider.useCallback[showNotification]\"], []);\n    const showSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showSuccess]\": (message, options)=>{\n            showNotification(message, 'success', options);\n        }\n    }[\"NotificationsProvider.useCallback[showSuccess]\"], [\n        showNotification\n    ]);\n    const showError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showError]\": (message, options)=>{\n            showNotification(message, 'error', options);\n        }\n    }[\"NotificationsProvider.useCallback[showError]\"], [\n        showNotification\n    ]);\n    const showInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showInfo]\": (message, options)=>{\n            showNotification(message, 'info', options);\n        }\n    }[\"NotificationsProvider.useCallback[showInfo]\"], [\n        showNotification\n    ]);\n    const showWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showWarning]\": (message, options)=>{\n            showNotification(message, 'warning', options);\n        }\n    }[\"NotificationsProvider.useCallback[showWarning]\"], [\n        showNotification\n    ]);\n    const clearAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[clearAll]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].dismiss();\n        }\n    }[\"NotificationsProvider.useCallback[clearAll]\"], []);\n    const value = {\n        showNotification,\n        showSuccess,\n        showError,\n        showInfo,\n        showWarning,\n        clearAll\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationsContext.Provider, {\n        value: value,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                reverseOrder: false,\n                gutter: 8,\n                containerClassName: \"\",\n                containerStyle: {},\n                toastOptions: {\n                    // Default options for all toasts\n                    duration: 4000,\n                    style: {\n                        background: '#363636',\n                        color: '#fff'\n                    },\n                    // Success\n                    success: {\n                        duration: 3000,\n                        style: {\n                            background: '#059669',\n                            color: '#fff'\n                        }\n                    },\n                    // Error\n                    error: {\n                        duration: 5000,\n                        style: {\n                            background: '#DC2626',\n                            color: '#fff'\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\NotificationsProvider.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\NotificationsProvider.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationsContext);\n    if (context === undefined) {\n        throw new Error('useNotifications must be used within a NotificationsProvider');\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/NotificationsProvider.tsx\n");

/***/ }),

/***/ "./src/providers/SettingsProvider.tsx":
/*!********************************************!*\
  !*** ./src/providers/SettingsProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/logger */ \"./src/utils/logger.ts\");\n\n\n\n\nconst defaultSettings = {\n    language: 'ru',\n    notifications: {\n        messages: true,\n        matches: true,\n        likes: true\n    },\n    privacy: {\n        showOnline: true,\n        showLastSeen: true,\n        allowMessages: 'all'\n    },\n    theme: 'system',\n    contactSync: false\n};\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst SETTINGS_STORAGE_KEY = 'likes-love-settings';\nconst SettingsProvider = ({ children })=>{\n    const { i18n } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"SettingsProvider.useState\": ()=>{\n            // Проверка на SSR\n            if (true) {\n                return defaultSettings;\n            }\n            try {\n                const stored = localStorage.getItem(SETTINGS_STORAGE_KEY);\n                return stored ? {\n                    ...defaultSettings,\n                    ...JSON.parse(stored)\n                } : defaultSettings;\n            } catch (error) {\n                _utils_logger__WEBPACK_IMPORTED_MODULE_3__.log.error('Ошибка загрузки настроек из localStorage', 'SettingsProvider', error);\n                return defaultSettings;\n            }\n        }\n    }[\"SettingsProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            // Сохранение только на клиенте\n            if (false) {}\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            // Проверка на инициализацию i18n для предотвращения ошибок при SSG\n            if (i18n && typeof i18n.changeLanguage === 'function') {\n                i18n.changeLanguage(settings.language);\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings.language,\n        i18n\n    ]);\n    const updateSettings = (newSettings)=>{\n        setSettings((prev)=>({\n                ...prev,\n                ...newSettings\n            }));\n    };\n    const resetSettings = ()=>{\n        setSettings(defaultSettings);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: {\n            settings,\n            updateSettings,\n            resetSettings\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\SettingsProvider.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSettings = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/providers/SettingsProvider.tsx\n");

/***/ }),

/***/ "./src/styles/theme-factory.ts":
/*!*************************************!*\
  !*** ./src/styles/theme-factory.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRESET_THEMES: () => (/* binding */ PRESET_THEMES),\n/* harmony export */   createLikesLoveTheme: () => (/* binding */ createLikesLoveTheme),\n/* harmony export */   createThemeByName: () => (/* binding */ createThemeByName)\n/* harmony export */ });\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__);\n\n// Утилиты для работы с цветами\nconst lightenColor = (color, amount)=>{\n    const hex = color.replace('#', '');\n    const num = parseInt(hex, 16);\n    const r = Math.min(255, Math.floor((num >> 16) + (255 - (num >> 16)) * amount));\n    const g = Math.min(255, Math.floor((num >> 8 & 0x00FF) + (255 - (num >> 8 & 0x00FF)) * amount));\n    const b = Math.min(255, Math.floor((num & 0x0000FF) + (255 - (num & 0x0000FF)) * amount));\n    return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;\n};\nconst darkenColor = (color, amount)=>{\n    const hex = color.replace('#', '');\n    const num = parseInt(hex, 16);\n    const r = Math.max(0, Math.floor((num >> 16) * (1 - amount)));\n    const g = Math.max(0, Math.floor((num >> 8 & 0x00FF) * (1 - amount)));\n    const b = Math.max(0, Math.floor((num & 0x0000FF) * (1 - amount)));\n    return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;\n};\n// Функция для определения контрастного цвета текста\nconst getContrastText = (backgroundColor)=>{\n    const hex = backgroundColor.replace('#', '');\n    const r = parseInt(hex.substr(0, 2), 16);\n    const g = parseInt(hex.substr(2, 2), 16);\n    const b = parseInt(hex.substr(4, 2), 16);\n    // Вычисляем яркость по формуле W3C\n    const brightness = (r * 299 + g * 587 + b * 114) / 1000;\n    // Если цвет светлый, используем темный текст, иначе светлый\n    return brightness > 128 ? '#2d3748' : '#ffffff';\n};\n// Создание темы на основе конфигурации\nconst createLikesLoveTheme = (config, customizations)=>{\n    const primaryColor = customizations?.primaryColor || config.primaryColor;\n    const secondaryColor = customizations?.secondaryColor || config.secondaryColor;\n    const themeOptions = {\n        palette: {\n            mode: config.id === 'dark' ? 'dark' : 'light',\n            primary: {\n                main: primaryColor,\n                light: lightenColor(primaryColor, 0.3),\n                dark: darkenColor(primaryColor, 0.3),\n                contrastText: getContrastText(primaryColor)\n            },\n            secondary: {\n                main: secondaryColor,\n                light: lightenColor(secondaryColor, 0.3),\n                dark: darkenColor(secondaryColor, 0.3),\n                contrastText: getContrastText(secondaryColor)\n            },\n            background: {\n                default: config.colors.background,\n                paper: config.colors.surface\n            },\n            text: {\n                primary: config.colors.text.primary,\n                secondary: config.colors.text.secondary\n            },\n            gradient: {\n                primary: config.gradients.primary,\n                secondary: config.gradients.secondary,\n                accent: config.gradients.accent\n            },\n            success: {\n                main: '#4caf50',\n                light: '#81c784',\n                dark: '#388e3c'\n            },\n            error: {\n                main: '#f44336',\n                light: '#e57373',\n                dark: '#d32f2f'\n            },\n            warning: {\n                main: '#ff9800',\n                light: '#ffb74d',\n                dark: '#f57c00'\n            },\n            info: {\n                main: '#2196f3',\n                light: '#64b5f6',\n                dark: '#1976d2'\n            }\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            h1: {\n                fontSize: config.typography.fontSizes.xxl,\n                fontWeight: config.typography.fontWeights.bold,\n                lineHeight: 1.3,\n                letterSpacing: '-0.02em',\n                marginBottom: '1.5rem'\n            },\n            h2: {\n                fontSize: config.typography.fontSizes.xl,\n                fontWeight: config.typography.fontWeights.bold,\n                lineHeight: 1.4,\n                letterSpacing: '-0.01em',\n                marginBottom: '1.25rem'\n            },\n            h3: {\n                fontSize: config.typography.fontSizes.lg,\n                fontWeight: config.typography.fontWeights.semibold,\n                lineHeight: 1.5,\n                marginBottom: '1rem'\n            },\n            h4: {\n                fontSize: config.typography.fontSizes.md,\n                fontWeight: config.typography.fontWeights.semibold,\n                lineHeight: 1.5,\n                marginBottom: '0.875rem'\n            },\n            h5: {\n                fontSize: config.typography.fontSizes.sm,\n                fontWeight: config.typography.fontWeights.medium,\n                lineHeight: 1.6,\n                marginBottom: '0.75rem'\n            },\n            h6: {\n                fontSize: config.typography.fontSizes.xs,\n                fontWeight: config.typography.fontWeights.medium,\n                lineHeight: 1.6,\n                marginBottom: '0.625rem'\n            },\n            body1: {\n                fontSize: config.typography.fontSizes.md,\n                fontWeight: config.typography.fontWeights.normal,\n                lineHeight: 1.7,\n                marginBottom: '1rem'\n            },\n            body2: {\n                fontSize: config.typography.fontSizes.sm,\n                fontWeight: config.typography.fontWeights.normal,\n                lineHeight: 1.7,\n                marginBottom: '0.875rem'\n            },\n            button: {\n                fontSize: config.typography.fontSizes.sm,\n                fontWeight: config.typography.fontWeights.semibold,\n                textTransform: 'none',\n                letterSpacing: '0.02em',\n                lineHeight: 1.5\n            },\n            caption: {\n                fontSize: config.typography.fontSizes.xs,\n                fontWeight: config.typography.fontWeights.normal,\n                lineHeight: 1.5\n            }\n        },\n        spacing: config.spacing.sm,\n        shape: {\n            borderRadius: config.borderRadius.md\n        },\n        components: {\n            MuiButton: {\n                styleOverrides: {\n                    root: {\n                        borderRadius: config.borderRadius.md,\n                        textTransform: 'none',\n                        fontWeight: config.typography.fontWeights.semibold,\n                        padding: `${config.spacing.md}px ${config.spacing.lg}px`,\n                        fontSize: config.typography.fontSizes.sm,\n                        minHeight: '48px',\n                        boxShadow: 'none',\n                        transition: 'all 0.2s ease-in-out',\n                        '&:hover': {\n                            boxShadow: config.shadows.md,\n                            transform: 'translateY(-1px)'\n                        }\n                    },\n                    contained: {\n                        background: config.gradients.primary,\n                        color: '#ffffff !important',\n                        fontWeight: 600,\n                        fontSize: config.typography.fontSizes.sm,\n                        textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n                        '&:hover': {\n                            background: config.gradients.primary,\n                            filter: 'brightness(1.1)',\n                            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n                            color: '#ffffff !important'\n                        }\n                    },\n                    outlined: {\n                        borderWidth: 2,\n                        color: primaryColor,\n                        borderColor: primaryColor,\n                        backgroundColor: 'rgba(255,255,255,0.9)',\n                        fontSize: config.typography.fontSizes.sm,\n                        '&:hover': {\n                            borderWidth: 2,\n                            backgroundColor: primaryColor,\n                            color: '#ffffff !important'\n                        }\n                    }\n                }\n            },\n            MuiCard: {\n                styleOverrides: {\n                    root: {\n                        borderRadius: config.borderRadius.lg,\n                        boxShadow: config.shadows.md,\n                        border: `1px solid ${config.colors.border}`,\n                        transition: 'all 0.2s ease-in-out',\n                        '&:hover': {\n                            boxShadow: config.shadows.lg,\n                            transform: 'translateY(-2px)'\n                        }\n                    }\n                }\n            },\n            MuiTextField: {\n                styleOverrides: {\n                    root: {\n                        '& .MuiOutlinedInput-root': {\n                            borderRadius: config.borderRadius.md,\n                            fontSize: config.typography.fontSizes.sm,\n                            minHeight: '56px',\n                            '& input': {\n                                padding: `${config.spacing.md}px ${config.spacing.md}px`,\n                                fontSize: config.typography.fontSizes.sm,\n                                lineHeight: 1.5\n                            },\n                            '&:hover .MuiOutlinedInput-notchedOutline': {\n                                borderColor: primaryColor\n                            },\n                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                                borderColor: primaryColor,\n                                borderWidth: 2\n                            }\n                        },\n                        '& .MuiInputLabel-root': {\n                            fontSize: config.typography.fontSizes.sm,\n                            lineHeight: 1.5\n                        }\n                    }\n                }\n            },\n            MuiChip: {\n                styleOverrides: {\n                    root: {\n                        borderRadius: config.borderRadius.full,\n                        fontWeight: config.typography.fontWeights.medium\n                    },\n                    filled: {\n                        background: config.gradients.accent,\n                        color: '#ffffff'\n                    }\n                }\n            },\n            MuiDialog: {\n                styleOverrides: {\n                    paper: {\n                        borderRadius: config.borderRadius.lg,\n                        boxShadow: config.shadows.lg\n                    }\n                }\n            },\n            MuiAppBar: {\n                styleOverrides: {\n                    root: {\n                        background: config.gradients.primary,\n                        boxShadow: config.shadows.md\n                    }\n                }\n            },\n            MuiFab: {\n                styleOverrides: {\n                    root: {\n                        background: config.gradients.primary,\n                        boxShadow: config.shadows.md,\n                        '&:hover': {\n                            background: config.gradients.primary,\n                            filter: 'brightness(1.1)',\n                            boxShadow: config.shadows.lg\n                        }\n                    }\n                }\n            }\n        },\n        custom: {\n            gradients: {\n                primary: config.gradients.primary,\n                secondary: config.gradients.secondary,\n                accent: config.gradients.accent\n            },\n            shadows: {\n                card: config.shadows.md,\n                button: config.shadows.sm,\n                modal: config.shadows.lg\n            },\n            animations: {\n                transition: 'all 0.2s ease-in-out',\n                hover: 'transform 0.2s ease-in-out'\n            },\n            dating: {\n                likeColor: '#4caf50',\n                passColor: '#f44336',\n                superLikeColor: '#2196f3',\n                matchColor: '#ff9800'\n            }\n        }\n    };\n    return (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__.createTheme)(themeOptions);\n};\n// Предустановленные темы для быстрого доступа\nconst PRESET_THEMES = {\n    likesLove: '#ff6b9d',\n    romantic: '#e91e63',\n    elegant: '#673ab7',\n    modern: '#00bcd4',\n    dark: '#424242'\n};\n// Создание темы по названию\nconst createThemeByName = (themeName)=>{\n    const baseConfig = {\n        id: themeName,\n        name: themeName,\n        displayName: themeName,\n        primaryColor: PRESET_THEMES[themeName],\n        secondaryColor: '#4ecdc4',\n        gradients: {\n            primary: `linear-gradient(135deg, ${PRESET_THEMES[themeName]} 0%, #4ecdc4 100%)`,\n            secondary: 'linear-gradient(135deg, #4ecdc4 0%, #45b7aa 100%)',\n            accent: `linear-gradient(135deg, ${PRESET_THEMES[themeName]} 0%, ${lightenColor(PRESET_THEMES[themeName], 0.2)} 100%)`\n        },\n        colors: {\n            background: themeName === 'dark' ? '#121212' : '#ffffff',\n            surface: themeName === 'dark' ? '#1e1e1e' : '#f8f9fa',\n            text: {\n                primary: themeName === 'dark' ? '#ffffff' : '#2d3748',\n                secondary: themeName === 'dark' ? '#b0b0b0' : '#718096',\n                accent: PRESET_THEMES[themeName]\n            },\n            border: themeName === 'dark' ? '#333333' : '#e2e8f0',\n            shadow: themeName === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',\n            fontSizes: {\n                xs: 12,\n                sm: 14,\n                md: 16,\n                lg: 18,\n                xl: 24,\n                xxl: 32\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\n            md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1)',\n            lg: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'\n        }\n    };\n    return createLikesLoveTheme(baseConfig);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/theme-factory.ts\n");

/***/ }),

/***/ "./src/utils/contacts.ts":
/*!*******************************!*\
  !*** ./src/utils/contacts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToContacts: () => (/* binding */ addToContacts),\n/* harmony export */   checkContactPermission: () => (/* binding */ checkContactPermission),\n/* harmony export */   getStoredContacts: () => (/* binding */ getStoredContacts),\n/* harmony export */   removeContacts: () => (/* binding */ removeContacts),\n/* harmony export */   storeContact: () => (/* binding */ storeContact)\n/* harmony export */ });\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logger */ \"./src/utils/logger.ts\");\n// Добавляем типы для Contacts API\n\nconst CONTACTS_STORAGE_KEY = 'likes-love-contacts';\nasync function checkContactPermission() {\n    try {\n        // Check if auto contacts is enabled in settings\n        const autoContactsEnabled = localStorage.getItem('autoContactsEnabled');\n        if (autoContactsEnabled === 'false') {\n            return false;\n        }\n        // Check if the Permissions API is available\n        if (!navigator.permissions) {\n            return false;\n        }\n        // Query for contacts permission\n        const result = await navigator.permissions.query({\n            name: 'contacts'\n        });\n        return result.state === 'granted';\n    } catch (error) {\n        // If the permission query fails (e.g., not supported in the country/browser)\n        return false;\n    }\n}\nasync function getStoredContacts() {\n    const contactsJson = localStorage.getItem(CONTACTS_STORAGE_KEY);\n    return contactsJson ? JSON.parse(contactsJson) : [];\n}\nasync function storeContact(contact) {\n    const contacts = await getStoredContacts();\n    contacts.push(contact);\n    localStorage.setItem(CONTACTS_STORAGE_KEY, JSON.stringify(contacts));\n}\nasync function removeContacts(contactIds) {\n    if (!contactIds) {\n        // Remove all contacts\n        localStorage.removeItem(CONTACTS_STORAGE_KEY);\n        return;\n    }\n    const contacts = await getStoredContacts();\n    const filteredContacts = contacts.filter((contact)=>!contactIds.includes(contact.id));\n    localStorage.setItem(CONTACTS_STORAGE_KEY, JSON.stringify(filteredContacts));\n}\nasync function addToContacts(contactShare, skipPermissionCheck = false) {\n    try {\n        // Check permission unless explicitly skipped\n        if (!skipPermissionCheck) {\n            const hasPermission = await checkContactPermission();\n            if (!hasPermission) {\n                return false;\n            }\n        }\n        // Create Contact object with ID and timestamp\n        const contact = {\n            id: `contact-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            name: contactShare.name,\n            phoneNumbers: contactShare.phoneNumbers || [],\n            isIncognito: contactShare.isIncognito,\n            profileUrl: contactShare.profileUrl,\n            addedAt: new Date().toISOString()\n        };\n        // Store contact in local storage\n        await storeContact(contact);\n        // Try using the modern Contacts API first\n        if ('contacts' in navigator && navigator.contacts) {\n            const properties = await navigator.contacts.getProperties();\n            const supportedProps = [\n                'name',\n                'tel',\n                'url'\n            ];\n            // Check if the necessary properties are supported\n            if (!supportedProps.every((prop)=>properties.includes(prop))) {\n                throw new Error('Required contact properties not supported');\n            }\n            const contacts = await navigator.contacts.select(supportedProps);\n            if (!contacts.length) {\n                return false;\n            }\n            const contactToUpdate = contacts[0];\n            await contactToUpdate.update({\n                name: [\n                    contactShare.name\n                ],\n                tel: contactShare.phoneNumbers || [],\n                url: contactShare.isIncognito ? [] : [\n                    contactShare.profileUrl\n                ]\n            });\n            return true;\n        }\n        // Fall back to vCard download if Contacts API is not available\n        const vCardLines = [\n            'BEGIN:VCARD',\n            'VERSION:3.0',\n            `FN:${contactShare.name}`,\n            ...(contactShare.phoneNumbers || []).map((phone)=>`TEL;TYPE=CELL:${phone}`)\n        ];\n        // Add profile URL only for non-incognito contacts\n        if (!contactShare.isIncognito) {\n            vCardLines.push(`URL:${contactShare.profileUrl}`);\n        }\n        vCardLines.push('END:VCARD');\n        const vCard = vCardLines.join('\\n');\n        const blob = new Blob([\n            vCard\n        ], {\n            type: 'text/vcard'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${contactShare.name}.vcf`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        return true;\n    } catch (error) {\n        // Используем профессиональный логгер вместо console.error\n        (0,_logger__WEBPACK_IMPORTED_MODULE_0__.logError)('Failed to add contact to device', error, 'ContactsAPI', {\n            contactName: contactShare.name,\n            isIncognito: contactShare.isIncognito,\n            hasPhoneNumbers: Boolean(contactShare.phoneNumbers?.length),\n            skipPermissionCheck\n        });\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/contacts.ts\n");

/***/ }),

/***/ "./src/utils/createEmotionCache.ts":
/*!*****************************************!*\
  !*** ./src/utils/createEmotionCache.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction createEmotionCache() {\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: 'css',\n        prepend: true\n    });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvY3JlYXRlRW1vdGlvbkNhY2hlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF5QztBQUVsQyxTQUFTQztJQUNkLE9BQU9ELDBEQUFXQSxDQUFDO1FBQUVFLEtBQUs7UUFBT0MsU0FBUztJQUFLO0FBQ2pEO0FBRUEsaUVBQWVGLGtCQUFrQkEsRUFBQyIsInNvdXJjZXMiOlsiRjpcXEN1cnNvclxcbGwuY29tXFxpbnN0YWxsXFx3ZWJcXHNyY1xcdXRpbHNcXGNyZWF0ZUVtb3Rpb25DYWNoZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlQ2FjaGUgZnJvbSAnQGVtb3Rpb24vY2FjaGUnO1xuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRW1vdGlvbkNhY2hlKCkge1xuICByZXR1cm4gY3JlYXRlQ2FjaGUoeyBrZXk6ICdjc3MnLCBwcmVwZW5kOiB0cnVlIH0pO1xufVxuXG5leHBvcnQgZGVmYXVsdCBjcmVhdGVFbW90aW9uQ2FjaGU7XG4iXSwibmFtZXMiOlsiY3JlYXRlQ2FjaGUiLCJjcmVhdGVFbW90aW9uQ2FjaGUiLCJrZXkiLCJwcmVwZW5kIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/utils/createEmotionCache.ts\n");

/***/ }),

/***/ "./src/utils/logger.ts":
/*!*****************************!*\
  !*** ./src/utils/logger.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   logDebug: () => (/* binding */ logDebug),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarn: () => (/* binding */ logWarn),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/**\n * Professional logging utility for web application\n * Provides structured logging with different levels and proper error handling\n */ const LogLevel = {\n    ERROR: 'error',\n    WARN: 'warn',\n    INFO: 'info',\n    DEBUG: 'debug'\n};\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === 'development';\n        this.isTest = \"development\" === 'test';\n    }\n    createLogEntry(level, message, context, error, metadata) {\n        return {\n            level,\n            message,\n            timestamp: new Date().toISOString(),\n            context,\n            error,\n            metadata\n        };\n    }\n    shouldLog(level) {\n        // В тестах логируем только критические ошибки\n        if (this.isTest) {\n            return level === LogLevel.ERROR && this.isDevelopment;\n        }\n        return true;\n    }\n    formatMessage(entry) {\n        const { level, message, timestamp, context, error, metadata } = entry;\n        let formattedMessage = `[${timestamp}] ${level.toUpperCase()}`;\n        if (context) {\n            formattedMessage += ` [${context}]`;\n        }\n        formattedMessage += `: ${message}`;\n        if (error) {\n            formattedMessage += `\\nError: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formattedMessage += `\\nStack: ${error.stack}`;\n            }\n        }\n        if (metadata && Object.keys(metadata).length > 0) {\n            formattedMessage += `\\nMetadata: ${JSON.stringify(metadata, null, 2)}`;\n        }\n        return formattedMessage;\n    }\n    log(entry) {\n        if (!this.shouldLog(entry.level)) {\n            return;\n        }\n        const formattedMessage = this.formatMessage(entry);\n        switch(entry.level){\n            case LogLevel.ERROR:\n                console.error(formattedMessage);\n                break;\n            case LogLevel.WARN:\n                console.warn(formattedMessage);\n                break;\n            case LogLevel.INFO:\n                console.info(formattedMessage);\n                break;\n            case LogLevel.DEBUG:\n                console.debug(formattedMessage);\n                break;\n        }\n    }\n    error(message, error, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.ERROR, message, context, error, metadata);\n        this.log(entry);\n    }\n    warn(message, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.WARN, message, context, undefined, metadata);\n        this.log(entry);\n    }\n    info(message, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.INFO, message, context, undefined, metadata);\n        this.log(entry);\n    }\n    debug(message, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.DEBUG, message, context, undefined, metadata);\n        this.log(entry);\n    }\n    // Специальный метод для тестов - не выводит в консоль\n    logForTest(level, message, error) {\n        return this.createLogEntry(level, message, 'TEST', error);\n    }\n}\n// Singleton instance\nconst logger = new Logger();\n// Convenience exports\nconst logError = (message, error, context, metadata)=>logger.error(message, error, context, metadata);\nconst logWarn = (message, context, metadata)=>logger.warn(message, context, metadata);\nconst logInfo = (message, context, metadata)=>logger.info(message, context, metadata);\nconst logDebug = (message, context, metadata)=>logger.debug(message, context, metadata);\n// Enterprise logging interface\nconst log = {\n    error: (message, context, error)=>logger.error(message, error, context),\n    warn: (message, context, metadata)=>logger.warn(message, context, metadata),\n    info: (message, context, metadata)=>logger.info(message, context, metadata),\n    debug: (message, context, metadata)=>logger.debug(message, context, metadata),\n    // Context-specific loggers\n    auth: (message, data)=>logger.info(message, 'Auth', data),\n    api: (message, data)=>logger.debug(message, 'API', data),\n    ui: (message, data)=>logger.debug(message, 'UI', data),\n    performance: (message, data)=>logger.info(message, 'Performance', data),\n    security: (message, data)=>logger.warn(message, 'Security', data),\n    userAction: (action, data)=>logger.info(`User action: ${action}`, 'UserAction', data),\n    apiError: (endpoint, error, requestData)=>{\n        logger.error(`API Error: ${endpoint}`, error instanceof Error ? error : new Error(String(error)), 'API', {\n            endpoint,\n            requestData,\n            timestamp: Date.now()\n        });\n    },\n    performanceMetric: (metric, value, unit = 'ms')=>{\n        logger.info(`Performance metric: ${metric}`, 'Performance', {\n            metric,\n            value,\n            unit,\n            timestamp: Date.now()\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/logger.ts\n");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = import("@emotion/server/create-instance");;

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useMediaQuery":
/*!********************************************!*\
  !*** external "@mui/system/useMediaQuery" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useMediaQuery");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/utils":
/*!*****************************!*\
  !*** external "@mui/utils" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("@mui/utils");

/***/ }),

/***/ "@mui/utils/appendOwnerState":
/*!**********************************************!*\
  !*** external "@mui/utils/appendOwnerState" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/appendOwnerState");

/***/ }),

/***/ "@mui/utils/capitalize":
/*!****************************************!*\
  !*** external "@mui/utils/capitalize" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/capitalize");

/***/ }),

/***/ "@mui/utils/chainPropTypes":
/*!********************************************!*\
  !*** external "@mui/utils/chainPropTypes" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/chainPropTypes");

/***/ }),

/***/ "@mui/utils/composeClasses":
/*!********************************************!*\
  !*** external "@mui/utils/composeClasses" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/composeClasses");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/elementAcceptingRef":
/*!*************************************************!*\
  !*** external "@mui/utils/elementAcceptingRef" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementAcceptingRef");

/***/ }),

/***/ "@mui/utils/elementTypeAcceptingRef":
/*!*****************************************************!*\
  !*** external "@mui/utils/elementTypeAcceptingRef" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementTypeAcceptingRef");

/***/ }),

/***/ "@mui/utils/extractEventHandlers":
/*!**************************************************!*\
  !*** external "@mui/utils/extractEventHandlers" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/extractEventHandlers");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "@mui/utils/generateUtilityClasses":
/*!****************************************************!*\
  !*** external "@mui/utils/generateUtilityClasses" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClasses");

/***/ }),

/***/ "@mui/utils/getReactElementRef":
/*!************************************************!*\
  !*** external "@mui/utils/getReactElementRef" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getReactElementRef");

/***/ }),

/***/ "@mui/utils/integerPropType":
/*!*********************************************!*\
  !*** external "@mui/utils/integerPropType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/integerPropType");

/***/ }),

/***/ "@mui/utils/mergeSlotProps":
/*!********************************************!*\
  !*** external "@mui/utils/mergeSlotProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/mergeSlotProps");

/***/ }),

/***/ "@mui/utils/refType":
/*!*************************************!*\
  !*** external "@mui/utils/refType" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/refType");

/***/ }),

/***/ "@mui/utils/resolveComponentProps":
/*!***************************************************!*\
  !*** external "@mui/utils/resolveComponentProps" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveComponentProps");

/***/ }),

/***/ "@mui/utils/useEventCallback":
/*!**********************************************!*\
  !*** external "@mui/utils/useEventCallback" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEventCallback");

/***/ }),

/***/ "@mui/utils/useForkRef":
/*!****************************************!*\
  !*** external "@mui/utils/useForkRef" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useForkRef");

/***/ }),

/***/ "@mui/utils/useIsFocusVisible":
/*!***********************************************!*\
  !*** external "@mui/utils/useIsFocusVisible" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useIsFocusVisible");

/***/ }),

/***/ "@mui/utils/useSlotProps":
/*!******************************************!*\
  !*** external "@mui/utils/useSlotProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useSlotProps");

/***/ }),

/***/ "@mui/utils/useTimeout":
/*!****************************************!*\
  !*** external "@mui/utils/useTimeout" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useTimeout");

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "@tanstack/react-query-devtools":
/*!*************************************************!*\
  !*** external "@tanstack/react-query-devtools" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query-devtools");;

/***/ }),

/***/ "__barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js":
/*!****************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport default from dynamic */ _Alert__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Snackbar: () => (/* reexport default from dynamic */ _Snackbar__WEBPACK_IMPORTED_MODULE_1___default.a)\n/* harmony export */ });\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Alert */ \"./node_modules/@mui/material/node/Alert/index.js\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Alert__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Snackbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Snackbar */ \"./node_modules/@mui/material/node/Snackbar/index.js\");\n/* harmony import */ var _Snackbar__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Snackbar__WEBPACK_IMPORTED_MODULE_1__);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydCxTbmFja2JhciE9IS4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUMwQyIsInNvdXJjZXMiOlsiRjpcXEN1cnNvclxcbGwuY29tXFxpbnN0YWxsXFx3ZWJcXG5vZGVfbW9kdWxlc1xcQG11aVxcbWF0ZXJpYWxcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBbGVydCB9IGZyb20gXCIuL0FsZXJ0XCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU25hY2tiYXIgfSBmcm9tIFwiLi9TbmFja2JhclwiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=CssBaseline!=!./node_modules/@mui/material/index.js":
/*!*************************************************************************************!*\
  !*** __barrel_optimize__?names=CssBaseline!=!./node_modules/@mui/material/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CssBaseline: () => (/* reexport safe */ _CssBaseline__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _CssBaseline__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CssBaseline */ "./node_modules/@mui/material/node/CssBaseline/index.js");



/***/ }),

/***/ "__barrel_optimize__?names=useMediaQuery!=!./node_modules/@mui/material/index.js":
/*!***************************************************************************************!*\
  !*** __barrel_optimize__?names=useMediaQuery!=!./node_modules/@mui/material/index.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useMediaQuery: () => (/* reexport safe */ _useMediaQuery__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _useMediaQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useMediaQuery */ "./node_modules/@mui/material/node/useMediaQuery/index.js");



/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("clsx");

/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("react-hot-toast");;

/***/ }),

/***/ "react-transition-group":
/*!*****************************************!*\
  !*** external "react-transition-group" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("react-transition-group");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("./webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui","vendor-chunks/@babel"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();