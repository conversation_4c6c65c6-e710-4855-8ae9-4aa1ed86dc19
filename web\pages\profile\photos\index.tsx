import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardActions,
  IconButton,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Tooltip,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Add as AddIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Verified as VerifiedIcon,
  Pending as PendingIcon,
  Error as ErrorIcon,
  DragIndicator,
  PhotoCamera
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';
import { 
  getProfilePhotos, 
  deleteProfilePhoto, 
  setMainProfilePhoto, 
  reorderProfilePhotos 
} from '../../../src/services/profileService';
import { ProfilePhoto } from '../../../src/types/profile.types';

const PhotosGalleryPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [photos, setPhotos] = useState<ProfilePhoto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [photoToDelete, setPhotoToDelete] = useState<string | null>(null);
  const [reordering, setReordering] = useState(false);

  const maxPhotos = 9;

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadPhotos();
  }, [user, router]);

  const loadPhotos = async () => {
    try {
      setLoading(true);
      setError(null);
      const photosData = await getProfilePhotos();
      setPhotos(photosData.sort((a, b) => a.order - b.order));
    } catch (err: any) {
      setError('Ошибка загрузки фотографий');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePhoto = async () => {
    if (!photoToDelete) return;

    try {
      await deleteProfilePhoto(photoToDelete);
      setPhotos(prev => prev.filter(photo => photo.id !== photoToDelete));
      
      // Если удаляем главную фото и есть другие, делаем первую главной
      const deletedPhoto = photos.find(p => p.id === photoToDelete);
      if (deletedPhoto?.isMain && photos.length > 1) {
        const remainingPhotos = photos.filter(p => p.id !== photoToDelete);
        if (remainingPhotos.length > 0) {
          await handleSetMainPhoto(remainingPhotos[0].id);
        }
      }
    } catch (err: any) {
      setError('Ошибка удаления фотографии');
    }

    setDeleteDialogOpen(false);
    setPhotoToDelete(null);
  };

  const handleSetMainPhoto = async (photoId: string) => {
    try {
      await setMainProfilePhoto(photoId);
      setPhotos(prev => prev.map(photo => ({
        ...photo,
        isMain: photo.id === photoId
      })));
    } catch (err: any) {
      setError('Ошибка установки главной фотографии');
    }
  };

  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;

    const items = Array.from(photos);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Обновляем порядок локально
    const reorderedPhotos = items.map((photo, index) => ({
      ...photo,
      order: index
    }));
    setPhotos(reorderedPhotos);

    try {
      setReordering(true);
      await reorderProfilePhotos(reorderedPhotos.map(p => p.id));
    } catch (err: any) {
      setError('Ошибка изменения порядка фотографий');
      // Возвращаем исходный порядок при ошибке
      loadPhotos();
    } finally {
      setReordering(false);
    }
  };

  const getModerationStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <VerifiedIcon color="success" />;
      case 'pending':
        return <PendingIcon color="warning" />;
      case 'rejected':
        return <ErrorIcon color="error" />;
      default:
        return null;
    }
  };

  const getModerationStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Одобрено';
      case 'pending':
        return 'На модерации';
      case 'rejected':
        return 'Отклонено';
      default:
        return 'Неизвестно';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Мои фотографии - Likes & Love</title>
        <meta 
          name="description" 
          content="Управляйте фотографиями вашего профиля в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/profile/photos" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    Мои фотографии
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {photos.length} из {maxPhotos} фотографий
                  </Typography>
                </Box>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => router.push('/profile/photos/upload')}
                  disabled={photos.length >= maxPhotos}
                  sx={{
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                  }}
                >
                  Добавить фото
                </Button>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка фотографий...
                  </Typography>
                </Box>
              ) : photos.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <PhotoCamera sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    У вас пока нет фотографий
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Добавьте фотографии, чтобы сделать ваш профиль более привлекательным
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => router.push('/profile/photos/upload')}
                    size="large"
                  >
                    Добавить первую фотографию
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Tips */}
                    <Alert severity="info" sx={{ mb: 3 }}>
                      <Typography variant="body2">
                        💡 <strong>Советы:</strong> Перетаскивайте фотографии для изменения порядка. 
                        Нажмите на звездочку, чтобы сделать фото главным.
                      </Typography>
                    </Alert>

                    {/* Photos grid with drag and drop */}
                    <DragDropContext onDragEnd={handleDragEnd}>
                      <Droppable droppableId="photos" direction={isMobile ? "vertical" : "horizontal"}>
                        {(provided) => (
                          <Grid 
                            container 
                            spacing={2}
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                          >
                            {photos.map((photo, index) => (
                              <Draggable key={photo.id} draggableId={photo.id} index={index}>
                                {(provided, snapshot) => (
                                  <Grid 
                                    item 
                                    xs={12} 
                                    sm={6} 
                                    md={4} 
                                    lg={3}
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                  >
                                    <Card 
                                      sx={{ 
                                        position: 'relative',
                                        transform: snapshot.isDragging ? 'rotate(5deg)' : 'none',
                                        boxShadow: snapshot.isDragging ? theme.shadows[8] : theme.shadows[2],
                                        transition: 'all 0.2s ease'
                                      }}
                                    >
                                      {/* Drag handle */}
                                      <Box
                                        {...provided.dragHandleProps}
                                        sx={{
                                          position: 'absolute',
                                          top: 8,
                                          right: 8,
                                          backgroundColor: 'rgba(0,0,0,0.5)',
                                          borderRadius: 1,
                                          p: 0.5,
                                          cursor: 'grab',
                                          '&:active': { cursor: 'grabbing' }
                                        }}
                                      >
                                        <DragIndicator sx={{ color: 'white', fontSize: 20 }} />
                                      </Box>

                                      <CardMedia
                                        component="img"
                                        height="250"
                                        image={photo.url}
                                        alt={`Фото ${index + 1}`}
                                        sx={{ objectFit: 'cover' }}
                                      />

                                      {/* Main photo indicator */}
                                      <IconButton
                                        sx={{
                                          position: 'absolute',
                                          top: 8,
                                          left: 8,
                                          backgroundColor: 'rgba(0,0,0,0.5)',
                                          color: photo.isMain ? 'gold' : 'white',
                                          '&:hover': {
                                            backgroundColor: 'rgba(0,0,0,0.7)'
                                          }
                                        }}
                                        onClick={() => handleSetMainPhoto(photo.id)}
                                      >
                                        {photo.isMain ? <StarIcon /> : <StarBorderIcon />}
                                      </IconButton>

                                      {/* Moderation status */}
                                      <Box sx={{
                                        position: 'absolute',
                                        bottom: 8,
                                        left: 8
                                      }}>
                                        <Tooltip title={getModerationStatusText(photo.moderationStatus)}>
                                          <Chip
                                            icon={getModerationStatusIcon(photo.moderationStatus)}
                                            label={getModerationStatusText(photo.moderationStatus)}
                                            size="small"
                                            color={
                                              photo.moderationStatus === 'approved' ? 'success' :
                                              photo.moderationStatus === 'pending' ? 'warning' : 'error'
                                            }
                                            sx={{ backgroundColor: 'rgba(255,255,255,0.9)' }}
                                          />
                                        </Tooltip>
                                      </Box>

                                      <CardActions sx={{ justifyContent: 'space-between', p: 1 }}>
                                        <Typography variant="caption" color="text.secondary">
                                          {photo.isMain ? 'Главная фотография' : `Фото ${index + 1}`}
                                        </Typography>
                                        <IconButton
                                          size="small"
                                          onClick={() => {
                                            setPhotoToDelete(photo.id);
                                            setDeleteDialogOpen(true);
                                          }}
                                          color="error"
                                        >
                                          <DeleteIcon />
                                        </IconButton>
                                      </CardActions>
                                    </Card>
                                  </Grid>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </Grid>
                        )}
                      </Droppable>
                    </DragDropContext>

                    {reordering && (
                      <Box sx={{ textAlign: 'center', mt: 2 }}>
                        <CircularProgress size={24} />
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          Сохранение порядка...
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}
            </Paper>
          </Box>
        </Container>

        {/* Delete confirmation dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Удалить фотографию?</DialogTitle>
          <DialogContent>
            <Typography>
              Вы уверены, что хотите удалить эту фотографию? Это действие нельзя отменить.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              Отмена
            </Button>
            <Button 
              onClick={handleDeletePhoto}
              color="error"
              variant="contained"
            >
              Удалить
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default PhotosGalleryPage;
