"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/settings",{

/***/ "(pages-dir-browser)/./pages/settings/index.tsx":
/*!**********************************!*\
  !*** ./pages/settings/index.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,Container,Divider,Fade,FormControl,Grid,InputLabel,List,ListItem,ListItemIcon,ListItemSecondaryAction,ListItemText,MenuItem,Paper,Select,Switch,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,Container,Divider,Fade,FormControl,Grid,InputLabel,List,ListItem,ListItemIcon,ListItemSecondaryAction,ListItemText,MenuItem,Paper,Select,Switch,Typography,useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,ArrowBack,ChevronRight,Diamond,Info,LocationOn,Lock,Notifications,Palette,Schedule,Security,Settings,Star,Verified,Warning!=!@mui/icons-material */ \"(pages-dir-browser)/__barrel_optimize__?names=AccountCircle,ArrowBack,ChevronRight,Diamond,Info,LocationOn,Lock,Notifications,Palette,Schedule,Security,Settings,Star,Verified,Warning!=!./node_modules/@mui/icons-material/esm/index.js\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Layout/Layout */ \"(pages-dir-browser)/./components/Layout/Layout.tsx\");\n/* harmony import */ var _src_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../src/providers/AuthProvider */ \"(pages-dir-browser)/./src/providers/AuthProvider.tsx\");\n/* harmony import */ var _src_providers_SettingsProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../src/providers/SettingsProvider */ \"(pages-dir-browser)/./src/providers/SettingsProvider.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SettingsPage = ()=>{\n    var _user_photos_, _user_photos, _user_firstName, _user_subscription;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const theme = (0,_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useTheme)();\n    const isMobile = (0,_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useMediaQuery)(theme.breakpoints.down('md'));\n    const { user } = (0,_src_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { settings, loading, error, loadSettings, updatePreferences } = (0,_src_providers_SettingsProvider__WEBPACK_IMPORTED_MODULE_6__.useSettings)();\n    const [quickSettingsLoading, setQuickSettingsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (!user) {\n                router.push('/auth/login');\n                return;\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user,\n        router\n    ]);\n    const handleQuickToggle = async (setting, value)=>{\n        try {\n            setQuickSettingsLoading(setting);\n            const updates = {};\n            switch(setting){\n                case 'theme':\n                    updates.theme = value ? 'dark' : 'light';\n                    break;\n                case 'notifications':\n                    updates.notifications = {\n                        ...settings === null || settings === void 0 ? void 0 : settings.notifications,\n                        messages: value\n                    };\n                    break;\n                case 'location':\n                    updates.showLocation = value;\n                    break;\n                case 'online':\n                    updates.showOnlineStatus = value;\n                    break;\n            }\n            if (setting === 'notifications') {\n                // This would be handled by updateNotificationSettings\n                // For now, just show success\n                setSuccess('Настройки уведомлений обновлены');\n            } else {\n                await updatePreferences({\n                    preferences: updates\n                });\n                setSuccess('Настройки обновлены');\n            }\n        } catch (err) {\n        // Error handled by context\n        } finally{\n            setQuickSettingsLoading(null);\n        }\n    };\n    const settingsMenuItems = [\n        {\n            id: 'account',\n            title: 'Аккаунт',\n            description: 'Профиль, верификация, подписка',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.AccountCircle, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 120,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/account',\n            badge: (user === null || user === void 0 ? void 0 : user.isVerified) ? 'Верифицирован' : null,\n            badgeColor: 'success'\n        },\n        {\n            id: 'privacy',\n            title: 'Приватность',\n            description: 'Видимость профиля, блокировки',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Lock, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 129,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/privacy',\n            badge: (settings === null || settings === void 0 ? void 0 : settings.privacy.showOnline) ? null : 'Скрыт',\n            badgeColor: 'warning'\n        },\n        {\n            id: 'notifications',\n            title: 'Уведомления',\n            description: 'Push, email, SMS уведомления',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Notifications, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 138,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/notifications',\n            badge: (settings === null || settings === void 0 ? void 0 : settings.notifications.messages) ? 'Включены' : 'Отключены',\n            badgeColor: (settings === null || settings === void 0 ? void 0 : settings.notifications.messages) ? 'success' : 'default'\n        },\n        {\n            id: 'security',\n            title: 'Безопасность',\n            description: 'Пароль, 2FA, активные сессии',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Security, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 147,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/security',\n            badge: null,\n            badgeColor: 'success'\n        },\n        {\n            id: 'blocked',\n            title: 'Заблокированные',\n            description: 'Управление заблокированными пользователями',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Warning, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 156,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/blocked',\n            badge: null,\n            badgeColor: 'error'\n        },\n        {\n            id: 'data',\n            title: 'Мои данные',\n            description: 'Экспорт, удаление аккаунта',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Info, {\n                color: \"primary\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 165,\n                columnNumber: 13\n            }, undefined),\n            path: '/settings/data',\n            badge: null,\n            badgeColor: 'default'\n        }\n    ];\n    const quickSettings = [\n        {\n            id: 'theme',\n            title: 'Темная тема',\n            description: 'Переключить на темную тему',\n            enabled: (settings === null || settings === void 0 ? void 0 : settings.theme) === 'dark',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Palette, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 178,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 'notifications',\n            title: 'Push уведомления',\n            description: 'Получать уведомления на устройство',\n            enabled: (settings === null || settings === void 0 ? void 0 : settings.notifications.messages) || false,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Notifications, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 185,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 'location',\n            title: 'Показывать местоположение',\n            description: 'Отображать ваше местоположение в профиле',\n            enabled: false,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.LocationOn, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 192,\n                columnNumber: 13\n            }, undefined)\n        },\n        {\n            id: 'online',\n            title: 'Статус онлайн',\n            description: 'Показывать когда вы в сети',\n            enabled: (settings === null || settings === void 0 ? void 0 : settings.privacy.showOnlineStatus) || false,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Schedule, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 199,\n                columnNumber: 13\n            }, undefined)\n        }\n    ];\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Настройки - Likes & Love\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Настройки аккаунта в приложении знакомств Likes & Love\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 209,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Container, {\n                    maxWidth: \"md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                        sx: {\n                            py: {\n                                xs: 2,\n                                md: 4\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                sx: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    mb: 4\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.ArrowBack, {}, void 0, false, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 28\n                                        }, void 0),\n                                        onClick: ()=>router.back(),\n                                        sx: {\n                                            mr: 2\n                                        },\n                                        children: \"Назад\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                        variant: isMobile ? \"h5\" : \"h4\",\n                                        sx: {\n                                            flexGrow: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Settings, {\n                                                sx: {\n                                                    mr: 2,\n                                                    verticalAlign: 'middle'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Настройки\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                severity: \"error\",\n                                sx: {\n                                    mb: 3\n                                },\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, undefined),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                severity: \"success\",\n                                sx: {\n                                    mb: 3\n                                },\n                                onClose: ()=>setSuccess(null),\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                sx: {\n                                    textAlign: 'center',\n                                    py: 8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.CircularProgress, {\n                                        size: 60\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                        variant: \"body1\",\n                                        sx: {\n                                            mt: 2\n                                        },\n                                        children: \"Загрузка настроек...\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Fade, {\n                                in: true,\n                                timeout: 600,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 3,\n                                            sx: {\n                                                p: 3,\n                                                mb: 4\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                sx: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                                        src: (_user_photos = user.photos) === null || _user_photos === void 0 ? void 0 : (_user_photos_ = _user_photos[0]) === null || _user_photos_ === void 0 ? void 0 : _user_photos_.url,\n                                                        sx: {\n                                                            width: 80,\n                                                            height: 80\n                                                        },\n                                                        children: (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                        sx: {\n                                                            flexGrow: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                sx: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: 1,\n                                                                    mb: 1\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                        variant: \"h6\",\n                                                                        children: [\n                                                                            user.firstName,\n                                                                            \" \",\n                                                                            user.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 270,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    user.isVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Verified, {\n                                                                        color: \"primary\",\n                                                                        fontSize: \"small\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    ((_user_subscription = user.subscription) === null || _user_subscription === void 0 ? void 0 : _user_subscription.plan) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Chip, {\n                                                                        label: user.subscription.plan.displayName,\n                                                                        color: \"primary\",\n                                                                        size: \"small\",\n                                                                        icon: user.subscription.plan.name === 'vip' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Diamond, {}, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 77\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.Star, {}, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 281,\n                                                                            columnNumber: 95\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                variant: \"body2\",\n                                                                color: \"text.secondary\",\n                                                                gutterBottom: true,\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                variant: \"caption\",\n                                                                color: \"text.secondary\",\n                                                                children: [\n                                                                    \"Аккаунт создан: \",\n                                                                    new Date(user.createdAt).toLocaleDateString('ru-RU')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                        variant: \"outlined\",\n                                                        onClick: ()=>router.push('/profile/edit'),\n                                                        children: \"Редактировать\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 2,\n                                            sx: {\n                                                p: 3,\n                                                mb: 4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Быстрые настройки\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.List, {\n                                                    children: quickSettings.map((setting, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItem, {\n                                                                    sx: {\n                                                                        px: 0\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemIcon, {\n                                                                            children: setting.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemText, {\n                                                                            primary: setting.title,\n                                                                            secondary: setting.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemSecondaryAction, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Switch, {\n                                                                                checked: setting.enabled,\n                                                                                onChange: (e)=>handleQuickToggle(setting.id, e.target.checked),\n                                                                                disabled: quickSettingsLoading === setting.id\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 322,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                            lineNumber: 321,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                index < quickSettings.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Divider, {}, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 64\n                                                                }, undefined)\n                                                            ]\n                                                        }, setting.id, true, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 2,\n                                            sx: {\n                                                mb: 4\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.List, {\n                                                children: settingsMenuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItem, {\n                                                                button: true,\n                                                                onClick: ()=>router.push(item.path),\n                                                                sx: {\n                                                                    py: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemIcon, {\n                                                                        children: item.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.ListItemText, {\n                                                                        primary: item.title,\n                                                                        secondary: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 348,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                        sx: {\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            gap: 1\n                                                                        },\n                                                                        children: [\n                                                                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Chip, {\n                                                                                label: item.badge,\n                                                                                size: \"small\",\n                                                                                color: item.badgeColor,\n                                                                                variant: \"outlined\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_ArrowBack_ChevronRight_Diamond_Info_LocationOn_Lock_Notifications_Palette_Schedule_Security_Settings_Star_Verified_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__.ChevronRight, {\n                                                                                color: \"action\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            index < settingsMenuItems.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Divider, {}, void 0, false, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 68\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 25\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 2,\n                                            sx: {\n                                                p: 3,\n                                                mb: 4\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Предпочтения приложения\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                    container: true,\n                                                    spacing: 3,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Язык\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: (settings === null || settings === void 0 ? void 0 : settings.preferences.language) || 'ru',\n                                                                        label: \"Язык\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"ru\",\n                                                                                children: \"Русский\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"en\",\n                                                                                children: \"English\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Часовой пояс\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: (settings === null || settings === void 0 ? void 0 : settings.preferences.timezone) || 'Europe/Moscow',\n                                                                        label: \"Часовой пояс\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"Europe/Moscow\",\n                                                                                children: \"Москва (UTC+3)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 399,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"Europe/Kiev\",\n                                                                                children: \"Киев (UTC+2)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"Asia/Almaty\",\n                                                                                children: \"Алматы (UTC+6)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Формат даты\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 408,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: (settings === null || settings === void 0 ? void 0 : settings.preferences.dateFormat) || 'DD/MM/YYYY',\n                                                                        label: \"Формат даты\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"DD/MM/YYYY\",\n                                                                                children: \"ДД/ММ/ГГГГ\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"MM/DD/YYYY\",\n                                                                                children: \"ММ/ДД/ГГГГ\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"YYYY-MM-DD\",\n                                                                                children: \"ГГГГ-ММ-ДД\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.FormControl, {\n                                                                fullWidth: true,\n                                                                size: \"small\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.InputLabel, {\n                                                                        children: \"Формат времени\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                        value: (settings === null || settings === void 0 ? void 0 : settings.preferences.timeFormat) || '24h',\n                                                                        label: \"Формат времени\",\n                                                                        disabled: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"24h\",\n                                                                                children: \"24 часа\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.MenuItem, {\n                                                                                value: \"12h\",\n                                                                                children: \"12 часов\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"caption\",\n                                                    color: \"text.secondary\",\n                                                    sx: {\n                                                        mt: 2,\n                                                        display: 'block'\n                                                    },\n                                                    children: \"Для изменения этих настроек перейдите в соответствующие разделы\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Paper, {\n                                            elevation: 1,\n                                            sx: {\n                                                p: 3,\n                                                backgroundColor: 'background.default'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"О приложении\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                    container: true,\n                                                    spacing: 2,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 6,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    color: \"text.secondary\",\n                                                                    children: \"Версия\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    children: \"1.0.0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 6,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    color: \"text.secondary\",\n                                                                    children: \"Сборка\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Typography, {\n                                                                    variant: \"body2\",\n                                                                    children: \"2024.01.15\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                                                            item: true,\n                                                            xs: 12,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Box, {\n                                                                sx: {\n                                                                    display: 'flex',\n                                                                    gap: 1,\n                                                                    mt: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\",\n                                                                        onClick: ()=>router.push('/help'),\n                                                                        children: \"Помощь\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\",\n                                                                        onClick: ()=>router.push('/about'),\n                                                                        children: \"О нас\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                        size: \"small\",\n                                                                        variant: \"outlined\",\n                                                                        onClick: ()=>router.push('/privacy-policy'),\n                                                                        children: \"Политика\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\settings\\\\index.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(SettingsPage, \"rfINC/Ifb6yyO+BjF+iV9Ms2yPw=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useTheme,\n        _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_Container_Divider_Fade_FormControl_Grid_InputLabel_List_ListItem_ListItemIcon_ListItemSecondaryAction_ListItemText_MenuItem_Paper_Select_Switch_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__.useMediaQuery,\n        _src_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _src_providers_SettingsProvider__WEBPACK_IMPORTED_MODULE_6__.useSettings\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/settings/index.tsx\n"));

/***/ })

});