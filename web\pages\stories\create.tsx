import React, { useState, useRef } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  TextField,
  Tabs,
  Tab,
  Grid,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  ArrowBack,
  PhotoCamera as PhotoCameraIcon,
  Videocam as VideocamIcon,
  TextFields as TextFieldsIcon,
  Upload as UploadIcon,
  Send as SendIcon,
  Palette as PaletteIcon,
  FormatSize as FormatSizeIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

// Типы для создания истории
interface CreateStoryForm {
  type: 'photo' | 'video' | 'text';
  content: {
    file?: File;
    text?: string;
    backgroundColor?: string;
    textColor?: string;
    fontSize?: number;
  };
  duration?: number;
}

// Схема валидации
const createStorySchema = yup.object({
  type: yup.string().oneOf(['photo', 'video', 'text']).required('Выберите тип истории'),
  content: yup.object({
    file: yup.mixed().when('type', {
      is: (type: string) => type === 'photo' || type === 'video',
      then: (schema) => schema.required('Загрузите файл'),
      otherwise: (schema) => schema.notRequired()
    }),
    text: yup.string().when('type', {
      is: 'text',
      then: (schema) => schema.required('Введите текст').max(200, 'Максимум 200 символов'),
      otherwise: (schema) => schema.notRequired()
    }),
    backgroundColor: yup.string(),
    textColor: yup.string(),
    fontSize: yup.number()
  }),
  duration: yup.number().min(3, 'Минимум 3 секунды').max(30, 'Максимум 30 секунд')
});

const CreateStoryPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<CreateStoryForm>({
    resolver: yupResolver(createStorySchema),
    defaultValues: {
      type: 'photo',
      content: {
        text: '',
        backgroundColor: '#6366f1',
        textColor: '#ffffff',
        fontSize: 24
      },
      duration: 5
    }
  });

  const watchedType = watch('type');
  const watchedText = watch('content.text');
  const watchedBackgroundColor = watch('content.backgroundColor');
  const watchedTextColor = watch('content.textColor');
  const watchedFontSize = watch('content.fontSize');

  // Предустановленные цвета фона
  const backgroundColors = [
    '#6366f1', '#8b5cf6', '#ec4899', '#ef4444',
    '#f59e0b', '#10b981', '#06b6d4', '#3b82f6',
    '#1f2937', '#374151', '#6b7280', '#ffffff'
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    const types = ['photo', 'video', 'text'];
    setValue('type', types[newValue] as 'photo' | 'video' | 'text');
    setPreviewUrl(null);
    setSelectedFile(null);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setValue('content.file', file);

      // Создаем превью
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Устанавливаем длительность по умолчанию
      if (file.type.startsWith('video/')) {
        setValue('duration', 15);
      } else {
        setValue('duration', 5);
      }
    }
  };

  const handleCreateStory = async (data: CreateStoryForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для создания истории
      // const formData = new FormData();
      // if (data.content.file) {
      //   formData.append('file', data.content.file);
      // }
      // formData.append('type', data.type);
      // formData.append('duration', data.duration?.toString() || '5');
      // if (data.type === 'text') {
      //   formData.append('text', data.content.text || '');
      //   formData.append('backgroundColor', data.content.backgroundColor || '#6366f1');
      //   formData.append('textColor', data.content.textColor || '#ffffff');
      //   formData.append('fontSize', data.content.fontSize?.toString() || '24');
      // }
      // const response = await createStory(formData);

      setSuccess('История успешно создана!');
      
      // Перенаправляем на страницу историй через 2 секунды
      setTimeout(() => {
        router.push('/stories');
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Ошибка при создании истории');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/stories');
  };

  if (!user) {
    return (
      <Layout title="Создать историю">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Создать историю - Likes & Love</title>
        <meta name="description" content="Создайте и поделитесь своей историей в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Создать историю">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Создать историю
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Поделитесь моментом своей жизни
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <form onSubmit={handleSubmit(handleCreateStory)}>
              <Grid container spacing={3}>
                {/* Выбор типа истории */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        variant="fullWidth"
                        sx={{ mb: 3 }}
                      >
                        <Tab icon={<PhotoCameraIcon />} label="Фото" />
                        <Tab icon={<VideocamIcon />} label="Видео" />
                        <Tab icon={<TextFieldsIcon />} label="Текст" />
                      </Tabs>

                      {/* Фото */}
                      {activeTab === 0 && (
                        <Box>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/*"
                            onChange={handleFileSelect}
                            style={{ display: 'none' }}
                          />
                          
                          {previewUrl ? (
                            <Box sx={{ position: 'relative', mb: 2 }}>
                              <img
                                src={previewUrl}
                                alt="Превью"
                                style={{
                                  width: '100%',
                                  maxHeight: 400,
                                  objectFit: 'contain',
                                  borderRadius: 8
                                }}
                              />
                              <IconButton
                                onClick={() => {
                                  setPreviewUrl(null);
                                  setSelectedFile(null);
                                  setValue('content.file', undefined);
                                }}
                                sx={{
                                  position: 'absolute',
                                  top: 8,
                                  right: 8,
                                  backgroundColor: 'rgba(0,0,0,0.5)',
                                  color: 'white'
                                }}
                              >
                                <CloseIcon />
                              </IconButton>
                            </Box>
                          ) : (
                            <Box
                              sx={{
                                border: '2px dashed',
                                borderColor: 'primary.main',
                                borderRadius: 2,
                                p: 4,
                                textAlign: 'center',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: 'action.hover'
                                }
                              }}
                              onClick={() => fileInputRef.current?.click()}
                            >
                              <PhotoCameraIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                              <Typography variant="h6" color="primary" sx={{ mb: 1 }}>
                                Выберите фото
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Нажмите для выбора изображения
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      )}

                      {/* Видео */}
                      {activeTab === 1 && (
                        <Box>
                          <input
                            ref={videoInputRef}
                            type="file"
                            accept="video/*"
                            onChange={handleFileSelect}
                            style={{ display: 'none' }}
                          />
                          
                          {previewUrl ? (
                            <Box sx={{ position: 'relative', mb: 2 }}>
                              <video
                                src={previewUrl}
                                controls
                                style={{
                                  width: '100%',
                                  maxHeight: 400,
                                  borderRadius: 8
                                }}
                              />
                              <IconButton
                                onClick={() => {
                                  setPreviewUrl(null);
                                  setSelectedFile(null);
                                  setValue('content.file', undefined);
                                }}
                                sx={{
                                  position: 'absolute',
                                  top: 8,
                                  right: 8,
                                  backgroundColor: 'rgba(0,0,0,0.5)',
                                  color: 'white'
                                }}
                              >
                                <CloseIcon />
                              </IconButton>
                            </Box>
                          ) : (
                            <Box
                              sx={{
                                border: '2px dashed',
                                borderColor: 'primary.main',
                                borderRadius: 2,
                                p: 4,
                                textAlign: 'center',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: 'action.hover'
                                }
                              }}
                              onClick={() => videoInputRef.current?.click()}
                            >
                              <VideocamIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                              <Typography variant="h6" color="primary" sx={{ mb: 1 }}>
                                Выберите видео
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Максимум 30 секунд
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      )}

                      {/* Текст */}
                      {activeTab === 2 && (
                        <Box>
                          {/* Превью текстовой истории */}
                          <Box
                            sx={{
                              height: 300,
                              borderRadius: 2,
                              backgroundColor: watchedBackgroundColor,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              mb: 3,
                              p: 2
                            }}
                          >
                            <Typography
                              sx={{
                                color: watchedTextColor,
                                fontSize: watchedFontSize,
                                fontWeight: 'bold',
                                textAlign: 'center',
                                wordBreak: 'break-word'
                              }}
                            >
                              {watchedText || 'Введите текст...'}
                            </Typography>
                          </Box>

                          {/* Поле ввода текста */}
                          <Controller
                            name="content.text"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Текст истории"
                                multiline
                                rows={3}
                                fullWidth
                                error={!!errors.content?.text}
                                helperText={errors.content?.text?.message || `${watchedText?.length || 0}/200`}
                                sx={{ mb: 3 }}
                              />
                            )}
                          />

                          {/* Настройки оформления */}
                          <Grid container spacing={2}>
                            {/* Цвет фона */}
                            <Grid item xs={12}>
                              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                Цвет фона
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                {backgroundColors.map((color) => (
                                  <Box
                                    key={color}
                                    sx={{
                                      width: 32,
                                      height: 32,
                                      borderRadius: 1,
                                      backgroundColor: color,
                                      cursor: 'pointer',
                                      border: watchedBackgroundColor === color ? '3px solid' : '1px solid',
                                      borderColor: watchedBackgroundColor === color ? 'primary.main' : 'divider'
                                    }}
                                    onClick={() => setValue('content.backgroundColor', color)}
                                  />
                                ))}
                              </Box>
                            </Grid>

                            {/* Размер шрифта */}
                            <Grid item xs={12}>
                              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                Размер шрифта: {watchedFontSize}px
                              </Typography>
                              <Controller
                                name="content.fontSize"
                                control={control}
                                render={({ field }) => (
                                  <Slider
                                    {...field}
                                    min={16}
                                    max={48}
                                    step={2}
                                    marks
                                    valueLabelDisplay="auto"
                                  />
                                )}
                              />
                            </Grid>

                            {/* Цвет текста */}
                            <Grid item xs={12}>
                              <FormControl fullWidth>
                                <InputLabel>Цвет текста</InputLabel>
                                <Controller
                                  name="content.textColor"
                                  control={control}
                                  render={({ field }) => (
                                    <Select {...field} label="Цвет текста">
                                      <MenuItem value="#ffffff">Белый</MenuItem>
                                      <MenuItem value="#000000">Черный</MenuItem>
                                      <MenuItem value="#ef4444">Красный</MenuItem>
                                      <MenuItem value="#10b981">Зеленый</MenuItem>
                                      <MenuItem value="#3b82f6">Синий</MenuItem>
                                      <MenuItem value="#f59e0b">Желтый</MenuItem>
                                    </Select>
                                  )}
                                />
                              </FormControl>
                            </Grid>
                          </Grid>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Настройки длительности */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                        Длительность показа
                      </Typography>
                      <Controller
                        name="duration"
                        control={control}
                        render={({ field }) => (
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {field.value} секунд
                            </Typography>
                            <Slider
                              {...field}
                              min={3}
                              max={30}
                              step={1}
                              marks={[
                                { value: 3, label: '3с' },
                                { value: 15, label: '15с' },
                                { value: 30, label: '30с' }
                              ]}
                              valueLabelDisplay="auto"
                            />
                          </Box>
                        )}
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* Кнопки */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button onClick={handleBack} disabled={loading}>
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading}
                      startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                    >
                      {loading ? 'Создание...' : 'Опубликовать'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default CreateStoryPage;
