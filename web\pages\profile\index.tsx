import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Avatar,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
  Divider,
  IconButton,
  Badge,
  Alert,
  LinearProgress
} from '@mui/material';
import { 
  Edit, 
  PhotoCamera, 
  Verified, 
  LocationOn,
  Cake,
  Work,
  School,
  Favorite,
  Star,
  Share,
  MoreVert,
  Security,
  Visibility,
  Settings
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import axios from 'axios';

interface ProfileStats {
  totalLikes: number;
  totalMatches: number;
  profileViews: number;
  likesThisWeek: number;
}

interface ProfileBadge {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
}

const ProfilePage: React.FC = () => {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [badges, setBadges] = useState<ProfileBadge[]>([]);
  const [profileCompletion, setProfileCompletion] = useState(0);

  useEffect(() => {
    if (user) {
      loadProfileData();
      calculateProfileCompletion();
    }
  }, [user]);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      
      // Загружаем статистику профиля
      const statsResponse = await axios.get('/api/profile/stats');
      setStats(statsResponse.data);

      // Загружаем достижения/бейджи
      const badgesResponse = await axios.get('/api/profile/badges');
      setBadges(badgesResponse.data);
      
    } catch (error) {
      console.error('Ошибка загрузки данных профиля:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateProfileCompletion = () => {
    if (!user?.profile) return;

    const profile = user.profile;
    let completed = 0;
    const total = 12;

    // Основная информация
    if (profile.firstName) completed++;
    if (profile.lastName) completed++;
    if (profile.dateOfBirth) completed++;
    if (profile.bio) completed++;
    
    // Контактная информация
    if (user.emailVerified) completed++;
    if (user.phoneVerified) completed++;
    
    // Фотографии
    if (profile.avatarUrl) completed++;
    if (profile.photos && profile.photos.length >= 3) completed++;
    
    // Дополнительная информация
    if (profile.interests && profile.interests.length >= 3) completed++;
    if (profile.socialLinks && Object.keys(profile.socialLinks).length > 0) completed++;
    
    // Верификация
    if (profile.verificationStatus?.photo) completed++;

    setProfileCompletion(Math.round((completed / total) * 100));
  };

  const getVerificationStatus = () => {
    const verificationCount = [
      user?.emailVerified,
      user?.phoneVerified,
      user?.profile?.verificationStatus?.photo,
      user?.profile?.verificationStatus?.social
    ].filter(Boolean).length;

    if (verificationCount >= 3) return { level: 'Высокий', color: 'success' as const };
    if (verificationCount >= 2) return { level: 'Средний', color: 'warning' as const };
    return { level: 'Базовый', color: 'default' as const };
  };

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const handlePhotoUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const formData = new FormData();
        formData.append('avatar', file);
        
        try {
          const response = await axios.post('/api/profile/upload-avatar', formData);
          if (response.data.success && user && user.profile) {
            updateUser({
              ...user,
              profile: {
                ...user.profile,
                avatarUrl: response.data.avatarUrl
              }
            });
          }
        } catch (error) {
          console.error('Ошибка загрузки фото:', error);
        }
      }
    };
    input.click();
  };

  const handleShareProfile = async () => {
    const profileUrl = `${window.location.origin}/users/${user?.id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Профиль ${user?.profile?.displayName}`,
          text: 'Посмотри мой профиль в Likes & Love!',
          url: profileUrl
        });
      } catch (error) {
        console.log('Ошибка share API:', error);
      }
    } else {
      // Fallback - копирование в буфер обмена
      await navigator.clipboard.writeText(profileUrl);
      alert('Ссылка скопирована в буфер обмена!');
    }
  };

  const verificationStatus = getVerificationStatus();

  if (!user) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="h6">Загрузка профиля...</Typography>
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="md">
        <Box sx={{ mt: 2, mb: 4 }}>
          {/* Заголовок профиля */}
          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Stack direction="row" spacing={3} alignItems="flex-start">
              {/* Аватар */}
              <Box sx={{ position: 'relative' }}>
                <Badge
                  overlap="circular"
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  badgeContent={
                    <IconButton
                      size="small"
                      onClick={handlePhotoUpload}
                      sx={{ 
                        bgcolor: 'primary.main',
                        color: 'white',
                        '&:hover': { bgcolor: 'primary.dark' }
                      }}
                    >
                      <PhotoCamera fontSize="small" />
                    </IconButton>
                  }
                >
                  <Avatar
                    src={user.profile?.avatarUrl}
                    alt={user.profile?.displayName}
                    sx={{ width: 120, height: 120 }}
                  >
                    {user.profile?.firstName?.[0]}{user.profile?.lastName?.[0]}
                  </Avatar>
                </Badge>
              </Box>

              {/* Основная информация */}
              <Box sx={{ flex: 1 }}>
                <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                  <Typography variant="h5">
                    {user.profile?.displayName || `${user.profile?.firstName} ${user.profile?.lastName}`}
                  </Typography>
                  {user.profile?.verificationStatus?.photo && (
                    <Verified color="primary" />
                  )}
                  <Chip 
                    label={verificationStatus.level}
                    color={verificationStatus.color}
                    size="small"
                  />
                </Stack>

                {user.profile?.dateOfBirth && (
                  <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                    <Cake fontSize="small" color="action" />
                    <Typography variant="body2" color="text.secondary">
                      {calculateAge(user.profile.dateOfBirth)} лет
                    </Typography>
                  </Stack>
                )}

                {user.profile?.location && (
                  <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                    <LocationOn fontSize="small" color="action" />
                    <Typography variant="body2" color="text.secondary">
                      {typeof user.profile.location === 'string' 
                        ? user.profile.location 
                        : user.profile.location.city || 'Город не указан'}
                    </Typography>
                  </Stack>
                )}

                {user.profile?.bio && (
                  <Typography variant="body2" sx={{ mt: 2, maxWidth: '400px' }}>
                    {user.profile.bio}
                  </Typography>
                )}
              </Box>

              {/* Действия */}
              <Stack spacing={1}>
                <IconButton onClick={handleShareProfile}>
                  <Share />
                </IconButton>
                <IconButton onClick={() => router.push('/profile/edit')}>
                  <Edit />
                </IconButton>
                <IconButton>
                  <MoreVert />
                </IconButton>
              </Stack>
            </Stack>
          </Paper>

          {/* Завершенность профиля */}
          {profileCompletion < 100 && (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>
                Заполненность профиля: {profileCompletion}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={profileCompletion} 
                sx={{ mb: 1 }}
              />
              <Typography variant="body2">
                Завершите заполнение профиля, чтобы получать больше совпадений!
                <Button
                  size="small"
                  onClick={() => router.push('/profile/edit')}
                  sx={{ ml: 1 }}
                >
                  Дополнить
                </Button>
              </Typography>
            </Alert>
          )}

          <Grid container spacing={3}>
            {/* Статистика */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Статистика
                  </Typography>
                  
                  {stats ? (
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="primary">
                            {stats.totalLikes}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Лайков получено
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={6}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="success.main">
                            {stats.totalMatches}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Совпадений
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={6}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="info.main">
                            {stats.profileViews}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Просмотров
                          </Typography>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={6}>
                        <Box textAlign="center">
                          <Typography variant="h4" color="warning.main">
                            {stats.likesThisWeek}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            За неделю
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Загрузка статистики...
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Достижения */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Достижения
                  </Typography>
                  
                  {badges.length > 0 ? (
                    <Stack spacing={1}>
                      {badges.slice(0, 3).map((badge) => (
                        <Chip
                          key={badge.id}
                          label={badge.name}
                          icon={<span>{badge.icon}</span>}
                          variant="outlined"
                          size="small"
                        />
                      ))}
                      {badges.length > 3 && (
                        <Typography variant="body2" color="text.secondary">
                          и еще {badges.length - 3} достижений
                        </Typography>
                      )}
                    </Stack>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Пока нет достижений
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Дополнительная информация */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Дополнительная информация
                  </Typography>
                  
                  {user.profile?.interests && user.profile.interests.length > 0 ? (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Интересы:
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                        {user.profile.interests.slice(0, 8).map((interest, index) => (
                          <Chip
                            key={index}
                            label={interest}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                        {user.profile.interests.length > 8 && (
                          <Chip
                            label={`+${user.profile.interests.length - 8}`}
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </Stack>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Интересы пока не добавлены
                      <Button
                        size="small"
                        onClick={() => router.push('/profile/edit')}
                        sx={{ ml: 1 }}
                      >
                        Добавить
                      </Button>
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Быстрые действия */}
          <Paper elevation={1} sx={{ p: 2, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Быстрые действия
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PhotoCamera />}
                  onClick={() => router.push('/profile/photos')}
                >
                  Фото
                </Button>
              </Grid>
              
              <Grid item xs={6} sm={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Security />}
                  onClick={() => router.push('/profile/verification')}
                >
                  Верификация
                </Button>
              </Grid>
              
              <Grid item xs={6} sm={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Visibility />}
                  onClick={() => router.push('/profile/privacy')}
                >
                  Приватность
                </Button>
              </Grid>
              
              <Grid item xs={6} sm={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Settings />}
                  onClick={() => router.push('/settings')}
                >
                  Настройки
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default ProfilePage;
