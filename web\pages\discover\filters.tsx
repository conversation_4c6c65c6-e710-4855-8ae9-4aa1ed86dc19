import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  RadioGroup,
  Radio,
  Checkbox,
  Slider,
  TextField,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Autocomplete,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getDiscoverSettings,
  updateDiscoverSettings,
  saveFilters,
  getSavedFilters,
  deleteSavedFilter
} from '../../src/services/discoverService';
import { 
  DiscoverFilters,
  DiscoverSettings 
} from '../../src/types/discover.types';

const FiltersPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [filters, setFilters] = useState<DiscoverFilters>({
    ageRange: { min: 18, max: 35 },
    distanceRange: 50,
    interests: [],
    relationshipGoals: [],
    lifestyle: {},
    verifiedOnly: false,
    onlineOnly: false,
    hasPhotosOnly: true,
    premiumOnly: false,
    heightRange: { min: 150, max: 200 },
    education: [],
    occupation: []
  });

  const [settings, setSettings] = useState<DiscoverSettings | null>(null);
  const [savedFilters, setSavedFilters] = useState<DiscoverFilters[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Available options
  const interestOptions = [
    'Спорт', 'Музыка', 'Кино', 'Путешествия', 'Книги', 'Кулинария',
    'Фотография', 'Танцы', 'Искусство', 'Технологии', 'Природа', 'Животные'
  ];

  const relationshipGoalOptions = [
    'Серьезные отношения', 'Знакомства', 'Дружба', 'Общение', 'Флирт'
  ];

  const educationOptions = [
    'Среднее образование', 'Среднее специальное', 'Высшее образование',
    'Магистратура', 'Аспирантура', 'Ученая степень'
  ];

  const occupationOptions = [
    'IT', 'Медицина', 'Образование', 'Бизнес', 'Искусство', 'Спорт',
    'Наука', 'Инженерия', 'Маркетинг', 'Финансы', 'Юриспруденция'
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadData();
  }, [user, router]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [settingsData, savedFiltersData] = await Promise.all([
        getDiscoverSettings(),
        getSavedFilters()
      ]);
      
      setSettings(settingsData);
      setSavedFilters(savedFiltersData);
      
      // Set current filters from settings
      if (settingsData) {
        setFilters({
          ageRange: settingsData.ageRange,
          distanceRange: settingsData.maxDistance,
          interests: [],
          relationshipGoals: [],
          lifestyle: {},
          verifiedOnly: settingsData.showOnlyVerified,
          onlineOnly: settingsData.showOnlyOnline,
          hasPhotosOnly: settingsData.showOnlyWithPhotos,
          premiumOnly: false,
          heightRange: { min: 150, max: 200 },
          education: [],
          occupation: []
        });
      }
    } catch (err: any) {
      setError('Ошибка загрузки настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveFilters = async () => {
    try {
      setSaving(true);
      setError(null);

      // Update discover settings
      if (settings) {
        const updatedSettings: Partial<DiscoverSettings> = {
          ageRange: filters.ageRange,
          maxDistance: filters.distanceRange,
          showOnlyVerified: filters.verifiedOnly,
          showOnlyOnline: filters.onlineOnly,
          showOnlyWithPhotos: filters.hasPhotosOnly
        };

        await updateDiscoverSettings(updatedSettings);
      }

      // Save filters
      await saveFilters(filters);
      
      setSuccess('Фильтры сохранены');
      
      // Redirect back with applied filters
      setTimeout(() => {
        router.push('/discover');
      }, 1000);
    } catch (err: any) {
      setError('Ошибка сохранения фильтров');
    } finally {
      setSaving(false);
    }
  };

  const handleClearFilters = () => {
    setFilters({
      ageRange: { min: 18, max: 35 },
      distanceRange: 50,
      interests: [],
      relationshipGoals: [],
      lifestyle: {},
      verifiedOnly: false,
      onlineOnly: false,
      hasPhotosOnly: true,
      premiumOnly: false,
      heightRange: { min: 150, max: 200 },
      education: [],
      occupation: []
    });
  };

  const handleDeleteSavedFilter = async (filterId: string) => {
    try {
      await deleteSavedFilter(filterId);
      setSavedFilters(prev => prev.filter((_, index) => index.toString() !== filterId));
      setSuccess('Сохраненный фильтр удален');
    } catch (err: any) {
      setError('Ошибка удаления фильтра');
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Фильтры поиска - Likes & Love</title>
        <meta 
          name="description" 
          content="Настройте фильтры поиска в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <FilterIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Фильтры поиска
              </Typography>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={handleClearFilters}
                size={isMobile ? "small" : "medium"}
              >
                Очистить
              </Button>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка настроек...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
                  {/* Age Range */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>
                      Возраст: {filters.ageRange.min} - {filters.ageRange.max} лет
                    </FormLabel>
                    <Slider
                      value={[filters.ageRange.min, filters.ageRange.max]}
                      onChange={(_, value) => {
                        const [min, max] = value as number[];
                        setFilters(prev => ({
                          ...prev,
                          ageRange: { min, max }
                        }));
                      }}
                      valueLabelDisplay="auto"
                      min={18}
                      max={80}
                      marks={[
                        { value: 18, label: '18' },
                        { value: 25, label: '25' },
                        { value: 35, label: '35' },
                        { value: 50, label: '50' },
                        { value: 80, label: '80' }
                      ]}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Distance */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>
                      Максимальное расстояние: {filters.distanceRange} км
                    </FormLabel>
                    <Slider
                      value={filters.distanceRange}
                      onChange={(_, value) => setFilters(prev => ({
                        ...prev,
                        distanceRange: value as number
                      }))}
                      valueLabelDisplay="auto"
                      min={1}
                      max={200}
                      marks={[
                        { value: 1, label: '1 км' },
                        { value: 25, label: '25 км' },
                        { value: 50, label: '50 км' },
                        { value: 100, label: '100 км' },
                        { value: 200, label: '200 км' }
                      ]}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Height Range */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>
                      Рост: {filters.heightRange?.min} - {filters.heightRange?.max} см
                    </FormLabel>
                    <Slider
                      value={[filters.heightRange?.min || 150, filters.heightRange?.max || 200]}
                      onChange={(_, value) => {
                        const [min, max] = value as number[];
                        setFilters(prev => ({
                          ...prev,
                          heightRange: { min, max }
                        }));
                      }}
                      valueLabelDisplay="auto"
                      min={140}
                      max={220}
                      marks={[
                        { value: 140, label: '140' },
                        { value: 160, label: '160' },
                        { value: 180, label: '180' },
                        { value: 200, label: '200' },
                        { value: 220, label: '220' }
                      ]}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Interests */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>Интересы</FormLabel>
                    <Autocomplete
                      multiple
                      options={interestOptions}
                      value={filters.interests || []}
                      onChange={(_, value) => setFilters(prev => ({
                        ...prev,
                        interests: value
                      }))}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={option}
                            {...getTagProps({ index })}
                            key={option}
                          />
                        ))
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Выберите интересы"
                        />
                      )}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Relationship Goals */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>Цели знакомства</FormLabel>
                    <Autocomplete
                      multiple
                      options={relationshipGoalOptions}
                      value={filters.relationshipGoals || []}
                      onChange={(_, value) => setFilters(prev => ({
                        ...prev,
                        relationshipGoals: value
                      }))}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={option}
                            {...getTagProps({ index })}
                            key={option}
                          />
                        ))
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Выберите цели знакомства"
                        />
                      )}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Education */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>Образование</FormLabel>
                    <Autocomplete
                      multiple
                      options={educationOptions}
                      value={filters.education || []}
                      onChange={(_, value) => setFilters(prev => ({
                        ...prev,
                        education: value
                      }))}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={option}
                            {...getTagProps({ index })}
                            key={option}
                          />
                        ))
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Выберите уровень образования"
                        />
                      )}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Occupation */}
                  <FormControl fullWidth sx={{ mb: 4 }}>
                    <FormLabel sx={{ mb: 2 }}>Сфера деятельности</FormLabel>
                    <Autocomplete
                      multiple
                      options={occupationOptions}
                      value={filters.occupation || []}
                      onChange={(_, value) => setFilters(prev => ({
                        ...prev,
                        occupation: value
                      }))}
                      renderTags={(value, getTagProps) =>
                        value.map((option, index) => (
                          <Chip
                            variant="outlined"
                            label={option}
                            {...getTagProps({ index })}
                            key={option}
                          />
                        ))
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Выберите сферу деятельности"
                        />
                      )}
                    />
                  </FormControl>

                  <Divider sx={{ my: 3 }} />

                  {/* Additional Filters */}
                  <FormControl component="fieldset" sx={{ mb: 4 }}>
                    <FormLabel component="legend" sx={{ mb: 2 }}>
                      Дополнительные фильтры
                    </FormLabel>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={filters.verifiedOnly || false}
                            onChange={(e) => setFilters(prev => ({
                              ...prev,
                              verifiedOnly: e.target.checked
                            }))}
                          />
                        }
                        label="Только проверенные профили"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={filters.onlineOnly || false}
                            onChange={(e) => setFilters(prev => ({
                              ...prev,
                              onlineOnly: e.target.checked
                            }))}
                          />
                        }
                        label="Только пользователи онлайн"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={filters.hasPhotosOnly || false}
                            onChange={(e) => setFilters(prev => ({
                              ...prev,
                              hasPhotosOnly: e.target.checked
                            }))}
                          />
                        }
                        label="Только с фотографиями"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={filters.premiumOnly || false}
                            onChange={(e) => setFilters(prev => ({
                              ...prev,
                              premiumOnly: e.target.checked
                            }))}
                          />
                        }
                        label="Только премиум пользователи"
                      />
                    </FormGroup>
                  </FormControl>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 4 }}>
                    <Button
                      variant="outlined"
                      onClick={() => router.back()}
                      disabled={saving}
                    >
                      Отмена
                    </Button>
                    <Button
                      variant="contained"
                      startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                      onClick={handleSaveFilters}
                      disabled={saving}
                    >
                      {saving ? 'Сохранение...' : 'Применить фильтры'}
                    </Button>
                  </Box>
                </Paper>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default FiltersPage;
