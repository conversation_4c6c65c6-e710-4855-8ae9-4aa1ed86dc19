import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Grid,
  Avatar,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  FormHelperText
} from '@mui/material';
import { PhotoCamera, Delete, Add } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ruLocale from 'date-fns/locale/ru';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import axios from 'axios';

interface ProfileData {
  firstName: string;
  lastName: string;
  displayName: string;
  birthDate: Date | null;
  gender: string;
  bio: string;
  interests: string[];
  lookingFor: string;
  education: string;
  work: string;
  height: number;
  children: string;
  smoking: string;
  drinking: string;
  religion: string;
}

const interests = [
  'Спорт', 'Музыка', 'Кино', 'Путешествия', 'Книги', 'Фотография',
  'Кулинария', 'Танцы', 'Йога', 'Искусство', 'Технологии', 'Природа',
  'Животные', 'Игры', 'Театр', 'Мода', 'Психология', 'Наука'
];

const EditProfilePage: React.FC = () => {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [profileData, setProfileData] = useState<ProfileData>({
    firstName: '',
    lastName: '',
    displayName: '',
    birthDate: null,
    gender: '',
    bio: '',
    interests: [],
    lookingFor: '',
    education: '',
    work: '',
    height: 0,
    children: '',
    smoking: '',
    drinking: '',
    religion: ''
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/profile');
      const profile = response.data;
      setProfileData({
        ...profile,
        birthDate: profile.birthDate ? new Date(profile.birthDate) : null,
        interests: profile.interests || []
      });
    } catch (err) {
      setError('Ошибка загрузки профиля');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string) => (e: any) => {
    setProfileData(prev => ({
      ...prev,
      [name]: e.target.value
    }));
  };

  const handleDateChange = (date: Date | null) => {
    setProfileData(prev => ({
      ...prev,
      birthDate: date
    }));
  };

  const handleInterestToggle = (interest: string) => {
    setProfileData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setSaving(true);

    try {
      const response = await axios.put('/api/profile', profileData);
      updateUser(response.data);
      setSuccess('Профиль успешно обновлен');
      setTimeout(() => {
        router.push('/profile');
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка сохранения профиля');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container maxWidth="md">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Typography variant="h4" gutterBottom>
              Редактирование профиля
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {success}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Основная информация */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Основная информация
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Имя"
                    name="firstName"
                    value={profileData.firstName}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Фамилия"
                    name="lastName"
                    value={profileData.lastName}
                    onChange={handleChange}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Отображаемое имя"
                    name="displayName"
                    value={profileData.displayName}
                    onChange={handleChange}
                    helperText="Как вас будут видеть другие пользователи"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ruLocale}>
                    <DatePicker
                      label="Дата рождения"
                      value={profileData.birthDate}
                      onChange={handleDateChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true
                        }
                      }}
                    />
                  </LocalizationProvider>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Пол</InputLabel>
                    <Select
                      value={profileData.gender}
                      onChange={handleSelectChange('gender')}
                      label="Пол"
                    >
                      <MenuItem value="male">Мужской</MenuItem>
                      <MenuItem value="female">Женский</MenuItem>
                      <MenuItem value="other">Другой</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Ищу</InputLabel>
                    <Select
                      value={profileData.lookingFor}
                      onChange={handleSelectChange('lookingFor')}
                      label="Ищу"
                    >
                      <MenuItem value="relationship">Серьезные отношения</MenuItem>
                      <MenuItem value="friendship">Дружбу</MenuItem>
                      <MenuItem value="dating">Свидания</MenuItem>
                      <MenuItem value="networking">Нетворкинг</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="О себе"
                    name="bio"
                    value={profileData.bio}
                    onChange={handleChange}
                    multiline
                    rows={4}
                    helperText="Расскажите о себе (максимум 500 символов)"
                    inputProps={{ maxLength: 500 }}
                  />
                </Grid>

                {/* Интересы */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Интересы
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <FormHelperText>Выберите до 10 интересов</FormHelperText>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {interests.map((interest) => (
                      <Chip
                        key={interest}
                        label={interest}
                        onClick={() => handleInterestToggle(interest)}
                        color={profileData.interests.includes(interest) ? 'primary' : 'default'}
                        disabled={!profileData.interests.includes(interest) && profileData.interests.length >= 10}
                      />
                    ))}
                  </Box>
                </Grid>

                {/* Дополнительная информация */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Дополнительная информация
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Образование"
                    name="education"
                    value={profileData.education}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Работа"
                    name="work"
                    value={profileData.work}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Рост (см)"
                    name="height"
                    type="number"
                    value={profileData.height || ''}
                    onChange={handleChange}
                    inputProps={{ min: 100, max: 250 }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Дети</InputLabel>
                    <Select
                      value={profileData.children}
                      onChange={handleSelectChange('children')}
                      label="Дети"
                    >
                      <MenuItem value="">Не указано</MenuItem>
                      <MenuItem value="no">Нет</MenuItem>
                      <MenuItem value="yes">Есть</MenuItem>
                      <MenuItem value="want">Хочу</MenuItem>
                      <MenuItem value="dont_want">Не хочу</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel>Курение</InputLabel>
                    <Select
                      value={profileData.smoking}
                      onChange={handleSelectChange('smoking')}
                      label="Курение"
                    >
                      <MenuItem value="">Не указано</MenuItem>
                      <MenuItem value="no">Не курю</MenuItem>
                      <MenuItem value="sometimes">Иногда</MenuItem>
                      <MenuItem value="yes">Курю</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel>Алкоголь</InputLabel>
                    <Select
                      value={profileData.drinking}
                      onChange={handleSelectChange('drinking')}
                      label="Алкоголь"
                    >
                      <MenuItem value="">Не указано</MenuItem>
                      <MenuItem value="no">Не пью</MenuItem>
                      <MenuItem value="sometimes">Иногда</MenuItem>
                      <MenuItem value="yes">Пью</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel>Религия</InputLabel>
                    <Select
                      value={profileData.religion}
                      onChange={handleSelectChange('religion')}
                      label="Религия"
                    >
                      <MenuItem value="">Не указано</MenuItem>
                      <MenuItem value="christian">Христианство</MenuItem>
                      <MenuItem value="muslim">Ислам</MenuItem>
                      <MenuItem value="jewish">Иудаизм</MenuItem>
                      <MenuItem value="buddhist">Буддизм</MenuItem>
                      <MenuItem value="atheist">Атеизм</MenuItem>
                      <MenuItem value="other">Другое</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Кнопки действий */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={() => router.push('/profile')}
                      disabled={saving}
                    >
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={saving}
                    >
                      {saving ? (
                        <>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          Сохранение...
                        </>
                      ) : (
                        'Сохранить'
                      )}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default EditProfilePage;
