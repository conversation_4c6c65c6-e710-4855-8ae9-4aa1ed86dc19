import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Rating,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Restaurant as RestaurantIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Map as MapIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Verified as VerifiedIcon,
  Event as EventIcon,
  People as MeetingIcon,
  LocalParking as ParkingIcon,
  Accessible as AccessibleIcon,
  CreditCard as CardIcon,
  Pets as PetsIcon,
  Delivery as DeliveryIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getPlaces,
  getNearbyPlaces,
  getPopularPlaces,
  addToFavorites,
  removeFromFavorites,
  getUserLocation
} from '../../src/services/placesService';
import { 
  Place,
  PlaceFilters 
} from '../../src/types/places.types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`restaurant-tabpanel-${index}`}
      aria-labelledby={`restaurant-tab-${index}`}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const RestaurantsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [allRestaurants, setAllRestaurants] = useState<Place[]>([]);
  const [nearbyRestaurants, setNearbyRestaurants] = useState<Place[]>([]);
  const [popularRestaurants, setPopularRestaurants] = useState<Place[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [priceFilter, setPriceFilter] = useState<Place['priceRange'] | ''>('');
  const [cuisineFilter, setCuisineFilter] = useState<string>('');

  const tabs = [
    { label: 'Все рестораны', key: 'all' },
    { label: 'Рядом', key: 'nearby' },
    { label: 'Популярные', key: 'popular' }
  ];

  const cuisineTypes = [
    'Русская', 'Итальянская', 'Японская', 'Китайская', 'Французская',
    'Грузинская', 'Узбекская', 'Мексиканская', 'Индийская', 'Тайская',
    'Средиземноморская', 'Американская', 'Корейская', 'Вьетнамская'
  ];

  // Restaurant specific filters
  const getRestaurantFilters = (): PlaceFilters => ({
    category: ['restaurant', 'fine-dining', 'casual-dining', 'fast-food'],
    priceRange: priceFilter ? [priceFilter] : undefined,
    tags: cuisineFilter ? [cuisineFilter.toLowerCase()] : undefined
  });

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadInitialData();
  }, [user, router]);

  useEffect(() => {
    loadTabData();
  }, [activeTab, userLocation, priceFilter, cuisineFilter]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Try to get user location for nearby search
      try {
        const location = await getUserLocation();
        setUserLocation(location);
      } catch (err) {
        console.warn('Could not get user location:', err);
      }
      
      // Load initial data
      await loadTabData();
    } catch (err: any) {
      setError('Ошибка загрузки ресторанов');
    } finally {
      setLoading(false);
    }
  };

  const loadTabData = async () => {
    try {
      setError(null);
      
      const filters = getRestaurantFilters();
      
      switch (activeTab) {
        case 0: // All restaurants
          const allPlaces = await getPlaces(filters);
          setAllRestaurants(allPlaces.data);
          break;
        case 1: // Nearby restaurants
          if (userLocation) {
            const nearby = await getNearbyPlaces(
              userLocation.latitude, 
              userLocation.longitude, 
              15, // 15km radius
              'restaurant'
            );
            setNearbyRestaurants(nearby);
          }
          break;
        case 2: // Popular restaurants
          const popular = await getPopularPlaces('restaurant');
          setPopularRestaurants(popular);
          break;
      }
    } catch (err: any) {
      setError('Ошибка загрузки данных');
    }
  };

  const handleToggleFavorite = async (place: Place) => {
    try {
      setActionLoading(place.id);
      setError(null);

      if (place.userInteraction?.isFavorite) {
        await removeFromFavorites(place.id);
        setSuccess('Ресторан удален из избранного');
      } else {
        await addToFavorites(place.id);
        setSuccess('Ресторан добавлен в избранное');
      }
      
      // Update local state
      const updatePlaces = (places: Place[]) => 
        places.map(p => 
          p.id === place.id 
            ? { 
                ...p, 
                userInteraction: { 
                  ...p.userInteraction, 
                  isFavorite: !p.userInteraction?.isFavorite 
                } 
              }
            : p
        );
      
      setAllRestaurants(updatePlaces);
      setNearbyRestaurants(updatePlaces);
      setPopularRestaurants(updatePlaces);
      
    } catch (err: any) {
      setError('Ошибка обновления избранного');
    } finally {
      setActionLoading(null);
    }
  };

  const formatPriceRange = (priceRange: Place['priceRange']) => {
    switch (priceRange) {
      case '$':
        return 'Бюджетно';
      case '$$':
        return 'Умеренно';
      case '$$$':
        return 'Дорого';
      case '$$$$':
        return 'Очень дорого';
      default:
        return priceRange;
    }
  };

  const formatDistance = (distance?: Place['distance']) => {
    if (!distance) return null;
    
    if (distance.value < 1) {
      return `${Math.round(distance.value * 1000)} м`;
    }
    return `${distance.value.toFixed(1)} км`;
  };

  const getRestaurantCuisine = (place: Place) => {
    const cuisines = place.tags.filter(tag => 
      cuisineTypes.some(cuisine => cuisine.toLowerCase() === tag)
    );
    return cuisines.length > 0 ? cuisines[0] : 'Смешанная кухня';
  };

  const getRestaurantFeatures = (place: Place) => {
    const features = [];
    
    if (place.hasDelivery) features.push('Доставка');
    if (place.hasTakeaway) features.push('На вынос');
    if (place.reservationRequired) features.push('Бронирование');
    if (place.tags.includes('romantic')) features.push('Романтично');
    if (place.tags.includes('family')) features.push('Семейный');
    if (place.tags.includes('business')) features.push('Деловой');
    
    return features;
  };

  const getCurrentPlaces = () => {
    switch (activeTab) {
      case 0:
        return allRestaurants;
      case 1:
        return nearbyRestaurants;
      case 2:
        return popularRestaurants;
      default:
        return [];
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Рестораны - Likes & Love</title>
        <meta 
          name="description" 
          content="Лучшие рестораны для встреч и свиданий в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/restaurants" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <RestaurantIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Рестораны
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => router.push('/places/map?category=restaurant')}>
                  <MapIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/restaurants?filters=true')}>
                  <FilterIcon />
                </IconButton>
                <IconButton onClick={loadTabData} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Filters */}
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Ценовая категория</InputLabel>
                    <Select
                      value={priceFilter}
                      onChange={(e) => setPriceFilter(e.target.value as Place['priceRange'] | '')}
                      label="Ценовая категория"
                    >
                      <MenuItem value="">Любая</MenuItem>
                      <MenuItem value="$">$ - Бюджетно</MenuItem>
                      <MenuItem value="$$">$$ - Умеренно</MenuItem>
                      <MenuItem value="$$$">$$$ - Дорого</MenuItem>
                      <MenuItem value="$$$$">$$$$ - Очень дорого</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Кухня</InputLabel>
                    <Select
                      value={cuisineFilter}
                      onChange={(e) => setCuisineFilter(e.target.value)}
                      label="Кухня"
                    >
                      <MenuItem value="">Любая</MenuItem>
                      {cuisineTypes.map((cuisine) => (
                        <MenuItem key={cuisine} value={cuisine}>
                          {cuisine}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setPriceFilter('');
                      setCuisineFilter('');
                    }}
                    disabled={!priceFilter && !cuisineFilter}
                  >
                    Сбросить фильтры
                  </Button>
                </Grid>
              </Grid>
            </Paper>

            {/* Location Alert for Nearby Tab */}
            {activeTab === 1 && !userLocation && (
              <Alert severity="info" sx={{ mb: 3 }}>
                Разрешите доступ к геолокации, чтобы видеть рестораны рядом с вами
              </Alert>
            )}

            {/* Tabs */}
            <Paper elevation={2} sx={{ mb: 3 }}>
              <Tabs
                value={activeTab}
                onChange={(_, newValue) => setActiveTab(newValue)}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons="auto"
              >
                {tabs.map((tab, index) => (
                  <Tab key={index} label={tab.label} />
                ))}
              </Tabs>
            </Paper>

            {/* Content */}
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка ресторанов...
                </Typography>
              </Box>
            ) : getCurrentPlaces().length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <RestaurantIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  {activeTab === 1 ? 'Рестораны рядом не найдены' : 'Рестораны не найдены'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {activeTab === 1 
                    ? 'Попробуйте увеличить радиус поиска или разрешите доступ к геолокации'
                    : 'Попробуйте изменить фильтры или посмотрите другие категории мест'
                  }
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => router.push('/places')}
                >
                  Все места
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {getCurrentPlaces().map((place) => (
                    <Grid item xs={12} sm={6} md={4} key={place.id}>
                      <Card 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[8]
                          },
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onClick={() => router.push(`/places/${place.id}`)}
                      >
                        <Box sx={{ position: 'relative' }}>
                          <CardMedia
                            component="img"
                            height="200"
                            image={place.photos[0]?.url || '/placeholder-restaurant.jpg'}
                            alt={place.name}
                            sx={{ objectFit: 'cover' }}
                          />
                          
                          {/* Favorite Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleFavorite(place);
                              }}
                              disabled={actionLoading === place.id}
                              sx={{ 
                                backgroundColor: 'rgba(255,255,255,0.9)',
                                '&:hover': { backgroundColor: 'rgba(255,255,255,1)' }
                              }}
                            >
                              {place.userInteraction?.isFavorite ? (
                                <FavoriteIcon color="error" />
                              ) : (
                                <FavoriteBorderIcon />
                              )}
                            </IconButton>
                          </Box>

                          {/* Price Range Badge */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            zIndex: 1
                          }}>
                            <Chip
                              label={place.priceRange}
                              size="small"
                              color="warning"
                              sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                            />
                          </Box>

                          {/* Verification Badge */}
                          {place.verification.isVerified && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<VerifiedIcon />}
                                label="Проверено"
                                size="small"
                                color="primary"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}

                          {/* Distance Badge */}
                          {place.distance && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              right: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                label={formatDistance(place.distance)}
                                size="small"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}
                        </Box>

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" component="h3" gutterBottom>
                            {place.name}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Rating
                              value={place.rating.average}
                              precision={0.1}
                              size="small"
                              readOnly
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              {place.rating.average.toFixed(1)} ({place.rating.count})
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {place.location.district || place.location.city}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <MoneyIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {formatPriceRange(place.priceRange)}
                            </Typography>
                          </Box>

                          <Typography variant="body2" color="primary" sx={{ mb: 2, fontWeight: 'medium' }}>
                            {getRestaurantCuisine(place)}
                          </Typography>

                          {place.shortDescription && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {place.shortDescription.length > 80 
                                ? `${place.shortDescription.substring(0, 80)}...`
                                : place.shortDescription
                              }
                            </Typography>
                          )}

                          {/* Restaurant Features */}
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                            {place.acceptsCards && (
                              <Chip
                                icon={<CardIcon />}
                                label="Карты"
                                size="small"
                                variant="outlined"
                              />
                            )}
                            {place.hasParking && (
                              <Chip
                                icon={<ParkingIcon />}
                                label="Парковка"
                                size="small"
                                variant="outlined"
                              />
                            )}
                            {place.isAccessible && (
                              <Chip
                                icon={<AccessibleIcon />}
                                label="Доступно"
                                size="small"
                                variant="outlined"
                              />
                            )}
                            {place.isPetFriendly && (
                              <Chip
                                icon={<PetsIcon />}
                                label="С питомцами"
                                size="small"
                                variant="outlined"
                              />
                            )}
                            {place.hasDelivery && (
                              <Chip
                                icon={<DeliveryIcon />}
                                label="Доставка"
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>

                          {/* Restaurant Type Features */}
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {getRestaurantFeatures(place).slice(0, 2).map((feature, index) => (
                              <Chip
                                key={index}
                                label={feature}
                                size="small"
                                color="secondary"
                                variant="outlined"
                              />
                            ))}
                            {getRestaurantFeatures(place).length > 2 && (
                              <Chip
                                label={`+${getRestaurantFeatures(place).length - 2}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              size="small"
                              startIcon={<MeetingIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/meetings/create?placeId=${place.id}`);
                              }}
                            >
                              Встреча
                            </Button>
                            <Button
                              size="small"
                              startIcon={<EventIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/events/create?placeId=${place.id}`);
                              }}
                            >
                              Событие
                            </Button>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                            <Typography variant="caption" color="text.secondary">
                              {place.workingHours.isAlwaysOpen ? '24/7' : 'Работает'}
                            </Typography>
                          </Box>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default RestaurantsPage;
