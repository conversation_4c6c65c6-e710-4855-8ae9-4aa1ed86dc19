import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  useTheme,
  useMediaQuery,
  Fade,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ArrowBack,
  Security as SecurityIcon,
  Lock as LockIcon,
  Key as KeyIcon,
  Shield as ShieldIcon,
  PhoneAndroid as PhoneIcon,
  Computer as ComputerIcon,
  Tablet as TabletIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  VpnKey as VpnKeyIcon,
  History as HistoryIcon,
  DeviceHub as DeviceHubIcon,
  Logout as LogoutIcon,
  QrCode as QrCodeIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/contexts/AuthContext';
import { useSettings } from '../../src/contexts/SettingsContext';
import { formatLastSeen, formatDeviceInfo } from '../../src/services/settingsService';

const SecuritySettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    settings, 
    loading, 
    error, 
    updateSecuritySettings,
    changePassword,
    enable2FA,
    disable2FA,
    removeTrustedDevice,
    terminateSession,
    terminateAllSessions,
    loadSettings 
  } = useSettings();

  const [localSettings, setLocalSettings] = useState<any>(null);
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [show2FADialog, setShow2FADialog] = useState(false);
  const [showSessionsDialog, setShowSessionsDialog] = useState(false);
  const [showDevicesDialog, setShowDevicesDialog] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    
    if (settings?.security) {
      setLocalSettings(settings.security);
    }
  }, [user, router, settings]);

  const handleSettingChange = (key: string, value: any) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    if (!localSettings) return;
    
    try {
      setSaving(true);
      await updateSecuritySettings({ security: localSettings });
      setSuccess('Настройки безопасности обновлены');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setSuccess('Пароли не совпадают');
      return;
    }

    try {
      setSaving(true);
      await changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
        confirmPassword: passwordForm.confirmPassword
      });
      setSuccess('Пароль успешно изменен');
      setShowPasswordDialog(false);
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (err: any) {
      // Error handled by context
    } finally {
      setSaving(false);
    }
  };

  const handleEnable2FA = async () => {
    try {
      setSaving(true);
      const result = await enable2FA({ method: 'app' });
      setQrCode(result.qrCode || null);
      setBackupCodes(result.backupCodes);
      setSuccess('Двухфакторная аутентификация включена');
    } catch (err: any) {
      // Error handled by context
    } finally {
      setSaving(false);
    }
  };

  const handleDisable2FA = async () => {
    try {
      setSaving(true);
      await disable2FA(passwordForm.currentPassword);
      setSuccess('Двухфакторная аутентификация отключена');
      setShow2FADialog(false);
    } catch (err: any) {
      // Error handled by context
    } finally {
      setSaving(false);
    }
  };

  const handleRemoveDevice = async (deviceId: string) => {
    try {
      await removeTrustedDevice(deviceId);
      setSuccess('Устройство удалено из доверенных');
      loadSettings();
    } catch (err: any) {
      // Error handled by context
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      await terminateSession(sessionId);
      setSuccess('Сессия завершена');
      loadSettings();
    } catch (err: any) {
      // Error handled by context
    }
  };

  const handleTerminateAllSessions = async () => {
    try {
      await terminateAllSessions();
      setSuccess('Все сессии завершены');
      loadSettings();
    } catch (err: any) {
      // Error handled by context
    }
  };

  const securitySections = [
    {
      id: 'password',
      title: 'Пароль и аутентификация',
      icon: <LockIcon color="primary" />,
      description: 'Управление паролем и двухфакторной аутентификацией'
    },
    {
      id: 'devices',
      title: 'Доверенные устройства',
      icon: <DeviceHubIcon color="primary" />,
      description: 'Управление устройствами с доступом к аккаунту'
    },
    {
      id: 'sessions',
      title: 'Активные сессии',
      icon: <HistoryIcon color="primary" />,
      description: 'Просмотр и управление активными сессиями'
    },
    {
      id: 'settings',
      title: 'Настройки безопасности',
      icon: <ShieldIcon color="primary" />,
      description: 'Дополнительные параметры безопасности'
    }
  ];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Настройки безопасности - Likes & Love</title>
        <meta 
          name="description" 
          content="Настройки безопасности и защиты аккаунта в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <SecurityIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Настройки безопасности
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка настроек безопасности...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* Security Status */}
                  <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Статус безопасности
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          {settings?.security.twoFactorAuth.enabled ? (
                            <CheckCircleIcon color="success" />
                          ) : (
                            <WarningIcon color="warning" />
                          )}
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Двухфакторная аутентификация
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {settings?.security.twoFactorAuth.enabled ? 'Включена' : 'Отключена'}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <CheckCircleIcon color="success" />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Надежный пароль
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Соответствует требованиям
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <InfoIcon color="info" />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Доверенные устройства
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {settings?.security.trustedDevices.length || 0} устройств
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <InfoIcon color="info" />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              Активные сессии
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {settings?.security.loginHistory.filter(s => s.isActive).length || 0} сессий
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>

                  {/* Security Sections */}
                  {securitySections.map((section, index) => (
                    <Accordion key={section.id} defaultExpanded={index === 0}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          {section.icon}
                          <Box>
                            <Typography variant="h6">{section.title}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {section.description}
                            </Typography>
                          </Box>
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        {section.id === 'password' && (
                          <Box>
                            <List>
                              <ListItem>
                                <ListItemIcon>
                                  <KeyIcon />
                                </ListItemIcon>
                                <ListItemText
                                  primary="Изменить пароль"
                                  secondary="Обновить пароль для входа в аккаунт"
                                />
                                <ListItemSecondaryAction>
                                  <Button
                                    variant="outlined"
                                    onClick={() => setShowPasswordDialog(true)}
                                  >
                                    Изменить
                                  </Button>
                                </ListItemSecondaryAction>
                              </ListItem>

                              <Divider />

                              <ListItem>
                                <ListItemIcon>
                                  <VpnKeyIcon />
                                </ListItemIcon>
                                <ListItemText
                                  primary="Двухфакторная аутентификация"
                                  secondary={
                                    settings?.security.twoFactorAuth.enabled
                                      ? 'Включена для дополнительной защиты'
                                      : 'Добавьте дополнительный уровень защиты'
                                  }
                                />
                                <ListItemSecondaryAction>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {settings?.security.twoFactorAuth.enabled && (
                                      <Chip label="Включена" color="success" size="small" />
                                    )}
                                    <Button
                                      variant={settings?.security.twoFactorAuth.enabled ? "outlined" : "contained"}
                                      color={settings?.security.twoFactorAuth.enabled ? "error" : "primary"}
                                      onClick={() => {
                                        if (settings?.security.twoFactorAuth.enabled) {
                                          setShow2FADialog(true);
                                        } else {
                                          handleEnable2FA();
                                        }
                                      }}
                                    >
                                      {settings?.security.twoFactorAuth.enabled ? 'Отключить' : 'Включить'}
                                    </Button>
                                  </Box>
                                </ListItemSecondaryAction>
                              </ListItem>
                            </List>
                          </Box>
                        )}

                        {section.id === 'devices' && (
                          <Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                Устройства с доступом к вашему аккаунту
                              </Typography>
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={() => setShowDevicesDialog(true)}
                              >
                                Управление
                              </Button>
                            </Box>

                            <List>
                              {settings?.security.trustedDevices.slice(0, 3).map((device) => (
                                <ListItem key={device.id}>
                                  <ListItemIcon>
                                    {device.deviceType === 'mobile' ? <PhoneIcon /> :
                                     device.deviceType === 'tablet' ? <TabletIcon /> : <ComputerIcon />}
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={device.name}
                                    secondary={`${device.os}, ${device.browser} • ${formatLastSeen(device.lastUsed)}`}
                                  />
                                  {device.isCurrentDevice && (
                                    <Chip label="Текущее" color="primary" size="small" />
                                  )}
                                </ListItem>
                              ))}

                              {(settings?.security.trustedDevices.length || 0) > 3 && (
                                <ListItem>
                                  <ListItemText
                                    primary={`Еще ${(settings?.security.trustedDevices.length || 0) - 3} устройств`}
                                    secondary="Нажмите 'Управление' для просмотра всех"
                                  />
                                </ListItem>
                              )}
                            </List>
                          </Box>
                        )}

                        {section.id === 'sessions' && (
                          <Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                Активные сессии в приложении
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  onClick={() => setShowSessionsDialog(true)}
                                >
                                  Все сессии
                                </Button>
                                <Button
                                  variant="outlined"
                                  size="small"
                                  color="error"
                                  onClick={handleTerminateAllSessions}
                                >
                                  Завершить все
                                </Button>
                              </Box>
                            </Box>

                            <List>
                              {settings?.security.loginHistory.filter(s => s.isActive).slice(0, 3).map((session) => (
                                <ListItem key={session.id}>
                                  <ListItemIcon>
                                    {session.deviceInfo.type === 'mobile' ? <PhoneIcon /> :
                                     session.deviceInfo.type === 'tablet' ? <TabletIcon /> : <ComputerIcon />}
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={`${session.deviceInfo.browser} на ${session.deviceInfo.os}`}
                                    secondary={`${session.location.city}, ${session.location.country} • ${formatLastSeen(session.loginTime)}`}
                                  />
                                  <ListItemSecondaryAction>
                                    <IconButton
                                      edge="end"
                                      onClick={() => handleTerminateSession(session.id)}
                                      size="small"
                                    >
                                      <LogoutIcon />
                                    </IconButton>
                                  </ListItemSecondaryAction>
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}

                        {section.id === 'settings' && (
                          <Box>
                            <List>
                              <ListItem>
                                <ListItemText
                                  primary="Уведомления о входе"
                                  secondary="Получать уведомления при входе с новых устройств"
                                />
                                <ListItemSecondaryAction>
                                  <Switch
                                    checked={localSettings?.loginAlerts || false}
                                    onChange={(e) => handleSettingChange('loginAlerts', e.target.checked)}
                                    disabled={saving}
                                  />
                                </ListItemSecondaryAction>
                              </ListItem>

                              <Divider />

                              <ListItem>
                                <ListItemText
                                  primary="Отслеживание устройств"
                                  secondary="Сохранять информацию об используемых устройствах"
                                />
                                <ListItemSecondaryAction>
                                  <Switch
                                    checked={localSettings?.deviceTracking || false}
                                    onChange={(e) => handleSettingChange('deviceTracking', e.target.checked)}
                                    disabled={saving}
                                  />
                                </ListItemSecondaryAction>
                              </ListItem>

                              <Divider />

                              <ListItem>
                                <ListItemText
                                  primary="Автоматический выход"
                                  secondary={`Выходить из аккаунта через ${localSettings?.sessionTimeout || 30} минут неактивности`}
                                />
                                <ListItemSecondaryAction>
                                  <TextField
                                    select
                                    size="small"
                                    value={localSettings?.sessionTimeout || 30}
                                    onChange={(e) => handleSettingChange('sessionTimeout', Number(e.target.value))}
                                    disabled={saving}
                                    sx={{ minWidth: 100 }}
                                  >
                                    <MenuItem value={15}>15 мин</MenuItem>
                                    <MenuItem value={30}>30 мин</MenuItem>
                                    <MenuItem value={60}>1 час</MenuItem>
                                    <MenuItem value={120}>2 часа</MenuItem>
                                    <MenuItem value={0}>Никогда</MenuItem>
                                  </TextField>
                                </ListItemSecondaryAction>
                              </ListItem>
                            </List>

                            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                              <Button
                                variant="contained"
                                startIcon={<SaveIcon />}
                                onClick={handleSave}
                                disabled={saving}
                              >
                                {saving ? 'Сохранение...' : 'Сохранить настройки'}
                              </Button>
                            </Box>
                          </Box>
                        )}
                      </AccordionDetails>
                    </Accordion>
                  ))}

                  {/* Security Tips */}
                  <Alert severity="info" sx={{ mt: 4 }}>
                    <Typography variant="body2">
                      <strong>Совет:</strong> Регулярно проверяйте активные сессии и доверенные устройства.
                      Включите двухфакторную аутентификацию для максимальной защиты аккаунта.
                    </Typography>
                  </Alert>
                </Box>
              </Fade>
            )}

            {/* Password Change Dialog */}
            <Dialog
              open={showPasswordDialog}
              onClose={() => setShowPasswordDialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Изменить пароль</DialogTitle>
              <DialogContent>
                <Box sx={{ pt: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    fullWidth
                    label="Текущий пароль"
                    type={showPasswords.current ? 'text' : 'password'}
                    value={passwordForm.currentPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                    InputProps={{
                      endAdornment: (
                        <IconButton
                          onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                          edge="end"
                        >
                          {showPasswords.current ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      )
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Новый пароль"
                    type={showPasswords.new ? 'text' : 'password'}
                    value={passwordForm.newPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                    InputProps={{
                      endAdornment: (
                        <IconButton
                          onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                          edge="end"
                        >
                          {showPasswords.new ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      )
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Подтвердите новый пароль"
                    type={showPasswords.confirm ? 'text' : 'password'}
                    value={passwordForm.confirmPassword}
                    onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                    InputProps={{
                      endAdornment: (
                        <IconButton
                          onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                          edge="end"
                        >
                          {showPasswords.confirm ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      )
                    }}
                  />
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShowPasswordDialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handlePasswordChange}
                  variant="contained"
                  disabled={saving || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                >
                  {saving ? 'Изменение...' : 'Изменить пароль'}
                </Button>
              </DialogActions>
            </Dialog>

            {/* 2FA Disable Dialog */}
            <Dialog
              open={show2FADialog}
              onClose={() => setShow2FADialog(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Отключить двухфакторную аутентификацию</DialogTitle>
              <DialogContent>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Отключение двухфакторной аутентификации снизит безопасность вашего аккаунта.
                </Alert>

                <TextField
                  fullWidth
                  label="Введите пароль для подтверждения"
                  type="password"
                  value={passwordForm.currentPassword}
                  onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
                />
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setShow2FADialog(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleDisable2FA}
                  variant="contained"
                  color="error"
                  disabled={saving || !passwordForm.currentPassword}
                >
                  {saving ? 'Отключение...' : 'Отключить 2FA'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SecuritySettingsPage;
