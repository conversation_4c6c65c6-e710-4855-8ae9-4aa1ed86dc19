import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardMedia,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Avatar,
  Grid,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  Favorite as FavoriteIcon,
  Comment as CommentIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Schedule as ScheduleIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

// Типы для историй
interface Story {
  id: string;
  userId: string;
  user: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
  };
  type: 'photo' | 'video' | 'text';
  content: {
    url?: string;
    text?: string;
    backgroundColor?: string;
  };
  thumbnail: string;
  duration: number; // в секундах
  createdAt: string;
  expiresAt: string;
  views: number;
  likes: number;
  comments: number;
  isViewed: boolean;
  isLiked: boolean;
}

interface StoryGroup {
  userId: string;
  user: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
  };
  stories: Story[];
  hasUnviewed: boolean;
  lastStoryAt: string;
}

const StoriesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storyGroups, setStoryGroups] = useState<StoryGroup[]>([]);
  const [selectedStory, setSelectedStory] = useState<Story | null>(null);
  const [storyViewerOpen, setStoryViewerOpen] = useState(false);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [storyProgress, setStoryProgress] = useState(0);

  // Загрузка историй при монтировании компонента
  useEffect(() => {
    loadStories();
  }, []);

  const loadStories = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения историй
      // const response = await getStories();
      
      // Мок данные
      const mockStoryGroups: StoryGroup[] = [
        {
          userId: 'user1',
          user: {
            id: 'user1',
            name: 'Анна Петрова',
            avatar: '/avatars/anna.jpg',
            isVerified: true
          },
          stories: [
            {
              id: 'story1',
              userId: 'user1',
              user: {
                id: 'user1',
                name: 'Анна Петрова',
                avatar: '/avatars/anna.jpg',
                isVerified: true
              },
              type: 'photo',
              content: {
                url: '/stories/story1.jpg'
              },
              thumbnail: '/stories/story1_thumb.jpg',
              duration: 5,
              createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              expiresAt: new Date(Date.now() + 22 * 60 * 60 * 1000).toISOString(),
              views: 45,
              likes: 12,
              comments: 3,
              isViewed: false,
              isLiked: false
            }
          ],
          hasUnviewed: true,
          lastStoryAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          userId: 'user2',
          user: {
            id: 'user2',
            name: 'Михаил Иванов',
            avatar: '/avatars/mikhail.jpg',
            isVerified: false
          },
          stories: [
            {
              id: 'story2',
              userId: 'user2',
              user: {
                id: 'user2',
                name: 'Михаил Иванов',
                avatar: '/avatars/mikhail.jpg',
                isVerified: false
              },
              type: 'video',
              content: {
                url: '/stories/story2.mp4'
              },
              thumbnail: '/stories/story2_thumb.jpg',
              duration: 15,
              createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
              expiresAt: new Date(Date.now() + 20 * 60 * 60 * 1000).toISOString(),
              views: 23,
              likes: 8,
              comments: 1,
              isViewed: true,
              isLiked: true
            }
          ],
          hasUnviewed: false,
          lastStoryAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
        }
      ];

      setStoryGroups(mockStoryGroups);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке историй');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStory = () => {
    router.push('/stories/create');
  };

  const handleStoryClick = (groupIndex: number, storyIndex: number = 0) => {
    setCurrentGroupIndex(groupIndex);
    setCurrentStoryIndex(storyIndex);
    setSelectedStory(storyGroups[groupIndex].stories[storyIndex]);
    setStoryViewerOpen(true);
    setStoryProgress(0);
  };

  const handleCloseStoryViewer = () => {
    setStoryViewerOpen(false);
    setSelectedStory(null);
    setStoryProgress(0);
  };

  const handleNextStory = () => {
    const currentGroup = storyGroups[currentGroupIndex];
    if (currentStoryIndex < currentGroup.stories.length - 1) {
      // Следующая история в той же группе
      const nextIndex = currentStoryIndex + 1;
      setCurrentStoryIndex(nextIndex);
      setSelectedStory(currentGroup.stories[nextIndex]);
      setStoryProgress(0);
    } else if (currentGroupIndex < storyGroups.length - 1) {
      // Первая история следующей группы
      const nextGroupIndex = currentGroupIndex + 1;
      setCurrentGroupIndex(nextGroupIndex);
      setCurrentStoryIndex(0);
      setSelectedStory(storyGroups[nextGroupIndex].stories[0]);
      setStoryProgress(0);
    } else {
      // Конец всех историй
      handleCloseStoryViewer();
    }
  };

  const handlePrevStory = () => {
    if (currentStoryIndex > 0) {
      // Предыдущая история в той же группе
      const prevIndex = currentStoryIndex - 1;
      setCurrentStoryIndex(prevIndex);
      setSelectedStory(storyGroups[currentGroupIndex].stories[prevIndex]);
      setStoryProgress(0);
    } else if (currentGroupIndex > 0) {
      // Последняя история предыдущей группы
      const prevGroupIndex = currentGroupIndex - 1;
      const prevGroup = storyGroups[prevGroupIndex];
      const lastStoryIndex = prevGroup.stories.length - 1;
      setCurrentGroupIndex(prevGroupIndex);
      setCurrentStoryIndex(lastStoryIndex);
      setSelectedStory(prevGroup.stories[lastStoryIndex]);
      setStoryProgress(0);
    }
  };

  const handleLikeStory = async (storyId: string) => {
    try {
      // Здесь будет вызов API для лайка истории
      // await likeStory(storyId);
      
      // Обновляем локальное состояние
      setStoryGroups(prev => prev.map(group => ({
        ...group,
        stories: group.stories.map(story => 
          story.id === storyId 
            ? { ...story, isLiked: !story.isLiked, likes: story.isLiked ? story.likes - 1 : story.likes + 1 }
            : story
        )
      })));

      if (selectedStory && selectedStory.id === storyId) {
        setSelectedStory(prev => prev ? {
          ...prev,
          isLiked: !prev.isLiked,
          likes: prev.isLiked ? prev.likes - 1 : prev.likes + 1
        } : null);
      }
    } catch (err: any) {
      setError(err.message || 'Ошибка при лайке истории');
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ч назад`;
    return `${Math.floor(diffInMinutes / 1440)} дн назад`;
  };

  // Автопрогресс для просмотра историй
  useEffect(() => {
    if (storyViewerOpen && selectedStory) {
      const interval = setInterval(() => {
        setStoryProgress(prev => {
          const newProgress = prev + (100 / selectedStory.duration);
          if (newProgress >= 100) {
            handleNextStory();
            return 0;
          }
          return newProgress;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [storyViewerOpen, selectedStory, currentStoryIndex, currentGroupIndex]);

  if (!user) {
    return (
      <Layout title="Истории">
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Истории - Likes & Love</title>
        <meta name="description" content="Смотрите истории других пользователей и делитесь своими моментами в приложении знакомств Likes & Love" />
        <meta name="keywords" content="истории, stories, знакомства, фото, видео, моменты" />
      </Head>

      <Layout title="Истории">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h4" component="h1" fontWeight="bold" sx={{ mb: 2 }}>
                Истории
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Делитесь моментами своей жизни и смотрите истории других
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {/* Загрузка */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                {/* Сетка историй */}
                <Grid container spacing={2}>
                  {/* Кнопка создания истории */}
                  <Grid item xs={6} sm={4} md={3} lg={2}>
                    <Card 
                      sx={{ 
                        height: 200, 
                        cursor: 'pointer',
                        border: '2px dashed',
                        borderColor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        }
                      }}
                      onClick={handleCreateStory}
                    >
                      <Box sx={{ textAlign: 'center' }}>
                        <AddIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                        <Typography variant="body2" color="primary">
                          Создать историю
                        </Typography>
                      </Box>
                    </Card>
                  </Grid>

                  {/* Истории пользователей */}
                  {storyGroups.map((group, groupIndex) => (
                    <Grid item xs={6} sm={4} md={3} lg={2} key={group.userId}>
                      <Card 
                        sx={{ 
                          height: 200, 
                          cursor: 'pointer',
                          position: 'relative',
                          overflow: 'hidden',
                          '&:hover': {
                            transform: 'scale(1.02)',
                            transition: 'transform 0.2s'
                          }
                        }}
                        onClick={() => handleStoryClick(groupIndex)}
                      >
                        <CardMedia
                          component="img"
                          height="200"
                          image={group.stories[0].thumbnail}
                          alt={`История ${group.user.name}`}
                          sx={{ objectFit: 'cover' }}
                        />
                        
                        {/* Градиент для текста */}
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                            p: 1
                          }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar 
                              src={group.user.avatar} 
                              sx={{ 
                                width: 24, 
                                height: 24,
                                border: group.hasUnviewed ? '2px solid' : 'none',
                                borderColor: 'primary.main'
                              }} 
                            />
                            <Typography 
                              variant="caption" 
                              color="white" 
                              sx={{ 
                                fontWeight: 'bold',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {group.user.name}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="rgba(255,255,255,0.8)">
                            {formatTimeAgo(group.lastStoryAt)}
                          </Typography>
                        </Box>

                        {/* Индикатор непросмотренных */}
                        {group.hasUnviewed && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              backgroundColor: 'primary.main'
                            }}
                          />
                        )}

                        {/* Количество историй */}
                        {group.stories.length > 1 && (
                          <Chip
                            label={group.stories.length}
                            size="small"
                            sx={{
                              position: 'absolute',
                              top: 8,
                              left: 8,
                              backgroundColor: 'rgba(0,0,0,0.6)',
                              color: 'white'
                            }}
                          />
                        )}
                      </Card>
                    </Grid>
                  ))}
                </Grid>

                {/* Пустое состояние */}
                {storyGroups.length === 0 && (
                  <Box sx={{ textAlign: 'center', py: 8 }}>
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                      Пока нет историй
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Станьте первым, кто поделится своей историей!
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleCreateStory}
                    >
                      Создать историю
                    </Button>
                  </Box>
                )}
              </>
            )}

            {/* FAB для создания истории */}
            <Fab
              color="primary"
              aria-label="создать историю"
              sx={{
                position: 'fixed',
                bottom: 16,
                right: 16,
                display: { xs: 'flex', sm: 'none' }
              }}
              onClick={handleCreateStory}
            >
              <AddIcon />
            </Fab>

            {/* Просмотрщик историй */}
            <Dialog
              open={storyViewerOpen}
              onClose={handleCloseStoryViewer}
              maxWidth={false}
              fullScreen={isMobile}
              PaperProps={{
                sx: {
                  backgroundColor: 'black',
                  maxWidth: isMobile ? '100%' : 400,
                  maxHeight: isMobile ? '100%' : 600,
                  m: 0
                }
              }}
            >
              {selectedStory && (
                <>
                  {/* Прогресс бар */}
                  <LinearProgress
                    variant="determinate"
                    value={storyProgress}
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      zIndex: 1,
                      backgroundColor: 'rgba(255,255,255,0.3)',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: 'white'
                      }
                    }}
                  />

                  {/* Заголовок */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 16,
                      left: 16,
                      right: 16,
                      zIndex: 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar src={selectedStory.user.avatar} sx={{ width: 32, height: 32 }} />
                      <Box>
                        <Typography variant="body2" color="white" fontWeight="bold">
                          {selectedStory.user.name}
                        </Typography>
                        <Typography variant="caption" color="rgba(255,255,255,0.8)">
                          {formatTimeAgo(selectedStory.createdAt)}
                        </Typography>
                      </Box>
                    </Box>
                    <IconButton onClick={handleCloseStoryViewer} sx={{ color: 'white' }}>
                      <CloseIcon />
                    </IconButton>
                  </Box>

                  {/* Контент истории */}
                  <Box
                    sx={{
                      position: 'relative',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    {selectedStory.type === 'photo' && (
                      <img
                        src={selectedStory.content.url}
                        alt="История"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '100%',
                          objectFit: 'contain'
                        }}
                      />
                    )}
                    {selectedStory.type === 'video' && (
                      <video
                        src={selectedStory.content.url}
                        autoPlay
                        muted
                        style={{
                          maxWidth: '100%',
                          maxHeight: '100%',
                          objectFit: 'contain'
                        }}
                      />
                    )}

                    {/* Области для навигации */}
                    <Box
                      sx={{
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '30%',
                        cursor: 'pointer'
                      }}
                      onClick={handlePrevStory}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        right: 0,
                        top: 0,
                        bottom: 0,
                        width: '30%',
                        cursor: 'pointer'
                      }}
                      onClick={handleNextStory}
                    />
                  </Box>

                  {/* Действия */}
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 16,
                      left: 16,
                      right: 16,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <IconButton
                          onClick={() => handleLikeStory(selectedStory.id)}
                          sx={{ color: selectedStory.isLiked ? 'error.main' : 'white' }}
                        >
                          <FavoriteIcon />
                        </IconButton>
                        <Typography variant="caption" color="white">
                          {selectedStory.likes}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <VisibilityIcon sx={{ color: 'white', fontSize: 20 }} />
                        <Typography variant="caption" color="white">
                          {selectedStory.views}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </>
              )}
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default StoriesPage;
