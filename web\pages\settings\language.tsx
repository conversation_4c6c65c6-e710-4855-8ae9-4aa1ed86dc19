import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Divider,
  Avatar
} from '@mui/material';
import {
  ArrowBack,
  Language as LanguageIcon,
  Check as CheckIcon,
  Save as SaveIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  Translate as TranslateIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

// Типы для языков
interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  isAvailable: boolean;
  completeness: number; // процент переведенных строк
}

const LanguagePage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState(user?.language || 'ru');

  // Доступные языки
  const languages: Language[] = [
    {
      code: 'ru',
      name: 'Русский',
      nativeName: 'Русский',
      flag: '🇷🇺',
      isAvailable: true,
      completeness: 100
    },
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      isAvailable: true,
      completeness: 100
    },
    {
      code: 'uk',
      name: 'Українська',
      nativeName: 'Українська',
      flag: '🇺🇦',
      isAvailable: true,
      completeness: 95
    },
    {
      code: 'be',
      name: 'Беларуская',
      nativeName: 'Беларуская',
      flag: '🇧🇾',
      isAvailable: true,
      completeness: 90
    },
    {
      code: 'kk',
      name: 'Қазақша',
      nativeName: 'Қазақша',
      flag: '🇰🇿',
      isAvailable: true,
      completeness: 85
    },
    {
      code: 'de',
      name: 'Deutsch',
      nativeName: 'Deutsch',
      flag: '🇩🇪',
      isAvailable: true,
      completeness: 80
    },
    {
      code: 'fr',
      name: 'Français',
      nativeName: 'Français',
      flag: '🇫🇷',
      isAvailable: true,
      completeness: 75
    },
    {
      code: 'es',
      name: 'Español',
      nativeName: 'Español',
      flag: '🇪🇸',
      isAvailable: true,
      completeness: 70
    },
    {
      code: 'it',
      name: 'Italiano',
      nativeName: 'Italiano',
      flag: '🇮🇹',
      isAvailable: false,
      completeness: 60
    },
    {
      code: 'pt',
      name: 'Português',
      nativeName: 'Português',
      flag: '🇵🇹',
      isAvailable: false,
      completeness: 55
    },
    {
      code: 'zh',
      name: '中文',
      nativeName: '中文',
      flag: '🇨🇳',
      isAvailable: false,
      completeness: 40
    },
    {
      code: 'ja',
      name: '日本語',
      nativeName: '日本語',
      flag: '🇯🇵',
      isAvailable: false,
      completeness: 30
    }
  ];

  const handleLanguageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedLanguage(event.target.value);
  };

  const handleSaveLanguage = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для сохранения языка
      // await updateUserLanguage(selectedLanguage);

      // Обновляем профиль пользователя
      await updateProfile({
        language: selectedLanguage
      });

      setSuccess('Язык интерфейса успешно изменен');
      
      // Здесь можно добавить логику для перезагрузки страницы с новым языком
      // window.location.reload();
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении языка');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/settings');
  };

  const getCompletenessColor = (completeness: number) => {
    if (completeness >= 95) return 'success';
    if (completeness >= 80) return 'warning';
    return 'error';
  };

  const getCompletenessText = (completeness: number) => {
    if (completeness >= 95) return 'Полный перевод';
    if (completeness >= 80) return 'Почти готов';
    if (completeness >= 60) return 'В разработке';
    return 'Скоро';
  };

  if (!user) {
    return (
      <Layout title="Язык интерфейса">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  const availableLanguages = languages.filter(lang => lang.isAvailable);
  const upcomingLanguages = languages.filter(lang => !lang.isAvailable);
  const hasChanges = selectedLanguage !== (user.language || 'ru');

  return (
    <>
      <Head>
        <title>Язык интерфейса - Likes & Love</title>
        <meta name="description" content="Выбор языка интерфейса в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Язык интерфейса">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Typography color="text.primary">Язык</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <LanguageIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Язык интерфейса
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Выберите предпочитаемый язык для интерфейса приложения
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Информация */}
            <Alert severity="info" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Примечание:</strong> После смены языка интерфейс будет отображаться на выбранном языке. 
                Некоторые языки могут быть переведены не полностью.
              </Typography>
            </Alert>

            {/* Доступные языки */}
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                  Доступные языки
                </Typography>

                <FormControl component="fieldset" fullWidth>
                  <RadioGroup
                    value={selectedLanguage}
                    onChange={handleLanguageChange}
                  >
                    <List>
                      {availableLanguages.map((language, index) => (
                        <React.Fragment key={language.code}>
                          <ListItem>
                            <ListItemIcon>
                              <Avatar sx={{ width: 32, height: 32, fontSize: 16 }}>
                                {language.flag}
                              </Avatar>
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                  <Typography variant="body1" fontWeight="bold">
                                    {language.nativeName}
                                  </Typography>
                                  {language.name !== language.nativeName && (
                                    <Typography variant="body2" color="text.secondary">
                                      ({language.name})
                                    </Typography>
                                  )}
                                  {language.completeness < 100 && (
                                    <Typography 
                                      variant="caption" 
                                      color={`${getCompletenessColor(language.completeness)}.main`}
                                      sx={{ 
                                        bgcolor: `${getCompletenessColor(language.completeness)}.light`,
                                        px: 1,
                                        py: 0.5,
                                        borderRadius: 1
                                      }}
                                    >
                                      {getCompletenessText(language.completeness)} ({language.completeness}%)
                                    </Typography>
                                  )}
                                </Box>
                              }
                              secondary={
                                language.completeness < 100 ? 
                                  `Переведено ${language.completeness}% интерфейса` : 
                                  'Полный перевод интерфейса'
                              }
                            />
                            <ListItemSecondaryAction>
                              <FormControlLabel
                                value={language.code}
                                control={<Radio />}
                                label=""
                                sx={{ mr: 0 }}
                              />
                            </ListItemSecondaryAction>
                          </ListItem>
                          {index < availableLanguages.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  </RadioGroup>
                </FormControl>

                {/* Кнопка сохранения */}
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
                  <Button onClick={handleBack} disabled={loading}>
                    Отмена
                  </Button>
                  <Button
                    onClick={handleSaveLanguage}
                    variant="contained"
                    disabled={loading || !hasChanges}
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    {loading ? 'Сохранение...' : 'Сохранить'}
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Языки в разработке */}
            {upcomingLanguages.length > 0 && (
              <Card>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                    Языки в разработке
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Эти языки находятся в процессе перевода и будут доступны в ближайшее время
                  </Typography>

                  <List>
                    {upcomingLanguages.map((language, index) => (
                      <React.Fragment key={language.code}>
                        <ListItem>
                          <ListItemIcon>
                            <Avatar sx={{ width: 32, height: 32, fontSize: 16, opacity: 0.6 }}>
                              {language.flag}
                            </Avatar>
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Typography variant="body1" color="text.secondary">
                                  {language.nativeName}
                                </Typography>
                                {language.name !== language.nativeName && (
                                  <Typography variant="body2" color="text.disabled">
                                    ({language.name})
                                  </Typography>
                                )}
                                <Typography 
                                  variant="caption" 
                                  color={`${getCompletenessColor(language.completeness)}.main`}
                                  sx={{ 
                                    bgcolor: `${getCompletenessColor(language.completeness)}.light`,
                                    px: 1,
                                    py: 0.5,
                                    borderRadius: 1
                                  }}
                                >
                                  {getCompletenessText(language.completeness)} ({language.completeness}%)
                                </Typography>
                              </Box>
                            }
                            secondary={`Переведено ${language.completeness}% интерфейса`}
                          />
                        </ListItem>
                        {index < upcomingLanguages.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </CardContent>
              </Card>
            )}

            {/* Дополнительная информация */}
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <TranslateIcon color="primary" />
                  <Typography variant="h6" fontWeight="bold">
                    Помочь с переводом
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Хотите помочь перевести приложение на ваш язык или улучшить существующий перевод? 
                  Мы будем рады вашей помощи!
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<TranslateIcon />}
                  onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                >
                  Связаться с нами
                </Button>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default LanguagePage;
