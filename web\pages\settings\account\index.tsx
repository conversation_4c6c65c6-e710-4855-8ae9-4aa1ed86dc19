import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  Fade,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ArrowBack,
  AccountCircle as AccountIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Lock as LockIcon,
  Edit as EditIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  ChevronRight as ChevronRightIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Типы для настроек аккаунта
interface AccountSettings {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  bio: string;
}

// Схема валидации
const accountSchema = yup.object({
  firstName: yup.string().required('Имя обязательно').min(2, 'Минимум 2 символа'),
  lastName: yup.string().required('Фамилия обязательна').min(2, 'Минимум 2 символа'),
  email: yup.string().email('Неверный формат email').required('Email обязателен'),
  phoneNumber: yup.string().matches(/^\+7\d{10}$/, 'Неверный формат телефона'),
  bio: yup.string().max(500, 'Максимум 500 символов')
});

const AccountSettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch
  } = useForm<AccountSettings>({
    resolver: yupResolver(accountSchema),
    defaultValues: {
      firstName: user?.profile?.firstName || '',
      lastName: user?.profile?.lastName || '',
      email: user?.email || '',
      phoneNumber: user?.phoneNumber || '',
      dateOfBirth: user?.profile?.dateOfBirth || '',
      gender: user?.profile?.gender || 'other',
      bio: user?.profile?.bio || ''
    }
  });

  useEffect(() => {
    if (user) {
      reset({
        firstName: user.profile?.firstName || '',
        lastName: user.profile?.lastName || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
        dateOfBirth: user.profile?.dateOfBirth || '',
        gender: user.profile?.gender || 'other',
        bio: user.profile?.bio || ''
      });
    }
  }, [user, reset]);

  const handleSave = async (data: AccountSettings) => {
    try {
      setLoading(true);
      setError(null);
      
      await updateProfile({
        profile: {
          firstName: data.firstName,
          lastName: data.lastName,
          dateOfBirth: data.dateOfBirth,
          gender: data.gender,
          bio: data.bio
        },
        email: data.email,
        phoneNumber: data.phoneNumber
      });

      setSuccess('Настройки аккаунта успешно обновлены');
      setEditMode(false);
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    reset();
    setEditMode(false);
    setError(null);
  };

  const handleDeleteAccount = () => {
    setDeleteDialogOpen(true);
  };

  const confirmDeleteAccount = async () => {
    try {
      setLoading(true);
      // Здесь будет вызов API для удаления аккаунта
      // await deleteAccount();
      router.push('/auth/login');
    } catch (err: any) {
      setError(err.message || 'Ошибка при удалении аккаунта');
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  if (!user) {
    return (
      <Layout title="Настройки аккаунта">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Настройки аккаунта - Likes & Love</title>
        <meta name="description" content="Управление настройками аккаунта в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Настройки аккаунта">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton 
                onClick={() => router.back()} 
                sx={{ display: { xs: 'flex', md: 'none' } }}
              >
                <ArrowBack />
              </IconButton>
              <AccountIcon color="primary" sx={{ fontSize: 32 }} />
              <Typography variant="h4" component="h1" fontWeight="bold">
                Настройки аккаунта
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <Grid container spacing={3}>
              {/* Основная информация */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                      <Typography variant="h6" fontWeight="bold">
                        Основная информация
                      </Typography>
                      {!editMode && (
                        <Button
                          startIcon={<EditIcon />}
                          onClick={() => setEditMode(true)}
                          variant="outlined"
                          size="small"
                        >
                          Редактировать
                        </Button>
                      )}
                    </Box>

                    <form onSubmit={handleSubmit(handleSave)}>
                      <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="firstName"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Имя"
                                fullWidth
                                disabled={!editMode}
                                error={!!errors.firstName}
                                helperText={errors.firstName?.message}
                                variant={editMode ? "outlined" : "filled"}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="lastName"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Фамилия"
                                fullWidth
                                disabled={!editMode}
                                error={!!errors.lastName}
                                helperText={errors.lastName?.message}
                                variant={editMode ? "outlined" : "filled"}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <Controller
                            name="bio"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="О себе"
                                fullWidth
                                multiline
                                rows={3}
                                disabled={!editMode}
                                error={!!errors.bio}
                                helperText={errors.bio?.message || `${watch('bio')?.length || 0}/500`}
                                variant={editMode ? "outlined" : "filled"}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="dateOfBirth"
                            control={control}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                label="Дата рождения"
                                type="date"
                                fullWidth
                                disabled={!editMode}
                                InputLabelProps={{ shrink: true }}
                                variant={editMode ? "outlined" : "filled"}
                              />
                            )}
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Controller
                            name="gender"
                            control={control}
                            render={({ field }) => (
                              <FormControl fullWidth disabled={!editMode}>
                                <InputLabel>Пол</InputLabel>
                                <Select {...field} label="Пол">
                                  <MenuItem value="male">Мужской</MenuItem>
                                  <MenuItem value="female">Женский</MenuItem>
                                  <MenuItem value="other">Другой</MenuItem>
                                </Select>
                              </FormControl>
                            )}
                          />
                        </Grid>

                        {editMode && (
                          <Grid item xs={12}>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                              <Button
                                onClick={handleCancel}
                                startIcon={<CancelIcon />}
                                disabled={loading}
                              >
                                Отмена
                              </Button>
                              <Button
                                type="submit"
                                variant="contained"
                                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                                disabled={loading || !isDirty}
                              >
                                Сохранить
                              </Button>
                            </Box>
                          </Grid>
                        )}
                      </Grid>
                    </form>
                  </CardContent>
                </Card>
              </Grid>

              {/* Контактная информация */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                      Контактная информация
                    </Typography>

                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <EmailIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Email"
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {user.email}
                              {user.emailVerified ? (
                                <Chip
                                  icon={<VerifiedIcon />}
                                  label="Подтвержден"
                                  size="small"
                                  color="success"
                                />
                              ) : (
                                <Chip
                                  icon={<WarningIcon />}
                                  label="Не подтвержден"
                                  size="small"
                                  color="warning"
                                />
                              )}
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Button
                            size="small"
                            onClick={() => router.push('/settings/account/email')}
                            endIcon={<ChevronRightIcon />}
                          >
                            Изменить
                          </Button>
                        </ListItemSecondaryAction>
                      </ListItem>

                      <Divider />

                      <ListItem>
                        <ListItemIcon>
                          <PhoneIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Телефон"
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {user.phoneNumber || 'Не указан'}
                              {user.phoneVerified ? (
                                <Chip
                                  icon={<VerifiedIcon />}
                                  label="Подтвержден"
                                  size="small"
                                  color="success"
                                />
                              ) : (
                                <Chip
                                  icon={<WarningIcon />}
                                  label="Не подтвержден"
                                  size="small"
                                  color="warning"
                                />
                              )}
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Button
                            size="small"
                            onClick={() => router.push('/settings/account/phone')}
                            endIcon={<ChevronRightIcon />}
                          >
                            Изменить
                          </Button>
                        </ListItemSecondaryAction>
                      </ListItem>

                      <Divider />

                      <ListItem>
                        <ListItemIcon>
                          <LockIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Пароль"
                          secondary="Последнее изменение: 2 недели назад"
                        />
                        <ListItemSecondaryAction>
                          <Button
                            size="small"
                            onClick={() => router.push('/settings/account/password')}
                            endIcon={<ChevronRightIcon />}
                          >
                            Изменить
                          </Button>
                        </ListItemSecondaryAction>
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Опасная зона */}
              <Grid item xs={12}>
                <Card sx={{ border: '1px solid', borderColor: 'error.main' }}>
                  <CardContent>
                    <Typography variant="h6" fontWeight="bold" color="error" sx={{ mb: 2 }}>
                      Опасная зона
                    </Typography>

                    <Alert severity="warning" sx={{ mb: 3 }}>
                      Удаление аккаунта необратимо. Все ваши данные, сообщения и совпадения будут удалены навсегда.
                    </Alert>

                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={handleDeleteAccount}
                      fullWidth={isMobile}
                    >
                      Удалить аккаунт
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </Container>

        {/* Диалог подтверждения удаления */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <DeleteIcon color="error" />
              Удалить аккаунт?
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography>
              Вы уверены, что хотите удалить свой аккаунт? Это действие нельзя отменить.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Будут удалены:
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText primary="• Профиль и все фотографии" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• Все сообщения и совпадения" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• История активности" />
              </ListItem>
              <ListItem>
                <ListItemText primary="• Подписка и платежная информация" />
              </ListItem>
            </List>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>
              Отмена
            </Button>
            <Button
              onClick={confirmDeleteAccount}
              color="error"
              variant="contained"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
            >
              Удалить навсегда
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default AccountSettingsPage;
