import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Divider,
  LinearProgress,
  useTheme,
  useMediaQuery,
  Fade,
  Zoom
} from '@mui/material';
import {
  ArrowBack,
  Diamond as DiamondIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Crown as CrownIcon,
  Verified as VerifiedIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Support as SupportIcon,
  FlashOn as FlashOnIcon,
  Visibility as VisibilityIcon,
  LocationOn as LocationIcon,
  Analytics as AnalyticsIcon,
  VpnKey as VpnKeyIcon,
  Shield as ShieldIcon,
  Chat as ChatIcon,
  VideoCall as VideoCallIcon,
  Favorite as FavoriteIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { useSubscription } from '../../src/contexts/SubscriptionContext';
import { formatCurrency } from '../../src/services/subscriptionService';

interface VipBenefit {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  isExclusive: boolean;
  value?: string;
}

const VipPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    currentSubscription, 
    availablePlans, 
    usage,
    loading, 
    hasFeature,
    getFeatureUsage 
  } = useSubscription();

  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  const isVipUser = currentSubscription?.plan.name === 'vip' && currentSubscription.status === 'active';
  const vipPlan = availablePlans.find(plan => plan.name === 'vip');

  const vipBenefits: VipBenefit[] = [
    {
      id: 'unlimited_everything',
      title: 'Безлимитные возможности',
      description: 'Неограниченные лайки, супер-лайки, бусты и все остальные функции',
      icon: <FlashOnIcon sx={{ fontSize: 40, color: '#ffc107' }} />,
      isExclusive: true,
      value: '∞'
    },
    {
      id: 'priority_matching',
      title: 'Приоритетное сопоставление',
      description: 'Ваш профиль показывается первым в поиске других пользователей',
      icon: <TrendingUpIcon sx={{ fontSize: 40, color: '#ff5722' }} />,
      isExclusive: true
    },
    {
      id: 'exclusive_features',
      title: 'Эксклюзивные функции',
      description: 'Доступ к функциям, недоступным обычным пользователям',
      icon: <DiamondIcon sx={{ fontSize: 40, color: '#9c27b0' }} />,
      isExclusive: true
    },
    {
      id: 'global_passport',
      title: 'Глобальный паспорт',
      description: 'Знакомьтесь с людьми в любой точке мира без ограничений',
      icon: <LocationIcon sx={{ fontSize: 40, color: '#2196f3' }} />,
      isExclusive: true
    },
    {
      id: 'advanced_analytics',
      title: 'Расширенная аналитика',
      description: 'Подробная статистика и инсайты о вашем профиле и активности',
      icon: <AnalyticsIcon sx={{ fontSize: 40, color: '#3f51b5' }} />,
      isExclusive: true
    },
    {
      id: 'vip_support',
      title: 'VIP поддержка',
      description: 'Персональный менеджер и мгновенная поддержка 24/7',
      icon: <SupportIcon sx={{ fontSize: 40, color: '#4caf50' }} />,
      isExclusive: true
    },
    {
      id: 'profile_verification',
      title: 'Мгновенная верификация',
      description: 'Приоритетная проверка профиля и получение статуса верификации',
      icon: <VerifiedIcon sx={{ fontSize: 40, color: '#00bcd4' }} />,
      isExclusive: false
    },
    {
      id: 'incognito_mode',
      title: 'Режим невидимки',
      description: 'Просматривайте профили анонимно, не оставляя следов',
      icon: <VisibilityIcon sx={{ fontSize: 40, color: '#607d8b' }} />,
      isExclusive: false
    },
    {
      id: 'premium_security',
      title: 'Максимальная безопасность',
      description: 'Усиленная защита данных и дополнительные меры безопасности',
      icon: <ShieldIcon sx={{ fontSize: 40, color: '#f44336' }} />,
      isExclusive: true
    }
  ];

  const vipStats = [
    {
      title: 'Больше совпадений',
      value: '+300%',
      description: 'VIP пользователи получают в 3 раза больше совпадений',
      icon: <FavoriteIcon color="error" />
    },
    {
      title: 'Быстрее отклик',
      value: '2x',
      description: 'В 2 раза быстрее получают ответы на сообщения',
      icon: <ChatIcon color="primary" />
    },
    {
      title: 'Качество профилей',
      value: '95%',
      description: 'Процент верифицированных профилей среди VIP',
      icon: <VerifiedIcon color="success" />
    },
    {
      title: 'Успешные встречи',
      value: '+250%',
      description: 'Больше успешных встреч и долгосрочных отношений',
      icon: <StarIcon sx={{ color: '#ffc107' }} />
    }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
  }, [user, router]);

  const handleUpgradeToVip = () => {
    router.push('/subscription/plans?highlight=vip');
  };

  const getUsagePercentage = (feature: string) => {
    if (!usage) return 0;
    const featureUsage = getFeatureUsage(feature);
    if (featureUsage.limit === 'unlimited') return 100;
    return Math.min((featureUsage.used / (featureUsage.limit as number)) * 100, 100);
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>VIP статус - Likes & Love</title>
        <meta 
          name="description" 
          content="Получите VIP статус в приложении знакомств Likes & Love и откройте эксклюзивные возможности" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/subscription/vip" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <CrownIcon sx={{ mr: 2, verticalAlign: 'middle', color: '#ffc107' }} />
                VIP статус
              </Typography>
              {!isVipUser && (
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<CrownIcon />}
                  onClick={handleUpgradeToVip}
                  sx={{
                    background: 'linear-gradient(45deg, #ffc107 30%, #ff8f00 90%)',
                    color: 'white',
                    fontWeight: 'bold'
                  }}
                >
                  Стать VIP
                </Button>
              )}
            </Box>

            {/* VIP Status Alert */}
            {isVipUser ? (
              <Alert 
                severity="success" 
                sx={{ 
                  mb: 4,
                  background: 'linear-gradient(45deg, #4caf50 30%, #8bc34a 90%)',
                  color: 'white',
                  '& .MuiAlert-icon': { color: 'white' }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CrownIcon />
                  <Typography variant="body1" fontWeight="bold">
                    Поздравляем! У вас VIP статус
                  </Typography>
                </Box>
              </Alert>
            ) : (
              <Alert severity="info" sx={{ mb: 4 }}>
                <Typography variant="body1">
                  Получите VIP статус и откройте все эксклюзивные возможности приложения
                </Typography>
              </Alert>
            )}

            {/* Hero Section */}
            <Paper
              elevation={4}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                p: 4,
                mb: 4,
                borderRadius: 3,
                textAlign: 'center'
              }}
            >
              <CrownIcon sx={{ fontSize: 80, mb: 2, color: '#ffc107' }} />
              <Typography variant="h3" gutterBottom fontWeight="bold">
                VIP статус
              </Typography>
              <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
                Эксклюзивные возможности для особенных людей
              </Typography>
              {vipPlan && (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
                  <Typography variant="h4" fontWeight="bold">
                    {formatCurrency(vipPlan.pricing.yearly.price)}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.8 }}>
                    в год
                  </Typography>
                  {vipPlan.pricing.yearly.originalPrice && (
                    <Typography
                      variant="h6"
                      sx={{
                        textDecoration: 'line-through',
                        opacity: 0.6
                      }}
                    >
                      {formatCurrency(vipPlan.pricing.yearly.originalPrice)}
                    </Typography>
                  )}
                </Box>
              )}
            </Paper>

            {/* VIP Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {vipStats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Zoom in timeout={300 + index * 100}>
                    <Card elevation={3} sx={{ textAlign: 'center', p: 2 }}>
                      <CardContent>
                        <Box sx={{ mb: 2 }}>
                          {stat.icon}
                        </Box>
                        <Typography variant="h4" color="primary" fontWeight="bold" gutterBottom>
                          {stat.value}
                        </Typography>
                        <Typography variant="h6" gutterBottom>
                          {stat.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {stat.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Zoom>
                </Grid>
              ))}
            </Grid>

            {/* VIP Benefits */}
            <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
              <DiamondIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Эксклюзивные возможности VIP
            </Typography>

            <Grid container spacing={3} sx={{ mb: 4 }}>
              {vipBenefits.map((benefit, index) => (
                <Grid item xs={12} sm={6} md={4} key={benefit.id}>
                  <Zoom in timeout={400 + index * 50}>
                    <Card
                      elevation={benefit.isExclusive ? 6 : 3}
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        border: benefit.isExclusive ? '2px solid #ffc107' : 'none',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: theme.shadows[10]
                        },
                        transition: 'all 0.3s ease-in-out'
                      }}
                    >
                      {/* Exclusive Badge */}
                      {benefit.isExclusive && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: -8,
                            right: 16,
                            zIndex: 1
                          }}
                        >
                          <Chip
                            label="Только VIP"
                            size="small"
                            sx={{
                              background: 'linear-gradient(45deg, #ffc107 30%, #ff8f00 90%)',
                              color: 'white',
                              fontWeight: 'bold'
                            }}
                            icon={<CrownIcon />}
                          />
                        </Box>
                      )}

                      <CardContent sx={{ flexGrow: 1, textAlign: 'center', pt: benefit.isExclusive ? 3 : 2 }}>
                        {/* Benefit Icon */}
                        <Box sx={{ mb: 2 }}>
                          {benefit.icon}
                        </Box>

                        {/* Benefit Title */}
                        <Typography variant="h6" gutterBottom>
                          {benefit.title}
                        </Typography>

                        {/* Benefit Value */}
                        {benefit.value && (
                          <Typography 
                            variant="h4" 
                            color="primary" 
                            fontWeight="bold" 
                            sx={{ mb: 1 }}
                          >
                            {benefit.value}
                          </Typography>
                        )}

                        {/* Benefit Description */}
                        <Typography variant="body2" color="text.secondary">
                          {benefit.description}
                        </Typography>
                      </CardContent>

                      <CardActions sx={{ justifyContent: 'center', p: 2 }}>
                        {isVipUser ? (
                          <Chip
                            label="Активно"
                            color="success"
                            icon={<CheckIcon />}
                            variant="filled"
                          />
                        ) : (
                          <Chip
                            label={benefit.isExclusive ? "Только VIP" : "Доступно"}
                            color={benefit.isExclusive ? "warning" : "primary"}
                            icon={benefit.isExclusive ? <CrownIcon /> : <StarIcon />}
                            variant="outlined"
                          />
                        )}
                      </CardActions>
                    </Card>
                  </Zoom>
                </Grid>
              ))}
            </Grid>

            {/* Current Usage (for VIP users) */}
            {isVipUser && usage && (
              <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  <TrendingUpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Ваша VIP активность
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="primary" fontWeight="bold">
                        ∞
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Лайки сегодня
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="secondary" fontWeight="bold">
                        ∞
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Супер-лайки
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main" fontWeight="bold">
                        ∞
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Бусты профиля
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main" fontWeight="bold">
                        ∞
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Видеозвонки
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>
            )}

            {/* Call to Action */}
            {!isVipUser && (
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  p: 4, 
                  background: 'linear-gradient(135deg, #ffc107 0%, #ff8f00 100%)',
                  borderRadius: 3,
                  color: 'white'
                }}
              >
                <CrownIcon sx={{ fontSize: 80, mb: 2 }} />
                <Typography variant="h4" gutterBottom fontWeight="bold">
                  Станьте VIP уже сегодня
                </Typography>
                <Typography variant="h6" sx={{ mb: 3, opacity: 0.9 }}>
                  Получите доступ ко всем эксклюзивным функциям и найдите свою любовь быстрее
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleUpgradeToVip}
                  sx={{
                    backgroundColor: 'white',
                    color: '#ff8f00',
                    fontWeight: 'bold',
                    px: 4,
                    py: 1.5,
                    '&:hover': {
                      backgroundColor: 'rgba(255,255,255,0.9)'
                    }
                  }}
                  startIcon={<CrownIcon />}
                >
                  Получить VIP статус
                </Button>
              </Box>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default VipPage;
