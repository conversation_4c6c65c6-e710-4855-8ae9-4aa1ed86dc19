import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip
} from '@mui/material';
import {
  ArrowBack,
  Storage as StorageIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Folder as FolderIcon,
  Description as DescriptionIcon,
  Image as ImageIcon,
  Message as MessageIcon,
  Favorite as FavoriteIcon,
  Visibility as VisibilityIcon,
  Schedule as ScheduleIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для управления данными
interface DataExportRequest {
  includeProfile: boolean;
  includePhotos: boolean;
  includeMessages: boolean;
  includeLikes: boolean;
  includeMatches: boolean;
  includeActivity: boolean;
}

interface DeleteAccountForm {
  reason: string;
  feedback: string;
  password: string;
}

// Схема валидации для удаления аккаунта
const deleteAccountSchema = yup.object({
  reason: yup.string().required('Выберите причину удаления'),
  feedback: yup.string().max(500, 'Максимум 500 символов'),
  password: yup.string().required('Введите пароль для подтверждения')
});

const DataManagementPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [exportData, setExportData] = useState<DataExportRequest>({
    includeProfile: true,
    includePhotos: true,
    includeMessages: true,
    includeLikes: true,
    includeMatches: true,
    includeActivity: true
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<DeleteAccountForm>({
    resolver: yupResolver(deleteAccountSchema),
    defaultValues: {
      reason: '',
      feedback: '',
      password: ''
    }
  });

  // Типы данных для экспорта
  const dataTypes = [
    {
      key: 'includeProfile' as keyof DataExportRequest,
      label: 'Данные профиля',
      description: 'Личная информация, настройки, предпочтения',
      icon: <DescriptionIcon />,
      size: '~2 MB'
    },
    {
      key: 'includePhotos' as keyof DataExportRequest,
      label: 'Фотографии',
      description: 'Все загруженные фотографии профиля',
      icon: <ImageIcon />,
      size: '~50 MB'
    },
    {
      key: 'includeMessages' as keyof DataExportRequest,
      label: 'Сообщения',
      description: 'История переписок и чатов',
      icon: <MessageIcon />,
      size: '~10 MB'
    },
    {
      key: 'includeLikes' as keyof DataExportRequest,
      label: 'Лайки',
      description: 'Отправленные и полученные лайки',
      icon: <FavoriteIcon />,
      size: '~1 MB'
    },
    {
      key: 'includeMatches' as keyof DataExportRequest,
      label: 'Совпадения',
      description: 'История совпадений и взаимных лайков',
      icon: <Visibility />,
      size: '~1 MB'
    },
    {
      key: 'includeActivity' as keyof DataExportRequest,
      label: 'Активность',
      description: 'Логи активности и статистика',
      icon: <ScheduleIcon />,
      size: '~5 MB'
    }
  ];

  // Причины удаления аккаунта
  const deleteReasons = [
    'Нашел(ла) отношения',
    'Не нравится приложение',
    'Слишком много спама',
    'Проблемы с безопасностью',
    'Временный перерыв',
    'Другая причина'
  ];

  const handleExportData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для экспорта данных
      // const response = await requestDataExport(exportData);

      // Симуляция экспорта
      await new Promise(resolve => setTimeout(resolve, 3000));

      setSuccess('Запрос на экспорт данных отправлен. Вы получите ссылку на скачивание на email в течение 24 часов.');
      setExportDialogOpen(false);
    } catch (err: any) {
      setError(err.message || 'Ошибка при экспорте данных');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async (data: DeleteAccountForm) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для удаления аккаунта
      // await deleteUserAccount(data);

      setSuccess('Запрос на удаление аккаунта отправлен. Аккаунт будет удален в течение 30 дней.');
      setDeleteDialogOpen(false);
      reset();
      
      // Перенаправление на страницу выхода
      setTimeout(() => {
        router.push('/auth/logout');
      }, 3000);
    } catch (err: any) {
      setError(err.message || 'Ошибка при удалении аккаунта');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/settings/privacy');
  };

  const getTotalSize = () => {
    let total = 0;
    dataTypes.forEach(type => {
      if (exportData[type.key]) {
        const size = parseInt(type.size.match(/\d+/)?.[0] || '0');
        total += size;
      }
    });
    return `~${total} MB`;
  };

  if (!user) {
    return (
      <Layout title="Управление данными">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Управление данными - Likes & Love</title>
        <meta name="description" content="Экспорт и удаление данных в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Управление данными">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/privacy" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/privacy'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SecurityIcon fontSize="small" />
                Приватность
              </Link>
              <Typography color="text.primary">Данные</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <StorageIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Управление данными
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Экспорт, просмотр и удаление ваших данных
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <Grid container spacing={3}>
              {/* Экспорт данных */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <DownloadIcon color="primary" />
                      <Typography variant="h6" fontWeight="bold">
                        Экспорт данных
                      </Typography>
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      Скачайте копию всех ваших данных в формате JSON. Это может включать профиль, 
                      фотографии, сообщения и другую информацию.
                    </Typography>

                    <Button
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      onClick={() => setExportDialogOpen(true)}
                      disabled={loading}
                    >
                      Экспортировать данные
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              {/* Информация о данных */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <InfoIcon color="primary" />
                      <Typography variant="h6" fontWeight="bold">
                        Ваши данные
                      </Typography>
                    </Box>

                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <DescriptionIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Профиль создан"
                          secondary={new Date(user.createdAt || Date.now()).toLocaleDateString('ru-RU')}
                        />
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemIcon>
                          <ImageIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Загружено фотографий"
                          secondary={`${user.photos?.length || 0} фото`}
                        />
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemIcon>
                          <MessageIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Сообщений отправлено"
                          secondary="Данные доступны при экспорте"
                        />
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemIcon>
                          <FavoriteIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Лайков отправлено"
                          secondary="Данные доступны при экспорте"
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* Удаление аккаунта */}
              <Grid item xs={12}>
                <Card sx={{ border: '1px solid', borderColor: 'error.main' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <DeleteIcon color="error" />
                      <Typography variant="h6" fontWeight="bold" color="error">
                        Удаление аккаунта
                      </Typography>
                    </Box>

                    <Alert severity="error" sx={{ mb: 3 }}>
                      <Typography variant="body2">
                        <strong>Внимание!</strong> Удаление аккаунта необратимо. Все ваши данные, 
                        сообщения, фотографии и совпадения будут удалены навсегда.
                      </Typography>
                    </Alert>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      После удаления аккаунта у вас будет 30 дней для восстановления. 
                      По истечении этого срока данные будут удалены безвозвратно.
                    </Typography>

                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<DeleteIcon />}
                      onClick={() => setDeleteDialogOpen(true)}
                      disabled={loading}
                    >
                      Удалить аккаунт
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Диалог экспорта данных */}
            <Dialog
              open={exportDialogOpen}
              onClose={() => setExportDialogOpen(false)}
              maxWidth="md"
              fullWidth
            >
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <DownloadIcon />
                  Экспорт данных
                </Box>
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1" sx={{ mb: 3 }}>
                  Выберите типы данных для экспорта:
                </Typography>

                <List>
                  {dataTypes.map((type, index) => (
                    <React.Fragment key={type.key}>
                      <ListItem>
                        <ListItemIcon>
                          {type.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {type.label}
                              <Chip label={type.size} size="small" variant="outlined" />
                            </Box>
                          }
                          secondary={type.description}
                        />
                        <ListItemSecondaryAction>
                          <input
                            type="checkbox"
                            checked={exportData[type.key]}
                            onChange={(e) => setExportData(prev => ({
                              ...prev,
                              [type.key]: e.target.checked
                            }))}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < dataTypes.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>

                <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Typography variant="body2">
                    <strong>Общий размер:</strong> {getTotalSize()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Ссылка для скачивания будет отправлена на ваш email в течение 24 часов
                  </Typography>
                </Box>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setExportDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleExportData}
                  variant="contained"
                  disabled={loading || !Object.values(exportData).some(Boolean)}
                  startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
                >
                  {loading ? 'Экспорт...' : 'Экспортировать'}
                </Button>
              </DialogActions>
            </Dialog>

            {/* Диалог удаления аккаунта */}
            <Dialog
              open={deleteDialogOpen}
              onClose={() => setDeleteDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <WarningIcon color="error" />
                  Удаление аккаунта
                </Box>
              </DialogTitle>
              <DialogContent>
                <Alert severity="error" sx={{ mb: 3 }}>
                  <Typography variant="body2">
                    Это действие нельзя отменить. Все ваши данные будут удалены.
                  </Typography>
                </Alert>

                <form onSubmit={handleSubmit(handleDeleteAccount)}>
                  <Controller
                    name="reason"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label="Причина удаления"
                        fullWidth
                        error={!!errors.reason}
                        helperText={errors.reason?.message}
                        sx={{ mb: 2 }}
                        SelectProps={{ native: true }}
                      >
                        <option value="">Выберите причину</option>
                        {deleteReasons.map((reason) => (
                          <option key={reason} value={reason}>
                            {reason}
                          </option>
                        ))}
                      </TextField>
                    )}
                  />

                  <Controller
                    name="feedback"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Дополнительные комментарии (необязательно)"
                        multiline
                        rows={3}
                        fullWidth
                        error={!!errors.feedback}
                        helperText={errors.feedback?.message}
                        sx={{ mb: 2 }}
                      />
                    )}
                  />

                  <Controller
                    name="password"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Подтвердите паролем"
                        type="password"
                        fullWidth
                        error={!!errors.password}
                        helperText={errors.password?.message}
                      />
                    )}
                  />
                </form>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDeleteDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleSubmit(handleDeleteAccount)}
                  color="error"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}
                >
                  {loading ? 'Удаление...' : 'Удалить аккаунт'}
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default DataManagementPage;
