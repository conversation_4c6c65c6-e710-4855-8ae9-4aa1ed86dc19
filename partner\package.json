{"name": "@likes-love/partner", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src", "test": "vitest", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "prepare": "husky install"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.15.21", "@mui/material": "^5.15.21", "@mui/x-date-pickers": "^7.29.4", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.77.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0", "react-redux": "^9.2.0", "react-router-dom": "^6.8.1", "socket.io-client": "^4.8.1", "zod": "^3.25.62", "stripe": "^18.1.1", "square": "^39.0.0", "paypal-rest-sdk": "^1.8.1", "qr-code-styling": "^1.8.4", "chart.js": "^4.4.6", "react-chartjs-2": "^5.2.0", "lodash": "^4.17.21", "uuid": "^11.0.3", "crypto-js": "^4.2.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "validator": "^13.12.0", "moment": "^2.30.1", "numeral": "^2.0.6", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "jspdf": "^2.5.2", "html2canvas": "^1.4.1", "react-qr-code": "^2.0.15"}, "devDependencies": {"@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.15.21", "@types/react": "^18.2.79", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.2.25", "@types/react-icons": "^3.0.0", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.2.0", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.0.11", "jsdom": "^26.1.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "vite": "^6.3.5", "vitest": "^3.2.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}