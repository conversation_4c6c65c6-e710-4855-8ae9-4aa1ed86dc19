import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { GetStaticProps, GetStaticPaths } from 'next';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Breadcrumbs,
  Link,
  IconButton,
  Badge,
  Pagination,
  CircularProgress,
  Alert,
  Divider,
  Avatar,
  Rating
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Store as StoreIcon,
  LocalOffer as OfferIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import Head from 'next/head';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  discountPrice?: number;
  images: ProductImage[];
  category: ProductCategory;
  store: Store;
  shoppingCenter: ShoppingCenter;
  availability: ProductAvailability;
  rating: number;
  reviewsCount: number;
  tags: string[];
}

interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
}

interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  icon: string;
}

interface Store {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  floor: number;
  location: string;
}

interface ShoppingCenter {
  id: string;
  name: string;
  slug: string;
}

interface ProductAvailability {
  inStock: boolean;
  availableQuantity: number;
}

interface ShoppingCenterProductsPageProps {
  shoppingCenter: ShoppingCenter;
  initialProducts: Product[];
  categories: ProductCategory[];
  stores: Store[];
  priceRange: { min: number; max: number };
}

const ShoppingCenterProductsPage: React.FC<ShoppingCenterProductsPageProps> = ({
  shoppingCenter,
  initialProducts,
  categories,
  stores,
  priceRange
}) => {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [loading, setLoading] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [cart, setCart] = useState<Set<string>>(new Set());
  
  // Фильтры
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStore, setSelectedStore] = useState('');
  const [priceFilter, setPriceFilter] = useState<number[]>([priceRange.min, priceRange.max]);
  const [sortBy, setSortBy] = useState('createdAt');
  const [inStockOnly, setInStockOnly] = useState(false);
  
  // Пагинация
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchProducts();
  }, [search, selectedCategory, selectedStore, priceFilter, sortBy, inStockOnly, page]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        shoppingCenter: shoppingCenter.slug,
        page: page.toString(),
        limit: '12',
        sortBy,
        sortOrder: 'desc'
      });

      if (search) params.append('search', search);
      if (selectedCategory) params.append('category', selectedCategory);
      if (selectedStore) params.append('store', selectedStore);
      if (priceFilter[0] > priceRange.min) params.append('minPrice', priceFilter[0].toString());
      if (priceFilter[1] < priceRange.max) params.append('maxPrice', priceFilter[1].toString());
      if (inStockOnly) params.append('inStock', 'true');

      const response = await fetch(`/api/products?${params}`);
      const data = await response.json();
      
      setProducts(data.products);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (productId: string) => {
    setCart(prev => new Set([...prev, productId]));
    // В реальном приложении здесь будет запрос к API корзины
  };

  const handleToggleFavorite = (productId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(productId)) {
        newFavorites.delete(productId);
      } else {
        newFavorites.add(productId);
      }
      return newFavorites;
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getDiscountPercent = (price: number, discountPrice: number) => {
    return Math.round(((price - discountPrice) / price) * 100);
  };

  return (
    <>
      <Head>
        <title>Товары в {shoppingCenter.name} | Likes & Love</title>
        <meta name="description" content={`Каталог товаров торгового центра ${shoppingCenter.name}. Покупайте онлайн с доставкой или самовывозом.`} />
      </Head>

      <Layout>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          {/* Хлебные крошки */}
          <Breadcrumbs sx={{ mb: 3 }}>
            <Link href="/" color="inherit">Главная</Link>
            <Link href="/shopping-centers" color="inherit">Торговые центры</Link>
            <Link href={`/shopping-center/${shoppingCenter.slug}`} color="inherit">
              {shoppingCenter.name}
            </Link>
            <Typography color="text.primary">Товары</Typography>
          </Breadcrumbs>

          {/* Заголовок */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Товары в {shoppingCenter.name}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Покупайте товары онлайн с доставкой или самовывозом из торгового центра
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {/* Фильтры */}
            <Grid item xs={12} md={3}>
              <Card sx={{ p: 2, mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Фильтры
                </Typography>
                
                {/* Поиск */}
                <TextField
                  fullWidth
                  placeholder="Поиск товаров..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                  sx={{ mb: 2 }}
                />

                {/* Категория */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Категория</InputLabel>
                  <Select
                    value={selectedCategory}
                    label="Категория"
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    <MenuItem value="">Все категории</MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category.id} value={category.slug}>
                        {category.icon} {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Магазин */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Магазин</InputLabel>
                  <Select
                    value={selectedStore}
                    label="Магазин"
                    onChange={(e) => setSelectedStore(e.target.value)}
                  >
                    <MenuItem value="">Все магазины</MenuItem>
                    {stores.map((store) => (
                      <MenuItem key={store.id} value={store.slug}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {store.logo && (
                            <Avatar src={store.logo} sx={{ width: 20, height: 20 }} />
                          )}
                          {store.name}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Цена */}
                <Typography variant="subtitle2" gutterBottom>
                  Цена: {formatPrice(priceFilter[0])} - {formatPrice(priceFilter[1])}
                </Typography>
                <Slider
                  value={priceFilter}
                  onChange={(_, newValue) => setPriceFilter(newValue as number[])}
                  valueLabelDisplay="auto"
                  min={priceRange.min}
                  max={priceRange.max}
                  step={1000}
                  sx={{ mb: 2 }}
                />

                {/* Только в наличии */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <Button
                    variant={inStockOnly ? "contained" : "outlined"}
                    onClick={() => setInStockOnly(!inStockOnly)}
                    startIcon={<FilterIcon />}
                  >
                    Только в наличии
                  </Button>
                </FormControl>
              </Card>
            </Grid>

            {/* Товары */}
            <Grid item xs={12} md={9}>
              {/* Сортировка */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  Найдено товаров: {products.length}
                </Typography>
                
                <FormControl size="small" sx={{ minWidth: 200 }}>
                  <InputLabel>Сортировка</InputLabel>
                  <Select
                    value={sortBy}
                    label="Сортировка"
                    onChange={(e) => setSortBy(e.target.value)}
                  >
                    <MenuItem value="createdAt">По новизне</MenuItem>
                    <MenuItem value="price">По цене</MenuItem>
                    <MenuItem value="rating">По рейтингу</MenuItem>
                    <MenuItem value="name">По названию</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              {/* Список товаров */}
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                  <CircularProgress />
                </Box>
              ) : products.length === 0 ? (
                <Alert severity="info">
                  Товары не найдены. Попробуйте изменить фильтры поиска.
                </Alert>
              ) : (
                <>
                  <Grid container spacing={3}>
                    {products.map((product) => (
                      <Grid item xs={12} sm={6} lg={4} key={product.id}>
                        <Card 
                          sx={{ 
                            height: '100%', 
                            display: 'flex', 
                            flexDirection: 'column',
                            position: 'relative',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: 3,
                              transition: 'all 0.3s ease'
                            }
                          }}
                        >
                          {/* Скидка */}
                          {product.discountPrice && (
                            <Chip
                              label={`-${getDiscountPercent(product.price, product.discountPrice)}%`}
                              color="error"
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: 8,
                                left: 8,
                                zIndex: 1
                              }}
                            />
                          )}

                          {/* Избранное */}
                          <IconButton
                            sx={{
                              position: 'absolute',
                              top: 8,
                              right: 8,
                              zIndex: 1,
                              bgcolor: 'background.paper',
                              '&:hover': { bgcolor: 'background.paper' }
                            }}
                            onClick={() => handleToggleFavorite(product.id)}
                          >
                            {favorites.has(product.id) ? (
                              <FavoriteIcon color="error" />
                            ) : (
                              <FavoriteBorderIcon />
                            )}
                          </IconButton>

                          {/* Изображение */}
                          <CardMedia
                            component="img"
                            height="200"
                            image={product.images.find(img => img.isPrimary)?.url || product.images[0]?.url}
                            alt={product.name}
                            sx={{ objectFit: 'cover' }}
                          />

                          <CardContent sx={{ flexGrow: 1 }}>
                            {/* Магазин */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              {product.store.logo && (
                                <Avatar src={product.store.logo} sx={{ width: 20, height: 20 }} />
                              )}
                              <Typography variant="caption" color="text.secondary">
                                {product.store.name} • {product.store.location}
                              </Typography>
                            </Box>

                            {/* Название */}
                            <Typography variant="h6" component="h3" gutterBottom sx={{ 
                              fontSize: '1rem',
                              lineHeight: 1.3,
                              height: '2.6em',
                              overflow: 'hidden',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}>
                              {product.name}
                            </Typography>

                            {/* Рейтинг */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <Rating value={product.rating} precision={0.1} size="small" readOnly />
                              <Typography variant="caption" color="text.secondary">
                                ({product.reviewsCount})
                              </Typography>
                            </Box>

                            {/* Цена */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                              <Typography variant="h6" color="primary">
                                {formatPrice(product.discountPrice || product.price)}
                              </Typography>
                              {product.discountPrice && (
                                <Typography 
                                  variant="body2" 
                                  color="text.secondary"
                                  sx={{ textDecoration: 'line-through' }}
                                >
                                  {formatPrice(product.price)}
                                </Typography>
                              )}
                            </Box>

                            {/* Наличие */}
                            <Typography 
                              variant="caption" 
                              color={product.availability.inStock ? 'success.main' : 'error.main'}
                            >
                              {product.availability.inStock 
                                ? `В наличии: ${product.availability.availableQuantity} шт.`
                                : 'Нет в наличии'
                              }
                            </Typography>
                          </CardContent>

                          <CardActions sx={{ p: 2, pt: 0 }}>
                            <Button
                              fullWidth
                              variant="contained"
                              startIcon={<CartIcon />}
                              onClick={() => handleAddToCart(product.id)}
                              disabled={!product.availability.inStock || cart.has(product.id)}
                            >
                              {cart.has(product.id) ? 'В корзине' : 'В корзину'}
                            </Button>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>

                  {/* Пагинация */}
                  {totalPages > 1 && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                      <Pagination
                        count={totalPages}
                        page={page}
                        onChange={(_, newPage) => setPage(newPage)}
                        color="primary"
                        size="large"
                      />
                    </Box>
                  )}
                </>
              )}
            </Grid>
          </Grid>
        </Container>
      </Layout>
    </>
  );
};

export const getStaticPaths: GetStaticPaths = async () => {
  // Возвращаем пустые пути для ISR
  return {
    paths: [],
    fallback: 'blocking'
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const { slug } = params!;

  try {
    // Мок данные для статической генерации
    const shoppingCenter = {
      id: '1',
      name: 'Торговый центр',
      slug: slug as string
    };

    const initialProducts: Product[] = [];
    const categories: ProductCategory[] = [];
    const stores: Store[] = [];
    const priceRange = { min: 0, max: 200000 };

    return {
      props: {
        shoppingCenter,
        initialProducts,
        categories,
        stores,
        priceRange
      },
      revalidate: 3600 // Revalidate every hour
    };
  } catch (error) {
    console.error('Error fetching data:', error);
    return { notFound: true };
  }
};

export default ShoppingCenterProductsPage;
