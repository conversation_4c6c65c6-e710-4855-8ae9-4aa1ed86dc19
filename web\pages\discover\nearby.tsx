import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Slider,
  FormControl,
  FormLabel,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  LocationOn as LocationIcon,
  Refresh as RefreshIcon,
  Favorite as LikeIcon,
  Message as MessageIcon,
  Verified as VerifiedIcon,
  Schedule as ScheduleIcon,
  MyLocation as MyLocationIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getNearbyUsers,
  updateLocation,
  swipeUser
} from '../../src/services/discoverService';
import { 
  NearbyUser,
  SwipeAction 
} from '../../src/types/discover.types';

const NearbyUsersPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [nearbyUsers, setNearbyUsers] = useState<NearbyUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [radius, setRadius] = useState(25);
  const [locationLoading, setLocationLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadNearbyUsers();
  }, [user, router, radius]);

  const loadNearbyUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const users = await getNearbyUsers(radius);
      setNearbyUsers(users);
    } catch (err: any) {
      setError('Ошибка загрузки пользователей рядом');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateLocation = async () => {
    if (!navigator.geolocation) {
      setError('Геолокация не поддерживается вашим браузером');
      return;
    }

    try {
      setLocationLoading(true);
      setError(null);

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        });
      });

      await updateLocation(
        position.coords.latitude,
        position.coords.longitude
      );

      setSuccess('Местоположение обновлено');
      loadNearbyUsers();
    } catch (err: any) {
      if (err.code === 1) {
        setError('Доступ к геолокации запрещен');
      } else if (err.code === 2) {
        setError('Не удалось определить местоположение');
      } else if (err.code === 3) {
        setError('Время ожидания геолокации истекло');
      } else {
        setError('Ошибка обновления местоположения');
      }
    } finally {
      setLocationLoading(false);
    }
  };

  const handleLike = async (userId: string) => {
    try {
      setActionLoading(userId);
      setError(null);

      const swipeAction: SwipeAction = {
        userId,
        action: 'like',
        timestamp: new Date().toISOString()
      };

      const result = await swipeUser(swipeAction);
      
      if (result.success) {
        if (result.isMatch) {
          setSuccess('Это совпадение! 🎉');
        } else {
          setSuccess('Лайк отправлен');
        }
        
        // Remove user from list
        setNearbyUsers(prev => prev.filter(u => u.user.id !== userId));
      } else {
        setError(result.error || 'Ошибка отправки лайка');
      }
    } catch (err: any) {
      setError('Ошибка отправки лайка');
    } finally {
      setActionLoading(null);
    }
  };

  const formatDistance = (distance: number) => {
    if (distance < 1) {
      return `${Math.round(distance * 1000)} м`;
    }
    return `${distance.toFixed(1)} км`;
  };

  const formatLastSeen = (dateString: string) => {
    const now = new Date();
    const lastSeen = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Только что';
    } else if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Люди рядом - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите людей рядом с вами в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/discover/nearby" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <LocationIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Люди рядом
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<MyLocationIcon />}
                  onClick={handleUpdateLocation}
                  disabled={locationLoading}
                  size={isMobile ? "small" : "medium"}
                >
                  {locationLoading ? 'Обновление...' : 'Обновить местоположение'}
                </Button>
                <IconButton onClick={loadNearbyUsers} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Radius Control */}
            <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
              <FormControl fullWidth>
                <FormLabel sx={{ mb: 2 }}>
                  Радиус поиска: {radius} км
                </FormLabel>
                <Slider
                  value={radius}
                  onChange={(_, value) => setRadius(value as number)}
                  min={1}
                  max={100}
                  step={1}
                  marks={[
                    { value: 1, label: '1 км' },
                    { value: 25, label: '25 км' },
                    { value: 50, label: '50 км' },
                    { value: 100, label: '100 км' }
                  ]}
                  valueLabelDisplay="auto"
                />
              </FormControl>
            </Paper>

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Поиск людей рядом...
                </Typography>
              </Box>
            ) : nearbyUsers.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <LocationIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Рядом никого нет
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Попробуйте увеличить радиус поиска или зайдите позже
                </Typography>
                <Button
                  variant="contained"
                  onClick={loadNearbyUsers}
                >
                  Обновить поиск
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Найдено {nearbyUsers.length} {nearbyUsers.length === 1 ? 'человек' : 'людей'} в радиусе {radius} км
                  </Typography>
                  
                  <Grid container spacing={3}>
                    {nearbyUsers.map((nearbyUser) => (
                      <Grid item xs={12} sm={6} md={4} key={nearbyUser.user.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ position: 'relative' }}>
                            {/* Online/Distance Badge */}
                            <Box sx={{
                              position: 'absolute',
                              top: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                label={formatDistance(nearbyUser.distance)}
                                size="small"
                                color="primary"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>

                            {/* Verification Badge */}
                            {nearbyUser.user.verificationStatus.phone && (
                              <Box sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                zIndex: 1,
                                backgroundColor: 'primary.main',
                                borderRadius: '50%',
                                p: 0.5
                              }}>
                                <VerifiedIcon sx={{ color: 'white', fontSize: 16 }} />
                              </Box>
                            )}

                            <CardMedia
                              component="img"
                              height="250"
                              image={nearbyUser.user.photos[0]?.url || '/default-avatar.png'}
                              alt={nearbyUser.user.firstName}
                              sx={{ objectFit: 'cover' }}
                            />
                          </Box>

                          <CardContent sx={{ flexGrow: 1 }}>
                            <Typography variant="h6" gutterBottom>
                              {nearbyUser.user.firstName}, {nearbyUser.user.age}
                            </Typography>

                            {nearbyUser.user.location && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                                <Typography variant="body2" color="text.secondary">
                                  {nearbyUser.user.location.city}
                                </Typography>
                              </Box>
                            )}

                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {nearbyUser.user.isOnline ? 'Онлайн' : formatLastSeen(nearbyUser.lastSeenAt)}
                              </Typography>
                            </Box>

                            {nearbyUser.isCurrentlyNearby && (
                              <Chip
                                label="Сейчас рядом"
                                size="small"
                                color="success"
                                sx={{ mb: 1 }}
                              />
                            )}

                            {nearbyUser.user.bio && (
                              <Typography variant="body2" sx={{ mb: 2 }}>
                                {nearbyUser.user.bio.length > 100 
                                  ? `${nearbyUser.user.bio.substring(0, 100)}...`
                                  : nearbyUser.user.bio
                                }
                              </Typography>
                            )}

                            {nearbyUser.user.interests.length > 0 && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {nearbyUser.user.interests.slice(0, 3).map((interest, index) => (
                                  <Chip
                                    key={index}
                                    label={interest}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                                {nearbyUser.user.interests.length > 3 && (
                                  <Chip
                                    label={`+${nearbyUser.user.interests.length - 3}`}
                                    size="small"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            )}
                          </CardContent>

                          <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                            <Button
                              variant="outlined"
                              onClick={() => router.push(`/users/${nearbyUser.user.id}`)}
                              size="small"
                            >
                              Профиль
                            </Button>
                            
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                color="primary"
                                onClick={() => handleLike(nearbyUser.user.id)}
                                disabled={actionLoading === nearbyUser.user.id}
                                sx={{
                                  backgroundColor: 'error.light',
                                  color: 'error.dark',
                                  '&:hover': { backgroundColor: 'error.main', color: 'white' }
                                }}
                              >
                                {actionLoading === nearbyUser.user.id ? (
                                  <CircularProgress size={20} />
                                ) : (
                                  <LikeIcon />
                                )}
                              </IconButton>
                              
                              <IconButton
                                color="primary"
                                onClick={() => router.push(`/chat/new?userId=${nearbyUser.user.id}`)}
                              >
                                <MessageIcon />
                              </IconButton>
                            </Box>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default NearbyUsersPage;
