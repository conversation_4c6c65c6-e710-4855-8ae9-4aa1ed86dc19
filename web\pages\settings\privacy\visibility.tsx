import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Switch,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Chip,
  Grid
} from '@mui/material';
import {
  ArrowBack,
  Visibility as VisibilityIcon,
  Public as PublicIcon,
  People as PeopleIcon,
  Lock as LockIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Cake as CakeIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Info as InfoIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для настроек видимости
interface VisibilitySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showAge: boolean;
  showLocation: boolean;
  showLastSeen: boolean;
  showOnlineStatus: boolean;
  showWorkInfo: boolean;
  showEducationInfo: boolean;
  showInterests: boolean;
  allowProfileViews: boolean;
  allowMessageRequests: boolean;
  showInSearch: boolean;
  showInRecommendations: boolean;
}

// Схема валидации
const visibilitySchema = yup.object({
  profileVisibility: yup.string()
    .oneOf(['public', 'friends', 'private'])
    .required('Выберите уровень видимости'),
  showAge: yup.boolean(),
  showLocation: yup.boolean(),
  showLastSeen: yup.boolean(),
  showOnlineStatus: yup.boolean(),
  showWorkInfo: yup.boolean(),
  showEducationInfo: yup.boolean(),
  showInterests: yup.boolean(),
  allowProfileViews: yup.boolean(),
  allowMessageRequests: yup.boolean(),
  showInSearch: yup.boolean(),
  showInRecommendations: yup.boolean()
});

const ProfileVisibilityPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, updateProfile } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    watch
  } = useForm<VisibilitySettings>({
    resolver: yupResolver(visibilitySchema),
    defaultValues: {
      profileVisibility: user?.privacy?.profileVisibility || 'public',
      showAge: user?.privacy?.showAge ?? true,
      showLocation: user?.privacy?.showLocation ?? true,
      showLastSeen: user?.privacy?.showLastSeen ?? true,
      showOnlineStatus: user?.privacy?.showOnlineStatus ?? true,
      showWorkInfo: user?.privacy?.showWorkInfo ?? true,
      showEducationInfo: user?.privacy?.showEducationInfo ?? true,
      showInterests: user?.privacy?.showInterests ?? true,
      allowProfileViews: user?.privacy?.allowProfileViews ?? true,
      allowMessageRequests: user?.privacy?.allowMessageRequests ?? true,
      showInSearch: user?.privacy?.showInSearch ?? true,
      showInRecommendations: user?.privacy?.showInRecommendations ?? true
    }
  });

  const watchedVisibility = watch('profileVisibility');

  // Опции видимости профиля
  const visibilityOptions = [
    {
      value: 'public',
      label: 'Публичный',
      description: 'Ваш профиль видят все пользователи',
      icon: <PublicIcon />,
      color: 'success'
    },
    {
      value: 'friends',
      label: 'Только совпадения',
      description: 'Профиль видят только пользователи с взаимными лайками',
      icon: <PeopleIcon />,
      color: 'warning'
    },
    {
      value: 'private',
      label: 'Приватный',
      description: 'Профиль скрыт от всех пользователей',
      icon: <LockIcon />,
      color: 'error'
    }
  ];

  // Настройки информации профиля
  const profileInfoSettings = [
    {
      key: 'showAge' as keyof VisibilitySettings,
      label: 'Показывать возраст',
      description: 'Другие пользователи увидят ваш возраст',
      icon: <CakeIcon />
    },
    {
      key: 'showLocation' as keyof VisibilitySettings,
      label: 'Показывать местоположение',
      description: 'Отображение города и расстояния до вас',
      icon: <LocationIcon />
    },
    {
      key: 'showWorkInfo' as keyof VisibilitySettings,
      label: 'Показывать место работы',
      description: 'Информация о вашей работе и должности',
      icon: <WorkIcon />
    },
    {
      key: 'showEducationInfo' as keyof VisibilitySettings,
      label: 'Показывать образование',
      description: 'Информация об учебных заведениях',
      icon: <SchoolIcon />
    },
    {
      key: 'showInterests' as keyof VisibilitySettings,
      label: 'Показывать интересы',
      description: 'Ваши хобби и интересы',
      icon: <InfoIcon />
    }
  ];

  // Настройки активности
  const activitySettings = [
    {
      key: 'showOnlineStatus' as keyof VisibilitySettings,
      label: 'Показывать статус "онлайн"',
      description: 'Другие увидят, когда вы в сети',
      icon: <ScheduleIcon />
    },
    {
      key: 'showLastSeen' as keyof VisibilitySettings,
      label: 'Показывать время последней активности',
      description: 'Когда вы были в сети в последний раз',
      icon: <ScheduleIcon />
    }
  ];

  // Настройки взаимодействия
  const interactionSettings = [
    {
      key: 'allowProfileViews' as keyof VisibilitySettings,
      label: 'Разрешить просмотр профиля',
      description: 'Пользователи могут открывать ваш полный профиль',
      icon: <VisibilityIcon />
    },
    {
      key: 'allowMessageRequests' as keyof VisibilitySettings,
      label: 'Разрешить запросы сообщений',
      description: 'Пользователи без совпадений могут писать вам',
      icon: <PeopleIcon />
    },
    {
      key: 'showInSearch' as keyof VisibilitySettings,
      label: 'Показывать в поиске',
      description: 'Ваш профиль появляется в результатах поиска',
      icon: <PublicIcon />
    },
    {
      key: 'showInRecommendations' as keyof VisibilitySettings,
      label: 'Показывать в рекомендациях',
      description: 'Ваш профиль предлагается другим пользователям',
      icon: <PublicIcon />
    }
  ];

  const handleSaveSettings = async (data: VisibilitySettings) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для сохранения настроек видимости
      // await updatePrivacySettings(data);

      // Обновляем профиль пользователя
      await updateProfile({
        privacy: {
          ...user?.privacy,
          ...data
        }
      });

      setSuccess('Настройки видимости успешно обновлены');
    } catch (err: any) {
      setError(err.message || 'Ошибка при сохранении настроек');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    router.push('/settings/privacy');
  };

  if (!user) {
    return (
      <Layout title="Видимость профиля">
        <Container maxWidth="md">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Видимость профиля - Likes & Love</title>
        <meta name="description" content="Настройки видимости профиля в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Видимость профиля">
        <Container maxWidth="md">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/privacy" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/privacy'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SecurityIcon fontSize="small" />
                Приватность
              </Link>
              <Typography color="text.primary">Видимость</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <VisibilityIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Видимость профиля
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Управляйте тем, кто может видеть ваш профиль и какую информацию
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            <form onSubmit={handleSubmit(handleSaveSettings)}>
              <Grid container spacing={3}>
                {/* Основная видимость профиля */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Уровень видимости профиля
                      </Typography>

                      <Controller
                        name="profileVisibility"
                        control={control}
                        render={({ field }) => (
                          <FormControl component="fieldset" fullWidth>
                            <RadioGroup {...field}>
                              {visibilityOptions.map((option) => (
                                <Card 
                                  key={option.value}
                                  variant="outlined"
                                  sx={{ 
                                    mb: 2,
                                    border: field.value === option.value ? 2 : 1,
                                    borderColor: field.value === option.value ? `${option.color}.main` : 'divider'
                                  }}
                                >
                                  <CardContent sx={{ py: 2 }}>
                                    <FormControlLabel
                                      value={option.value}
                                      control={<Radio />}
                                      label={
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                                          {option.icon}
                                          <Box sx={{ flex: 1 }}>
                                            <Typography variant="subtitle1" fontWeight="bold">
                                              {option.label}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                              {option.description}
                                            </Typography>
                                          </Box>
                                          {field.value === option.value && (
                                            <Chip 
                                              label="Выбрано" 
                                              size="small" 
                                              color={option.color as any}
                                            />
                                          )}
                                        </Box>
                                      }
                                      sx={{ margin: 0, width: '100%' }}
                                    />
                                  </CardContent>
                                </Card>
                              ))}
                            </RadioGroup>
                          </FormControl>
                        )}
                      />
                    </CardContent>
                  </Card>
                </Grid>

                {/* Информация профиля */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Информация профиля
                      </Typography>

                      <List>
                        {profileInfoSettings.map((setting, index) => (
                          <React.Fragment key={setting.key}>
                            <ListItem>
                              <ListItemIcon>
                                {setting.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={setting.label}
                                secondary={setting.description}
                              />
                              <ListItemSecondaryAction>
                                <Controller
                                  name={setting.key}
                                  control={control}
                                  render={({ field }) => (
                                    <Switch
                                      checked={field.value}
                                      onChange={field.onChange}
                                      disabled={watchedVisibility === 'private'}
                                    />
                                  )}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < profileInfoSettings.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Активность */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Активность
                      </Typography>

                      <List>
                        {activitySettings.map((setting, index) => (
                          <React.Fragment key={setting.key}>
                            <ListItem>
                              <ListItemIcon>
                                {setting.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={setting.label}
                                secondary={setting.description}
                              />
                              <ListItemSecondaryAction>
                                <Controller
                                  name={setting.key}
                                  control={control}
                                  render={({ field }) => (
                                    <Switch
                                      checked={field.value}
                                      onChange={field.onChange}
                                      disabled={watchedVisibility === 'private'}
                                    />
                                  )}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < activitySettings.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Взаимодействие */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 3 }}>
                        Взаимодействие
                      </Typography>

                      <List>
                        {interactionSettings.map((setting, index) => (
                          <React.Fragment key={setting.key}>
                            <ListItem>
                              <ListItemIcon>
                                {setting.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={setting.label}
                                secondary={setting.description}
                              />
                              <ListItemSecondaryAction>
                                <Controller
                                  name={setting.key}
                                  control={control}
                                  render={({ field }) => (
                                    <Switch
                                      checked={field.value}
                                      onChange={field.onChange}
                                      disabled={watchedVisibility === 'private'}
                                    />
                                  )}
                                />
                              </ListItemSecondaryAction>
                            </ListItem>
                            {index < interactionSettings.length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Предупреждение для приватного режима */}
                {watchedVisibility === 'private' && (
                  <Grid item xs={12}>
                    <Alert severity="warning">
                      <Typography variant="body2">
                        <strong>Приватный режим:</strong> В приватном режиме ваш профиль полностью скрыт от других пользователей. 
                        Вы не будете получать новые совпадения и сообщения.
                      </Typography>
                    </Alert>
                  </Grid>
                )}

                {/* Кнопки сохранения */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button onClick={handleBack} disabled={loading}>
                      Отмена
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={loading || !isDirty}
                      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      {loading ? 'Сохранение...' : 'Сохранить'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default ProfileVisibilityPage;
