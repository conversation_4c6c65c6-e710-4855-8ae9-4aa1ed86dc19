import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  ArrowBack,
  Security as SecurityIcon,
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  Shield as ShieldIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
  Report as ReportIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Camera as CameraIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';

// Типы для советов по безопасности
interface SafetyTip {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'profile' | 'communication' | 'meetings' | 'privacy' | 'reporting';
  priority: 'high' | 'medium' | 'low';
  details: string[];
}

interface EmergencyContact {
  name: string;
  phone: string;
  relationship: string;
}

// Схема валидации для экстренного контакта
const emergencyContactSchema = yup.object({
  name: yup.string().required('Введите имя контакта'),
  phone: yup.string().required('Введите номер телефона'),
  relationship: yup.string().required('Укажите отношения')
});

const SafetyPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [expandedTip, setExpandedTip] = useState<string | false>(false);
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<EmergencyContact>({
    resolver: yupResolver(emergencyContactSchema),
    defaultValues: {
      name: '',
      phone: '',
      relationship: ''
    }
  });

  // Советы по безопасности
  const safetyTips: SafetyTip[] = [
    {
      id: 'profile_safety',
      title: 'Безопасность профиля',
      description: 'Защитите свою личную информацию в профиле',
      icon: <PersonIcon />,
      category: 'profile',
      priority: 'high',
      details: [
        'Не указывайте точный адрес проживания или работы',
        'Используйте только свои фотографии',
        'Не публикуйте номер телефона или email в открытом доступе',
        'Регулярно проверяйте настройки приватности',
        'Не указывайте финансовую информацию'
      ]
    },
    {
      id: 'communication_safety',
      title: 'Безопасное общение',
      description: 'Правила безопасного общения с новыми знакомыми',
      icon: <MessageIcon />,
      category: 'communication',
      priority: 'high',
      details: [
        'Общайтесь только через платформу до установления доверия',
        'Не делитесь личными данными слишком рано',
        'Будьте осторожны с просьбами о деньгах',
        'Доверяйте своей интуиции',
        'Сообщайте о подозрительном поведении'
      ]
    },
    {
      id: 'meeting_safety',
      title: 'Безопасные встречи',
      description: 'Как безопасно встречаться с людьми из приложения',
      icon: <LocationIcon />,
      category: 'meetings',
      priority: 'high',
      details: [
        'Первые встречи только в общественных местах',
        'Сообщите друзьям о своих планах',
        'Встречайтесь днем в людных местах',
        'Добирайтесь своим транспортом',
        'Доверяйте своим инстинктам'
      ]
    },
    {
      id: 'photo_safety',
      title: 'Безопасность фотографий',
      description: 'Как защитить свои фотографии от неправильного использования',
      icon: <CameraIcon />,
      category: 'privacy',
      priority: 'medium',
      details: [
        'Не отправляйте интимные фотографии',
        'Проверяйте метаданные фотографий',
        'Используйте водяные знаки при необходимости',
        'Будьте осторожны с фотографиями в купальниках',
        'Сообщайте о неправомерном использовании ваших фото'
      ]
    },
    {
      id: 'privacy_settings',
      title: 'Настройки приватности',
      description: 'Управляйте своей приватностью и видимостью',
      icon: <LockIcon />,
      category: 'privacy',
      priority: 'medium',
      details: [
        'Регулярно проверяйте настройки приватности',
        'Ограничьте видимость своего профиля',
        'Контролируйте, кто может вам писать',
        'Используйте функцию "Невидимый режим"',
        'Настройте уведомления о просмотрах'
      ]
    },
    {
      id: 'reporting_blocking',
      title: 'Жалобы и блокировка',
      description: 'Как сообщить о проблемах и заблокировать пользователей',
      icon: <ReportIcon />,
      category: 'reporting',
      priority: 'medium',
      details: [
        'Блокируйте пользователей при неподобающем поведении',
        'Сообщайте о нарушениях через функцию жалоб',
        'Сохраняйте скриншоты как доказательства',
        'Не стесняйтесь обращаться в поддержку',
        'Помогайте создавать безопасное сообщество'
      ]
    }
  ];

  const handleTipExpand = (tipId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedTip(isExpanded ? tipId : false);
  };

  const handleEmergencyContactSubmit = async (data: EmergencyContact) => {
    try {
      // Здесь будет вызов API для сохранения экстренного контакта
      // await saveEmergencyContact(data);
      
      console.log('Экстренный контакт сохранен:', data);
      setEmergencyDialogOpen(false);
      reset();
    } catch (err) {
      console.error('Ошибка при сохранении контакта:', err);
    }
  };

  const handleQuickReport = () => {
    router.push('/help/report');
  };

  const handleBack = () => {
    router.push('/help');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'Высокий приоритет';
      case 'medium': return 'Средний приоритет';
      case 'low': return 'Низкий приоритет';
      default: return priority;
    }
  };

  return (
    <>
      <Head>
        <title>Советы по безопасности - Likes & Love</title>
        <meta name="description" content="Важные советы по безопасности для пользователей приложения знакомств Likes & Love" />
        <meta name="keywords" content="безопасность, советы, защита, приватность, знакомства" />
      </Head>

      <Layout title="Советы по безопасности">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton onClick={handleBack}>
                  <ArrowBack />
                </IconButton>
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Советы по безопасности
                </Typography>
              </Box>
              <Typography variant="h6" color="text.secondary">
                Важная информация для безопасного использования приложения
              </Typography>
            </Box>

            {/* Важное предупреждение */}
            <Alert severity="warning" sx={{ mb: 4 }}>
              <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                Ваша безопасность - наш приоритет
              </Typography>
              <Typography variant="body2">
                Пожалуйста, внимательно ознакомьтесь с этими рекомендациями. 
                При возникновении любых подозрений или угроз немедленно обращайтесь в службу поддержки.
              </Typography>
            </Alert>

            {/* Быстрые действия */}
            <Grid container spacing={2} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                  onClick={handleQuickReport}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <ReportIcon color="error" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Пожаловаться
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Сообщить о нарушении
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                  onClick={() => setEmergencyDialogOpen(true)}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <PhoneIcon color="primary" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Экстренный контакт
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Добавить контакт
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                  onClick={() => router.push('/settings/privacy')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <ShieldIcon color="success" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Приватность
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Настройки безопасности
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': { transform: 'scale(1.02)', transition: 'transform 0.2s' }
                  }}
                  onClick={() => router.push('/help/contact')}
                >
                  <CardContent sx={{ textAlign: 'center' }}>
                    <SecurityIcon color="info" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Поддержка
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Связаться с нами
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Советы по безопасности */}
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
              Рекомендации по безопасности
            </Typography>

            {safetyTips.map((tip) => (
              <Accordion
                key={tip.id}
                expanded={expandedTip === tip.id}
                onChange={handleTipExpand(tip.id)}
                sx={{ mb: 2 }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls={`${tip.id}-content`}
                  id={`${tip.id}-header`}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    <Box sx={{ color: 'primary.main' }}>
                      {tip.icon}
                    </Box>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" fontWeight="bold">
                        {tip.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {tip.description}
                      </Typography>
                    </Box>
                    <Chip 
                      label={getPriorityLabel(tip.priority)}
                      color={getPriorityColor(tip.priority) as any}
                      size="small"
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ pl: 5 }}>
                    {tip.details.map((detail, index) => (
                      <Box key={index} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
                        <Box sx={{ 
                          width: 6, 
                          height: 6, 
                          borderRadius: '50%', 
                          backgroundColor: 'primary.main',
                          mt: 1,
                          flexShrink: 0
                        }} />
                        <Typography variant="body2">
                          {detail}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}

            {/* Экстренные номера */}
            <Card sx={{ mt: 4, backgroundColor: 'error.light', color: 'error.contrastText' }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Экстренные номера
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Служба экстренного реагирования:</strong> 112
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Полиция:</strong> 102
                    </Typography>
                    <Typography variant="body2">
                      <strong>Скорая помощь:</strong> 103
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Телефон доверия:</strong> 8-800-2000-122
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Поддержка Likes & Love:</strong> 8-800-555-0123
                    </Typography>
                    <Typography variant="body2">
                      <strong>Email поддержки:</strong> <EMAIL>
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Дополнительные ресурсы */}
            <Card sx={{ mt: 4 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                  Дополнительные ресурсы
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={() => router.push('/help/faq')}
                      sx={{ mb: 1 }}
                    >
                      Часто задаваемые вопросы
                    </Button>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={() => router.push('/legal/terms')}
                      sx={{ mb: 1 }}
                    >
                      Условия использования
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={() => router.push('/legal/privacy')}
                      sx={{ mb: 1 }}
                    >
                      Политика конфиденциальности
                    </Button>
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={() => router.push('/settings/security')}
                    >
                      Настройки безопасности
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Диалог экстренного контакта */}
            <Dialog
              open={emergencyDialogOpen}
              onClose={() => setEmergencyDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                Добавить экстренный контакт
              </DialogTitle>
              <DialogContent>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Укажите контакт человека, которого следует уведомить в экстренной ситуации
                </Typography>
                <form onSubmit={handleSubmit(handleEmergencyContactSubmit)}>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Controller
                        name="name"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Имя контакта"
                            fullWidth
                            error={!!errors.name}
                            helperText={errors.name?.message}
                          />
                        )}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Controller
                        name="phone"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Номер телефона"
                            fullWidth
                            error={!!errors.phone}
                            helperText={errors.phone?.message}
                          />
                        )}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Controller
                        name="relationship"
                        control={control}
                        render={({ field }) => (
                          <TextField
                            {...field}
                            label="Отношения (например: мама, друг)"
                            fullWidth
                            error={!!errors.relationship}
                            helperText={errors.relationship?.message}
                          />
                        )}
                      />
                    </Grid>
                  </Grid>
                </form>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setEmergencyDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleSubmit(handleEmergencyContactSubmit)}
                  variant="contained"
                >
                  Сохранить
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SafetyPage;
