import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Alert,
  CircularProgress,
  Stack,
  Stepper,
  Step,
  StepLabel,
  Chip
} from '@mui/material';
import { 
  CheckCircle, 
  Verified, 
  AccountBalance, 
  Person,
  Security,
  Assignment
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import axios from 'axios';

interface GosuslugiData {
  firstName: string;
  lastName: string;
  middleName?: string;
  birthDate: string;
  passportNumber?: string;
  inn?: string;
  snils?: string;
  trusted: boolean;
  verificationLevel: 'simplified' | 'standard' | 'confirmed';
}

const GosuslugiVerifyPage: React.FC = () => {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const { code, state, error } = router.query;
  const [status, setStatus] = useState<'loading' | 'processing' | 'success' | 'error' | 'confirmation'>('loading');
  const [message, setMessage] = useState('');
  const [gosuslugiData, setGosuslugiData] = useState<GosuslugiData | null>(null);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    'Авторизация через Госуслуги',
    'Получение данных',
    'Подтверждение верификации'
  ];

  const verificationLevels = {
    simplified: { name: 'Упрощенная', color: 'default' as const, icon: '⚪' },
    standard: { name: 'Стандартная', color: 'primary' as const, icon: '🔵' },
    confirmed: { name: 'Подтвержденная', color: 'success' as const, icon: '🟢' }
  };

  useEffect(() => {
    if (router.isReady) {
      handleGosuslugiCallback();
    }
  }, [router.isReady, code, state, error]);

  const handleGosuslugiCallback = async () => {
    // Проверяем наличие ошибки
    if (error) {
      setStatus('error');
      setMessage(getErrorMessage(error as string));
      return;
    }

    // Проверяем обязательные параметры
    if (!code) {
      setStatus('error');
      setMessage('Отсутствует код авторизации от Госуслуг');
      return;
    }

    try {
      setStatus('processing');
      setCurrentStep(1);

      // Обмениваем код на данные пользователя
      const response = await axios.post('/api/auth/gosuslugi/verify', {
        code,
        state
      });

      const { success, userData } = response.data;

      if (success && userData) {
        setGosuslugiData(userData);
        setStatus('confirmation');
        setCurrentStep(2);
      } else {
        setStatus('error');
        setMessage('Не удалось получить данные от Госуслуг');
      }
    } catch (error: any) {
      setStatus('error');
      setMessage(error.response?.data?.message || 'Ошибка верификации через Госуслуги');
    }
  };

  const confirmVerification = async () => {
    if (!gosuslugiData) return;

    try {
      setStatus('processing');

      const response = await axios.post('/api/users/verify-gosuslugi', {
        gosuslugiData
      });

      if (response.data.success) {
        // Обновляем пользователя с новыми данными верификации
        if (user) {
          const updatedUser = {
            ...user,
            profile: {
              ...user.profile,
              firstName: gosuslugiData.firstName,
              lastName: gosuslugiData.lastName,
              displayName: user.profile?.displayName || `${gosuslugiData.firstName} ${gosuslugiData.lastName}`,
              verificationStatus: {
                ...user.profile?.verificationStatus,
                photo: user.profile?.verificationStatus?.photo || false,
                email: user.profile?.verificationStatus?.email || false,
                phone: user.profile?.verificationStatus?.phone || false,
                social: user.profile?.verificationStatus?.social || false
              }
            }
          };
          updateUser(updatedUser);
        }

        setStatus('success');
        setMessage('Верификация через Госуслуги успешно завершена!');

        // Переход в профиль через 3 секунды
        setTimeout(() => {
          router.push('/profile?verified=gosuslugi');
        }, 3000);
      }
    } catch (error: any) {
      setStatus('error');
      setMessage(error.response?.data?.message || 'Ошибка сохранения данных верификации');
    }
  };

  const getErrorMessage = (error: string) => {
    switch (error) {
      case 'access_denied':
        return 'Доступ запрещен. Авторизация в Госуслугах была отменена.';
      case 'invalid_request':
        return 'Неверный запрос к Госуслугам.';
      case 'invalid_client':
        return 'Неверные данные клиента.';
      case 'invalid_grant':
        return 'Неверный код авторизации.';
      case 'unsupported_grant_type':
        return 'Неподдерживаемый тип авторизации.';
      default:
        return `Ошибка Госуслуг: ${error}`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU');
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <Stack spacing={3} alignItems="center">
            <CircularProgress size={60} />
            <Typography variant="h6">
              Подключение к Госуслугам...
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Ожидаем ответ от портала Госуслуг
            </Typography>
          </Stack>
        );

      case 'processing':
        return (
          <Stack spacing={3} alignItems="center">
            <CircularProgress size={60} />
            <Typography variant="h6">
              {currentStep === 1 ? 'Получение данных...' : 'Сохранение данных...'}
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              {currentStep === 1 
                ? 'Загружаем ваши данные с портала Госуслуг' 
                : 'Сохраняем информацию для верификации'
              }
            </Typography>
          </Stack>
        );

      case 'confirmation':
        return (
          <Stack spacing={4}>
            <Box textAlign="center">
              <AccountBalance color="primary" sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Подтвердите данные из Госуслуг
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Проверьте корректность полученных данных
              </Typography>
            </Box>

            {gosuslugiData && (
              <Box>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                  Персональные данные:
                </Typography>
                
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      ФИО:
                    </Typography>
                    <Typography variant="body1">
                      {gosuslugiData.lastName} {gosuslugiData.firstName} {gosuslugiData.middleName || ''}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Дата рождения:
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(gosuslugiData.birthDate)}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Уровень учетной записи:
                    </Typography>
                    <Chip
                      label={verificationLevels[gosuslugiData.verificationLevel].name}
                      color={verificationLevels[gosuslugiData.verificationLevel].color}
                      size="small"
                      icon={<span>{verificationLevels[gosuslugiData.verificationLevel].icon}</span>}
                    />
                  </Box>

                  {gosuslugiData.snils && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        СНИЛС:
                      </Typography>
                      <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                        {gosuslugiData.snils}
                      </Typography>
                    </Box>
                  )}

                  {gosuslugiData.inn && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        ИНН:
                      </Typography>
                      <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                        {gosuslugiData.inn}
                      </Typography>
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Статус доверия:
                    </Typography>
                    <Chip
                      label={gosuslugiData.trusted ? 'Доверенная' : 'Обычная'}
                      color={gosuslugiData.trusted ? 'success' : 'default'}
                      size="small"
                      icon={gosuslugiData.trusted ? <Verified /> : <Person />}
                    />
                  </Box>
                </Stack>

                <Alert severity="info" sx={{ mt: 3 }}>
                  <Typography variant="body2">
                    <strong>Что это дает:</strong>
                    <br />
                    • Подтверждение вашей личности
                    <br />
                    • Повышение доверия к профилю
                    <br />
                    • Доступ к дополнительным функциям
                    <br />
                    • Защита от мошенников
                  </Typography>
                </Alert>

                <Button
                  variant="contained"
                  fullWidth
                  onClick={confirmVerification}
                  startIcon={<Security />}
                  sx={{ mt: 3 }}
                >
                  Подтвердить верификацию
                </Button>
              </Box>
            )}
          </Stack>
        );

      case 'success':
        return (
          <Stack spacing={3} alignItems="center">
            <CheckCircle color="success" sx={{ fontSize: 80 }} />
            <Typography variant="h5" color="success.main">
              Верификация завершена!
            </Typography>
            <Typography variant="body1" textAlign="center">
              Ваш профиль успешно верифицирован через Госуслуги. 
              Теперь у вас есть официальное подтверждение личности.
            </Typography>
            <Stack direction="row" spacing={1} alignItems="center">
              <Verified color="success" />
              <Typography variant="body2" color="success.main">
                Профиль верифицирован
              </Typography>
            </Stack>
            <Button
              variant="contained"
              onClick={() => router.push('/profile')}
            >
              Перейти в профиль
            </Button>
          </Stack>
        );

      case 'error':
        return (
          <Stack spacing={3} alignItems="center">
            <Alert severity="error" sx={{ width: '100%' }}>
              {message}
            </Alert>
            <Typography variant="body2" textAlign="center">
              Попробуйте повторить верификацию позже или обратитесь в поддержку.
            </Typography>
            <Stack direction="row" spacing={2}>
              <Button
                variant="contained"
                onClick={() => router.push('/profile/verification')}
              >
                Попробовать снова
              </Button>
              <Button
                variant="outlined"
                onClick={() => router.push('/help/contact')}
              >
                Связаться с поддержкой
              </Button>
            </Stack>
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <Layout>
      <Container maxWidth="md">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Typography variant="h4" align="center" gutterBottom>
              Верификация через Госуслуги
            </Typography>
            
            <Box sx={{ mb: 4 }}>
              <Stepper activeStep={currentStep} alternativeLabel>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
            </Box>

            {renderContent()}

            {status !== 'loading' && status !== 'success' && (
              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Верификация через Госуслуги использует официальные API портала 
                  и полностью соответствует требованиям безопасности.
                </Typography>
              </Box>
            )}
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default GosuslugiVerifyPage;
