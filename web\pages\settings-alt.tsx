import React from 'react';
import { GetServerSideProps } from 'next';
import { useAuth } from '../src/providers/AuthProvider';
import { useRouter } from 'next/router';
import SettingsPage from '../components/Profile/SettingsPage';

const SettingsPageRoute: React.FC = () => {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  React.useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth');
    }
  }, [user, isLoading, router]);

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <div>Загрузка...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return <SettingsPage />;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  return {
    props: {},
  };
};

export default SettingsPageRoute;
