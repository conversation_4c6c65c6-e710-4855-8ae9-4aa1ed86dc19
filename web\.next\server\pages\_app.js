"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./components/common/Notification.tsx":
/*!********************************************!*\
  !*** ./components/common/Notification.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notification: () => (/* binding */ Notification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Snackbar!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js\");\n\n\n\nconst Notification = ({ open, message, severity = 'success', onClose, autoHideDuration = 6000 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_2__.Snackbar, {\n        open: open,\n        autoHideDuration: autoHideDuration,\n        onClose: onClose,\n        anchorOrigin: {\n            vertical: 'bottom',\n            horizontal: 'center'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n            onClose: onClose,\n            severity: severity,\n            children: message\n        }, void 0, false, {\n            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\components\\\\common\\\\Notification.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\components\\\\common\\\\Notification.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvY29tbW9uL05vdGlmaWNhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNrQztBQVVyRCxNQUFNRyxlQUE0QyxDQUFDLEVBQ3hEQyxJQUFJLEVBQ0pDLE9BQU8sRUFDUEMsV0FBVyxTQUFTLEVBQ3BCQyxPQUFPLEVBQ1BDLG1CQUFtQixJQUFJLEVBQ3hCO0lBQ0MscUJBQ0UsOERBQUNQLHdGQUFRQTtRQUNQRyxNQUFNQTtRQUNOSSxrQkFBa0JBO1FBQ2xCRCxTQUFTQTtRQUNURSxjQUFjO1lBQUVDLFVBQVU7WUFBVUMsWUFBWTtRQUFTO2tCQUV6RCw0RUFBQ1QscUZBQUtBO1lBQUNLLFNBQVNBO1lBQVNELFVBQVVBO3NCQUNoQ0Q7Ozs7Ozs7Ozs7O0FBSVQsRUFBRSIsInNvdXJjZXMiOlsiRjpcXEN1cnNvclxcbGwuY29tXFxpbnN0YWxsXFx3ZWJcXGNvbXBvbmVudHNcXGNvbW1vblxcTm90aWZpY2F0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU25hY2tiYXIsIEFsZXJ0LCBBbGVydFByb3BzIH0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb25Qcm9wcyB7XG4gIG9wZW46IGJvb2xlYW47XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgc2V2ZXJpdHk/OiBBbGVydFByb3BzWydzZXZlcml0eSddO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBhdXRvSGlkZUR1cmF0aW9uPzogbnVtYmVyO1xufVxuXG5leHBvcnQgY29uc3QgTm90aWZpY2F0aW9uOiBSZWFjdC5GQzxOb3RpZmljYXRpb25Qcm9wcz4gPSAoe1xuICBvcGVuLFxuICBtZXNzYWdlLFxuICBzZXZlcml0eSA9ICdzdWNjZXNzJyxcbiAgb25DbG9zZSxcbiAgYXV0b0hpZGVEdXJhdGlvbiA9IDYwMDAsXG59KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFNuYWNrYmFyXG4gICAgICBvcGVuPXtvcGVufVxuICAgICAgYXV0b0hpZGVEdXJhdGlvbj17YXV0b0hpZGVEdXJhdGlvbn1cbiAgICAgIG9uQ2xvc2U9e29uQ2xvc2V9XG4gICAgICBhbmNob3JPcmlnaW49e3sgdmVydGljYWw6ICdib3R0b20nLCBob3Jpem9udGFsOiAnY2VudGVyJyB9fVxuICAgID5cbiAgICAgIDxBbGVydCBvbkNsb3NlPXtvbkNsb3NlfSBzZXZlcml0eT17c2V2ZXJpdHl9PlxuICAgICAgICB7bWVzc2FnZX1cbiAgICAgIDwvQWxlcnQ+XG4gICAgPC9TbmFja2Jhcj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbmFja2JhciIsIkFsZXJ0IiwiTm90aWZpY2F0aW9uIiwib3BlbiIsIm1lc3NhZ2UiLCJzZXZlcml0eSIsIm9uQ2xvc2UiLCJhdXRvSGlkZUR1cmF0aW9uIiwiYW5jaG9yT3JpZ2luIiwidmVydGljYWwiLCJob3Jpem9udGFsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/common/Notification.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(pages-dir-node)/./node_modules/@mui/material/node/CssBaseline/index.js\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @emotion/react */ \"@emotion/react\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"@tanstack/react-query\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../src/createEmotionCache */ \"(pages-dir-node)/./src/createEmotionCache.ts\");\n/* harmony import */ var _src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../src/providers/AppProviders */ \"(pages-dir-node)/./src/providers/AppProviders.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_react__WEBPACK_IMPORTED_MODULE_1__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__, _src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__, _src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__]);\n([_emotion_react__WEBPACK_IMPORTED_MODULE_1__, _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__, _src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__, _src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n// Динамический импорт ReactQueryDevtools только для клиентской стороны\nconst ReactQueryDevtools = next_dynamic__WEBPACK_IMPORTED_MODULE_5___default()(()=>Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @tanstack/react-query-devtools */ \"@tanstack/react-query-devtools\")).then((mod)=>mod.ReactQueryDevtools), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\_app.tsx -> \" + \"@tanstack/react-query-devtools\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\n\n// i18n управляется через next-i18next\n\n// Client-side cache, shared for the whole session of the user in the browser.\nconst clientSideEmotionCache = (0,_src_createEmotionCache__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n// Create a client\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 1000 * 60 * 5,\n            gcTime: 1000 * 60 * 10,\n            retry: 3,\n            refetchOnWindowFocus: false\n        }\n    }\n});\nfunction MyApp(props) {\n    const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_1__.CacheProvider, {\n        value: emotionCache,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Likes & Love - Найдите свою настоящую любовь\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Платформа для серьезных знакомств с проверенными профилями\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n                client: queryClient,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_providers_AppProviders__WEBPACK_IMPORTED_MODULE_7__.AppProviders, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                ...pageProps\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQueryDevtools, {\n                        initialIsOpen: false\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\pages\\\\_app.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_i18next__WEBPACK_IMPORTED_MODULE_3__.appWithTranslation)(MyApp));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/config/design-system.ts":
/*!*************************************!*\
  !*** ./src/config/design-system.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CROSS_PLATFORM_THEMES: () => (/* binding */ CROSS_PLATFORM_THEMES),\n/* harmony export */   DESIGN_TOKENS: () => (/* binding */ DESIGN_TOKENS),\n/* harmony export */   exportTokensForPlatform: () => (/* binding */ exportTokensForPlatform),\n/* harmony export */   getThemeById: () => (/* binding */ getThemeById),\n/* harmony export */   syncThemeAcrossPlatforms: () => (/* binding */ syncThemeAcrossPlatforms)\n/* harmony export */ });\n/**\r\n * Кроссплатформенная система дизайна для likes-love.com\r\n * Используется во всех версиях приложения: Web, iOS, Android, Huawei\r\n */ // Глобальные токены дизайна\nconst DESIGN_TOKENS = [\n    // Цвета\n    {\n        id: 'color-likes-love-primary',\n        name: 'Likes Love Primary',\n        value: '#ff6b9d',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'color-likes-love-secondary',\n        name: 'Likes Love Secondary',\n        value: '#4ecdc4',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'color-romantic-primary',\n        name: 'Romantic Primary',\n        value: '#ff6b9d',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'color-romantic-secondary',\n        name: 'Romantic Secondary',\n        value: '#ff8e53',\n        category: 'color',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    // Градиенты\n    {\n        id: 'gradient-likes-love-primary',\n        name: 'Likes Love Primary Gradient',\n        value: 'linear-gradient(135deg, #ff6b9d 0%, #4ecdc4 100%)',\n        category: 'gradient',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'gradient-romantic-primary',\n        name: 'Romantic Primary Gradient',\n        value: 'linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%)',\n        category: 'gradient',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    // Отступы (в пикселях)\n    {\n        id: 'spacing-xs',\n        name: 'Extra Small Spacing',\n        value: '4',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-sm',\n        name: 'Small Spacing',\n        value: '8',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-md',\n        name: 'Medium Spacing',\n        value: '16',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-lg',\n        name: 'Large Spacing',\n        value: '24',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'spacing-xl',\n        name: 'Extra Large Spacing',\n        value: '32',\n        category: 'spacing',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    // Радиусы скругления\n    {\n        id: 'radius-sm',\n        name: 'Small Border Radius',\n        value: '8',\n        category: 'radius',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'radius-md',\n        name: 'Medium Border Radius',\n        value: '12',\n        category: 'radius',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    },\n    {\n        id: 'radius-lg',\n        name: 'Large Border Radius',\n        value: '16',\n        category: 'radius',\n        platforms: [\n            'web',\n            'ios',\n            'android',\n            'huawei'\n        ]\n    }\n];\n// Конфигурации тем для всех платформ\nconst CROSS_PLATFORM_THEMES = [\n    {\n        id: 'likes-love',\n        name: 'likes-love',\n        displayName: 'Likes & Love',\n        primaryColor: '#ff6b9d',\n        secondaryColor: '#4ecdc4',\n        gradients: {\n            primary: 'linear-gradient(135deg, #ff6b9d 0%, #4ecdc4 100%)',\n            secondary: 'linear-gradient(135deg, #4ecdc4 0%, #45b7aa 100%)',\n            accent: 'linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#f8f9fa',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#ff6b9d',\n                inverse: '#ffffff'\n            },\n            border: '#e2e8f0',\n            shadow: 'rgba(0, 0, 0, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\n            md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1)',\n            lg: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'\n        }\n    },\n    {\n        id: 'romantic',\n        name: 'romantic',\n        displayName: 'Романтическая',\n        primaryColor: '#ff6b9d',\n        secondaryColor: '#ff8e53',\n        gradients: {\n            primary: 'linear-gradient(135deg, #ff6b9d 0%, #ff8e53 100%)',\n            secondary: 'linear-gradient(135deg, #ff8e53 0%, #ffb347 100%)',\n            accent: 'linear-gradient(135deg, #ff6b9d 0%, #ff7fab 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#fef7f7',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#ff6b9d',\n                inverse: '#ffffff'\n            },\n            border: '#f7e8e8',\n            shadow: 'rgba(255, 107, 157, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(255, 107, 157, 0.12), 0 1px 2px rgba(255, 107, 157, 0.24)',\n            md: '0 4px 6px rgba(255, 107, 157, 0.07), 0 1px 3px rgba(255, 107, 157, 0.1)',\n            lg: '0 10px 25px rgba(255, 107, 157, 0.1), 0 4px 6px rgba(255, 107, 157, 0.05)'\n        }\n    },\n    {\n        id: 'elegant',\n        name: 'elegant',\n        displayName: 'Элегантная',\n        primaryColor: '#9f7aea',\n        secondaryColor: '#667eea',\n        gradients: {\n            primary: 'linear-gradient(135deg, #9f7aea 0%, #667eea 100%)',\n            secondary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            accent: 'linear-gradient(135deg, #9f7aea 0%, #b794f6 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#faf5ff',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#9f7aea',\n                inverse: '#ffffff'\n            },\n            border: '#e9d8fd',\n            shadow: 'rgba(159, 122, 234, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',\n            fontSizes: {\n                xs: 12,\n                sm: 14,\n                md: 16,\n                lg: 18,\n                xl: 24,\n                xxl: 32\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(159, 122, 234, 0.12), 0 1px 2px rgba(159, 122, 234, 0.24)',\n            md: '0 4px 6px rgba(159, 122, 234, 0.07), 0 1px 3px rgba(159, 122, 234, 0.1)',\n            lg: '0 10px 25px rgba(159, 122, 234, 0.1), 0 4px 6px rgba(159, 122, 234, 0.05)'\n        }\n    },\n    {\n        id: 'modern',\n        name: 'modern',\n        displayName: 'Современная',\n        primaryColor: '#38b2ac',\n        secondaryColor: '#4fd1c7',\n        gradients: {\n            primary: 'linear-gradient(135deg, #38b2ac 0%, #4fd1c7 100%)',\n            secondary: 'linear-gradient(135deg, #4fd1c7 0%, #81e6d9 100%)',\n            accent: 'linear-gradient(135deg, #38b2ac 0%, #319795 100%)'\n        },\n        colors: {\n            background: '#ffffff',\n            surface: '#f0fffe',\n            text: {\n                primary: '#2d3748',\n                secondary: '#718096',\n                accent: '#38b2ac',\n                inverse: '#ffffff'\n            },\n            border: '#b2f5ea',\n            shadow: 'rgba(56, 178, 172, 0.1)',\n            overlay: 'rgba(0, 0, 0, 0.5)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(56, 178, 172, 0.12), 0 1px 2px rgba(56, 178, 172, 0.24)',\n            md: '0 4px 6px rgba(56, 178, 172, 0.07), 0 1px 3px rgba(56, 178, 172, 0.1)',\n            lg: '0 10px 25px rgba(56, 178, 172, 0.1), 0 4px 6px rgba(56, 178, 172, 0.05)'\n        }\n    },\n    {\n        id: 'dark',\n        name: 'dark',\n        displayName: 'Темная',\n        primaryColor: '#f56565',\n        secondaryColor: '#48bb78',\n        gradients: {\n            primary: 'linear-gradient(135deg, #f56565 0%, #48bb78 100%)',\n            secondary: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',\n            accent: 'linear-gradient(135deg, #f56565 0%, #fc8181 100%)'\n        },\n        colors: {\n            background: '#1a202c',\n            surface: '#2d3748',\n            text: {\n                primary: '#f7fafc',\n                secondary: '#a0aec0',\n                accent: '#f56565',\n                inverse: '#2d3748'\n            },\n            border: '#4a5568',\n            shadow: 'rgba(0, 0, 0, 0.3)',\n            overlay: 'rgba(0, 0, 0, 0.7)',\n            dating: {\n                like: '#4caf50',\n                pass: '#f44336',\n                superLike: '#2196f3',\n                match: '#ff9800',\n                online: '#4caf50',\n                verified: '#2196f3',\n                premium: '#ffd700'\n            },\n            success: '#4caf50',\n            warning: '#ff9800',\n            error: '#f44336',\n            info: '#2196f3'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            fontSizes: {\n                xs: 16,\n                sm: 18,\n                md: 20,\n                lg: 24,\n                xl: 32,\n                xxl: 42 // Увеличено с 32 для hero-заголовков\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            },\n            lineHeights: {\n                tight: 1.2,\n                normal: 1.5,\n                relaxed: 1.7,\n                loose: 2.0\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\n            md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1)',\n            lg: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'\n        }\n    }\n];\n// Функция для получения темы по ID\nconst getThemeById = (themeId)=>{\n    return CROSS_PLATFORM_THEMES.find((theme)=>theme.id === themeId);\n};\n// Функция для экспорта токенов в различные форматы\nconst exportTokensForPlatform = (platform)=>{\n    const platformTokens = DESIGN_TOKENS.filter((token)=>token.platforms.includes(platform));\n    switch(platform){\n        case 'web':\n            return exportToCSSVariables(platformTokens);\n        case 'ios':\n            return exportToSwiftUITokens(platformTokens);\n        case 'android':\n            return exportToXMLColors(platformTokens);\n        case 'huawei':\n            return exportToHarmonyOSTokens(platformTokens);\n        default:\n            return platformTokens;\n    }\n};\n// Экспорт в CSS переменные для Web\nconst exportToCSSVariables = (tokens)=>{\n    const cssVars = tokens.map((token)=>{\n        const cssName = `--${token.id.replace(/[A-Z]/g, (letter)=>`-${letter.toLowerCase()}`)}`;\n        return `  ${cssName}: ${token.value};`;\n    }).join('\\n');\n    return `:root {\\n${cssVars}\\n}`;\n};\n// Экспорт для SwiftUI (iOS)\nconst exportToSwiftUITokens = (tokens)=>{\n    const swiftTokens = tokens.map((token)=>{\n        const swiftName = token.id.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());\n        if (token.category === 'color') {\n            return `  static let ${swiftName} = Color(hex: \"${token.value}\")`;\n        }\n        return `  static let ${swiftName} = ${token.value}`;\n    }).join('\\n');\n    return `struct DesignTokens {\\n${swiftTokens}\\n}`;\n};\n// Экспорт для Android XML\nconst exportToXMLColors = (tokens)=>{\n    const colorTokens = tokens.filter((token)=>token.category === 'color');\n    const xmlColors = colorTokens.map((token)=>{\n        const xmlName = token.id.replace(/-/g, '_');\n        return `  <color name=\"${xmlName}\">${token.value}</color>`;\n    }).join('\\n');\n    return `<resources>\\n${xmlColors}\\n</resources>`;\n};\n// Экспорт для HarmonyOS\nconst exportToHarmonyOSTokens = (tokens)=>{\n    const harmonyTokens = tokens.map((token)=>{\n        const harmonyName = token.id.replace(/-([a-z])/g, (_, letter)=>letter.toUpperCase());\n        return `  ${harmonyName}: '${token.value}',`;\n    }).join('\\n');\n    return `export const DesignTokens = {\\n${harmonyTokens}\\n};`;\n};\nconst syncThemeAcrossPlatforms = async (preferences)=>{\n    // Логика синхронизации настроек темы между платформами\n    // Здесь будет интеграция с API для сохранения настроек пользователя\n    return {\n        success: true,\n        syncedAt: new Date().toISOString(),\n        platforms: preferences.syncedPlatforms\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb25maWcvZGVzaWduLXN5c3RlbS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOzs7Q0FHQyxHQXdGRCw0QkFBNEI7QUFDckIsTUFBTUEsZ0JBQStCO0lBQzFDLFFBQVE7SUFDUjtRQUNFQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFdBQVc7WUFBQztZQUFPO1lBQU87WUFBVztTQUFTO0lBQ2hEO0lBQ0E7UUFDRUosSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxXQUFXO1lBQUM7WUFBTztZQUFPO1lBQVc7U0FBUztJQUNoRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsV0FBVztZQUFDO1lBQU87WUFBTztZQUFXO1NBQVM7SUFDaEQ7SUFDQTtRQUNFSixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFdBQVc7WUFBQztZQUFPO1lBQU87WUFBVztTQUFTO0lBQ2hEO0lBRUEsWUFBWTtJQUNaO1FBQ0VKLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsV0FBVztZQUFDO1lBQU87WUFBTztZQUFXO1NBQVM7SUFDaEQ7SUFDQTtRQUNFSixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFdBQVc7WUFBQztZQUFPO1lBQU87WUFBVztTQUFTO0lBQ2hEO0lBRUEsdUJBQXVCO0lBQ3ZCO1FBQ0VKLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsV0FBVztZQUFDO1lBQU87WUFBTztZQUFXO1NBQVM7SUFDaEQ7SUFDQTtRQUNFSixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFdBQVc7WUFBQztZQUFPO1lBQU87WUFBVztTQUFTO0lBQ2hEO0lBQ0E7UUFDRUosSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxXQUFXO1lBQUM7WUFBTztZQUFPO1lBQVc7U0FBUztJQUNoRDtJQUNBO1FBQ0VKLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsV0FBVztZQUFDO1lBQU87WUFBTztZQUFXO1NBQVM7SUFDaEQ7SUFDQTtRQUNFSixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFdBQVc7WUFBQztZQUFPO1lBQU87WUFBVztTQUFTO0lBQ2hEO0lBRUEscUJBQXFCO0lBQ3JCO1FBQ0VKLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsV0FBVztZQUFDO1lBQU87WUFBTztZQUFXO1NBQVM7SUFDaEQ7SUFDQTtRQUNFSixJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFdBQVc7WUFBQztZQUFPO1lBQU87WUFBVztTQUFTO0lBQ2hEO0lBQ0E7UUFDRUosSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxXQUFXO1lBQUM7WUFBTztZQUFPO1lBQVc7U0FBUztJQUNoRDtDQUNELENBQUM7QUFFRixxQ0FBcUM7QUFDOUIsTUFBTUMsd0JBQXVDO0lBQ2xEO1FBQ0VMLElBQUk7UUFDSkMsTUFBTTtRQUNOSyxhQUFhO1FBQ2JDLGNBQWM7UUFDZEMsZ0JBQWdCO1FBQ2hCQyxXQUFXO1lBQ1RDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxRQUFRO1FBQ1Y7UUFDQUMsUUFBUTtZQUNOQyxZQUFZO1lBQ1pDLFNBQVM7WUFDVEMsTUFBTTtnQkFDSk4sU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsUUFBUTtnQkFDUkssU0FBUztZQUNYO1lBQ0FDLFFBQVE7WUFDUkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFFBQVE7Z0JBQ05DLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLFNBQVM7WUFDWDtZQUNBQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7UUFDQUMsU0FBUztZQUNQQyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtRQUNBQyxjQUFjO1lBQ1pKLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pHLE1BQU07UUFDUjtRQUNBQyxZQUFZO1lBQ1ZDLFlBQVk7WUFDWkMsV0FBVztnQkFDVFQsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSk0sS0FBSyxHQUFNLHFDQUFxQztZQUNsRDtZQUNBQyxhQUFhO2dCQUNYQyxRQUFRO2dCQUNSQyxRQUFRO2dCQUNSQyxVQUFVO2dCQUNWQyxNQUFNO1lBQ1I7WUFDQUMsYUFBYTtnQkFDWEMsT0FBTztnQkFDUEwsUUFBUTtnQkFDUk0sU0FBUztnQkFDVEMsT0FBTztZQUNUO1FBQ0Y7UUFDQUMsU0FBUztZQUNQbkIsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtJQUNGO0lBQ0E7UUFDRXJDLElBQUk7UUFDSkMsTUFBTTtRQUNOSyxhQUFhO1FBQ2JDLGNBQWM7UUFDZEMsZ0JBQWdCO1FBQ2hCQyxXQUFXO1lBQ1RDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxRQUFRO1FBQ1Y7UUFDQUMsUUFBUTtZQUNOQyxZQUFZO1lBQ1pDLFNBQVM7WUFDVEMsTUFBTTtnQkFDSk4sU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsUUFBUTtnQkFDUkssU0FBUztZQUNYO1lBQ0FDLFFBQVE7WUFDUkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFFBQVE7Z0JBQ05DLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLFNBQVM7WUFDWDtZQUNBQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7UUFDQUMsU0FBUztZQUNQQyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtRQUNBQyxjQUFjO1lBQ1pKLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pHLE1BQU07UUFDUjtRQUNBQyxZQUFZO1lBQ1ZDLFlBQVk7WUFDWkMsV0FBVztnQkFDVFQsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSk0sS0FBSyxHQUFNLHFDQUFxQztZQUNsRDtZQUNBQyxhQUFhO2dCQUNYQyxRQUFRO2dCQUNSQyxRQUFRO2dCQUNSQyxVQUFVO2dCQUNWQyxNQUFNO1lBQ1I7WUFDQUMsYUFBYTtnQkFDWEMsT0FBTztnQkFDUEwsUUFBUTtnQkFDUk0sU0FBUztnQkFDVEMsT0FBTztZQUNUO1FBQ0Y7UUFDQUMsU0FBUztZQUNQbkIsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtJQUNGO0lBQ0E7UUFDRXJDLElBQUk7UUFDSkMsTUFBTTtRQUNOSyxhQUFhO1FBQ2JDLGNBQWM7UUFDZEMsZ0JBQWdCO1FBQ2hCQyxXQUFXO1lBQ1RDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxRQUFRO1FBQ1Y7UUFDQUMsUUFBUTtZQUNOQyxZQUFZO1lBQ1pDLFNBQVM7WUFDVEMsTUFBTTtnQkFDSk4sU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsUUFBUTtnQkFDUkssU0FBUztZQUNYO1lBQ0FDLFFBQVE7WUFDUkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFFBQVE7Z0JBQ05DLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLFdBQVc7Z0JBQ1hDLE9BQU87Z0JBQ1BDLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLFNBQVM7WUFDWDtZQUNBQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7UUFDQUMsU0FBUztZQUNQQyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7UUFDTjtRQUNBQyxjQUFjO1lBQ1pKLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pHLE1BQU07UUFDUjtRQUNBQyxZQUFZO1lBQ1ZDLFlBQVk7WUFDWkMsV0FBVztnQkFDVFQsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSkMsSUFBSTtnQkFDSk0sS0FBSztZQUNQO1lBQ0FDLGFBQWE7Z0JBQ1hDLFFBQVE7Z0JBQ1JDLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLE1BQU07WUFDUjtRQUNGO1FBQ0FLLFNBQVM7WUFDUG5CLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1FBQ047SUFDRjtJQUNBO1FBQ0VyQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkssYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLGdCQUFnQjtRQUNoQkMsV0FBVztZQUNUQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsUUFBUTtRQUNWO1FBQ0FDLFFBQVE7WUFDTkMsWUFBWTtZQUNaQyxTQUFTO1lBQ1RDLE1BQU07Z0JBQ0pOLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLFFBQVE7Z0JBQ1JLLFNBQVM7WUFDWDtZQUNBQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxRQUFRO2dCQUNOQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxXQUFXO2dCQUNYQyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxVQUFVO2dCQUNWQyxTQUFTO1lBQ1g7WUFDQUMsU0FBUztZQUNUQyxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLFNBQVM7WUFDUEMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1FBQ047UUFDQUMsY0FBYztZQUNaSixJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKRyxNQUFNO1FBQ1I7UUFDQUMsWUFBWTtZQUNWQyxZQUFZO1lBQ1pDLFdBQVc7Z0JBQ1RULElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pNLEtBQUssR0FBTSxxQ0FBcUM7WUFDbEQ7WUFDQUMsYUFBYTtnQkFDWEMsUUFBUTtnQkFDUkMsUUFBUTtnQkFDUkMsVUFBVTtnQkFDVkMsTUFBTTtZQUNSO1lBQ0FDLGFBQWE7Z0JBQ1hDLE9BQU87Z0JBQ1BMLFFBQVE7Z0JBQ1JNLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVDtRQUNGO1FBQ0FDLFNBQVM7WUFDUG5CLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1FBQ047SUFDRjtJQUNBO1FBQ0VyQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkssYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLGdCQUFnQjtRQUNoQkMsV0FBVztZQUNUQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsUUFBUTtRQUNWO1FBQ0FDLFFBQVE7WUFDTkMsWUFBWTtZQUNaQyxTQUFTO1lBQ1RDLE1BQU07Z0JBQ0pOLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLFFBQVE7Z0JBQ1JLLFNBQVM7WUFDWDtZQUNBQyxRQUFRO1lBQ1JDLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxRQUFRO2dCQUNOQyxNQUFNO2dCQUNOQyxNQUFNO2dCQUNOQyxXQUFXO2dCQUNYQyxPQUFPO2dCQUNQQyxRQUFRO2dCQUNSQyxVQUFVO2dCQUNWQyxTQUFTO1lBQ1g7WUFDQUMsU0FBUztZQUNUQyxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLFNBQVM7WUFDUEMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1FBQ047UUFDQUMsY0FBYztZQUNaSixJQUFJO1lBQ0pDLElBQUk7WUFDSkMsSUFBSTtZQUNKRyxNQUFNO1FBQ1I7UUFDQUMsWUFBWTtZQUNWQyxZQUFZO1lBQ1pDLFdBQVc7Z0JBQ1RULElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pDLElBQUk7Z0JBQ0pNLEtBQUssR0FBTSxxQ0FBcUM7WUFDbEQ7WUFDQUMsYUFBYTtnQkFDWEMsUUFBUTtnQkFDUkMsUUFBUTtnQkFDUkMsVUFBVTtnQkFDVkMsTUFBTTtZQUNSO1lBQ0FDLGFBQWE7Z0JBQ1hDLE9BQU87Z0JBQ1BMLFFBQVE7Z0JBQ1JNLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVDtRQUNGO1FBQ0FDLFNBQVM7WUFDUG5CLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1FBQ047SUFDRjtDQUNELENBQUM7QUFFRixtQ0FBbUM7QUFDNUIsTUFBTWtCLGVBQWUsQ0FBQ0M7SUFDM0IsT0FBT25ELHNCQUFzQm9ELElBQUksQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTTFELEVBQUUsS0FBS3dEO0FBQzFELEVBQUU7QUFFRixtREFBbUQ7QUFDNUMsTUFBTUcsMEJBQTBCLENBQUNDO0lBQ3RDLE1BQU1DLGlCQUFpQjlELGNBQWMrRCxNQUFNLENBQUNDLENBQUFBLFFBQzFDQSxNQUFNM0QsU0FBUyxDQUFDNEQsUUFBUSxDQUFDSjtJQUczQixPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPSyxxQkFBcUJKO1FBQzlCLEtBQUs7WUFDSCxPQUFPSyxzQkFBc0JMO1FBQy9CLEtBQUs7WUFDSCxPQUFPTSxrQkFBa0JOO1FBQzNCLEtBQUs7WUFDSCxPQUFPTyx3QkFBd0JQO1FBQ2pDO1lBQ0UsT0FBT0E7SUFDWDtBQUNGLEVBQUU7QUFFRixtQ0FBbUM7QUFDbkMsTUFBTUksdUJBQXVCLENBQUNJO0lBQzVCLE1BQU1DLFVBQVVELE9BQU9FLEdBQUcsQ0FBQ1IsQ0FBQUE7UUFDekIsTUFBTVMsVUFBVSxDQUFDLEVBQUUsRUFBRVQsTUFBTS9ELEVBQUUsQ0FBQ3lFLE9BQU8sQ0FBQyxVQUFVQyxDQUFBQSxTQUFVLENBQUMsQ0FBQyxFQUFFQSxPQUFPQyxXQUFXLElBQUksR0FBRztRQUN2RixPQUFPLENBQUMsRUFBRSxFQUFFSCxRQUFRLEVBQUUsRUFBRVQsTUFBTTdELEtBQUssQ0FBQyxDQUFDLENBQUM7SUFDeEMsR0FBRzBFLElBQUksQ0FBQztJQUVSLE9BQU8sQ0FBQyxTQUFTLEVBQUVOLFFBQVEsR0FBRyxDQUFDO0FBQ2pDO0FBRUEsNEJBQTRCO0FBQzVCLE1BQU1KLHdCQUF3QixDQUFDRztJQUM3QixNQUFNUSxjQUFjUixPQUFPRSxHQUFHLENBQUNSLENBQUFBO1FBQzdCLE1BQU1lLFlBQVlmLE1BQU0vRCxFQUFFLENBQUN5RSxPQUFPLENBQUMsYUFBYSxDQUFDTSxHQUFHTCxTQUFXQSxPQUFPTSxXQUFXO1FBQ2pGLElBQUlqQixNQUFNNUQsUUFBUSxLQUFLLFNBQVM7WUFDOUIsT0FBTyxDQUFDLGFBQWEsRUFBRTJFLFVBQVUsZUFBZSxFQUFFZixNQUFNN0QsS0FBSyxDQUFDLEVBQUUsQ0FBQztRQUNuRTtRQUNBLE9BQU8sQ0FBQyxhQUFhLEVBQUU0RSxVQUFVLEdBQUcsRUFBRWYsTUFBTTdELEtBQUssRUFBRTtJQUNyRCxHQUFHMEUsSUFBSSxDQUFDO0lBRVIsT0FBTyxDQUFDLHVCQUF1QixFQUFFQyxZQUFZLEdBQUcsQ0FBQztBQUNuRDtBQUVBLDBCQUEwQjtBQUMxQixNQUFNVixvQkFBb0IsQ0FBQ0U7SUFDekIsTUFBTVksY0FBY1osT0FBT1AsTUFBTSxDQUFDQyxDQUFBQSxRQUFTQSxNQUFNNUQsUUFBUSxLQUFLO0lBQzlELE1BQU0rRSxZQUFZRCxZQUFZVixHQUFHLENBQUNSLENBQUFBO1FBQ2hDLE1BQU1vQixVQUFVcEIsTUFBTS9ELEVBQUUsQ0FBQ3lFLE9BQU8sQ0FBQyxNQUFNO1FBQ3ZDLE9BQU8sQ0FBQyxlQUFlLEVBQUVVLFFBQVEsRUFBRSxFQUFFcEIsTUFBTTdELEtBQUssQ0FBQyxRQUFRLENBQUM7SUFDNUQsR0FBRzBFLElBQUksQ0FBQztJQUVSLE9BQU8sQ0FBQyxhQUFhLEVBQUVNLFVBQVUsY0FBYyxDQUFDO0FBQ2xEO0FBRUEsd0JBQXdCO0FBQ3hCLE1BQU1kLDBCQUEwQixDQUFDQztJQUMvQixNQUFNZSxnQkFBZ0JmLE9BQU9FLEdBQUcsQ0FBQ1IsQ0FBQUE7UUFDL0IsTUFBTXNCLGNBQWN0QixNQUFNL0QsRUFBRSxDQUFDeUUsT0FBTyxDQUFDLGFBQWEsQ0FBQ00sR0FBR0wsU0FBV0EsT0FBT00sV0FBVztRQUNuRixPQUFPLENBQUMsRUFBRSxFQUFFSyxZQUFZLEdBQUcsRUFBRXRCLE1BQU03RCxLQUFLLENBQUMsRUFBRSxDQUFDO0lBQzlDLEdBQUcwRSxJQUFJLENBQUM7SUFFUixPQUFPLENBQUMsK0JBQStCLEVBQUVRLGNBQWMsSUFBSSxDQUFDO0FBQzlEO0FBZU8sTUFBTUUsMkJBQTJCLE9BQU9DO0lBQzdDLHVEQUF1RDtJQUN2RCxvRUFBb0U7SUFDcEUsT0FBTztRQUNMMUQsU0FBUztRQUNUMkQsVUFBVSxJQUFJQyxPQUFPQyxXQUFXO1FBQ2hDdEYsV0FBV21GLFlBQVlJLGVBQWU7SUFDeEM7QUFDRixFQUFFIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcc3JjXFxjb25maWdcXGRlc2lnbi1zeXN0ZW0udHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqINCa0YDQvtGB0YHQv9C70LDRgtGE0L7RgNC80LXQvdC90LDRjyDRgdC40YHRgtC10LzQsCDQtNC40LfQsNC50L3QsCDQtNC70Y8gbGlrZXMtbG92ZS5jb21cclxuICog0JjRgdC/0L7Qu9GM0LfRg9C10YLRgdGPINCy0L4g0LLRgdC10YUg0LLQtdGA0YHQuNGP0YUg0L/RgNC40LvQvtC20LXQvdC40Y86IFdlYiwgaU9TLCBBbmRyb2lkLCBIdWF3ZWlcclxuICovXHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIERlc2lnblRva2VuIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICB2YWx1ZTogc3RyaW5nO1xyXG4gIGNhdGVnb3J5OiAnY29sb3InIHwgJ2dyYWRpZW50JyB8ICdzcGFjaW5nJyB8ICd0eXBvZ3JhcGh5JyB8ICdyYWRpdXMnIHwgJ3NoYWRvdyc7XHJcbiAgcGxhdGZvcm1zOiAoJ3dlYicgfCAnaW9zJyB8ICdhbmRyb2lkJyB8ICdodWF3ZWknKVtdO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIFRoZW1lQ29uZmlnIHtcclxuICBpZDogc3RyaW5nO1xyXG4gIG5hbWU6IHN0cmluZztcclxuICBkaXNwbGF5TmFtZTogc3RyaW5nO1xyXG4gIHByaW1hcnlDb2xvcjogc3RyaW5nO1xyXG4gIHNlY29uZGFyeUNvbG9yOiBzdHJpbmc7XHJcbiAgZ3JhZGllbnRzOiB7XHJcbiAgICBwcmltYXJ5OiBzdHJpbmc7XHJcbiAgICBzZWNvbmRhcnk6IHN0cmluZztcclxuICAgIGFjY2VudDogc3RyaW5nO1xyXG4gIH07XHJcbiAgY29sb3JzOiB7XHJcbiAgICBiYWNrZ3JvdW5kOiBzdHJpbmc7XHJcbiAgICBzdXJmYWNlOiBzdHJpbmc7XHJcbiAgICB0ZXh0OiB7XHJcbiAgICAgIHByaW1hcnk6IHN0cmluZztcclxuICAgICAgc2Vjb25kYXJ5OiBzdHJpbmc7XHJcbiAgICAgIGFjY2VudDogc3RyaW5nO1xyXG4gICAgICBpbnZlcnNlOiBzdHJpbmc7XHJcbiAgICB9O1xyXG4gICAgYm9yZGVyOiBzdHJpbmc7XHJcbiAgICBzaGFkb3c6IHN0cmluZztcclxuICAgIG92ZXJsYXk6IHN0cmluZztcclxuXHJcbiAgICAvLyDQodC/0LXRhtC40LDQu9GM0L3Ri9C1INGG0LLQtdGC0LAg0LTQu9GPINC/0YDQuNC70L7QttC10L3QuNGPINC30L3QsNC60L7QvNGB0YLQslxyXG4gICAgZGF0aW5nOiB7XHJcbiAgICAgIGxpa2U6IHN0cmluZztcclxuICAgICAgcGFzczogc3RyaW5nO1xyXG4gICAgICBzdXBlckxpa2U6IHN0cmluZztcclxuICAgICAgbWF0Y2g6IHN0cmluZztcclxuICAgICAgb25saW5lOiBzdHJpbmc7XHJcbiAgICAgIHZlcmlmaWVkOiBzdHJpbmc7XHJcbiAgICAgIHByZW1pdW06IHN0cmluZztcclxuICAgIH07XHJcblxyXG4gICAgLy8g0KHRgtCw0YLRg9GB0YtcclxuICAgIHN1Y2Nlc3M6IHN0cmluZztcclxuICAgIHdhcm5pbmc6IHN0cmluZztcclxuICAgIGVycm9yOiBzdHJpbmc7XHJcbiAgICBpbmZvOiBzdHJpbmc7XHJcbiAgfTtcclxuICBzcGFjaW5nOiB7XHJcbiAgICB4czogbnVtYmVyO1xyXG4gICAgc206IG51bWJlcjtcclxuICAgIG1kOiBudW1iZXI7XHJcbiAgICBsZzogbnVtYmVyO1xyXG4gICAgeGw6IG51bWJlcjtcclxuICB9O1xyXG4gIGJvcmRlclJhZGl1czoge1xyXG4gICAgc206IG51bWJlcjtcclxuICAgIG1kOiBudW1iZXI7XHJcbiAgICBsZzogbnVtYmVyO1xyXG4gICAgZnVsbDogbnVtYmVyO1xyXG4gIH07XHJcbiAgdHlwb2dyYXBoeToge1xyXG4gICAgZm9udEZhbWlseTogc3RyaW5nO1xyXG4gICAgZm9udFNpemVzOiB7XHJcbiAgICAgIHhzOiBudW1iZXI7XHJcbiAgICAgIHNtOiBudW1iZXI7XHJcbiAgICAgIG1kOiBudW1iZXI7XHJcbiAgICAgIGxnOiBudW1iZXI7XHJcbiAgICAgIHhsOiBudW1iZXI7XHJcbiAgICAgIHh4bDogbnVtYmVyO1xyXG4gICAgfTtcclxuICAgIGZvbnRXZWlnaHRzOiB7XHJcbiAgICAgIG5vcm1hbDogbnVtYmVyO1xyXG4gICAgICBtZWRpdW06IG51bWJlcjtcclxuICAgICAgc2VtaWJvbGQ6IG51bWJlcjtcclxuICAgICAgYm9sZDogbnVtYmVyO1xyXG4gICAgfTtcclxuICB9O1xyXG4gIHNoYWRvd3M6IHtcclxuICAgIHNtOiBzdHJpbmc7XHJcbiAgICBtZDogc3RyaW5nO1xyXG4gICAgbGc6IHN0cmluZztcclxuICB9O1xyXG59XHJcblxyXG4vLyDQk9C70L7QsdCw0LvRjNC90YvQtSDRgtC+0LrQtdC90Ysg0LTQuNC30LDQudC90LBcclxuZXhwb3J0IGNvbnN0IERFU0lHTl9UT0tFTlM6IERlc2lnblRva2VuW10gPSBbXHJcbiAgLy8g0KbQstC10YLQsFxyXG4gIHtcclxuICAgIGlkOiAnY29sb3ItbGlrZXMtbG92ZS1wcmltYXJ5JyxcclxuICAgIG5hbWU6ICdMaWtlcyBMb3ZlIFByaW1hcnknLFxyXG4gICAgdmFsdWU6ICcjZmY2YjlkJyxcclxuICAgIGNhdGVnb3J5OiAnY29sb3InLFxyXG4gICAgcGxhdGZvcm1zOiBbJ3dlYicsICdpb3MnLCAnYW5kcm9pZCcsICdodWF3ZWknXVxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdjb2xvci1saWtlcy1sb3ZlLXNlY29uZGFyeScsIFxyXG4gICAgbmFtZTogJ0xpa2VzIExvdmUgU2Vjb25kYXJ5JyxcclxuICAgIHZhbHVlOiAnIzRlY2RjNCcsXHJcbiAgICBjYXRlZ29yeTogJ2NvbG9yJyxcclxuICAgIHBsYXRmb3JtczogWyd3ZWInLCAnaW9zJywgJ2FuZHJvaWQnLCAnaHVhd2VpJ11cclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAnY29sb3Itcm9tYW50aWMtcHJpbWFyeScsXHJcbiAgICBuYW1lOiAnUm9tYW50aWMgUHJpbWFyeScsXHJcbiAgICB2YWx1ZTogJyNmZjZiOWQnLFxyXG4gICAgY2F0ZWdvcnk6ICdjb2xvcicsXHJcbiAgICBwbGF0Zm9ybXM6IFsnd2ViJywgJ2lvcycsICdhbmRyb2lkJywgJ2h1YXdlaSddXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2NvbG9yLXJvbWFudGljLXNlY29uZGFyeScsXHJcbiAgICBuYW1lOiAnUm9tYW50aWMgU2Vjb25kYXJ5JywgXHJcbiAgICB2YWx1ZTogJyNmZjhlNTMnLFxyXG4gICAgY2F0ZWdvcnk6ICdjb2xvcicsXHJcbiAgICBwbGF0Zm9ybXM6IFsnd2ViJywgJ2lvcycsICdhbmRyb2lkJywgJ2h1YXdlaSddXHJcbiAgfSxcclxuICBcclxuICAvLyDQk9GA0LDQtNC40LXQvdGC0YtcclxuICB7XHJcbiAgICBpZDogJ2dyYWRpZW50LWxpa2VzLWxvdmUtcHJpbWFyeScsXHJcbiAgICBuYW1lOiAnTGlrZXMgTG92ZSBQcmltYXJ5IEdyYWRpZW50JyxcclxuICAgIHZhbHVlOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmI5ZCAwJSwgIzRlY2RjNCAxMDAlKScsXHJcbiAgICBjYXRlZ29yeTogJ2dyYWRpZW50JywgXHJcbiAgICBwbGF0Zm9ybXM6IFsnd2ViJywgJ2lvcycsICdhbmRyb2lkJywgJ2h1YXdlaSddXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2dyYWRpZW50LXJvbWFudGljLXByaW1hcnknLFxyXG4gICAgbmFtZTogJ1JvbWFudGljIFByaW1hcnkgR3JhZGllbnQnLFxyXG4gICAgdmFsdWU6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY2YjlkIDAlLCAjZmY4ZTUzIDEwMCUpJyxcclxuICAgIGNhdGVnb3J5OiAnZ3JhZGllbnQnLFxyXG4gICAgcGxhdGZvcm1zOiBbJ3dlYicsICdpb3MnLCAnYW5kcm9pZCcsICdodWF3ZWknXVxyXG4gIH0sXHJcblxyXG4gIC8vINCe0YLRgdGC0YPQv9GLICjQsiDQv9C40LrRgdC10LvRj9GFKVxyXG4gIHtcclxuICAgIGlkOiAnc3BhY2luZy14cycsXHJcbiAgICBuYW1lOiAnRXh0cmEgU21hbGwgU3BhY2luZycsXHJcbiAgICB2YWx1ZTogJzQnLFxyXG4gICAgY2F0ZWdvcnk6ICdzcGFjaW5nJyxcclxuICAgIHBsYXRmb3JtczogWyd3ZWInLCAnaW9zJywgJ2FuZHJvaWQnLCAnaHVhd2VpJ11cclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAnc3BhY2luZy1zbScsXHJcbiAgICBuYW1lOiAnU21hbGwgU3BhY2luZycsIFxyXG4gICAgdmFsdWU6ICc4JyxcclxuICAgIGNhdGVnb3J5OiAnc3BhY2luZycsXHJcbiAgICBwbGF0Zm9ybXM6IFsnd2ViJywgJ2lvcycsICdhbmRyb2lkJywgJ2h1YXdlaSddXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ3NwYWNpbmctbWQnLFxyXG4gICAgbmFtZTogJ01lZGl1bSBTcGFjaW5nJyxcclxuICAgIHZhbHVlOiAnMTYnLFxyXG4gICAgY2F0ZWdvcnk6ICdzcGFjaW5nJyxcclxuICAgIHBsYXRmb3JtczogWyd3ZWInLCAnaW9zJywgJ2FuZHJvaWQnLCAnaHVhd2VpJ11cclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAnc3BhY2luZy1sZycsXHJcbiAgICBuYW1lOiAnTGFyZ2UgU3BhY2luZycsXHJcbiAgICB2YWx1ZTogJzI0JyxcclxuICAgIGNhdGVnb3J5OiAnc3BhY2luZycsXHJcbiAgICBwbGF0Zm9ybXM6IFsnd2ViJywgJ2lvcycsICdhbmRyb2lkJywgJ2h1YXdlaSddXHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ3NwYWNpbmcteGwnLFxyXG4gICAgbmFtZTogJ0V4dHJhIExhcmdlIFNwYWNpbmcnLFxyXG4gICAgdmFsdWU6ICczMicsXHJcbiAgICBjYXRlZ29yeTogJ3NwYWNpbmcnLFxyXG4gICAgcGxhdGZvcm1zOiBbJ3dlYicsICdpb3MnLCAnYW5kcm9pZCcsICdodWF3ZWknXVxyXG4gIH0sXHJcblxyXG4gIC8vINCg0LDQtNC40YPRgdGLINGB0LrRgNGD0LPQu9C10L3QuNGPXHJcbiAge1xyXG4gICAgaWQ6ICdyYWRpdXMtc20nLFxyXG4gICAgbmFtZTogJ1NtYWxsIEJvcmRlciBSYWRpdXMnLFxyXG4gICAgdmFsdWU6ICc4JyxcclxuICAgIGNhdGVnb3J5OiAncmFkaXVzJyxcclxuICAgIHBsYXRmb3JtczogWyd3ZWInLCAnaW9zJywgJ2FuZHJvaWQnLCAnaHVhd2VpJ11cclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAncmFkaXVzLW1kJyxcclxuICAgIG5hbWU6ICdNZWRpdW0gQm9yZGVyIFJhZGl1cycsXHJcbiAgICB2YWx1ZTogJzEyJyxcclxuICAgIGNhdGVnb3J5OiAncmFkaXVzJyxcclxuICAgIHBsYXRmb3JtczogWyd3ZWInLCAnaW9zJywgJ2FuZHJvaWQnLCAnaHVhd2VpJ11cclxuICB9LFxyXG4gIHtcclxuICAgIGlkOiAncmFkaXVzLWxnJyxcclxuICAgIG5hbWU6ICdMYXJnZSBCb3JkZXIgUmFkaXVzJyxcclxuICAgIHZhbHVlOiAnMTYnLFxyXG4gICAgY2F0ZWdvcnk6ICdyYWRpdXMnLFxyXG4gICAgcGxhdGZvcm1zOiBbJ3dlYicsICdpb3MnLCAnYW5kcm9pZCcsICdodWF3ZWknXVxyXG4gIH1cclxuXTtcclxuXHJcbi8vINCa0L7QvdGE0LjQs9GD0YDQsNGG0LjQuCDRgtC10Lwg0LTQu9GPINCy0YHQtdGFINC/0LvQsNGC0YTQvtGA0LxcclxuZXhwb3J0IGNvbnN0IENST1NTX1BMQVRGT1JNX1RIRU1FUzogVGhlbWVDb25maWdbXSA9IFtcclxuICB7XHJcbiAgICBpZDogJ2xpa2VzLWxvdmUnLFxyXG4gICAgbmFtZTogJ2xpa2VzLWxvdmUnLFxyXG4gICAgZGlzcGxheU5hbWU6ICdMaWtlcyAmIExvdmUnLFxyXG4gICAgcHJpbWFyeUNvbG9yOiAnI2ZmNmI5ZCcsXHJcbiAgICBzZWNvbmRhcnlDb2xvcjogJyM0ZWNkYzQnLFxyXG4gICAgZ3JhZGllbnRzOiB7XHJcbiAgICAgIHByaW1hcnk6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY2YjlkIDAlLCAjNGVjZGM0IDEwMCUpJyxcclxuICAgICAgc2Vjb25kYXJ5OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRlY2RjNCAwJSwgIzQ1YjdhYSAxMDAlKScsXHJcbiAgICAgIGFjY2VudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjZiOWQgMCUsICNmZjhmYWIgMTAwJSknXHJcbiAgICB9LFxyXG4gICAgY29sb3JzOiB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICcjZmZmZmZmJyxcclxuICAgICAgc3VyZmFjZTogJyNmOGY5ZmEnLFxyXG4gICAgICB0ZXh0OiB7XHJcbiAgICAgICAgcHJpbWFyeTogJyMyZDM3NDgnLFxyXG4gICAgICAgIHNlY29uZGFyeTogJyM3MTgwOTYnLFxyXG4gICAgICAgIGFjY2VudDogJyNmZjZiOWQnLFxyXG4gICAgICAgIGludmVyc2U6ICcjZmZmZmZmJ1xyXG4gICAgICB9LFxyXG4gICAgICBib3JkZXI6ICcjZTJlOGYwJyxcclxuICAgICAgc2hhZG93OiAncmdiYSgwLCAwLCAwLCAwLjEpJyxcclxuICAgICAgb3ZlcmxheTogJ3JnYmEoMCwgMCwgMCwgMC41KScsXHJcbiAgICAgIGRhdGluZzoge1xyXG4gICAgICAgIGxpa2U6ICcjNGNhZjUwJyxcclxuICAgICAgICBwYXNzOiAnI2Y0NDMzNicsXHJcbiAgICAgICAgc3VwZXJMaWtlOiAnIzIxOTZmMycsXHJcbiAgICAgICAgbWF0Y2g6ICcjZmY5ODAwJyxcclxuICAgICAgICBvbmxpbmU6ICcjNGNhZjUwJyxcclxuICAgICAgICB2ZXJpZmllZDogJyMyMTk2ZjMnLFxyXG4gICAgICAgIHByZW1pdW06ICcjZmZkNzAwJ1xyXG4gICAgICB9LFxyXG4gICAgICBzdWNjZXNzOiAnIzRjYWY1MCcsXHJcbiAgICAgIHdhcm5pbmc6ICcjZmY5ODAwJyxcclxuICAgICAgZXJyb3I6ICcjZjQ0MzM2JyxcclxuICAgICAgaW5mbzogJyMyMTk2ZjMnXHJcbiAgICB9LFxyXG4gICAgc3BhY2luZzoge1xyXG4gICAgICB4czogNCxcclxuICAgICAgc206IDgsXHJcbiAgICAgIG1kOiAxNixcclxuICAgICAgbGc6IDI0LFxyXG4gICAgICB4bDogMzJcclxuICAgIH0sXHJcbiAgICBib3JkZXJSYWRpdXM6IHtcclxuICAgICAgc206IDgsXHJcbiAgICAgIG1kOiAxMixcclxuICAgICAgbGc6IDE2LFxyXG4gICAgICBmdWxsOiA5OTk5XHJcbiAgICB9LFxyXG4gICAgdHlwb2dyYXBoeToge1xyXG4gICAgICBmb250RmFtaWx5OiAnXCJQbGF5ZmFpciBEaXNwbGF5XCIsIFwiR2VvcmdpYVwiLCBcIlRpbWVzIE5ldyBSb21hblwiLCBzZXJpZicsXHJcbiAgICAgIGZvbnRTaXplczoge1xyXG4gICAgICAgIHhzOiAxNiwgICAgLy8g0KPQstC10LvQuNGH0LXQvdC+INGBIDEyINC00LvRjyDQu9GD0YfRiNC10Lkg0YfQuNGC0LDQtdC80L7RgdGC0LhcclxuICAgICAgICBzbTogMTgsICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAxNCDQtNC70Y8g0LrQvtC80YTQvtGA0YLQsCDQs9C70LDQt1xyXG4gICAgICAgIG1kOiAyMCwgICAgLy8g0KPQstC10LvQuNGH0LXQvdC+INGBIDE2INC00LvRjyDQvtGB0L3QvtCy0L3QvtCz0L4g0YLQtdC60YHRgtCwXHJcbiAgICAgICAgbGc6IDI0LCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMTgg0LTQu9GPINC30LDQs9C+0LvQvtCy0LrQvtCyXHJcbiAgICAgICAgeGw6IDMyLCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMjQg0LTQu9GPINC60YDRg9C/0L3Ri9GFINC30LDQs9C+0LvQvtCy0LrQvtCyXHJcbiAgICAgICAgeHhsOiA0MiAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMzIg0LTQu9GPIGhlcm8t0LfQsNCz0L7Qu9C+0LLQutC+0LJcclxuICAgICAgfSxcclxuICAgICAgZm9udFdlaWdodHM6IHtcclxuICAgICAgICBub3JtYWw6IDQwMCxcclxuICAgICAgICBtZWRpdW06IDUwMCxcclxuICAgICAgICBzZW1pYm9sZDogNjAwLFxyXG4gICAgICAgIGJvbGQ6IDcwMFxyXG4gICAgICB9LFxyXG4gICAgICBsaW5lSGVpZ2h0czoge1xyXG4gICAgICAgIHRpZ2h0OiAxLjIsXHJcbiAgICAgICAgbm9ybWFsOiAxLjUsXHJcbiAgICAgICAgcmVsYXhlZDogMS43LFxyXG4gICAgICAgIGxvb3NlOiAyLjBcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIHNoYWRvd3M6IHtcclxuICAgICAgc206ICcwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEyKSwgMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4yNCknLFxyXG4gICAgICBtZDogJzAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMDcpLCAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcclxuICAgICAgbGc6ICcwIDEwcHggMjVweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMDUpJ1xyXG4gICAgfVxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdyb21hbnRpYycsXHJcbiAgICBuYW1lOiAncm9tYW50aWMnLFxyXG4gICAgZGlzcGxheU5hbWU6ICfQoNC+0LzQsNC90YLQuNGH0LXRgdC60LDRjycsXHJcbiAgICBwcmltYXJ5Q29sb3I6ICcjZmY2YjlkJyxcclxuICAgIHNlY29uZGFyeUNvbG9yOiAnI2ZmOGU1MycsXHJcbiAgICBncmFkaWVudHM6IHtcclxuICAgICAgcHJpbWFyeTogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZjZiOWQgMCUsICNmZjhlNTMgMTAwJSknLFxyXG4gICAgICBzZWNvbmRhcnk6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmY4ZTUzIDAlLCAjZmZiMzQ3IDEwMCUpJyxcclxuICAgICAgYWNjZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmNmI5ZCAwJSwgI2ZmN2ZhYiAxMDAlKSdcclxuICAgIH0sXHJcbiAgICBjb2xvcnM6IHtcclxuICAgICAgYmFja2dyb3VuZDogJyNmZmZmZmYnLFxyXG4gICAgICBzdXJmYWNlOiAnI2ZlZjdmNycsXHJcbiAgICAgIHRleHQ6IHtcclxuICAgICAgICBwcmltYXJ5OiAnIzJkMzc0OCcsXHJcbiAgICAgICAgc2Vjb25kYXJ5OiAnIzcxODA5NicsXHJcbiAgICAgICAgYWNjZW50OiAnI2ZmNmI5ZCcsXHJcbiAgICAgICAgaW52ZXJzZTogJyNmZmZmZmYnXHJcbiAgICAgIH0sXHJcbiAgICAgIGJvcmRlcjogJyNmN2U4ZTgnLFxyXG4gICAgICBzaGFkb3c6ICdyZ2JhKDI1NSwgMTA3LCAxNTcsIDAuMSknLFxyXG4gICAgICBvdmVybGF5OiAncmdiYSgwLCAwLCAwLCAwLjUpJyxcclxuICAgICAgZGF0aW5nOiB7XHJcbiAgICAgICAgbGlrZTogJyM0Y2FmNTAnLFxyXG4gICAgICAgIHBhc3M6ICcjZjQ0MzM2JyxcclxuICAgICAgICBzdXBlckxpa2U6ICcjMjE5NmYzJyxcclxuICAgICAgICBtYXRjaDogJyNmZjk4MDAnLFxyXG4gICAgICAgIG9ubGluZTogJyM0Y2FmNTAnLFxyXG4gICAgICAgIHZlcmlmaWVkOiAnIzIxOTZmMycsXHJcbiAgICAgICAgcHJlbWl1bTogJyNmZmQ3MDAnXHJcbiAgICAgIH0sXHJcbiAgICAgIHN1Y2Nlc3M6ICcjNGNhZjUwJyxcclxuICAgICAgd2FybmluZzogJyNmZjk4MDAnLFxyXG4gICAgICBlcnJvcjogJyNmNDQzMzYnLFxyXG4gICAgICBpbmZvOiAnIzIxOTZmMydcclxuICAgIH0sXHJcbiAgICBzcGFjaW5nOiB7XHJcbiAgICAgIHhzOiA0LFxyXG4gICAgICBzbTogOCxcclxuICAgICAgbWQ6IDE2LFxyXG4gICAgICBsZzogMjQsXHJcbiAgICAgIHhsOiAzMlxyXG4gICAgfSxcclxuICAgIGJvcmRlclJhZGl1czoge1xyXG4gICAgICBzbTogOCxcclxuICAgICAgbWQ6IDEyLFxyXG4gICAgICBsZzogMTYsXHJcbiAgICAgIGZ1bGw6IDk5OTlcclxuICAgIH0sXHJcbiAgICB0eXBvZ3JhcGh5OiB7XHJcbiAgICAgIGZvbnRGYW1pbHk6ICdcIlBsYXlmYWlyIERpc3BsYXlcIiwgXCJHZW9yZ2lhXCIsIFwiVGltZXMgTmV3IFJvbWFuXCIsIHNlcmlmJyxcclxuICAgICAgZm9udFNpemVzOiB7XHJcbiAgICAgICAgeHM6IDE2LCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMTIg0LTQu9GPINC70YPRh9GI0LXQuSDRh9C40YLQsNC10LzQvtGB0YLQuFxyXG4gICAgICAgIHNtOiAxOCwgICAgLy8g0KPQstC10LvQuNGH0LXQvdC+INGBIDE0INC00LvRjyDQutC+0LzRhNC+0YDRgtCwINCz0LvQsNC3XHJcbiAgICAgICAgbWQ6IDIwLCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMTYg0LTQu9GPINC+0YHQvdC+0LLQvdC+0LPQviDRgtC10LrRgdGC0LBcclxuICAgICAgICBsZzogMjQsICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAxOCDQtNC70Y8g0LfQsNCz0L7Qu9C+0LLQutC+0LJcclxuICAgICAgICB4bDogMzIsICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAyNCDQtNC70Y8g0LrRgNGD0L/QvdGL0YUg0LfQsNCz0L7Qu9C+0LLQutC+0LJcclxuICAgICAgICB4eGw6IDQyICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAzMiDQtNC70Y8gaGVyby3Qt9Cw0LPQvtC70L7QstC60L7QslxyXG4gICAgICB9LFxyXG4gICAgICBmb250V2VpZ2h0czoge1xyXG4gICAgICAgIG5vcm1hbDogNDAwLFxyXG4gICAgICAgIG1lZGl1bTogNTAwLFxyXG4gICAgICAgIHNlbWlib2xkOiA2MDAsXHJcbiAgICAgICAgYm9sZDogNzAwXHJcbiAgICAgIH0sXHJcbiAgICAgIGxpbmVIZWlnaHRzOiB7XHJcbiAgICAgICAgdGlnaHQ6IDEuMixcclxuICAgICAgICBub3JtYWw6IDEuNSxcclxuICAgICAgICByZWxheGVkOiAxLjcsXHJcbiAgICAgICAgbG9vc2U6IDIuMFxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgc2hhZG93czoge1xyXG4gICAgICBzbTogJzAgMXB4IDNweCByZ2JhKDI1NSwgMTA3LCAxNTcsIDAuMTIpLCAwIDFweCAycHggcmdiYSgyNTUsIDEwNywgMTU3LCAwLjI0KScsXHJcbiAgICAgIG1kOiAnMCA0cHggNnB4IHJnYmEoMjU1LCAxMDcsIDE1NywgMC4wNyksIDAgMXB4IDNweCByZ2JhKDI1NSwgMTA3LCAxNTcsIDAuMSknLFxyXG4gICAgICBsZzogJzAgMTBweCAyNXB4IHJnYmEoMjU1LCAxMDcsIDE1NywgMC4xKSwgMCA0cHggNnB4IHJnYmEoMjU1LCAxMDcsIDE1NywgMC4wNSknXHJcbiAgICB9XHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2VsZWdhbnQnLFxyXG4gICAgbmFtZTogJ2VsZWdhbnQnLFxyXG4gICAgZGlzcGxheU5hbWU6ICfQrdC70LXQs9Cw0L3RgtC90LDRjycsXHJcbiAgICBwcmltYXJ5Q29sb3I6ICcjOWY3YWVhJyxcclxuICAgIHNlY29uZGFyeUNvbG9yOiAnIzY2N2VlYScsXHJcbiAgICBncmFkaWVudHM6IHtcclxuICAgICAgcHJpbWFyeTogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM5ZjdhZWEgMCUsICM2NjdlZWEgMTAwJSknLFxyXG4gICAgICBzZWNvbmRhcnk6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpJyxcclxuICAgICAgYWNjZW50OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzlmN2FlYSAwJSwgI2I3OTRmNiAxMDAlKSdcclxuICAgIH0sXHJcbiAgICBjb2xvcnM6IHtcclxuICAgICAgYmFja2dyb3VuZDogJyNmZmZmZmYnLFxyXG4gICAgICBzdXJmYWNlOiAnI2ZhZjVmZicsXHJcbiAgICAgIHRleHQ6IHtcclxuICAgICAgICBwcmltYXJ5OiAnIzJkMzc0OCcsXHJcbiAgICAgICAgc2Vjb25kYXJ5OiAnIzcxODA5NicsXHJcbiAgICAgICAgYWNjZW50OiAnIzlmN2FlYScsXHJcbiAgICAgICAgaW52ZXJzZTogJyNmZmZmZmYnXHJcbiAgICAgIH0sXHJcbiAgICAgIGJvcmRlcjogJyNlOWQ4ZmQnLFxyXG4gICAgICBzaGFkb3c6ICdyZ2JhKDE1OSwgMTIyLCAyMzQsIDAuMSknLFxyXG4gICAgICBvdmVybGF5OiAncmdiYSgwLCAwLCAwLCAwLjUpJyxcclxuICAgICAgZGF0aW5nOiB7XHJcbiAgICAgICAgbGlrZTogJyM0Y2FmNTAnLFxyXG4gICAgICAgIHBhc3M6ICcjZjQ0MzM2JyxcclxuICAgICAgICBzdXBlckxpa2U6ICcjMjE5NmYzJyxcclxuICAgICAgICBtYXRjaDogJyNmZjk4MDAnLFxyXG4gICAgICAgIG9ubGluZTogJyM0Y2FmNTAnLFxyXG4gICAgICAgIHZlcmlmaWVkOiAnIzIxOTZmMycsXHJcbiAgICAgICAgcHJlbWl1bTogJyNmZmQ3MDAnXHJcbiAgICAgIH0sXHJcbiAgICAgIHN1Y2Nlc3M6ICcjNGNhZjUwJyxcclxuICAgICAgd2FybmluZzogJyNmZjk4MDAnLFxyXG4gICAgICBlcnJvcjogJyNmNDQzMzYnLFxyXG4gICAgICBpbmZvOiAnIzIxOTZmMydcclxuICAgIH0sXHJcbiAgICBzcGFjaW5nOiB7XHJcbiAgICAgIHhzOiA0LFxyXG4gICAgICBzbTogOCxcclxuICAgICAgbWQ6IDE2LFxyXG4gICAgICBsZzogMjQsXHJcbiAgICAgIHhsOiAzMlxyXG4gICAgfSxcclxuICAgIGJvcmRlclJhZGl1czoge1xyXG4gICAgICBzbTogOCxcclxuICAgICAgbWQ6IDEyLFxyXG4gICAgICBsZzogMTYsXHJcbiAgICAgIGZ1bGw6IDk5OTlcclxuICAgIH0sXHJcbiAgICB0eXBvZ3JhcGh5OiB7XHJcbiAgICAgIGZvbnRGYW1pbHk6ICdJbnRlciwgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCBzYW5zLXNlcmlmJyxcclxuICAgICAgZm9udFNpemVzOiB7XHJcbiAgICAgICAgeHM6IDEyLFxyXG4gICAgICAgIHNtOiAxNCxcclxuICAgICAgICBtZDogMTYsXHJcbiAgICAgICAgbGc6IDE4LFxyXG4gICAgICAgIHhsOiAyNCxcclxuICAgICAgICB4eGw6IDMyXHJcbiAgICAgIH0sXHJcbiAgICAgIGZvbnRXZWlnaHRzOiB7XHJcbiAgICAgICAgbm9ybWFsOiA0MDAsXHJcbiAgICAgICAgbWVkaXVtOiA1MDAsXHJcbiAgICAgICAgc2VtaWJvbGQ6IDYwMCxcclxuICAgICAgICBib2xkOiA3MDBcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIHNoYWRvd3M6IHtcclxuICAgICAgc206ICcwIDFweCAzcHggcmdiYSgxNTksIDEyMiwgMjM0LCAwLjEyKSwgMCAxcHggMnB4IHJnYmEoMTU5LCAxMjIsIDIzNCwgMC4yNCknLFxyXG4gICAgICBtZDogJzAgNHB4IDZweCByZ2JhKDE1OSwgMTIyLCAyMzQsIDAuMDcpLCAwIDFweCAzcHggcmdiYSgxNTksIDEyMiwgMjM0LCAwLjEpJyxcclxuICAgICAgbGc6ICcwIDEwcHggMjVweCByZ2JhKDE1OSwgMTIyLCAyMzQsIDAuMSksIDAgNHB4IDZweCByZ2JhKDE1OSwgMTIyLCAyMzQsIDAuMDUpJ1xyXG4gICAgfVxyXG4gIH0sXHJcbiAge1xyXG4gICAgaWQ6ICdtb2Rlcm4nLFxyXG4gICAgbmFtZTogJ21vZGVybicsXHJcbiAgICBkaXNwbGF5TmFtZTogJ9Ch0L7QstGA0LXQvNC10L3QvdCw0Y8nLFxyXG4gICAgcHJpbWFyeUNvbG9yOiAnIzM4YjJhYycsXHJcbiAgICBzZWNvbmRhcnlDb2xvcjogJyM0ZmQxYzcnLFxyXG4gICAgZ3JhZGllbnRzOiB7XHJcbiAgICAgIHByaW1hcnk6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMzhiMmFjIDAlLCAjNGZkMWM3IDEwMCUpJyxcclxuICAgICAgc2Vjb25kYXJ5OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRmZDFjNyAwJSwgIzgxZTZkOSAxMDAlKScsXHJcbiAgICAgIGFjY2VudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzOGIyYWMgMCUsICMzMTk3OTUgMTAwJSknXHJcbiAgICB9LFxyXG4gICAgY29sb3JzOiB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICcjZmZmZmZmJyxcclxuICAgICAgc3VyZmFjZTogJyNmMGZmZmUnLFxyXG4gICAgICB0ZXh0OiB7XHJcbiAgICAgICAgcHJpbWFyeTogJyMyZDM3NDgnLFxyXG4gICAgICAgIHNlY29uZGFyeTogJyM3MTgwOTYnLFxyXG4gICAgICAgIGFjY2VudDogJyMzOGIyYWMnLFxyXG4gICAgICAgIGludmVyc2U6ICcjZmZmZmZmJ1xyXG4gICAgICB9LFxyXG4gICAgICBib3JkZXI6ICcjYjJmNWVhJyxcclxuICAgICAgc2hhZG93OiAncmdiYSg1NiwgMTc4LCAxNzIsIDAuMSknLFxyXG4gICAgICBvdmVybGF5OiAncmdiYSgwLCAwLCAwLCAwLjUpJyxcclxuICAgICAgZGF0aW5nOiB7XHJcbiAgICAgICAgbGlrZTogJyM0Y2FmNTAnLFxyXG4gICAgICAgIHBhc3M6ICcjZjQ0MzM2JyxcclxuICAgICAgICBzdXBlckxpa2U6ICcjMjE5NmYzJyxcclxuICAgICAgICBtYXRjaDogJyNmZjk4MDAnLFxyXG4gICAgICAgIG9ubGluZTogJyM0Y2FmNTAnLFxyXG4gICAgICAgIHZlcmlmaWVkOiAnIzIxOTZmMycsXHJcbiAgICAgICAgcHJlbWl1bTogJyNmZmQ3MDAnXHJcbiAgICAgIH0sXHJcbiAgICAgIHN1Y2Nlc3M6ICcjNGNhZjUwJyxcclxuICAgICAgd2FybmluZzogJyNmZjk4MDAnLFxyXG4gICAgICBlcnJvcjogJyNmNDQzMzYnLFxyXG4gICAgICBpbmZvOiAnIzIxOTZmMydcclxuICAgIH0sXHJcbiAgICBzcGFjaW5nOiB7XHJcbiAgICAgIHhzOiA0LFxyXG4gICAgICBzbTogOCxcclxuICAgICAgbWQ6IDE2LFxyXG4gICAgICBsZzogMjQsXHJcbiAgICAgIHhsOiAzMlxyXG4gICAgfSxcclxuICAgIGJvcmRlclJhZGl1czoge1xyXG4gICAgICBzbTogOCxcclxuICAgICAgbWQ6IDEyLFxyXG4gICAgICBsZzogMTYsXHJcbiAgICAgIGZ1bGw6IDk5OTlcclxuICAgIH0sXHJcbiAgICB0eXBvZ3JhcGh5OiB7XHJcbiAgICAgIGZvbnRGYW1pbHk6ICdcIlBsYXlmYWlyIERpc3BsYXlcIiwgXCJHZW9yZ2lhXCIsIFwiVGltZXMgTmV3IFJvbWFuXCIsIHNlcmlmJyxcclxuICAgICAgZm9udFNpemVzOiB7XHJcbiAgICAgICAgeHM6IDE2LCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMTIg0LTQu9GPINC70YPRh9GI0LXQuSDRh9C40YLQsNC10LzQvtGB0YLQuFxyXG4gICAgICAgIHNtOiAxOCwgICAgLy8g0KPQstC10LvQuNGH0LXQvdC+INGBIDE0INC00LvRjyDQutC+0LzRhNC+0YDRgtCwINCz0LvQsNC3XHJcbiAgICAgICAgbWQ6IDIwLCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMTYg0LTQu9GPINC+0YHQvdC+0LLQvdC+0LPQviDRgtC10LrRgdGC0LBcclxuICAgICAgICBsZzogMjQsICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAxOCDQtNC70Y8g0LfQsNCz0L7Qu9C+0LLQutC+0LJcclxuICAgICAgICB4bDogMzIsICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAyNCDQtNC70Y8g0LrRgNGD0L/QvdGL0YUg0LfQsNCz0L7Qu9C+0LLQutC+0LJcclxuICAgICAgICB4eGw6IDQyICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAzMiDQtNC70Y8gaGVyby3Qt9Cw0LPQvtC70L7QstC60L7QslxyXG4gICAgICB9LFxyXG4gICAgICBmb250V2VpZ2h0czoge1xyXG4gICAgICAgIG5vcm1hbDogNDAwLFxyXG4gICAgICAgIG1lZGl1bTogNTAwLFxyXG4gICAgICAgIHNlbWlib2xkOiA2MDAsXHJcbiAgICAgICAgYm9sZDogNzAwXHJcbiAgICAgIH0sXHJcbiAgICAgIGxpbmVIZWlnaHRzOiB7XHJcbiAgICAgICAgdGlnaHQ6IDEuMixcclxuICAgICAgICBub3JtYWw6IDEuNSxcclxuICAgICAgICByZWxheGVkOiAxLjcsXHJcbiAgICAgICAgbG9vc2U6IDIuMFxyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gICAgc2hhZG93czoge1xyXG4gICAgICBzbTogJzAgMXB4IDNweCByZ2JhKDU2LCAxNzgsIDE3MiwgMC4xMiksIDAgMXB4IDJweCByZ2JhKDU2LCAxNzgsIDE3MiwgMC4yNCknLFxyXG4gICAgICBtZDogJzAgNHB4IDZweCByZ2JhKDU2LCAxNzgsIDE3MiwgMC4wNyksIDAgMXB4IDNweCByZ2JhKDU2LCAxNzgsIDE3MiwgMC4xKScsXHJcbiAgICAgIGxnOiAnMCAxMHB4IDI1cHggcmdiYSg1NiwgMTc4LCAxNzIsIDAuMSksIDAgNHB4IDZweCByZ2JhKDU2LCAxNzgsIDE3MiwgMC4wNSknXHJcbiAgICB9XHJcbiAgfSxcclxuICB7XHJcbiAgICBpZDogJ2RhcmsnLFxyXG4gICAgbmFtZTogJ2RhcmsnLFxyXG4gICAgZGlzcGxheU5hbWU6ICfQotC10LzQvdCw0Y8nLFxyXG4gICAgcHJpbWFyeUNvbG9yOiAnI2Y1NjU2NScsXHJcbiAgICBzZWNvbmRhcnlDb2xvcjogJyM0OGJiNzgnLFxyXG4gICAgZ3JhZGllbnRzOiB7XHJcbiAgICAgIHByaW1hcnk6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjU2NTY1IDAlLCAjNDhiYjc4IDEwMCUpJyxcclxuICAgICAgc2Vjb25kYXJ5OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQ4YmI3OCAwJSwgIzM4YTE2OSAxMDAlKScsXHJcbiAgICAgIGFjY2VudDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNTY1NjUgMCUsICNmYzgxODEgMTAwJSknXHJcbiAgICB9LFxyXG4gICAgY29sb3JzOiB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICcjMWEyMDJjJyxcclxuICAgICAgc3VyZmFjZTogJyMyZDM3NDgnLFxyXG4gICAgICB0ZXh0OiB7XHJcbiAgICAgICAgcHJpbWFyeTogJyNmN2ZhZmMnLFxyXG4gICAgICAgIHNlY29uZGFyeTogJyNhMGFlYzAnLFxyXG4gICAgICAgIGFjY2VudDogJyNmNTY1NjUnLFxyXG4gICAgICAgIGludmVyc2U6ICcjMmQzNzQ4J1xyXG4gICAgICB9LFxyXG4gICAgICBib3JkZXI6ICcjNGE1NTY4JyxcclxuICAgICAgc2hhZG93OiAncmdiYSgwLCAwLCAwLCAwLjMpJyxcclxuICAgICAgb3ZlcmxheTogJ3JnYmEoMCwgMCwgMCwgMC43KScsXHJcbiAgICAgIGRhdGluZzoge1xyXG4gICAgICAgIGxpa2U6ICcjNGNhZjUwJyxcclxuICAgICAgICBwYXNzOiAnI2Y0NDMzNicsXHJcbiAgICAgICAgc3VwZXJMaWtlOiAnIzIxOTZmMycsXHJcbiAgICAgICAgbWF0Y2g6ICcjZmY5ODAwJyxcclxuICAgICAgICBvbmxpbmU6ICcjNGNhZjUwJyxcclxuICAgICAgICB2ZXJpZmllZDogJyMyMTk2ZjMnLFxyXG4gICAgICAgIHByZW1pdW06ICcjZmZkNzAwJ1xyXG4gICAgICB9LFxyXG4gICAgICBzdWNjZXNzOiAnIzRjYWY1MCcsXHJcbiAgICAgIHdhcm5pbmc6ICcjZmY5ODAwJyxcclxuICAgICAgZXJyb3I6ICcjZjQ0MzM2JyxcclxuICAgICAgaW5mbzogJyMyMTk2ZjMnXHJcbiAgICB9LFxyXG4gICAgc3BhY2luZzoge1xyXG4gICAgICB4czogNCxcclxuICAgICAgc206IDgsXHJcbiAgICAgIG1kOiAxNixcclxuICAgICAgbGc6IDI0LFxyXG4gICAgICB4bDogMzJcclxuICAgIH0sXHJcbiAgICBib3JkZXJSYWRpdXM6IHtcclxuICAgICAgc206IDgsXHJcbiAgICAgIG1kOiAxMixcclxuICAgICAgbGc6IDE2LFxyXG4gICAgICBmdWxsOiA5OTk5XHJcbiAgICB9LFxyXG4gICAgdHlwb2dyYXBoeToge1xyXG4gICAgICBmb250RmFtaWx5OiAnXCJQbGF5ZmFpciBEaXNwbGF5XCIsIFwiR2VvcmdpYVwiLCBcIlRpbWVzIE5ldyBSb21hblwiLCBzZXJpZicsXHJcbiAgICAgIGZvbnRTaXplczoge1xyXG4gICAgICAgIHhzOiAxNiwgICAgLy8g0KPQstC10LvQuNGH0LXQvdC+INGBIDEyINC00LvRjyDQu9GD0YfRiNC10Lkg0YfQuNGC0LDQtdC80L7RgdGC0LhcclxuICAgICAgICBzbTogMTgsICAgIC8vINCj0LLQtdC70LjRh9C10L3QviDRgSAxNCDQtNC70Y8g0LrQvtC80YTQvtGA0YLQsCDQs9C70LDQt1xyXG4gICAgICAgIG1kOiAyMCwgICAgLy8g0KPQstC10LvQuNGH0LXQvdC+INGBIDE2INC00LvRjyDQvtGB0L3QvtCy0L3QvtCz0L4g0YLQtdC60YHRgtCwXHJcbiAgICAgICAgbGc6IDI0LCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMTgg0LTQu9GPINC30LDQs9C+0LvQvtCy0LrQvtCyXHJcbiAgICAgICAgeGw6IDMyLCAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMjQg0LTQu9GPINC60YDRg9C/0L3Ri9GFINC30LDQs9C+0LvQvtCy0LrQvtCyXHJcbiAgICAgICAgeHhsOiA0MiAgICAvLyDQo9Cy0LXQu9C40YfQtdC90L4g0YEgMzIg0LTQu9GPIGhlcm8t0LfQsNCz0L7Qu9C+0LLQutC+0LJcclxuICAgICAgfSxcclxuICAgICAgZm9udFdlaWdodHM6IHtcclxuICAgICAgICBub3JtYWw6IDQwMCxcclxuICAgICAgICBtZWRpdW06IDUwMCxcclxuICAgICAgICBzZW1pYm9sZDogNjAwLFxyXG4gICAgICAgIGJvbGQ6IDcwMFxyXG4gICAgICB9LFxyXG4gICAgICBsaW5lSGVpZ2h0czoge1xyXG4gICAgICAgIHRpZ2h0OiAxLjIsXHJcbiAgICAgICAgbm9ybWFsOiAxLjUsXHJcbiAgICAgICAgcmVsYXhlZDogMS43LFxyXG4gICAgICAgIGxvb3NlOiAyLjBcclxuICAgICAgfVxyXG4gICAgfSxcclxuICAgIHNoYWRvd3M6IHtcclxuICAgICAgc206ICcwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEyKSwgMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4yNCknLFxyXG4gICAgICBtZDogJzAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMDcpLCAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcclxuICAgICAgbGc6ICcwIDEwcHggMjVweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMDUpJ1xyXG4gICAgfVxyXG4gIH1cclxuXTtcclxuXHJcbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDQv9C+0LvRg9GH0LXQvdC40Y8g0YLQtdC80Ysg0L/QviBJRFxyXG5leHBvcnQgY29uc3QgZ2V0VGhlbWVCeUlkID0gKHRoZW1lSWQ6IHN0cmluZyk6IFRoZW1lQ29uZmlnIHwgdW5kZWZpbmVkID0+IHtcclxuICByZXR1cm4gQ1JPU1NfUExBVEZPUk1fVEhFTUVTLmZpbmQodGhlbWUgPT4gdGhlbWUuaWQgPT09IHRoZW1lSWQpO1xyXG59O1xyXG5cclxuLy8g0KTRg9C90LrRhtC40Y8g0LTQu9GPINGN0LrRgdC/0L7RgNGC0LAg0YLQvtC60LXQvdC+0LIg0LIg0YDQsNC30LvQuNGH0L3Ri9C1INGE0L7RgNC80LDRgtGLXHJcbmV4cG9ydCBjb25zdCBleHBvcnRUb2tlbnNGb3JQbGF0Zm9ybSA9IChwbGF0Zm9ybTogJ3dlYicgfCAnaW9zJyB8ICdhbmRyb2lkJyB8ICdodWF3ZWknKSA9PiB7XHJcbiAgY29uc3QgcGxhdGZvcm1Ub2tlbnMgPSBERVNJR05fVE9LRU5TLmZpbHRlcih0b2tlbiA9PiBcclxuICAgIHRva2VuLnBsYXRmb3Jtcy5pbmNsdWRlcyhwbGF0Zm9ybSlcclxuICApO1xyXG5cclxuICBzd2l0Y2ggKHBsYXRmb3JtKSB7XHJcbiAgICBjYXNlICd3ZWInOlxyXG4gICAgICByZXR1cm4gZXhwb3J0VG9DU1NWYXJpYWJsZXMocGxhdGZvcm1Ub2tlbnMpO1xyXG4gICAgY2FzZSAnaW9zJzpcclxuICAgICAgcmV0dXJuIGV4cG9ydFRvU3dpZnRVSVRva2VucyhwbGF0Zm9ybVRva2Vucyk7XHJcbiAgICBjYXNlICdhbmRyb2lkJzpcclxuICAgICAgcmV0dXJuIGV4cG9ydFRvWE1MQ29sb3JzKHBsYXRmb3JtVG9rZW5zKTtcclxuICAgIGNhc2UgJ2h1YXdlaSc6XHJcbiAgICAgIHJldHVybiBleHBvcnRUb0hhcm1vbnlPU1Rva2VucyhwbGF0Zm9ybVRva2Vucyk7XHJcbiAgICBkZWZhdWx0OlxyXG4gICAgICByZXR1cm4gcGxhdGZvcm1Ub2tlbnM7XHJcbiAgfVxyXG59O1xyXG5cclxuLy8g0K3QutGB0L/QvtGA0YIg0LIgQ1NTINC/0LXRgNC10LzQtdC90L3Ri9C1INC00LvRjyBXZWJcclxuY29uc3QgZXhwb3J0VG9DU1NWYXJpYWJsZXMgPSAodG9rZW5zOiBEZXNpZ25Ub2tlbltdKSA9PiB7XHJcbiAgY29uc3QgY3NzVmFycyA9IHRva2Vucy5tYXAodG9rZW4gPT4ge1xyXG4gICAgY29uc3QgY3NzTmFtZSA9IGAtLSR7dG9rZW4uaWQucmVwbGFjZSgvW0EtWl0vZywgbGV0dGVyID0+IGAtJHtsZXR0ZXIudG9Mb3dlckNhc2UoKX1gKX1gO1xyXG4gICAgcmV0dXJuIGAgICR7Y3NzTmFtZX06ICR7dG9rZW4udmFsdWV9O2A7XHJcbiAgfSkuam9pbignXFxuJyk7XHJcbiAgXHJcbiAgcmV0dXJuIGA6cm9vdCB7XFxuJHtjc3NWYXJzfVxcbn1gO1xyXG59O1xyXG5cclxuLy8g0K3QutGB0L/QvtGA0YIg0LTQu9GPIFN3aWZ0VUkgKGlPUylcclxuY29uc3QgZXhwb3J0VG9Td2lmdFVJVG9rZW5zID0gKHRva2VuczogRGVzaWduVG9rZW5bXSkgPT4ge1xyXG4gIGNvbnN0IHN3aWZ0VG9rZW5zID0gdG9rZW5zLm1hcCh0b2tlbiA9PiB7XHJcbiAgICBjb25zdCBzd2lmdE5hbWUgPSB0b2tlbi5pZC5yZXBsYWNlKC8tKFthLXpdKS9nLCAoXywgbGV0dGVyKSA9PiBsZXR0ZXIudG9VcHBlckNhc2UoKSk7XHJcbiAgICBpZiAodG9rZW4uY2F0ZWdvcnkgPT09ICdjb2xvcicpIHtcclxuICAgICAgcmV0dXJuIGAgIHN0YXRpYyBsZXQgJHtzd2lmdE5hbWV9ID0gQ29sb3IoaGV4OiBcIiR7dG9rZW4udmFsdWV9XCIpYDtcclxuICAgIH1cclxuICAgIHJldHVybiBgICBzdGF0aWMgbGV0ICR7c3dpZnROYW1lfSA9ICR7dG9rZW4udmFsdWV9YDtcclxuICB9KS5qb2luKCdcXG4nKTtcclxuICBcclxuICByZXR1cm4gYHN0cnVjdCBEZXNpZ25Ub2tlbnMge1xcbiR7c3dpZnRUb2tlbnN9XFxufWA7XHJcbn07XHJcblxyXG4vLyDQrdC60YHQv9C+0YDRgiDQtNC70Y8gQW5kcm9pZCBYTUxcclxuY29uc3QgZXhwb3J0VG9YTUxDb2xvcnMgPSAodG9rZW5zOiBEZXNpZ25Ub2tlbltdKSA9PiB7XHJcbiAgY29uc3QgY29sb3JUb2tlbnMgPSB0b2tlbnMuZmlsdGVyKHRva2VuID0+IHRva2VuLmNhdGVnb3J5ID09PSAnY29sb3InKTtcclxuICBjb25zdCB4bWxDb2xvcnMgPSBjb2xvclRva2Vucy5tYXAodG9rZW4gPT4ge1xyXG4gICAgY29uc3QgeG1sTmFtZSA9IHRva2VuLmlkLnJlcGxhY2UoLy0vZywgJ18nKTtcclxuICAgIHJldHVybiBgICA8Y29sb3IgbmFtZT1cIiR7eG1sTmFtZX1cIj4ke3Rva2VuLnZhbHVlfTwvY29sb3I+YDtcclxuICB9KS5qb2luKCdcXG4nKTtcclxuICBcclxuICByZXR1cm4gYDxyZXNvdXJjZXM+XFxuJHt4bWxDb2xvcnN9XFxuPC9yZXNvdXJjZXM+YDtcclxufTtcclxuXHJcbi8vINCt0LrRgdC/0L7RgNGCINC00LvRjyBIYXJtb255T1NcclxuY29uc3QgZXhwb3J0VG9IYXJtb255T1NUb2tlbnMgPSAodG9rZW5zOiBEZXNpZ25Ub2tlbltdKSA9PiB7XHJcbiAgY29uc3QgaGFybW9ueVRva2VucyA9IHRva2Vucy5tYXAodG9rZW4gPT4ge1xyXG4gICAgY29uc3QgaGFybW9ueU5hbWUgPSB0b2tlbi5pZC5yZXBsYWNlKC8tKFthLXpdKS9nLCAoXywgbGV0dGVyKSA9PiBsZXR0ZXIudG9VcHBlckNhc2UoKSk7XHJcbiAgICByZXR1cm4gYCAgJHtoYXJtb255TmFtZX06ICcke3Rva2VuLnZhbHVlfScsYDtcclxuICB9KS5qb2luKCdcXG4nKTtcclxuICBcclxuICByZXR1cm4gYGV4cG9ydCBjb25zdCBEZXNpZ25Ub2tlbnMgPSB7XFxuJHtoYXJtb255VG9rZW5zfVxcbn07YDtcclxufTtcclxuXHJcbi8vINCk0YPQvdC60YbQuNGPINC00LvRjyDRgdC40L3RhdGA0L7QvdC40LfQsNGG0LjQuCDQvdCw0YHRgtGA0L7QtdC6INC/0L7Qu9GM0LfQvtCy0LDRgtC10LvRjyDQvNC10LbQtNGDINGD0YHRgtGA0L7QudGB0YLQstCw0LzQuFxyXG5leHBvcnQgaW50ZXJmYWNlIFVzZXJUaGVtZVByZWZlcmVuY2VzIHtcclxuICB1c2VySWQ6IHN0cmluZztcclxuICBzZWxlY3RlZFRoZW1lSWQ6IHN0cmluZztcclxuICBjdXN0b21pemF0aW9ucz86IHtcclxuICAgIHByaW1hcnlDb2xvcj86IHN0cmluZztcclxuICAgIGZvbnRTaXplPzogJ3NtYWxsJyB8ICdtZWRpdW0nIHwgJ2xhcmdlJztcclxuICAgIGRhcmtNb2RlRW5hYmxlZD86IGJvb2xlYW47XHJcbiAgfTtcclxuICBsYXN0VXBkYXRlZDogc3RyaW5nO1xyXG4gIHN5bmNlZFBsYXRmb3JtczogKCd3ZWInIHwgJ2lvcycgfCAnYW5kcm9pZCcgfCAnaHVhd2VpJylbXTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IHN5bmNUaGVtZUFjcm9zc1BsYXRmb3JtcyA9IGFzeW5jIChwcmVmZXJlbmNlczogVXNlclRoZW1lUHJlZmVyZW5jZXMpID0+IHtcclxuICAvLyDQm9C+0LPQuNC60LAg0YHQuNC90YXRgNC+0L3QuNC30LDRhtC40Lgg0L3QsNGB0YLRgNC+0LXQuiDRgtC10LzRiyDQvNC10LbQtNGDINC/0LvQsNGC0YTQvtGA0LzQsNC80LhcclxuICAvLyDQl9C00LXRgdGMINCx0YPQtNC10YIg0LjQvdGC0LXQs9GA0LDRhtC40Y8g0YEgQVBJINC00LvRjyDRgdC+0YXRgNCw0L3QtdC90LjRjyDQvdCw0YHRgtGA0L7QtdC6INC/0L7Qu9GM0LfQvtCy0LDRgtC10LvRj1xyXG4gIHJldHVybiB7XHJcbiAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgc3luY2VkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgIHBsYXRmb3JtczogcHJlZmVyZW5jZXMuc3luY2VkUGxhdGZvcm1zXHJcbiAgfTtcclxufTtcclxuIl0sIm5hbWVzIjpbIkRFU0lHTl9UT0tFTlMiLCJpZCIsIm5hbWUiLCJ2YWx1ZSIsImNhdGVnb3J5IiwicGxhdGZvcm1zIiwiQ1JPU1NfUExBVEZPUk1fVEhFTUVTIiwiZGlzcGxheU5hbWUiLCJwcmltYXJ5Q29sb3IiLCJzZWNvbmRhcnlDb2xvciIsImdyYWRpZW50cyIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJhY2NlbnQiLCJjb2xvcnMiLCJiYWNrZ3JvdW5kIiwic3VyZmFjZSIsInRleHQiLCJpbnZlcnNlIiwiYm9yZGVyIiwic2hhZG93Iiwib3ZlcmxheSIsImRhdGluZyIsImxpa2UiLCJwYXNzIiwic3VwZXJMaWtlIiwibWF0Y2giLCJvbmxpbmUiLCJ2ZXJpZmllZCIsInByZW1pdW0iLCJzdWNjZXNzIiwid2FybmluZyIsImVycm9yIiwiaW5mbyIsInNwYWNpbmciLCJ4cyIsInNtIiwibWQiLCJsZyIsInhsIiwiYm9yZGVyUmFkaXVzIiwiZnVsbCIsInR5cG9ncmFwaHkiLCJmb250RmFtaWx5IiwiZm9udFNpemVzIiwieHhsIiwiZm9udFdlaWdodHMiLCJub3JtYWwiLCJtZWRpdW0iLCJzZW1pYm9sZCIsImJvbGQiLCJsaW5lSGVpZ2h0cyIsInRpZ2h0IiwicmVsYXhlZCIsImxvb3NlIiwic2hhZG93cyIsImdldFRoZW1lQnlJZCIsInRoZW1lSWQiLCJmaW5kIiwidGhlbWUiLCJleHBvcnRUb2tlbnNGb3JQbGF0Zm9ybSIsInBsYXRmb3JtIiwicGxhdGZvcm1Ub2tlbnMiLCJmaWx0ZXIiLCJ0b2tlbiIsImluY2x1ZGVzIiwiZXhwb3J0VG9DU1NWYXJpYWJsZXMiLCJleHBvcnRUb1N3aWZ0VUlUb2tlbnMiLCJleHBvcnRUb1hNTENvbG9ycyIsImV4cG9ydFRvSGFybW9ueU9TVG9rZW5zIiwidG9rZW5zIiwiY3NzVmFycyIsIm1hcCIsImNzc05hbWUiLCJyZXBsYWNlIiwibGV0dGVyIiwidG9Mb3dlckNhc2UiLCJqb2luIiwic3dpZnRUb2tlbnMiLCJzd2lmdE5hbWUiLCJfIiwidG9VcHBlckNhc2UiLCJjb2xvclRva2VucyIsInhtbENvbG9ycyIsInhtbE5hbWUiLCJoYXJtb255VG9rZW5zIiwiaGFybW9ueU5hbWUiLCJzeW5jVGhlbWVBY3Jvc3NQbGF0Zm9ybXMiLCJwcmVmZXJlbmNlcyIsInN5bmNlZEF0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3luY2VkUGxhdGZvcm1zIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/config/design-system.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/createEmotionCache.ts":
/*!***********************************!*\
  !*** ./src/createEmotionCache.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmotionCache: () => (/* binding */ createEmotionCache),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__]);\n_emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction createEmotionCache() {\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: 'css',\n        prepend: true\n    });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createEmotionCache);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jcmVhdGVFbW90aW9uQ2FjaGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlDO0FBRWxDLFNBQVNDO0lBQ2QsT0FBT0QsMERBQVdBLENBQUM7UUFBRUUsS0FBSztRQUFPQyxTQUFTO0lBQUs7QUFDakQ7QUFFQSxpRUFBZUYsa0JBQWtCQSxFQUFDIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcc3JjXFxjcmVhdGVFbW90aW9uQ2FjaGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUNhY2hlIGZyb20gJ0BlbW90aW9uL2NhY2hlJztcblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUVtb3Rpb25DYWNoZSgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNhY2hlKHsga2V5OiAnY3NzJywgcHJlcGVuZDogdHJ1ZSB9KTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgY3JlYXRlRW1vdGlvbkNhY2hlO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNhY2hlIiwiY3JlYXRlRW1vdGlvbkNhY2hlIiwia2V5IiwicHJlcGVuZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/createEmotionCache.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/hooks/useContacts.ts":
/*!**********************************!*\
  !*** ./src/hooks/useContacts.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContacts: () => (/* binding */ useContacts)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_contacts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/contacts */ \"(pages-dir-node)/./src/utils/contacts.ts\");\n\n\n\nconst useContacts = ()=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const [contacts, setContacts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const loadContacts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[loadContacts]\": async ()=>{\n            try {\n                setLoading(true);\n                const storedContacts = await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.getStoredContacts)();\n                setContacts(storedContacts);\n                setError(null);\n            } catch (err) {\n                setError(t('contacts.error.loadFailed'));\n                console.error('Error loading contacts:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[loadContacts]\"], [\n        t\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useContacts.useEffect\": ()=>{\n            loadContacts();\n        }\n    }[\"useContacts.useEffect\"], [\n        loadContacts\n    ]);\n    const addContact = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[addContact]\": async (contact)=>{\n            try {\n                setLoading(true);\n                const hasPermission = await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.checkContactPermission)();\n                if (!hasPermission) {\n                    throw new Error(t('contacts.error.permissionDenied'));\n                }\n                const fullContact = {\n                    ...contact,\n                    id: crypto.randomUUID(),\n                    addedAt: new Date().toISOString()\n                };\n                const success = await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.addToContacts)(fullContact);\n                if (success) {\n                    await loadContacts();\n                    return {\n                        success: true\n                    };\n                } else {\n                    throw new Error(t('contacts.error.addFailed'));\n                }\n            } catch (err) {\n                const error = err instanceof Error ? err.message : t('contacts.error.default');\n                setError(error);\n                return {\n                    success: false,\n                    error\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[addContact]\"], [\n        t,\n        loadContacts\n    ]);\n    const removeContact = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[removeContact]\": async (contactId)=>{\n            try {\n                setLoading(true);\n                await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.removeContacts)([\n                    contactId\n                ]);\n                await loadContacts();\n                return {\n                    success: true\n                };\n            } catch (err) {\n                const error = t('contacts.error.removeFailed');\n                setError(error);\n                return {\n                    success: false,\n                    error\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[removeContact]\"], [\n        t,\n        loadContacts\n    ]);\n    const removeAllContacts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useContacts.useCallback[removeAllContacts]\": async ()=>{\n            try {\n                setLoading(true);\n                await (0,_utils_contacts__WEBPACK_IMPORTED_MODULE_2__.removeContacts)();\n                setContacts([]);\n                return {\n                    success: true\n                };\n            } catch (err) {\n                const error = t('contacts.error.removeAllFailed');\n                setError(error);\n                return {\n                    success: false,\n                    error\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useContacts.useCallback[removeAllContacts]\"], [\n        t\n    ]);\n    return {\n        contacts,\n        loading,\n        error,\n        addContact,\n        removeContact,\n        removeAllContacts,\n        loadContacts\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/hooks/useContacts.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/hooks/useNotification.ts":
/*!**************************************!*\
  !*** ./src/hooks/useNotification.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useNotification: () => (/* binding */ useNotification)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useNotification = ()=>{\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        open: false,\n        message: '',\n        severity: 'success'\n    });\n    const showNotification = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotification.useCallback[showNotification]\": (message, severity = 'success')=>{\n            setNotification({\n                open: true,\n                message,\n                severity\n            });\n        }\n    }[\"useNotification.useCallback[showNotification]\"], []);\n    const hideNotification = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useNotification.useCallback[hideNotification]\": ()=>{\n            setNotification({\n                \"useNotification.useCallback[hideNotification]\": (prev)=>({\n                        ...prev,\n                        open: false\n                    })\n            }[\"useNotification.useCallback[hideNotification]\"]);\n        }\n    }[\"useNotification.useCallback[hideNotification]\"], []);\n    return {\n        notification,\n        showNotification,\n        hideNotification\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/hooks/useNotification.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/AdminThemeProvider.tsx":
/*!**********************************************!*\
  !*** ./src/providers/AdminThemeProvider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminThemeProvider: () => (/* binding */ AdminThemeProvider),\n/* harmony export */   useAdminTheme: () => (/* binding */ useAdminTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/styles */ \"(pages-dir-node)/./node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=useMediaQuery!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _config_design_system__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/design-system */ \"(pages-dir-node)/./src/config/design-system.ts\");\n/* harmony import */ var _styles_theme_factory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/theme-factory */ \"(pages-dir-node)/./src/styles/theme-factory.ts\");\n\n\n\n\n\n\nconst AdminThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AdminThemeProvider = ({ children })=>{\n    const [currentTheme, setCurrentTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availableThemes, setAvailableThemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewTheme, setPreviewTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isPreviewMode, setIsPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const prefersDarkMode = (0,_barrel_optimize_names_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_4__.useMediaQuery)('(prefers-color-scheme: dark)');\n    // Загрузка тем при инициализации\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminThemeProvider.useEffect\": ()=>{\n            loadThemesFromAdmin();\n        }\n    }[\"AdminThemeProvider.useEffect\"], []);\n    // Загрузка тем из админки\n    const loadThemesFromAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[loadThemesFromAdmin]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const response = await fetch('/api/admin/themes', {\n                    headers: {\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    }\n                });\n                if (response.ok) {\n                    const themesData = await response.json();\n                    // Проверяем, что получили массив тем\n                    const themes = Array.isArray(themesData) ? themesData : [];\n                    if (themes.length === 0) {\n                        // Если нет тем, создаем локальные\n                        const localThemes = createLocalThemes();\n                        setAvailableThemes(localThemes);\n                        setCurrentTheme(localThemes[0]);\n                    } else {\n                        setAvailableThemes(themes);\n                        // Находим активную тему или используем дефолтную\n                        const activeTheme = themes.find({\n                            \"AdminThemeProvider.useCallback[loadThemesFromAdmin]\": (t)=>t.isActive\n                        }[\"AdminThemeProvider.useCallback[loadThemesFromAdmin]\"]) || themes.find({\n                            \"AdminThemeProvider.useCallback[loadThemesFromAdmin]\": (t)=>t.isDefault\n                        }[\"AdminThemeProvider.useCallback[loadThemesFromAdmin]\"]) || createDefaultTheme();\n                        setCurrentTheme(activeTheme);\n                    }\n                } else {\n                    // Если API недоступно, используем локальные темы\n                    const localThemes = createLocalThemes();\n                    setAvailableThemes(localThemes);\n                    setCurrentTheme(localThemes[0]);\n                }\n            } catch (err) {\n                console.error('Ошибка загрузки тем:', err);\n                // Fallback к локальным темам\n                const localThemes = createLocalThemes();\n                setAvailableThemes(localThemes);\n                setCurrentTheme(localThemes[0]);\n                setError('Не удалось загрузить темы из админки, используются локальные');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[loadThemesFromAdmin]\"], []);\n    // Создание локальных тем на основе CROSS_PLATFORM_THEMES\n    const createLocalThemes = ()=>{\n        return _config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES.map((config, index)=>({\n                id: config.id,\n                name: config.name,\n                displayName: config.displayName,\n                isActive: index === 0,\n                isDefault: index === 0,\n                config,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            }));\n    };\n    // Создание дефолтной темы\n    const createDefaultTheme = ()=>{\n        const defaultConfig = _config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES[0];\n        return {\n            id: 'default',\n            name: 'default',\n            displayName: 'По умолчанию',\n            isActive: true,\n            isDefault: true,\n            config: defaultConfig,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n    };\n    // Установка темы\n    const setTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[setTheme]\": async (themeId)=>{\n            const theme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[setTheme].theme\": (t)=>t.id === themeId\n            }[\"AdminThemeProvider.useCallback[setTheme].theme\"]);\n            if (!theme) {\n                setError(`Тема с ID ${themeId} не найдена`);\n                return;\n            }\n            try {\n                // Сохраняем в админке\n                await fetch('/api/admin/themes/activate', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify({\n                        themeId\n                    })\n                });\n                // Обновляем локальное состояние\n                setAvailableThemes({\n                    \"AdminThemeProvider.useCallback[setTheme]\": (prev)=>prev.map({\n                            \"AdminThemeProvider.useCallback[setTheme]\": (t)=>({\n                                    ...t,\n                                    isActive: t.id === themeId\n                                })\n                        }[\"AdminThemeProvider.useCallback[setTheme]\"])\n                }[\"AdminThemeProvider.useCallback[setTheme]\"]);\n                setCurrentTheme(theme);\n                // Сохраняем в localStorage для быстрого доступа\n                localStorage.setItem('activeThemeId', themeId);\n            } catch (err) {\n                console.error('Ошибка установки темы:', err);\n                // Устанавливаем локально даже если API недоступно\n                setCurrentTheme(theme);\n                localStorage.setItem('activeThemeId', themeId);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[setTheme]\"], [\n        availableThemes\n    ]);\n    // Переключение темы (упрощенный метод для совместимости)\n    const switchTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[switchTheme]\": async (themeName)=>{\n            // Ищем тему по имени или создаем простое переключение light/dark\n            let targetTheme;\n            if (themeName === 'light') {\n                targetTheme = availableThemes.find({\n                    \"AdminThemeProvider.useCallback[switchTheme]\": (t)=>t.name.includes('light') || t.name === 'likes-love'\n                }[\"AdminThemeProvider.useCallback[switchTheme]\"]);\n            } else if (themeName === 'dark') {\n                targetTheme = availableThemes.find({\n                    \"AdminThemeProvider.useCallback[switchTheme]\": (t)=>t.name.includes('dark')\n                }[\"AdminThemeProvider.useCallback[switchTheme]\"]);\n            } else {\n                targetTheme = availableThemes.find({\n                    \"AdminThemeProvider.useCallback[switchTheme]\": (t)=>t.name === themeName\n                }[\"AdminThemeProvider.useCallback[switchTheme]\"]);\n            }\n            // Если не найдена, используем следующую доступную тему\n            if (!targetTheme) {\n                const currentIndex = availableThemes.findIndex({\n                    \"AdminThemeProvider.useCallback[switchTheme].currentIndex\": (t)=>t.id === currentTheme?.id\n                }[\"AdminThemeProvider.useCallback[switchTheme].currentIndex\"]);\n                const nextIndex = (currentIndex + 1) % availableThemes.length;\n                targetTheme = availableThemes[nextIndex];\n            }\n            if (targetTheme) {\n                await setTheme(targetTheme.id);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[switchTheme]\"], [\n        availableThemes,\n        currentTheme,\n        setTheme\n    ]);\n    // Создание кастомной темы\n    const createCustomTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[createCustomTheme]\": async (config)=>{\n            const newTheme = {\n                id: `custom-${Date.now()}`,\n                name: config.name || 'custom',\n                displayName: config.displayName || 'Кастомная тема',\n                isActive: false,\n                isDefault: false,\n                config: config.config || _config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES[0],\n                customizations: config.customizations,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            try {\n                const response = await fetch('/api/admin/themes', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify(newTheme)\n                });\n                if (response.ok) {\n                    const savedTheme = await response.json();\n                    setAvailableThemes({\n                        \"AdminThemeProvider.useCallback[createCustomTheme]\": (prev)=>[\n                                ...prev,\n                                savedTheme\n                            ]\n                    }[\"AdminThemeProvider.useCallback[createCustomTheme]\"]);\n                    return savedTheme;\n                }\n            } catch (err) {\n                console.error('Ошибка создания темы:', err);\n            }\n            // Добавляем локально если API недоступно\n            setAvailableThemes({\n                \"AdminThemeProvider.useCallback[createCustomTheme]\": (prev)=>[\n                        ...prev,\n                        newTheme\n                    ]\n            }[\"AdminThemeProvider.useCallback[createCustomTheme]\"]);\n            return newTheme;\n        }\n    }[\"AdminThemeProvider.useCallback[createCustomTheme]\"], []);\n    // Обновление темы\n    const updateTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[updateTheme]\": async (themeId, updates)=>{\n            try {\n                await fetch(`/api/admin/themes/${themeId}`, {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify(updates)\n                });\n                setAvailableThemes({\n                    \"AdminThemeProvider.useCallback[updateTheme]\": (prev)=>prev.map({\n                            \"AdminThemeProvider.useCallback[updateTheme]\": (theme)=>theme.id === themeId ? {\n                                    ...theme,\n                                    ...updates,\n                                    updatedAt: new Date().toISOString()\n                                } : theme\n                        }[\"AdminThemeProvider.useCallback[updateTheme]\"])\n                }[\"AdminThemeProvider.useCallback[updateTheme]\"]);\n                if (currentTheme?.id === themeId) {\n                    setCurrentTheme({\n                        \"AdminThemeProvider.useCallback[updateTheme]\": (prev)=>prev ? {\n                                ...prev,\n                                ...updates\n                            } : prev\n                    }[\"AdminThemeProvider.useCallback[updateTheme]\"]);\n                }\n            } catch (err) {\n                console.error('Ошибка обновления темы:', err);\n                setError('Не удалось обновить тему');\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[updateTheme]\"], [\n        currentTheme\n    ]);\n    // Удаление темы\n    const deleteTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[deleteTheme]\": async (themeId)=>{\n            const theme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[deleteTheme].theme\": (t)=>t.id === themeId\n            }[\"AdminThemeProvider.useCallback[deleteTheme].theme\"]);\n            if (theme?.isDefault) {\n                setError('Нельзя удалить дефолтную тему');\n                return;\n            }\n            try {\n                await fetch(`/api/admin/themes/${themeId}`, {\n                    method: 'DELETE',\n                    headers: {\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    }\n                });\n                setAvailableThemes({\n                    \"AdminThemeProvider.useCallback[deleteTheme]\": (prev)=>prev.filter({\n                            \"AdminThemeProvider.useCallback[deleteTheme]\": (t)=>t.id !== themeId\n                        }[\"AdminThemeProvider.useCallback[deleteTheme]\"])\n                }[\"AdminThemeProvider.useCallback[deleteTheme]\"]);\n                if (currentTheme?.id === themeId) {\n                    const defaultTheme = availableThemes.find({\n                        \"AdminThemeProvider.useCallback[deleteTheme].defaultTheme\": (t)=>t.isDefault\n                    }[\"AdminThemeProvider.useCallback[deleteTheme].defaultTheme\"]);\n                    if (defaultTheme) {\n                        await setTheme(defaultTheme.id);\n                    }\n                }\n            } catch (err) {\n                console.error('Ошибка удаления темы:', err);\n                setError('Не удалось удалить тему');\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[deleteTheme]\"], [\n        availableThemes,\n        currentTheme,\n        setTheme\n    ]);\n    // Сброс к дефолтной теме\n    const resetToDefault = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[resetToDefault]\": async ()=>{\n            const defaultTheme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[resetToDefault].defaultTheme\": (t)=>t.isDefault\n            }[\"AdminThemeProvider.useCallback[resetToDefault].defaultTheme\"]);\n            if (defaultTheme) {\n                await setTheme(defaultTheme.id);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[resetToDefault]\"], [\n        availableThemes,\n        setTheme\n    ]);\n    // Сохранение темы в админке\n    const saveThemeToAdmin = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[saveThemeToAdmin]\": async (theme)=>{\n            try {\n                await fetch('/api/admin/themes', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`\n                    },\n                    body: JSON.stringify(theme)\n                });\n            } catch (err) {\n                console.error('Ошибка сохранения темы:', err);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[saveThemeToAdmin]\"], []);\n    // Получение Material-UI темы\n    const getMuiTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[getMuiTheme]\": ()=>{\n            const activeTheme = previewTheme || currentTheme;\n            if (!activeTheme) {\n                return (0,_styles_theme_factory__WEBPACK_IMPORTED_MODULE_3__.createLikesLoveTheme)(_config_design_system__WEBPACK_IMPORTED_MODULE_2__.CROSS_PLATFORM_THEMES[0]);\n            }\n            return (0,_styles_theme_factory__WEBPACK_IMPORTED_MODULE_3__.createLikesLoveTheme)(activeTheme.config, activeTheme.customizations);\n        }\n    }[\"AdminThemeProvider.useCallback[getMuiTheme]\"], [\n        currentTheme,\n        previewTheme\n    ]);\n    // Предварительный просмотр темы\n    const handlePreviewTheme = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[handlePreviewTheme]\": (themeId)=>{\n            const theme = availableThemes.find({\n                \"AdminThemeProvider.useCallback[handlePreviewTheme].theme\": (t)=>t.id === themeId\n            }[\"AdminThemeProvider.useCallback[handlePreviewTheme].theme\"]);\n            if (theme) {\n                setPreviewTheme(theme);\n                setIsPreviewMode(true);\n            }\n        }\n    }[\"AdminThemeProvider.useCallback[handlePreviewTheme]\"], [\n        availableThemes\n    ]);\n    // Остановка предварительного просмотра\n    const stopPreview = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AdminThemeProvider.useCallback[stopPreview]\": ()=>{\n            setPreviewTheme(null);\n            setIsPreviewMode(false);\n        }\n    }[\"AdminThemeProvider.useCallback[stopPreview]\"], []);\n    const contextValue = {\n        currentTheme: currentTheme || createDefaultTheme(),\n        availableThemes,\n        isLoading,\n        error,\n        setTheme,\n        switchTheme,\n        createCustomTheme,\n        updateTheme,\n        deleteTheme,\n        resetToDefault,\n        loadThemesFromAdmin,\n        saveThemeToAdmin,\n        getMuiTheme,\n        previewTheme: handlePreviewTheme,\n        stopPreview,\n        isPreviewMode\n    };\n    const muiTheme = getMuiTheme();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminThemeContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n            theme: muiTheme,\n            children: children\n        }, void 0, false, {\n            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AdminThemeProvider.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AdminThemeProvider.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAdminTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AdminThemeContext);\n    if (!context) {\n        throw new Error('useAdminTheme must be used within AdminThemeProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/AdminThemeProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/AppProviders.tsx":
/*!****************************************!*\
  !*** ./src/providers/AppProviders.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProviders: () => (/* binding */ AppProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CssBaseline!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=CssBaseline!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthProvider */ \"(pages-dir-node)/./src/providers/AuthProvider.tsx\");\n/* harmony import */ var _NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationsProvider */ \"(pages-dir-node)/./src/providers/NotificationsProvider.tsx\");\n/* harmony import */ var _ContactsProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ContactsProvider */ \"(pages-dir-node)/./src/providers/ContactsProvider.tsx\");\n/* harmony import */ var _MatchProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MatchProvider */ \"(pages-dir-node)/./src/providers/MatchProvider.tsx\");\n/* harmony import */ var _ChatProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ChatProvider */ \"(pages-dir-node)/./src/providers/ChatProvider.tsx\");\n/* harmony import */ var _SettingsProvider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SettingsProvider */ \"(pages-dir-node)/./src/providers/SettingsProvider.tsx\");\n/* harmony import */ var _AdminThemeProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./AdminThemeProvider */ \"(pages-dir-node)/./src/providers/AdminThemeProvider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__, _MatchProvider__WEBPACK_IMPORTED_MODULE_5__, _ChatProvider__WEBPACK_IMPORTED_MODULE_6__]);\n([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__, _MatchProvider__WEBPACK_IMPORTED_MODULE_5__, _ChatProvider__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst AppProviders = ({ children })=>{\n    return(// I18nextProvider уже в _app.tsx, убираем дублирование\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminThemeProvider__WEBPACK_IMPORTED_MODULE_8__.AdminThemeProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_9__.CssBaseline, {}, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationsProvider__WEBPACK_IMPORTED_MODULE_3__.NotificationsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SettingsProvider__WEBPACK_IMPORTED_MODULE_7__.SettingsProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ContactsProvider__WEBPACK_IMPORTED_MODULE_4__.ContactsProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MatchProvider__WEBPACK_IMPORTED_MODULE_5__.MatchProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatProvider__WEBPACK_IMPORTED_MODULE_6__.ChatProvider, {\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AppProviders.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined));\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/AppProviders.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/AuthProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/AuthProvider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isAuthenticated = !!user && !!token;\n    // Загрузка токена из localStorage при инициализации\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const savedToken = localStorage.getItem('token');\n            const savedUser = localStorage.getItem('user');\n            if (savedToken && savedUser) {\n                try {\n                    const parsedUser = JSON.parse(savedUser);\n                    setToken(savedToken);\n                    setUser(parsedUser);\n                    // Проверяем валидность токена\n                    validateToken(savedToken).then({\n                        \"AuthProvider.useEffect\": (isValid)=>{\n                            if (!isValid) {\n                                logout();\n                            }\n                        }\n                    }[\"AuthProvider.useEffect\"]);\n                } catch (error) {\n                    console.error('Error parsing saved user data:', error);\n                    logout();\n                }\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Проверка валидности токена\n    const validateToken = async (tokenToValidate)=>{\n        try {\n            const response = await fetch('/api/auth/validate', {\n                method: 'GET',\n                headers: {\n                    'Authorization': `Bearer ${tokenToValidate}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.ok;\n        } catch (error) {\n            console.error('Token validation error:', error);\n            return false;\n        }\n    };\n    // Вход в систему\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                return {\n                    success: false,\n                    error: data.message || 'Login failed'\n                };\n            }\n            const { user: userData, token: userToken } = data;\n            // Сохраняем данные\n            setUser(userData);\n            setToken(userToken);\n            localStorage.setItem('token', userToken);\n            localStorage.setItem('user', JSON.stringify(userData));\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Login error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Network error'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Регистрация\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/auth/register', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(userData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                return {\n                    success: false,\n                    error: data.message || 'Registration failed'\n                };\n            }\n            const { user: newUser, token: userToken } = data;\n            // Сохраняем данные\n            setUser(newUser);\n            setToken(userToken);\n            localStorage.setItem('token', userToken);\n            localStorage.setItem('user', JSON.stringify(newUser));\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Registration error:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Network error'\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Выход из системы\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n    };\n    // Обновление данных пользователя\n    const updateUser = (userData)=>{\n        if (user) {\n            const updatedUser = {\n                ...user,\n                ...userData\n            };\n            setUser(updatedUser);\n            localStorage.setItem('user', JSON.stringify(updatedUser));\n        }\n    };\n    // Обновление токена\n    const refreshToken = async ()=>{\n        if (!token) return false;\n        try {\n            const response = await fetch('/api/auth/refresh', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                logout();\n                return false;\n            }\n            const data = await response.json();\n            const { token: newToken, user: userData } = data;\n            setToken(newToken);\n            setUser(userData);\n            localStorage.setItem('token', newToken);\n            localStorage.setItem('user', JSON.stringify(userData));\n            return true;\n        } catch (error) {\n            console.error('Token refresh error:', error);\n            logout();\n            return false;\n        }\n    };\n    const value = {\n        user,\n        token,\n        isLoading,\n        isAuthenticated,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshToken\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/ChatProvider.tsx":
/*!****************************************!*\
  !*** ./src/providers/ChatProvider.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatProvider: () => (/* binding */ ChatProvider),\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationsProvider */ \"(pages-dir-node)/./src/providers/NotificationsProvider.tsx\");\n/* harmony import */ var _MatchProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MatchProvider */ \"(pages-dir-node)/./src/providers/MatchProvider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__, _MatchProvider__WEBPACK_IMPORTED_MODULE_3__]);\n([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__, _MatchProvider__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst ChatContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ChatProvider = ({ children })=>{\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { showSuccess, showError } = (0,_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    const { hasActiveMatch } = (0,_MatchProvider__WEBPACK_IMPORTED_MODULE_3__.useMatch)();\n    // Load chats and messages from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            const loadData = {\n                \"ChatProvider.useEffect.loadData\": ()=>{\n                    try {\n                        const storedChats = localStorage.getItem('likes-love-chats');\n                        const storedMessages = localStorage.getItem('likes-love-messages');\n                        if (storedChats) {\n                            setChats(JSON.parse(storedChats));\n                        }\n                        if (storedMessages) {\n                            setMessages(JSON.parse(storedMessages));\n                        }\n                    } catch (error) {\n                        console.error('Error loading chat data:', error);\n                    }\n                }\n            }[\"ChatProvider.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"ChatProvider.useEffect\"], []);\n    // Save data to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            try {\n                localStorage.setItem('likes-love-chats', JSON.stringify(chats));\n            } catch (error) {\n                console.error('Error saving chats:', error);\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        chats\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatProvider.useEffect\": ()=>{\n            try {\n                localStorage.setItem('likes-love-messages', JSON.stringify(messages));\n            } catch (error) {\n                console.error('Error saving messages:', error);\n            }\n        }\n    }[\"ChatProvider.useEffect\"], [\n        messages\n    ]);\n    const createChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[createChat]\": async (participantId)=>{\n            // Check if users have an active match\n            if (!hasActiveMatch(participantId)) {\n                showError('Вы можете общаться только с матчами');\n                return null;\n            }\n            // Check if chat already exists\n            const existingChat = chats.find({\n                \"ChatProvider.useCallback[createChat].existingChat\": (chat)=>chat.participantIds.includes(participantId) && chat.isActive\n            }[\"ChatProvider.useCallback[createChat].existingChat\"]);\n            if (existingChat) {\n                return existingChat.id;\n            }\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"ChatProvider.useCallback[createChat]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"ChatProvider.useCallback[createChat]\"]);\n                const newChat = {\n                    id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    participantIds: [\n                        'current-user',\n                        participantId\n                    ],\n                    unreadCount: 0,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isActive: true\n                };\n                setChats({\n                    \"ChatProvider.useCallback[createChat]\": (prev)=>[\n                            ...prev,\n                            newChat\n                        ]\n                }[\"ChatProvider.useCallback[createChat]\"]);\n                showSuccess('Чат создан');\n                return newChat.id;\n            } catch (error) {\n                console.error('Error creating chat:', error);\n                showError('Ошибка при создании чата');\n                return null;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"ChatProvider.useCallback[createChat]\"], [\n        chats,\n        hasActiveMatch,\n        showSuccess,\n        showError\n    ]);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[sendMessage]\": async (chatId, content, type = 'text')=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"ChatProvider.useCallback[sendMessage]\": (resolve)=>setTimeout(resolve, 300)\n                }[\"ChatProvider.useCallback[sendMessage]\"]);\n                const chat = chats.find({\n                    \"ChatProvider.useCallback[sendMessage].chat\": (c)=>c.id === chatId\n                }[\"ChatProvider.useCallback[sendMessage].chat\"]);\n                if (!chat) {\n                    throw new Error('Chat not found');\n                }\n                const receiverId = chat.participantIds.find({\n                    \"ChatProvider.useCallback[sendMessage].receiverId\": (id)=>id !== 'current-user'\n                }[\"ChatProvider.useCallback[sendMessage].receiverId\"]);\n                if (!receiverId) {\n                    throw new Error('Receiver not found');\n                }\n                const newMessage = {\n                    id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    chatId,\n                    senderId: 'current-user',\n                    receiverId,\n                    content,\n                    type,\n                    timestamp: new Date().toISOString(),\n                    isRead: false\n                };\n                setMessages({\n                    \"ChatProvider.useCallback[sendMessage]\": (prev)=>[\n                            ...prev,\n                            newMessage\n                        ]\n                }[\"ChatProvider.useCallback[sendMessage]\"]);\n                // Update chat with last message\n                setChats({\n                    \"ChatProvider.useCallback[sendMessage]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[sendMessage]\": (chat)=>chat.id === chatId ? {\n                                    ...chat,\n                                    lastMessage: newMessage,\n                                    updatedAt: new Date().toISOString()\n                                } : chat\n                        }[\"ChatProvider.useCallback[sendMessage]\"])\n                }[\"ChatProvider.useCallback[sendMessage]\"]);\n                return true;\n            } catch (error) {\n                console.error('Error sending message:', error);\n                showError('Ошибка при отправке сообщения');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"ChatProvider.useCallback[sendMessage]\"], [\n        chats,\n        showError\n    ]);\n    const markAsRead = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[markAsRead]\": async (chatId)=>{\n            try {\n                // Mark all messages in chat as read\n                setMessages({\n                    \"ChatProvider.useCallback[markAsRead]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[markAsRead]\": (message)=>message.chatId === chatId && message.receiverId === 'current-user' ? {\n                                    ...message,\n                                    isRead: true\n                                } : message\n                        }[\"ChatProvider.useCallback[markAsRead]\"])\n                }[\"ChatProvider.useCallback[markAsRead]\"]);\n                // Reset unread count for chat\n                setChats({\n                    \"ChatProvider.useCallback[markAsRead]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[markAsRead]\": (chat)=>chat.id === chatId ? {\n                                    ...chat,\n                                    unreadCount: 0\n                                } : chat\n                        }[\"ChatProvider.useCallback[markAsRead]\"])\n                }[\"ChatProvider.useCallback[markAsRead]\"]);\n            } catch (error) {\n                console.error('Error marking as read:', error);\n            }\n        }\n    }[\"ChatProvider.useCallback[markAsRead]\"], []);\n    const getUnreadCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getUnreadCount]\": ()=>{\n            return messages.filter({\n                \"ChatProvider.useCallback[getUnreadCount]\": (message)=>message.receiverId === 'current-user' && !message.isRead\n            }[\"ChatProvider.useCallback[getUnreadCount]\"]).length;\n        }\n    }[\"ChatProvider.useCallback[getUnreadCount]\"], [\n        messages\n    ]);\n    const getChatMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[getChatMessages]\": (chatId)=>{\n            return messages.filter({\n                \"ChatProvider.useCallback[getChatMessages]\": (message)=>message.chatId === chatId\n            }[\"ChatProvider.useCallback[getChatMessages]\"]).sort({\n                \"ChatProvider.useCallback[getChatMessages]\": (a, b)=>new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()\n            }[\"ChatProvider.useCallback[getChatMessages]\"]);\n        }\n    }[\"ChatProvider.useCallback[getChatMessages]\"], [\n        messages\n    ]);\n    const deleteChat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatProvider.useCallback[deleteChat]\": async (chatId)=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"ChatProvider.useCallback[deleteChat]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"ChatProvider.useCallback[deleteChat]\"]);\n                setChats({\n                    \"ChatProvider.useCallback[deleteChat]\": (prev)=>prev.map({\n                            \"ChatProvider.useCallback[deleteChat]\": (chat)=>chat.id === chatId ? {\n                                    ...chat,\n                                    isActive: false\n                                } : chat\n                        }[\"ChatProvider.useCallback[deleteChat]\"])\n                }[\"ChatProvider.useCallback[deleteChat]\"]);\n                showSuccess('Чат удален');\n                return true;\n            } catch (error) {\n                console.error('Error deleting chat:', error);\n                showError('Ошибка при удалении чата');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"ChatProvider.useCallback[deleteChat]\"], [\n        showSuccess,\n        showError\n    ]);\n    const value = {\n        chats,\n        messages,\n        isLoading,\n        createChat,\n        sendMessage,\n        markAsRead,\n        getUnreadCount,\n        getChatMessages,\n        deleteChat\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\ChatProvider.tsx\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, undefined);\n};\nconst useChat = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ChatContext);\n    if (context === undefined) {\n        throw new Error('useChat must be used within a ChatProvider');\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wcm92aWRlcnMvQ2hhdFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDM0M7QUFDaEI7QUFzQzNDLE1BQU1RLDRCQUFjUCxvREFBYUEsQ0FBOEJRO0FBTXhELE1BQU1DLGVBQTRDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ3BFLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHViwrQ0FBUUEsQ0FBUyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ1csVUFBVUMsWUFBWSxHQUFHWiwrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUMxRCxNQUFNLENBQUNhLFdBQVdDLGFBQWEsR0FBR2QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxFQUFFZSxXQUFXLEVBQUVDLFNBQVMsRUFBRSxHQUFHYix3RUFBZ0JBO0lBQ25ELE1BQU0sRUFBRWMsY0FBYyxFQUFFLEdBQUdiLHdEQUFRQTtJQUVuQyxxREFBcUQ7SUFDckRGLGdEQUFTQTtrQ0FBQztZQUNSLE1BQU1nQjttREFBVztvQkFDZixJQUFJO3dCQUNGLE1BQU1DLGNBQWNDLGFBQWFDLE9BQU8sQ0FBQzt3QkFDekMsTUFBTUMsaUJBQWlCRixhQUFhQyxPQUFPLENBQUM7d0JBRTVDLElBQUlGLGFBQWE7NEJBQ2ZULFNBQVNhLEtBQUtDLEtBQUssQ0FBQ0w7d0JBQ3RCO3dCQUVBLElBQUlHLGdCQUFnQjs0QkFDbEJWLFlBQVlXLEtBQUtDLEtBQUssQ0FBQ0Y7d0JBQ3pCO29CQUNGLEVBQUUsT0FBT0csT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7b0JBQzVDO2dCQUNGOztZQUVBUDtRQUNGO2lDQUFHLEVBQUU7SUFFTCxnREFBZ0Q7SUFDaERoQixnREFBU0E7a0NBQUM7WUFDUixJQUFJO2dCQUNGa0IsYUFBYU8sT0FBTyxDQUFDLG9CQUFvQkosS0FBS0ssU0FBUyxDQUFDbkI7WUFDMUQsRUFBRSxPQUFPZ0IsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7WUFDdkM7UUFDRjtpQ0FBRztRQUFDaEI7S0FBTTtJQUVWUCxnREFBU0E7a0NBQUM7WUFDUixJQUFJO2dCQUNGa0IsYUFBYU8sT0FBTyxDQUFDLHVCQUF1QkosS0FBS0ssU0FBUyxDQUFDakI7WUFDN0QsRUFBRSxPQUFPYyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUMxQztRQUNGO2lDQUFHO1FBQUNkO0tBQVM7SUFFYixNQUFNa0IsYUFBYTVCLGtEQUFXQTtnREFBQyxPQUFPNkI7WUFDcEMsc0NBQXNDO1lBQ3RDLElBQUksQ0FBQ2IsZUFBZWEsZ0JBQWdCO2dCQUNsQ2QsVUFBVTtnQkFDVixPQUFPO1lBQ1Q7WUFFQSwrQkFBK0I7WUFDL0IsTUFBTWUsZUFBZXRCLE1BQU11QixJQUFJO3FFQUFDQyxDQUFBQSxPQUM5QkEsS0FBS0MsY0FBYyxDQUFDQyxRQUFRLENBQUNMLGtCQUFrQkcsS0FBS0csUUFBUTs7WUFHOUQsSUFBSUwsY0FBYztnQkFDaEIsT0FBT0EsYUFBYU0sRUFBRTtZQUN4QjtZQUVBdkIsYUFBYTtZQUNiLElBQUk7Z0JBQ0Ysb0JBQW9CO2dCQUNwQixNQUFNLElBQUl3Qjs0REFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzs7Z0JBRWpELE1BQU1FLFVBQWdCO29CQUNwQkosSUFBSSxDQUFDLEtBQUssRUFBRUssS0FBS0MsR0FBRyxHQUFHLENBQUMsRUFBRUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUcsSUFBSTtvQkFDbkViLGdCQUFnQjt3QkFBQzt3QkFBZ0JKO3FCQUFjO29CQUMvQ2tCLGFBQWE7b0JBQ2JDLFdBQVcsSUFBSVAsT0FBT1EsV0FBVztvQkFDakNDLFdBQVcsSUFBSVQsT0FBT1EsV0FBVztvQkFDakNkLFVBQVU7Z0JBQ1o7Z0JBRUExQjs0REFBUzBDLENBQUFBLE9BQVE7K0JBQUlBOzRCQUFNWDt5QkFBUTs7Z0JBQ25DMUIsWUFBWTtnQkFDWixPQUFPMEIsUUFBUUosRUFBRTtZQUNuQixFQUFFLE9BQU9aLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO2dCQUN0Q1QsVUFBVTtnQkFDVixPQUFPO1lBQ1QsU0FBVTtnQkFDUkYsYUFBYTtZQUNmO1FBQ0Y7K0NBQUc7UUFBQ0w7UUFBT1E7UUFBZ0JGO1FBQWFDO0tBQVU7SUFFbEQsTUFBTXFDLGNBQWNwRCxrREFBV0E7aURBQUMsT0FDOUJxRCxRQUNBQyxTQUNBQyxPQUFrQyxNQUFNO1lBRXhDMUMsYUFBYTtZQUNiLElBQUk7Z0JBQ0Ysb0JBQW9CO2dCQUNwQixNQUFNLElBQUl3Qjs2REFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUzs7Z0JBRWpELE1BQU1OLE9BQU94QixNQUFNdUIsSUFBSTtrRUFBQ3lCLENBQUFBLElBQUtBLEVBQUVwQixFQUFFLEtBQUtpQjs7Z0JBQ3RDLElBQUksQ0FBQ3JCLE1BQU07b0JBQ1QsTUFBTSxJQUFJeUIsTUFBTTtnQkFDbEI7Z0JBRUEsTUFBTUMsYUFBYTFCLEtBQUtDLGNBQWMsQ0FBQ0YsSUFBSTt3RUFBQ0ssQ0FBQUEsS0FBTUEsT0FBTzs7Z0JBQ3pELElBQUksQ0FBQ3NCLFlBQVk7b0JBQ2YsTUFBTSxJQUFJRCxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNRSxhQUEwQjtvQkFDOUJ2QixJQUFJLENBQUMsSUFBSSxFQUFFSyxLQUFLQyxHQUFHLEdBQUcsQ0FBQyxFQUFFQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxJQUFJO29CQUNsRU87b0JBQ0FPLFVBQVU7b0JBQ1ZGO29CQUNBSjtvQkFDQUM7b0JBQ0FNLFdBQVcsSUFBSXBCLE9BQU9RLFdBQVc7b0JBQ2pDYSxRQUFRO2dCQUNWO2dCQUVBbkQ7NkRBQVl3QyxDQUFBQSxPQUFROytCQUFJQTs0QkFBTVE7eUJBQVc7O2dCQUV6QyxnQ0FBZ0M7Z0JBQ2hDbEQ7NkRBQVMwQyxDQUFBQSxPQUFRQSxLQUFLWSxHQUFHO3FFQUFDL0IsQ0FBQUEsT0FDeEJBLEtBQUtJLEVBQUUsS0FBS2lCLFNBQ1I7b0NBQ0UsR0FBR3JCLElBQUk7b0NBQ1BnQyxhQUFhTDtvQ0FDYlQsV0FBVyxJQUFJVCxPQUFPUSxXQUFXO2dDQUNuQyxJQUNBakI7OztnQkFHTixPQUFPO1lBQ1QsRUFBRSxPQUFPUixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtnQkFDeENULFVBQVU7Z0JBQ1YsT0FBTztZQUNULFNBQVU7Z0JBQ1JGLGFBQWE7WUFDZjtRQUNGO2dEQUFHO1FBQUNMO1FBQU9PO0tBQVU7SUFFckIsTUFBTWtELGFBQWFqRSxrREFBV0E7Z0RBQUMsT0FBT3FEO1lBQ3BDLElBQUk7Z0JBQ0Ysb0NBQW9DO2dCQUNwQzFDOzREQUFZd0MsQ0FBQUEsT0FBUUEsS0FBS1ksR0FBRztvRUFBQ0csQ0FBQUEsVUFDM0JBLFFBQVFiLE1BQU0sS0FBS0EsVUFBVWEsUUFBUVIsVUFBVSxLQUFLLGlCQUNoRDtvQ0FBRSxHQUFHUSxPQUFPO29DQUFFSixRQUFRO2dDQUFLLElBQzNCSTs7O2dCQUdOLDhCQUE4QjtnQkFDOUJ6RDs0REFBUzBDLENBQUFBLE9BQVFBLEtBQUtZLEdBQUc7b0VBQUMvQixDQUFBQSxPQUN4QkEsS0FBS0ksRUFBRSxLQUFLaUIsU0FDUjtvQ0FBRSxHQUFHckIsSUFBSTtvQ0FBRWUsYUFBYTtnQ0FBRSxJQUMxQmY7OztZQUVSLEVBQUUsT0FBT1IsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDMUM7UUFDRjsrQ0FBRyxFQUFFO0lBRUwsTUFBTTJDLGlCQUFpQm5FLGtEQUFXQTtvREFBQztZQUNqQyxPQUFPVSxTQUFTMEQsTUFBTTs0REFBQ0YsQ0FBQUEsVUFDckJBLFFBQVFSLFVBQVUsS0FBSyxrQkFBa0IsQ0FBQ1EsUUFBUUosTUFBTTsyREFDeERPLE1BQU07UUFDVjttREFBRztRQUFDM0Q7S0FBUztJQUViLE1BQU00RCxrQkFBa0J0RSxrREFBV0E7cURBQUMsQ0FBQ3FEO1lBQ25DLE9BQU8zQyxTQUNKMEQsTUFBTTs2REFBQ0YsQ0FBQUEsVUFBV0EsUUFBUWIsTUFBTSxLQUFLQTs0REFDckNrQixJQUFJOzZEQUFDLENBQUNDLEdBQUdDLElBQU0sSUFBSWhDLEtBQUsrQixFQUFFWCxTQUFTLEVBQUVhLE9BQU8sS0FBSyxJQUFJakMsS0FBS2dDLEVBQUVaLFNBQVMsRUFBRWEsT0FBTzs7UUFDbkY7b0RBQUc7UUFBQ2hFO0tBQVM7SUFFYixNQUFNaUUsYUFBYTNFLGtEQUFXQTtnREFBQyxPQUFPcUQ7WUFDcEN4QyxhQUFhO1lBQ2IsSUFBSTtnQkFDRixvQkFBb0I7Z0JBQ3BCLE1BQU0sSUFBSXdCOzREQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTOztnQkFFakQ3Qjs0REFBUzBDLENBQUFBLE9BQVFBLEtBQUtZLEdBQUc7b0VBQUMvQixDQUFBQSxPQUN4QkEsS0FBS0ksRUFBRSxLQUFLaUIsU0FDUjtvQ0FBRSxHQUFHckIsSUFBSTtvQ0FBRUcsVUFBVTtnQ0FBTSxJQUMzQkg7OztnQkFHTmxCLFlBQVk7Z0JBQ1osT0FBTztZQUNULEVBQUUsT0FBT1UsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7Z0JBQ3RDVCxVQUFVO2dCQUNWLE9BQU87WUFDVCxTQUFVO2dCQUNSRixhQUFhO1lBQ2Y7UUFDRjsrQ0FBRztRQUFDQztRQUFhQztLQUFVO0lBRTNCLE1BQU02RCxRQUF5QjtRQUM3QnBFO1FBQ0FFO1FBQ0FFO1FBQ0FnQjtRQUNBd0I7UUFDQWE7UUFDQUU7UUFDQUc7UUFDQUs7SUFDRjtJQUVBLHFCQUNFLDhEQUFDdkUsWUFBWXlFLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQzFCckU7Ozs7OztBQUdQLEVBQUU7QUFFSyxNQUFNdUUsVUFBVTtJQUNyQixNQUFNQyxVQUFVakYsaURBQVVBLENBQUNNO0lBQzNCLElBQUkyRSxZQUFZMUUsV0FBVztRQUN6QixNQUFNLElBQUlvRCxNQUFNO0lBQ2xCO0lBQ0EsT0FBT3NCO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiRjpcXEN1cnNvclxcbGwuY29tXFxpbnN0YWxsXFx3ZWJcXHNyY1xccHJvdmlkZXJzXFxDaGF0UHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIFJlYWN0Tm9kZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlTm90aWZpY2F0aW9ucyB9IGZyb20gJy4vTm90aWZpY2F0aW9uc1Byb3ZpZGVyJztcbmltcG9ydCB7IHVzZU1hdGNoIH0gZnJvbSAnLi9NYXRjaFByb3ZpZGVyJztcblxuZXhwb3J0IGludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIGNoYXRJZDogc3RyaW5nO1xuICBzZW5kZXJJZDogc3RyaW5nO1xuICByZWNlaXZlcklkOiBzdHJpbmc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgdHlwZTogJ3RleHQnIHwgJ2ltYWdlJyB8ICdmaWxlJyB8ICd2b2ljZSc7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBpc1JlYWQ6IGJvb2xlYW47XG4gIGZpbGVVcmw/OiBzdHJpbmc7XG4gIGZpbGVOYW1lPzogc3RyaW5nO1xuICBmaWxlU2l6ZT86IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDaGF0IHtcbiAgaWQ6IHN0cmluZztcbiAgcGFydGljaXBhbnRJZHM6IHN0cmluZ1tdO1xuICBsYXN0TWVzc2FnZT86IENoYXRNZXNzYWdlO1xuICB1bnJlYWRDb3VudDogbnVtYmVyO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XG4gIGlzQWN0aXZlOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgQ2hhdENvbnRleHRUeXBlIHtcbiAgY2hhdHM6IENoYXRbXTtcbiAgbWVzc2FnZXM6IENoYXRNZXNzYWdlW107XG4gIGlzTG9hZGluZzogYm9vbGVhbjtcbiAgY3JlYXRlQ2hhdDogKHBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4gUHJvbWlzZTxzdHJpbmcgfCBudWxsPjtcbiAgc2VuZE1lc3NhZ2U6IChjaGF0SWQ6IHN0cmluZywgY29udGVudDogc3RyaW5nLCB0eXBlPzogJ3RleHQnIHwgJ2ltYWdlJyB8ICdmaWxlJykgPT4gUHJvbWlzZTxib29sZWFuPjtcbiAgbWFya0FzUmVhZDogKGNoYXRJZDogc3RyaW5nKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBnZXRVbnJlYWRDb3VudDogKCkgPT4gbnVtYmVyO1xuICBnZXRDaGF0TWVzc2FnZXM6IChjaGF0SWQ6IHN0cmluZykgPT4gQ2hhdE1lc3NhZ2VbXTtcbiAgZGVsZXRlQ2hhdDogKGNoYXRJZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xufVxuXG5jb25zdCBDaGF0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8Q2hhdENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5pbnRlcmZhY2UgQ2hhdFByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgY29uc3QgQ2hhdFByb3ZpZGVyOiBSZWFjdC5GQzxDaGF0UHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IFtjaGF0cywgc2V0Q2hhdHNdID0gdXNlU3RhdGU8Q2hhdFtdPihbXSk7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8Q2hhdE1lc3NhZ2VbXT4oW10pO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB7IHNob3dTdWNjZXNzLCBzaG93RXJyb3IgfSA9IHVzZU5vdGlmaWNhdGlvbnMoKTtcbiAgY29uc3QgeyBoYXNBY3RpdmVNYXRjaCB9ID0gdXNlTWF0Y2goKTtcblxuICAvLyBMb2FkIGNoYXRzIGFuZCBtZXNzYWdlcyBmcm9tIGxvY2FsU3RvcmFnZSBvbiBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGxvYWREYXRhID0gKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc3RvcmVkQ2hhdHMgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnbGlrZXMtbG92ZS1jaGF0cycpO1xuICAgICAgICBjb25zdCBzdG9yZWRNZXNzYWdlcyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdsaWtlcy1sb3ZlLW1lc3NhZ2VzJyk7XG4gICAgICAgIFxuICAgICAgICBpZiAoc3RvcmVkQ2hhdHMpIHtcbiAgICAgICAgICBzZXRDaGF0cyhKU09OLnBhcnNlKHN0b3JlZENoYXRzKSk7XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIGlmIChzdG9yZWRNZXNzYWdlcykge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKEpTT04ucGFyc2Uoc3RvcmVkTWVzc2FnZXMpKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBjaGF0IGRhdGE6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBsb2FkRGF0YSgpO1xuICB9LCBbXSk7XG5cbiAgLy8gU2F2ZSBkYXRhIHRvIGxvY2FsU3RvcmFnZSB3aGVuZXZlciBpdCBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdsaWtlcy1sb3ZlLWNoYXRzJywgSlNPTi5zdHJpbmdpZnkoY2hhdHMpKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGNoYXRzOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFtjaGF0c10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdsaWtlcy1sb3ZlLW1lc3NhZ2VzJywgSlNPTi5zdHJpbmdpZnkobWVzc2FnZXMpKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIG1lc3NhZ2VzOicsIGVycm9yKTtcbiAgICB9XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIGNvbnN0IGNyZWF0ZUNoYXQgPSB1c2VDYWxsYmFjayhhc3luYyAocGFydGljaXBhbnRJZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiA9PiB7XG4gICAgLy8gQ2hlY2sgaWYgdXNlcnMgaGF2ZSBhbiBhY3RpdmUgbWF0Y2hcbiAgICBpZiAoIWhhc0FjdGl2ZU1hdGNoKHBhcnRpY2lwYW50SWQpKSB7XG4gICAgICBzaG93RXJyb3IoJ9CS0Ysg0LzQvtC20LXRgtC1INC+0LHRidCw0YLRjNGB0Y8g0YLQvtC70YzQutC+INGBINC80LDRgtGH0LDQvNC4Jyk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICAvLyBDaGVjayBpZiBjaGF0IGFscmVhZHkgZXhpc3RzXG4gICAgY29uc3QgZXhpc3RpbmdDaGF0ID0gY2hhdHMuZmluZChjaGF0ID0+IFxuICAgICAgY2hhdC5wYXJ0aWNpcGFudElkcy5pbmNsdWRlcyhwYXJ0aWNpcGFudElkKSAmJiBjaGF0LmlzQWN0aXZlXG4gICAgKTtcblxuICAgIGlmIChleGlzdGluZ0NoYXQpIHtcbiAgICAgIHJldHVybiBleGlzdGluZ0NoYXQuaWQ7XG4gICAgfVxuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICAvLyBTaW11bGF0ZSBBUEkgY2FsbFxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwMCkpO1xuXG4gICAgICBjb25zdCBuZXdDaGF0OiBDaGF0ID0ge1xuICAgICAgICBpZDogYGNoYXRfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gLFxuICAgICAgICBwYXJ0aWNpcGFudElkczogWydjdXJyZW50LXVzZXInLCBwYXJ0aWNpcGFudElkXSwgLy8gY3VycmVudC11c2VyINC40LcgQXV0aENvbnRleHRcbiAgICAgICAgdW5yZWFkQ291bnQ6IDAsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICB9O1xuXG4gICAgICBzZXRDaGF0cyhwcmV2ID0+IFsuLi5wcmV2LCBuZXdDaGF0XSk7XG4gICAgICBzaG93U3VjY2Vzcygn0KfQsNGCINGB0L7Qt9C00LDQvScpO1xuICAgICAgcmV0dXJuIG5ld0NoYXQuaWQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGNoYXQ6JywgZXJyb3IpO1xuICAgICAgc2hvd0Vycm9yKCfQntGI0LjQsdC60LAg0L/RgNC4INGB0L7Qt9C00LDQvdC40Lgg0YfQsNGC0LAnKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW2NoYXRzLCBoYXNBY3RpdmVNYXRjaCwgc2hvd1N1Y2Nlc3MsIHNob3dFcnJvcl0pO1xuXG4gIGNvbnN0IHNlbmRNZXNzYWdlID0gdXNlQ2FsbGJhY2soYXN5bmMgKFxuICAgIGNoYXRJZDogc3RyaW5nLCBcbiAgICBjb250ZW50OiBzdHJpbmcsIFxuICAgIHR5cGU6ICd0ZXh0JyB8ICdpbWFnZScgfCAnZmlsZScgPSAndGV4dCdcbiAgKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICAvLyBTaW11bGF0ZSBBUEkgY2FsbFxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDMwMCkpO1xuXG4gICAgICBjb25zdCBjaGF0ID0gY2hhdHMuZmluZChjID0+IGMuaWQgPT09IGNoYXRJZCk7XG4gICAgICBpZiAoIWNoYXQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDaGF0IG5vdCBmb3VuZCcpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZWNlaXZlcklkID0gY2hhdC5wYXJ0aWNpcGFudElkcy5maW5kKGlkID0+IGlkICE9PSAnY3VycmVudC11c2VyJyk7XG4gICAgICBpZiAoIXJlY2VpdmVySWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdSZWNlaXZlciBub3QgZm91bmQnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgbmV3TWVzc2FnZTogQ2hhdE1lc3NhZ2UgPSB7XG4gICAgICAgIGlkOiBgbXNnXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YCxcbiAgICAgICAgY2hhdElkLFxuICAgICAgICBzZW5kZXJJZDogJ2N1cnJlbnQtdXNlcicsXG4gICAgICAgIHJlY2VpdmVySWQsXG4gICAgICAgIGNvbnRlbnQsXG4gICAgICAgIHR5cGUsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICBpc1JlYWQ6IGZhbHNlLFxuICAgICAgfTtcblxuICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgbmV3TWVzc2FnZV0pO1xuXG4gICAgICAvLyBVcGRhdGUgY2hhdCB3aXRoIGxhc3QgbWVzc2FnZVxuICAgICAgc2V0Q2hhdHMocHJldiA9PiBwcmV2Lm1hcChjaGF0ID0+IFxuICAgICAgICBjaGF0LmlkID09PSBjaGF0SWQgXG4gICAgICAgICAgPyB7IFxuICAgICAgICAgICAgICAuLi5jaGF0LCBcbiAgICAgICAgICAgICAgbGFzdE1lc3NhZ2U6IG5ld01lc3NhZ2UsXG4gICAgICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpIFxuICAgICAgICAgICAgfVxuICAgICAgICAgIDogY2hhdFxuICAgICAgKSk7XG5cbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIG1lc3NhZ2U6JywgZXJyb3IpO1xuICAgICAgc2hvd0Vycm9yKCfQntGI0LjQsdC60LAg0L/RgNC4INC+0YLQv9GA0LDQstC60LUg0YHQvtC+0LHRidC10L3QuNGPJyk7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbY2hhdHMsIHNob3dFcnJvcl0pO1xuXG4gIGNvbnN0IG1hcmtBc1JlYWQgPSB1c2VDYWxsYmFjayhhc3luYyAoY2hhdElkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gTWFyayBhbGwgbWVzc2FnZXMgaW4gY2hhdCBhcyByZWFkXG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1lc3NhZ2UgPT4gXG4gICAgICAgIG1lc3NhZ2UuY2hhdElkID09PSBjaGF0SWQgJiYgbWVzc2FnZS5yZWNlaXZlcklkID09PSAnY3VycmVudC11c2VyJ1xuICAgICAgICAgID8geyAuLi5tZXNzYWdlLCBpc1JlYWQ6IHRydWUgfVxuICAgICAgICAgIDogbWVzc2FnZVxuICAgICAgKSk7XG5cbiAgICAgIC8vIFJlc2V0IHVucmVhZCBjb3VudCBmb3IgY2hhdFxuICAgICAgc2V0Q2hhdHMocHJldiA9PiBwcmV2Lm1hcChjaGF0ID0+IFxuICAgICAgICBjaGF0LmlkID09PSBjaGF0SWQgXG4gICAgICAgICAgPyB7IC4uLmNoYXQsIHVucmVhZENvdW50OiAwIH1cbiAgICAgICAgICA6IGNoYXRcbiAgICAgICkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBtYXJraW5nIGFzIHJlYWQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIGNvbnN0IGdldFVucmVhZENvdW50ID0gdXNlQ2FsbGJhY2soKCk6IG51bWJlciA9PiB7XG4gICAgcmV0dXJuIG1lc3NhZ2VzLmZpbHRlcihtZXNzYWdlID0+IFxuICAgICAgbWVzc2FnZS5yZWNlaXZlcklkID09PSAnY3VycmVudC11c2VyJyAmJiAhbWVzc2FnZS5pc1JlYWRcbiAgICApLmxlbmd0aDtcbiAgfSwgW21lc3NhZ2VzXSk7XG5cbiAgY29uc3QgZ2V0Q2hhdE1lc3NhZ2VzID0gdXNlQ2FsbGJhY2soKGNoYXRJZDogc3RyaW5nKTogQ2hhdE1lc3NhZ2VbXSA9PiB7XG4gICAgcmV0dXJuIG1lc3NhZ2VzXG4gICAgICAuZmlsdGVyKG1lc3NhZ2UgPT4gbWVzc2FnZS5jaGF0SWQgPT09IGNoYXRJZClcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShhLnRpbWVzdGFtcCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoYi50aW1lc3RhbXApLmdldFRpbWUoKSk7XG4gIH0sIFttZXNzYWdlc10pO1xuXG4gIGNvbnN0IGRlbGV0ZUNoYXQgPSB1c2VDYWxsYmFjayhhc3luYyAoY2hhdElkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFNpbXVsYXRlIEFQSSBjYWxsXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSk7XG5cbiAgICAgIHNldENoYXRzKHByZXYgPT4gcHJldi5tYXAoY2hhdCA9PiBcbiAgICAgICAgY2hhdC5pZCA9PT0gY2hhdElkIFxuICAgICAgICAgID8geyAuLi5jaGF0LCBpc0FjdGl2ZTogZmFsc2UgfVxuICAgICAgICAgIDogY2hhdFxuICAgICAgKSk7XG5cbiAgICAgIHNob3dTdWNjZXNzKCfQp9Cw0YIg0YPQtNCw0LvQtdC9Jyk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgY2hhdDonLCBlcnJvcik7XG4gICAgICBzaG93RXJyb3IoJ9Ce0YjQuNCx0LrQsCDQv9GA0Lgg0YPQtNCw0LvQtdC90LjQuCDRh9Cw0YLQsCcpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW3Nob3dTdWNjZXNzLCBzaG93RXJyb3JdKTtcblxuICBjb25zdCB2YWx1ZTogQ2hhdENvbnRleHRUeXBlID0ge1xuICAgIGNoYXRzLFxuICAgIG1lc3NhZ2VzLFxuICAgIGlzTG9hZGluZyxcbiAgICBjcmVhdGVDaGF0LFxuICAgIHNlbmRNZXNzYWdlLFxuICAgIG1hcmtBc1JlYWQsXG4gICAgZ2V0VW5yZWFkQ291bnQsXG4gICAgZ2V0Q2hhdE1lc3NhZ2VzLFxuICAgIGRlbGV0ZUNoYXQsXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2hhdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0NoYXRDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZUNoYXQgPSAoKTogQ2hhdENvbnRleHRUeXBlID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQ2hhdENvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VDaGF0IG11c3QgYmUgdXNlZCB3aXRoaW4gYSBDaGF0UHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VFZmZlY3QiLCJ1c2VOb3RpZmljYXRpb25zIiwidXNlTWF0Y2giLCJDaGF0Q29udGV4dCIsInVuZGVmaW5lZCIsIkNoYXRQcm92aWRlciIsImNoaWxkcmVuIiwiY2hhdHMiLCJzZXRDaGF0cyIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzaG93U3VjY2VzcyIsInNob3dFcnJvciIsImhhc0FjdGl2ZU1hdGNoIiwibG9hZERhdGEiLCJzdG9yZWRDaGF0cyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzdG9yZWRNZXNzYWdlcyIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJjcmVhdGVDaGF0IiwicGFydGljaXBhbnRJZCIsImV4aXN0aW5nQ2hhdCIsImZpbmQiLCJjaGF0IiwicGFydGljaXBhbnRJZHMiLCJpbmNsdWRlcyIsImlzQWN0aXZlIiwiaWQiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJuZXdDaGF0IiwiRGF0ZSIsIm5vdyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInVucmVhZENvdW50IiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJwcmV2Iiwic2VuZE1lc3NhZ2UiLCJjaGF0SWQiLCJjb250ZW50IiwidHlwZSIsImMiLCJFcnJvciIsInJlY2VpdmVySWQiLCJuZXdNZXNzYWdlIiwic2VuZGVySWQiLCJ0aW1lc3RhbXAiLCJpc1JlYWQiLCJtYXAiLCJsYXN0TWVzc2FnZSIsIm1hcmtBc1JlYWQiLCJtZXNzYWdlIiwiZ2V0VW5yZWFkQ291bnQiLCJmaWx0ZXIiLCJsZW5ndGgiLCJnZXRDaGF0TWVzc2FnZXMiLCJzb3J0IiwiYSIsImIiLCJnZXRUaW1lIiwiZGVsZXRlQ2hhdCIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VDaGF0IiwiY29udGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/ChatProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/ContactsProvider.tsx":
/*!********************************************!*\
  !*** ./src/providers/ContactsProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactsProvider: () => (/* binding */ ContactsProvider),\n/* harmony export */   useContactsContext: () => (/* binding */ useContactsContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useContacts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useContacts */ \"(pages-dir-node)/./src/hooks/useContacts.ts\");\n/* harmony import */ var _hooks_useNotification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useNotification */ \"(pages-dir-node)/./src/hooks/useNotification.ts\");\n/* harmony import */ var _components_common_Notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/common/Notification */ \"(pages-dir-node)/./components/common/Notification.tsx\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nconst ContactsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ContactsProvider = ({ children })=>{\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const { contacts, loading, addContact: addContactToStore, removeContact: removeContactFromStore, removeAllContacts: removeAllContactsFromStore } = (0,_hooks_useContacts__WEBPACK_IMPORTED_MODULE_2__.useContacts)();\n    const { notification, showNotification, hideNotification } = (0,_hooks_useNotification__WEBPACK_IMPORTED_MODULE_3__.useNotification)();\n    const addContact = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContactsProvider.useCallback[addContact]\": async (contactData)=>{\n            try {\n                const result = await addContactToStore(contactData);\n                if (result.success) {\n                    showNotification(t('contacts.dialog.success'), 'success');\n                    return true;\n                } else {\n                    showNotification(result.error || t('contacts.dialog.error'), 'error');\n                    return false;\n                }\n            } catch (error) {\n                showNotification(t('contacts.error.default'), 'error');\n                return false;\n            }\n        }\n    }[\"ContactsProvider.useCallback[addContact]\"], [\n        addContactToStore,\n        showNotification,\n        t\n    ]);\n    const removeContact = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContactsProvider.useCallback[removeContact]\": async (contactId)=>{\n            try {\n                const result = await removeContactFromStore(contactId);\n                if (result.success) {\n                    showNotification(t('contacts.dialog.removeSuccess'), 'success');\n                    return true;\n                } else {\n                    showNotification(result.error || t('contacts.error.removeFailed'), 'error');\n                    return false;\n                }\n            } catch (error) {\n                showNotification(t('contacts.error.default'), 'error');\n                return false;\n            }\n        }\n    }[\"ContactsProvider.useCallback[removeContact]\"], [\n        removeContactFromStore,\n        showNotification,\n        t\n    ]);\n    const removeAllContacts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ContactsProvider.useCallback[removeAllContacts]\": async ()=>{\n            try {\n                const result = await removeAllContactsFromStore();\n                if (result.success) {\n                    showNotification(t('contacts.dialog.removeAllSuccess'), 'success');\n                    return true;\n                } else {\n                    showNotification(result.error || t('contacts.error.removeAllFailed'), 'error');\n                    return false;\n                }\n            } catch (error) {\n                showNotification(t('contacts.error.default'), 'error');\n                return false;\n            }\n        }\n    }[\"ContactsProvider.useCallback[removeAllContacts]\"], [\n        removeAllContactsFromStore,\n        showNotification,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactsContext.Provider, {\n        value: {\n            addContact,\n            removeContact,\n            removeAllContacts,\n            contacts,\n            loading\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Notification__WEBPACK_IMPORTED_MODULE_4__.Notification, {\n                open: notification.open,\n                message: notification.message,\n                severity: notification.severity,\n                onClose: hideNotification\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\ContactsProvider.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\ContactsProvider.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\nconst useContactsContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ContactsContext);\n    if (context === undefined) {\n        throw new Error('useContactsContext must be used within a ContactsProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/ContactsProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/MatchProvider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/MatchProvider.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchProvider: () => (/* binding */ MatchProvider),\n/* harmony export */   useMatch: () => (/* binding */ useMatch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NotificationsProvider */ \"(pages-dir-node)/./src/providers/NotificationsProvider.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__]);\n_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst MatchContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst MatchProvider = ({ children })=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { showSuccess, showError } = (0,_NotificationsProvider__WEBPACK_IMPORTED_MODULE_2__.useNotifications)();\n    // Load matches from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchProvider.useEffect\": ()=>{\n            const loadMatches = {\n                \"MatchProvider.useEffect.loadMatches\": ()=>{\n                    try {\n                        const storedMatches = localStorage.getItem('likes-love-matches');\n                        if (storedMatches) {\n                            setMatches(JSON.parse(storedMatches));\n                        }\n                    } catch (error) {\n                        console.error('Error loading matches:', error);\n                    }\n                }\n            }[\"MatchProvider.useEffect.loadMatches\"];\n            loadMatches();\n        }\n    }[\"MatchProvider.useEffect\"], []);\n    // Save matches to localStorage whenever matches change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchProvider.useEffect\": ()=>{\n            try {\n                localStorage.setItem('likes-love-matches', JSON.stringify(matches));\n            } catch (error) {\n                console.error('Error saving matches:', error);\n            }\n        }\n    }[\"MatchProvider.useEffect\"], [\n        matches\n    ]);\n    const createMatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[createMatch]\": async (userId)=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"MatchProvider.useCallback[createMatch]\": (resolve)=>setTimeout(resolve, 1000)\n                }[\"MatchProvider.useCallback[createMatch]\"]);\n                const newMatch = {\n                    id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    userId: 'current-user',\n                    matchedUserId: userId,\n                    createdAt: new Date().toISOString(),\n                    isActive: true,\n                    matchedUser: {\n                        id: userId,\n                        name: `User ${userId}`,\n                        photo: undefined,\n                        age: Math.floor(Math.random() * 20) + 18\n                    }\n                };\n                setMatches({\n                    \"MatchProvider.useCallback[createMatch]\": (prev)=>[\n                            ...prev,\n                            newMatch\n                        ]\n                }[\"MatchProvider.useCallback[createMatch]\"]);\n                showSuccess('Новый матч создан!');\n                return true;\n            } catch (error) {\n                console.error('Error creating match:', error);\n                showError('Ошибка при создании матча');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[createMatch]\"], [\n        showSuccess,\n        showError\n    ]);\n    const unmatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[unmatch]\": async (matchId)=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"MatchProvider.useCallback[unmatch]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"MatchProvider.useCallback[unmatch]\"]);\n                setMatches({\n                    \"MatchProvider.useCallback[unmatch]\": (prev)=>prev.map({\n                            \"MatchProvider.useCallback[unmatch]\": (match)=>match.id === matchId ? {\n                                    ...match,\n                                    isActive: false\n                                } : match\n                        }[\"MatchProvider.useCallback[unmatch]\"])\n                }[\"MatchProvider.useCallback[unmatch]\"]);\n                showSuccess('Матч отменен');\n                return true;\n            } catch (error) {\n                console.error('Error unmatching:', error);\n                showError('Ошибка при отмене матча');\n                return false;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[unmatch]\"], [\n        showSuccess,\n        showError\n    ]);\n    const getActiveMatches = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[getActiveMatches]\": async ()=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call\n                await new Promise({\n                    \"MatchProvider.useCallback[getActiveMatches]\": (resolve)=>setTimeout(resolve, 500)\n                }[\"MatchProvider.useCallback[getActiveMatches]\"]);\n                const activeMatches = matches.filter({\n                    \"MatchProvider.useCallback[getActiveMatches].activeMatches\": (match)=>match.isActive\n                }[\"MatchProvider.useCallback[getActiveMatches].activeMatches\"]);\n                return activeMatches;\n            } catch (error) {\n                console.error('Error getting active matches:', error);\n                showError('Ошибка при загрузке матчей');\n                return [];\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[getActiveMatches]\"], [\n        matches,\n        showError\n    ]);\n    const hasActiveMatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[hasActiveMatch]\": (userId)=>{\n            return matches.some({\n                \"MatchProvider.useCallback[hasActiveMatch]\": (match)=>match.matchedUserId === userId && match.isActive\n            }[\"MatchProvider.useCallback[hasActiveMatch]\"]);\n        }\n    }[\"MatchProvider.useCallback[hasActiveMatch]\"], [\n        matches\n    ]);\n    const getMatchById = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[getMatchById]\": (matchId)=>{\n            return matches.find({\n                \"MatchProvider.useCallback[getMatchById]\": (match)=>match.id === matchId\n            }[\"MatchProvider.useCallback[getMatchById]\"]);\n        }\n    }[\"MatchProvider.useCallback[getMatchById]\"], [\n        matches\n    ]);\n    const refreshMatches = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MatchProvider.useCallback[refreshMatches]\": async ()=>{\n            setIsLoading(true);\n            try {\n                // Simulate API call to refresh matches\n                await new Promise({\n                    \"MatchProvider.useCallback[refreshMatches]\": (resolve)=>setTimeout(resolve, 1000)\n                }[\"MatchProvider.useCallback[refreshMatches]\"]);\n                // In real app, this would fetch from API\n                // For now, just update lastInteraction for existing matches\n                setMatches({\n                    \"MatchProvider.useCallback[refreshMatches]\": (prev)=>prev.map({\n                            \"MatchProvider.useCallback[refreshMatches]\": (match)=>({\n                                    ...match,\n                                    lastInteraction: new Date().toISOString()\n                                })\n                        }[\"MatchProvider.useCallback[refreshMatches]\"])\n                }[\"MatchProvider.useCallback[refreshMatches]\"]);\n            } catch (error) {\n                console.error('Error refreshing matches:', error);\n                showError('Ошибка при обновлении матчей');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"MatchProvider.useCallback[refreshMatches]\"], [\n        showError\n    ]);\n    const value = {\n        matches,\n        isLoading,\n        createMatch,\n        unmatch,\n        getActiveMatches,\n        hasActiveMatch,\n        getMatchById,\n        refreshMatches\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MatchContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\MatchProvider.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\nconst useMatch = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(MatchContext);\n    if (context === undefined) {\n        throw new Error('useMatch must be used within a MatchProvider');\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/MatchProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/NotificationsProvider.tsx":
/*!*************************************************!*\
  !*** ./src/providers/NotificationsProvider.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationsProvider: () => (/* binding */ NotificationsProvider),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__]);\nreact_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst NotificationsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst NotificationsProvider = ({ children })=>{\n    const showNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showNotification]\": (message, type = 'info', options)=>{\n            const toastOptions = {\n                duration: options?.duration || 4000,\n                position: options?.position || 'top-right'\n            };\n            switch(type){\n                case 'success':\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(message, toastOptions);\n                    break;\n                case 'error':\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].error(message, toastOptions);\n                    break;\n                case 'warning':\n                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(message, {\n                        ...toastOptions,\n                        icon: '⚠️'\n                    });\n                    break;\n                case 'info':\n                default:\n                    (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(message, {\n                        ...toastOptions,\n                        icon: 'ℹ️'\n                    });\n                    break;\n            }\n        }\n    }[\"NotificationsProvider.useCallback[showNotification]\"], []);\n    const showSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showSuccess]\": (message, options)=>{\n            showNotification(message, 'success', options);\n        }\n    }[\"NotificationsProvider.useCallback[showSuccess]\"], [\n        showNotification\n    ]);\n    const showError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showError]\": (message, options)=>{\n            showNotification(message, 'error', options);\n        }\n    }[\"NotificationsProvider.useCallback[showError]\"], [\n        showNotification\n    ]);\n    const showInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showInfo]\": (message, options)=>{\n            showNotification(message, 'info', options);\n        }\n    }[\"NotificationsProvider.useCallback[showInfo]\"], [\n        showNotification\n    ]);\n    const showWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[showWarning]\": (message, options)=>{\n            showNotification(message, 'warning', options);\n        }\n    }[\"NotificationsProvider.useCallback[showWarning]\"], [\n        showNotification\n    ]);\n    const clearAll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationsProvider.useCallback[clearAll]\": ()=>{\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"].dismiss();\n        }\n    }[\"NotificationsProvider.useCallback[clearAll]\"], []);\n    const value = {\n        showNotification,\n        showSuccess,\n        showError,\n        showInfo,\n        showWarning,\n        clearAll\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NotificationsContext.Provider, {\n        value: value,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                reverseOrder: false,\n                gutter: 8,\n                containerClassName: \"\",\n                containerStyle: {},\n                toastOptions: {\n                    // Default options for all toasts\n                    duration: 4000,\n                    style: {\n                        background: '#363636',\n                        color: '#fff'\n                    },\n                    // Success\n                    success: {\n                        duration: 3000,\n                        style: {\n                            background: '#059669',\n                            color: '#fff'\n                        }\n                    },\n                    // Error\n                    error: {\n                        duration: 5000,\n                        style: {\n                            background: '#DC2626',\n                            color: '#fff'\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\NotificationsProvider.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\NotificationsProvider.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\nconst useNotifications = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(NotificationsContext);\n    if (context === undefined) {\n        throw new Error('useNotifications must be used within a NotificationsProvider');\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wcm92aWRlcnMvTm90aWZpY2F0aW9uc1Byb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUEyRjtBQUMxQztBQWtCakQsTUFBTU0scUNBQXVCTCxvREFBYUEsQ0FBdUNNO0FBTTFFLE1BQU1DLHdCQUE4RCxDQUFDLEVBQUVDLFFBQVEsRUFBRTtJQUN0RixNQUFNQyxtQkFBbUJQLGtEQUFXQTsrREFDbEMsQ0FBQ1EsU0FBaUJDLE9BQXlCLE1BQU0sRUFBRUM7WUFDakQsTUFBTUMsZUFBZTtnQkFDbkJDLFVBQVVGLFNBQVNFLFlBQVk7Z0JBQy9CQyxVQUFVSCxTQUFTRyxZQUFZO1lBQ2pDO1lBRUEsT0FBUUo7Z0JBQ04sS0FBSztvQkFDSFIsK0RBQWEsQ0FBQ08sU0FBU0c7b0JBQ3ZCO2dCQUNGLEtBQUs7b0JBQ0hWLDZEQUFXLENBQUNPLFNBQVNHO29CQUNyQjtnQkFDRixLQUFLO29CQUNIViwyREFBS0EsQ0FBQ08sU0FBUzt3QkFDYixHQUFHRyxZQUFZO3dCQUNmSyxNQUFNO29CQUNSO29CQUNBO2dCQUNGLEtBQUs7Z0JBQ0w7b0JBQ0VmLDJEQUFLQSxDQUFDTyxTQUFTO3dCQUNiLEdBQUdHLFlBQVk7d0JBQ2ZLLE1BQU07b0JBQ1I7b0JBQ0E7WUFDSjtRQUNGOzhEQUNBLEVBQUU7SUFHSixNQUFNQyxjQUFjakIsa0RBQVdBOzBEQUM3QixDQUFDUSxTQUFpQkU7WUFDaEJILGlCQUFpQkMsU0FBUyxXQUFXRTtRQUN2Qzt5REFDQTtRQUFDSDtLQUFpQjtJQUdwQixNQUFNVyxZQUFZbEIsa0RBQVdBO3dEQUMzQixDQUFDUSxTQUFpQkU7WUFDaEJILGlCQUFpQkMsU0FBUyxTQUFTRTtRQUNyQzt1REFDQTtRQUFDSDtLQUFpQjtJQUdwQixNQUFNWSxXQUFXbkIsa0RBQVdBO3VEQUMxQixDQUFDUSxTQUFpQkU7WUFDaEJILGlCQUFpQkMsU0FBUyxRQUFRRTtRQUNwQztzREFDQTtRQUFDSDtLQUFpQjtJQUdwQixNQUFNYSxjQUFjcEIsa0RBQVdBOzBEQUM3QixDQUFDUSxTQUFpQkU7WUFDaEJILGlCQUFpQkMsU0FBUyxXQUFXRTtRQUN2Qzt5REFDQTtRQUFDSDtLQUFpQjtJQUdwQixNQUFNYyxXQUFXckIsa0RBQVdBO3VEQUFDO1lBQzNCQywrREFBYTtRQUNmO3NEQUFHLEVBQUU7SUFFTCxNQUFNc0IsUUFBa0M7UUFDdENoQjtRQUNBVTtRQUNBQztRQUNBQztRQUNBQztRQUNBQztJQUNGO0lBRUEscUJBQ0UsOERBQUNsQixxQkFBcUJxQixRQUFRO1FBQUNELE9BQU9BOztZQUNuQ2pCOzBCQUNELDhEQUFDSixvREFBT0E7Z0JBQ05XLFVBQVM7Z0JBQ1RZLGNBQWM7Z0JBQ2RDLFFBQVE7Z0JBQ1JDLG9CQUFtQjtnQkFDbkJDLGdCQUFnQixDQUFDO2dCQUNqQmpCLGNBQWM7b0JBQ1osaUNBQWlDO29CQUNqQ0MsVUFBVTtvQkFDVmlCLE9BQU87d0JBQ0xDLFlBQVk7d0JBQ1pDLE9BQU87b0JBQ1Q7b0JBQ0EsVUFBVTtvQkFDVmpCLFNBQVM7d0JBQ1BGLFVBQVU7d0JBQ1ZpQixPQUFPOzRCQUNMQyxZQUFZOzRCQUNaQyxPQUFPO3dCQUNUO29CQUNGO29CQUNBLFFBQVE7b0JBQ1JoQixPQUFPO3dCQUNMSCxVQUFVO3dCQUNWaUIsT0FBTzs0QkFDTEMsWUFBWTs0QkFDWkMsT0FBTzt3QkFDVDtvQkFDRjtnQkFDRjs7Ozs7Ozs7Ozs7O0FBSVIsRUFBRTtBQUVLLE1BQU1DLG1CQUFtQjtJQUM5QixNQUFNQyxVQUFVbEMsaURBQVVBLENBQUNJO0lBQzNCLElBQUk4QixZQUFZN0IsV0FBVztRQUN6QixNQUFNLElBQUk4QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcc3JjXFxwcm92aWRlcnNcXE5vdGlmaWNhdGlvbnNQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHRvYXN0LCB7IFRvYXN0ZXIgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuXG5leHBvcnQgdHlwZSBOb3RpZmljYXRpb25UeXBlID0gJ3N1Y2Nlc3MnIHwgJ2Vycm9yJyB8ICdpbmZvJyB8ICd3YXJuaW5nJztcblxuaW50ZXJmYWNlIE5vdGlmaWNhdGlvbk9wdGlvbnMge1xuICBkdXJhdGlvbj86IG51bWJlcjtcbiAgcG9zaXRpb24/OiAndG9wLWxlZnQnIHwgJ3RvcC1jZW50ZXInIHwgJ3RvcC1yaWdodCcgfCAnYm90dG9tLWxlZnQnIHwgJ2JvdHRvbS1jZW50ZXInIHwgJ2JvdHRvbS1yaWdodCc7XG59XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb25zQ29udGV4dFR5cGUge1xuICBzaG93Tm90aWZpY2F0aW9uOiAobWVzc2FnZTogc3RyaW5nLCB0eXBlPzogTm90aWZpY2F0aW9uVHlwZSwgb3B0aW9ucz86IE5vdGlmaWNhdGlvbk9wdGlvbnMpID0+IHZvaWQ7XG4gIHNob3dTdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogTm90aWZpY2F0aW9uT3B0aW9ucykgPT4gdm9pZDtcbiAgc2hvd0Vycm9yOiAobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogTm90aWZpY2F0aW9uT3B0aW9ucykgPT4gdm9pZDtcbiAgc2hvd0luZm86IChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBOb3RpZmljYXRpb25PcHRpb25zKSA9PiB2b2lkO1xuICBzaG93V2FybmluZzogKG1lc3NhZ2U6IHN0cmluZywgb3B0aW9ucz86IE5vdGlmaWNhdGlvbk9wdGlvbnMpID0+IHZvaWQ7XG4gIGNsZWFyQWxsOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBOb3RpZmljYXRpb25zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8Tm90aWZpY2F0aW9uc0NvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5pbnRlcmZhY2UgTm90aWZpY2F0aW9uc1Byb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgY29uc3QgTm90aWZpY2F0aW9uc1Byb3ZpZGVyOiBSZWFjdC5GQzxOb3RpZmljYXRpb25zUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IHNob3dOb3RpZmljYXRpb24gPSB1c2VDYWxsYmFjayhcbiAgICAobWVzc2FnZTogc3RyaW5nLCB0eXBlOiBOb3RpZmljYXRpb25UeXBlID0gJ2luZm8nLCBvcHRpb25zPzogTm90aWZpY2F0aW9uT3B0aW9ucykgPT4ge1xuICAgICAgY29uc3QgdG9hc3RPcHRpb25zID0ge1xuICAgICAgICBkdXJhdGlvbjogb3B0aW9ucz8uZHVyYXRpb24gfHwgNDAwMCxcbiAgICAgICAgcG9zaXRpb246IG9wdGlvbnM/LnBvc2l0aW9uIHx8ICd0b3AtcmlnaHQnIGFzIGNvbnN0LFxuICAgICAgfTtcblxuICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MobWVzc2FnZSwgdG9hc3RPcHRpb25zKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICAgIHRvYXN0LmVycm9yKG1lc3NhZ2UsIHRvYXN0T3B0aW9ucyk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICAgIHRvYXN0KG1lc3NhZ2UsIHtcbiAgICAgICAgICAgIC4uLnRvYXN0T3B0aW9ucyxcbiAgICAgICAgICAgIGljb246ICfimqDvuI8nLFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdpbmZvJzpcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICB0b2FzdChtZXNzYWdlLCB7XG4gICAgICAgICAgICAuLi50b2FzdE9wdGlvbnMsXG4gICAgICAgICAgICBpY29uOiAn4oS577iPJyxcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9LFxuICAgIFtdXG4gICk7XG5cbiAgY29uc3Qgc2hvd1N1Y2Nlc3MgPSB1c2VDYWxsYmFjayhcbiAgICAobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogTm90aWZpY2F0aW9uT3B0aW9ucykgPT4ge1xuICAgICAgc2hvd05vdGlmaWNhdGlvbihtZXNzYWdlLCAnc3VjY2VzcycsIG9wdGlvbnMpO1xuICAgIH0sXG4gICAgW3Nob3dOb3RpZmljYXRpb25dXG4gICk7XG5cbiAgY29uc3Qgc2hvd0Vycm9yID0gdXNlQ2FsbGJhY2soXG4gICAgKG1lc3NhZ2U6IHN0cmluZywgb3B0aW9ucz86IE5vdGlmaWNhdGlvbk9wdGlvbnMpID0+IHtcbiAgICAgIHNob3dOb3RpZmljYXRpb24obWVzc2FnZSwgJ2Vycm9yJywgb3B0aW9ucyk7XG4gICAgfSxcbiAgICBbc2hvd05vdGlmaWNhdGlvbl1cbiAgKTtcblxuICBjb25zdCBzaG93SW5mbyA9IHVzZUNhbGxiYWNrKFxuICAgIChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBOb3RpZmljYXRpb25PcHRpb25zKSA9PiB7XG4gICAgICBzaG93Tm90aWZpY2F0aW9uKG1lc3NhZ2UsICdpbmZvJywgb3B0aW9ucyk7XG4gICAgfSxcbiAgICBbc2hvd05vdGlmaWNhdGlvbl1cbiAgKTtcblxuICBjb25zdCBzaG93V2FybmluZyA9IHVzZUNhbGxiYWNrKFxuICAgIChtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBOb3RpZmljYXRpb25PcHRpb25zKSA9PiB7XG4gICAgICBzaG93Tm90aWZpY2F0aW9uKG1lc3NhZ2UsICd3YXJuaW5nJywgb3B0aW9ucyk7XG4gICAgfSxcbiAgICBbc2hvd05vdGlmaWNhdGlvbl1cbiAgKTtcblxuICBjb25zdCBjbGVhckFsbCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICB0b2FzdC5kaXNtaXNzKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB2YWx1ZTogTm90aWZpY2F0aW9uc0NvbnRleHRUeXBlID0ge1xuICAgIHNob3dOb3RpZmljYXRpb24sXG4gICAgc2hvd1N1Y2Nlc3MsXG4gICAgc2hvd0Vycm9yLFxuICAgIHNob3dJbmZvLFxuICAgIHNob3dXYXJuaW5nLFxuICAgIGNsZWFyQWxsLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPE5vdGlmaWNhdGlvbnNDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgICA8VG9hc3RlclxuICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgIHJldmVyc2VPcmRlcj17ZmFsc2V9XG4gICAgICAgIGd1dHRlcj17OH1cbiAgICAgICAgY29udGFpbmVyQ2xhc3NOYW1lPVwiXCJcbiAgICAgICAgY29udGFpbmVyU3R5bGU9e3t9fVxuICAgICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgICAvLyBEZWZhdWx0IG9wdGlvbnMgZm9yIGFsbCB0b2FzdHNcbiAgICAgICAgICBkdXJhdGlvbjogNDAwMCxcbiAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJyMzNjM2MzYnLFxuICAgICAgICAgICAgY29sb3I6ICcjZmZmJyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIC8vIFN1Y2Nlc3NcbiAgICAgICAgICBzdWNjZXNzOiB7XG4gICAgICAgICAgICBkdXJhdGlvbjogMzAwMCxcbiAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjMDU5NjY5JyxcbiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgICAvLyBFcnJvclxuICAgICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgICBkdXJhdGlvbjogNTAwMCxcbiAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjREMyNjI2JyxcbiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgPC9Ob3RpZmljYXRpb25zQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VOb3RpZmljYXRpb25zID0gKCk6IE5vdGlmaWNhdGlvbnNDb250ZXh0VHlwZSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KE5vdGlmaWNhdGlvbnNDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlTm90aWZpY2F0aW9ucyBtdXN0IGJlIHVzZWQgd2l0aGluIGEgTm90aWZpY2F0aW9uc1Byb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VDYWxsYmFjayIsInRvYXN0IiwiVG9hc3RlciIsIk5vdGlmaWNhdGlvbnNDb250ZXh0IiwidW5kZWZpbmVkIiwiTm90aWZpY2F0aW9uc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJzaG93Tm90aWZpY2F0aW9uIiwibWVzc2FnZSIsInR5cGUiLCJvcHRpb25zIiwidG9hc3RPcHRpb25zIiwiZHVyYXRpb24iLCJwb3NpdGlvbiIsInN1Y2Nlc3MiLCJlcnJvciIsImljb24iLCJzaG93U3VjY2VzcyIsInNob3dFcnJvciIsInNob3dJbmZvIiwic2hvd1dhcm5pbmciLCJjbGVhckFsbCIsImRpc21pc3MiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwicmV2ZXJzZU9yZGVyIiwiZ3V0dGVyIiwiY29udGFpbmVyQ2xhc3NOYW1lIiwiY29udGFpbmVyU3R5bGUiLCJzdHlsZSIsImJhY2tncm91bmQiLCJjb2xvciIsInVzZU5vdGlmaWNhdGlvbnMiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/NotificationsProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/providers/SettingsProvider.tsx":
/*!********************************************!*\
  !*** ./src/providers/SettingsProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"next-i18next\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_i18next__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/logger */ \"(pages-dir-node)/./src/utils/logger.ts\");\n\n\n\n\nconst defaultSettings = {\n    language: 'ru',\n    notifications: {\n        messages: true,\n        matches: true,\n        likes: true\n    },\n    privacy: {\n        showOnline: true,\n        showLastSeen: true,\n        allowMessages: 'all'\n    },\n    theme: 'system',\n    contactSync: false\n};\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst SETTINGS_STORAGE_KEY = 'likes-love-settings';\nconst SettingsProvider = ({ children })=>{\n    const { i18n } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"SettingsProvider.useState\": ()=>{\n            // Проверка на SSR\n            if (true) {\n                return defaultSettings;\n            }\n            try {\n                const stored = localStorage.getItem(SETTINGS_STORAGE_KEY);\n                return stored ? {\n                    ...defaultSettings,\n                    ...JSON.parse(stored)\n                } : defaultSettings;\n            } catch (error) {\n                _utils_logger__WEBPACK_IMPORTED_MODULE_3__.log.error('Ошибка загрузки настроек из localStorage', 'SettingsProvider', error);\n                return defaultSettings;\n            }\n        }\n    }[\"SettingsProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            // Сохранение только на клиенте\n            if (false) {}\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            // Проверка на инициализацию i18n для предотвращения ошибок при SSG\n            if (i18n && typeof i18n.changeLanguage === 'function') {\n                i18n.changeLanguage(settings.language);\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings.language,\n        i18n\n    ]);\n    const updateSettings = (newSettings)=>{\n        setSettings((prev)=>({\n                ...prev,\n                ...newSettings\n            }));\n    };\n    const resetSettings = ()=>{\n        setSettings(defaultSettings);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: {\n            settings,\n            updateSettings,\n            resetSettings\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Cursor\\\\ll.com\\\\install\\\\web\\\\src\\\\providers\\\\SettingsProvider.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSettings = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wcm92aWRlcnMvU2V0dGluZ3NQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUF5RjtBQUMzQztBQUNSO0FBd0J0QyxNQUFNTyxrQkFBNEI7SUFDaENDLFVBQVU7SUFDVkMsZUFBZTtRQUNiQyxVQUFVO1FBQ1ZDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0lBQ0FDLFNBQVM7UUFDUEMsWUFBWTtRQUNaQyxjQUFjO1FBQ2RDLGVBQWU7SUFDakI7SUFDQUMsT0FBTztJQUNQQyxhQUFhO0FBQ2Y7QUFFQSxNQUFNQyxnQ0FBa0JsQixvREFBYUEsQ0FBa0NtQjtBQUV2RSxNQUFNQyx1QkFBdUI7QUFFdEIsTUFBTUMsbUJBQXNELENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQzlFLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUduQiw0REFBY0E7SUFDL0IsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHdkIsK0NBQVFBO3FDQUFXO1lBQ2pELGtCQUFrQjtZQUNsQixJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLE9BQU9JO1lBQ1Q7WUFFQSxJQUFJO2dCQUNGLE1BQU1vQixTQUFTQyxhQUFhQyxPQUFPLENBQUNSO2dCQUNwQyxPQUFPTSxTQUFTO29CQUFFLEdBQUdwQixlQUFlO29CQUFFLEdBQUd1QixLQUFLQyxLQUFLLENBQUNKLE9BQU87Z0JBQUMsSUFBSXBCO1lBQ2xFLEVBQUUsT0FBT3lCLE9BQU87Z0JBQ2QxQiw4Q0FBR0EsQ0FBQzBCLEtBQUssQ0FBQyw0Q0FBNEMsb0JBQW9CQTtnQkFDMUUsT0FBT3pCO1lBQ1Q7UUFDRjs7SUFFQUgsZ0RBQVNBO3NDQUFDO1lBQ1IsK0JBQStCO1lBQy9CLElBQUksS0FBNkIsRUFBRSxFQU1sQztRQUNIO3FDQUFHO1FBQUNxQjtLQUFTO0lBRWJyQixnREFBU0E7c0NBQUM7WUFDUixtRUFBbUU7WUFDbkUsSUFBSW9CLFFBQVEsT0FBT0EsS0FBS1csY0FBYyxLQUFLLFlBQVk7Z0JBQ3JEWCxLQUFLVyxjQUFjLENBQUNWLFNBQVNqQixRQUFRO1lBQ3ZDO1FBQ0Y7cUNBQUc7UUFBQ2lCLFNBQVNqQixRQUFRO1FBQUVnQjtLQUFLO0lBRTVCLE1BQU1ZLGlCQUFpQixDQUFDQztRQUN0QlgsWUFBWVksQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUCxHQUFHRCxXQUFXO1lBQ2hCO0lBQ0Y7SUFFQSxNQUFNRSxnQkFBZ0I7UUFDcEJiLFlBQVluQjtJQUNkO0lBRUEscUJBQ0UsOERBQUNZLGdCQUFnQnFCLFFBQVE7UUFDdkJDLE9BQU87WUFDTGhCO1lBQ0FXO1lBQ0FHO1FBQ0Y7a0JBRUNoQjs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU1tQixjQUFjO0lBQ3pCLE1BQU1DLFVBQVV6QyxpREFBVUEsQ0FBQ2lCO0lBQzNCLElBQUl3QixZQUFZdkIsV0FBVztRQUN6QixNQUFNLElBQUl3QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcc3JjXFxwcm92aWRlcnNcXFNldHRpbmdzUHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ25leHQtaTE4bmV4dCc7XG5pbXBvcnQgeyBsb2cgfSBmcm9tICcuLi91dGlscy9sb2dnZXInO1xuXG5pbnRlcmZhY2UgU2V0dGluZ3Mge1xuICBsYW5ndWFnZTogc3RyaW5nO1xuICBub3RpZmljYXRpb25zOiB7XG4gICAgbWVzc2FnZXM6IGJvb2xlYW47XG4gICAgbWF0Y2hlczogYm9vbGVhbjtcbiAgICBsaWtlczogYm9vbGVhbjtcbiAgfTtcbiAgcHJpdmFjeToge1xuICAgIHNob3dPbmxpbmU6IGJvb2xlYW47XG4gICAgc2hvd0xhc3RTZWVuOiBib29sZWFuO1xuICAgIGFsbG93TWVzc2FnZXM6ICdhbGwnIHwgJ21hdGNoZXMnIHwgJ25vbmUnO1xuICB9O1xuICB0aGVtZTogJ2xpZ2h0JyB8ICdkYXJrJyB8ICdzeXN0ZW0nO1xuICBjb250YWN0U3luYzogYm9vbGVhbjtcbn1cblxuaW50ZXJmYWNlIFNldHRpbmdzQ29udGV4dFR5cGUge1xuICBzZXR0aW5nczogU2V0dGluZ3M7XG4gIHVwZGF0ZVNldHRpbmdzOiAobmV3U2V0dGluZ3M6IFBhcnRpYWw8U2V0dGluZ3M+KSA9PiB2b2lkO1xuICByZXNldFNldHRpbmdzOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBkZWZhdWx0U2V0dGluZ3M6IFNldHRpbmdzID0ge1xuICBsYW5ndWFnZTogJ3J1JyxcbiAgbm90aWZpY2F0aW9uczoge1xuICAgIG1lc3NhZ2VzOiB0cnVlLFxuICAgIG1hdGNoZXM6IHRydWUsXG4gICAgbGlrZXM6IHRydWUsXG4gIH0sXG4gIHByaXZhY3k6IHtcbiAgICBzaG93T25saW5lOiB0cnVlLFxuICAgIHNob3dMYXN0U2VlbjogdHJ1ZSxcbiAgICBhbGxvd01lc3NhZ2VzOiAnYWxsJyxcbiAgfSxcbiAgdGhlbWU6ICdzeXN0ZW0nLFxuICBjb250YWN0U3luYzogZmFsc2UsXG59O1xuXG5jb25zdCBTZXR0aW5nc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFNldHRpbmdzQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmNvbnN0IFNFVFRJTkdTX1NUT1JBR0VfS0VZID0gJ2xpa2VzLWxvdmUtc2V0dGluZ3MnO1xuXG5leHBvcnQgY29uc3QgU2V0dGluZ3NQcm92aWRlcjogUmVhY3QuRkM8eyBjaGlsZHJlbjogUmVhY3ROb2RlIH0+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCB7IGkxOG4gfSA9IHVzZVRyYW5zbGF0aW9uKCk7XG4gIGNvbnN0IFtzZXR0aW5ncywgc2V0U2V0dGluZ3NdID0gdXNlU3RhdGU8U2V0dGluZ3M+KCgpID0+IHtcbiAgICAvLyDQn9GA0L7QstC10YDQutCwINC90LAgU1NSXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm4gZGVmYXVsdFNldHRpbmdzO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShTRVRUSU5HU19TVE9SQUdFX0tFWSk7XG4gICAgICByZXR1cm4gc3RvcmVkID8geyAuLi5kZWZhdWx0U2V0dGluZ3MsIC4uLkpTT04ucGFyc2Uoc3RvcmVkKSB9IDogZGVmYXVsdFNldHRpbmdzO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBsb2cuZXJyb3IoJ9Ce0YjQuNCx0LrQsCDQt9Cw0LPRgNGD0LfQutC4INC90LDRgdGC0YDQvtC10Log0LjQtyBsb2NhbFN0b3JhZ2UnLCAnU2V0dGluZ3NQcm92aWRlcicsIGVycm9yKTtcbiAgICAgIHJldHVybiBkZWZhdWx0U2V0dGluZ3M7XG4gICAgfVxuICB9KTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vINCh0L7RhdGA0LDQvdC10L3QuNC1INGC0L7Qu9GM0LrQviDQvdCwINC60LvQuNC10L3RgtC1XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB0cnkge1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTRVRUSU5HU19TVE9SQUdFX0tFWSwgSlNPTi5zdHJpbmdpZnkoc2V0dGluZ3MpKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGxvZy5lcnJvcign0J7RiNC40LHQutCwINGB0L7RhdGA0LDQvdC10L3QuNGPINC90LDRgdGC0YDQvtC10Log0LIgbG9jYWxTdG9yYWdlJywgJ1NldHRpbmdzUHJvdmlkZXInLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc2V0dGluZ3NdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vINCf0YDQvtCy0LXRgNC60LAg0L3QsCDQuNC90LjRhtC40LDQu9C40LfQsNGG0LjRjiBpMThuINC00LvRjyDQv9GA0LXQtNC+0YLQstGA0LDRidC10L3QuNGPINC+0YjQuNCx0L7QuiDQv9GA0LggU1NHXG4gICAgaWYgKGkxOG4gJiYgdHlwZW9mIGkxOG4uY2hhbmdlTGFuZ3VhZ2UgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgIGkxOG4uY2hhbmdlTGFuZ3VhZ2Uoc2V0dGluZ3MubGFuZ3VhZ2UpO1xuICAgIH1cbiAgfSwgW3NldHRpbmdzLmxhbmd1YWdlLCBpMThuXSk7XG5cbiAgY29uc3QgdXBkYXRlU2V0dGluZ3MgPSAobmV3U2V0dGluZ3M6IFBhcnRpYWw8U2V0dGluZ3M+KSA9PiB7XG4gICAgc2V0U2V0dGluZ3MocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIC4uLm5ld1NldHRpbmdzLFxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCByZXNldFNldHRpbmdzID0gKCkgPT4ge1xuICAgIHNldFNldHRpbmdzKGRlZmF1bHRTZXR0aW5ncyk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8U2V0dGluZ3NDb250ZXh0LlByb3ZpZGVyXG4gICAgICB2YWx1ZT17e1xuICAgICAgICBzZXR0aW5ncyxcbiAgICAgICAgdXBkYXRlU2V0dGluZ3MsXG4gICAgICAgIHJlc2V0U2V0dGluZ3MsXG4gICAgICB9fVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1NldHRpbmdzQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VTZXR0aW5ncyA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoU2V0dGluZ3NDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlU2V0dGluZ3MgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFNldHRpbmdzUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlVHJhbnNsYXRpb24iLCJsb2ciLCJkZWZhdWx0U2V0dGluZ3MiLCJsYW5ndWFnZSIsIm5vdGlmaWNhdGlvbnMiLCJtZXNzYWdlcyIsIm1hdGNoZXMiLCJsaWtlcyIsInByaXZhY3kiLCJzaG93T25saW5lIiwic2hvd0xhc3RTZWVuIiwiYWxsb3dNZXNzYWdlcyIsInRoZW1lIiwiY29udGFjdFN5bmMiLCJTZXR0aW5nc0NvbnRleHQiLCJ1bmRlZmluZWQiLCJTRVRUSU5HU19TVE9SQUdFX0tFWSIsIlNldHRpbmdzUHJvdmlkZXIiLCJjaGlsZHJlbiIsImkxOG4iLCJzZXR0aW5ncyIsInNldFNldHRpbmdzIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwic2V0SXRlbSIsInN0cmluZ2lmeSIsImNoYW5nZUxhbmd1YWdlIiwidXBkYXRlU2V0dGluZ3MiLCJuZXdTZXR0aW5ncyIsInByZXYiLCJyZXNldFNldHRpbmdzIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsInVzZVNldHRpbmdzIiwiY29udGV4dCIsIkVycm9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/providers/SettingsProvider.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/theme-factory.ts":
/*!*************************************!*\
  !*** ./src/styles/theme-factory.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PRESET_THEMES: () => (/* binding */ PRESET_THEMES),\n/* harmony export */   createLikesLoveTheme: () => (/* binding */ createLikesLoveTheme),\n/* harmony export */   createThemeByName: () => (/* binding */ createThemeByName)\n/* harmony export */ });\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(pages-dir-node)/./node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__);\n\n// Утилиты для работы с цветами\nconst lightenColor = (color, amount)=>{\n    const hex = color.replace('#', '');\n    const num = parseInt(hex, 16);\n    const r = Math.min(255, Math.floor((num >> 16) + (255 - (num >> 16)) * amount));\n    const g = Math.min(255, Math.floor((num >> 8 & 0x00FF) + (255 - (num >> 8 & 0x00FF)) * amount));\n    const b = Math.min(255, Math.floor((num & 0x0000FF) + (255 - (num & 0x0000FF)) * amount));\n    return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;\n};\nconst darkenColor = (color, amount)=>{\n    const hex = color.replace('#', '');\n    const num = parseInt(hex, 16);\n    const r = Math.max(0, Math.floor((num >> 16) * (1 - amount)));\n    const g = Math.max(0, Math.floor((num >> 8 & 0x00FF) * (1 - amount)));\n    const b = Math.max(0, Math.floor((num & 0x0000FF) * (1 - amount)));\n    return `#${(r << 16 | g << 8 | b).toString(16).padStart(6, '0')}`;\n};\n// Функция для определения контрастного цвета текста\nconst getContrastText = (backgroundColor)=>{\n    const hex = backgroundColor.replace('#', '');\n    const r = parseInt(hex.substr(0, 2), 16);\n    const g = parseInt(hex.substr(2, 2), 16);\n    const b = parseInt(hex.substr(4, 2), 16);\n    // Вычисляем яркость по формуле W3C\n    const brightness = (r * 299 + g * 587 + b * 114) / 1000;\n    // Если цвет светлый, используем темный текст, иначе светлый\n    return brightness > 128 ? '#2d3748' : '#ffffff';\n};\n// Создание темы на основе конфигурации\nconst createLikesLoveTheme = (config, customizations)=>{\n    const primaryColor = customizations?.primaryColor || config.primaryColor;\n    const secondaryColor = customizations?.secondaryColor || config.secondaryColor;\n    const themeOptions = {\n        palette: {\n            mode: config.id === 'dark' ? 'dark' : 'light',\n            primary: {\n                main: primaryColor,\n                light: lightenColor(primaryColor, 0.3),\n                dark: darkenColor(primaryColor, 0.3),\n                contrastText: getContrastText(primaryColor)\n            },\n            secondary: {\n                main: secondaryColor,\n                light: lightenColor(secondaryColor, 0.3),\n                dark: darkenColor(secondaryColor, 0.3),\n                contrastText: getContrastText(secondaryColor)\n            },\n            background: {\n                default: config.colors.background,\n                paper: config.colors.surface\n            },\n            text: {\n                primary: config.colors.text.primary,\n                secondary: config.colors.text.secondary\n            },\n            gradient: {\n                primary: config.gradients.primary,\n                secondary: config.gradients.secondary,\n                accent: config.gradients.accent\n            },\n            success: {\n                main: '#4caf50',\n                light: '#81c784',\n                dark: '#388e3c'\n            },\n            error: {\n                main: '#f44336',\n                light: '#e57373',\n                dark: '#d32f2f'\n            },\n            warning: {\n                main: '#ff9800',\n                light: '#ffb74d',\n                dark: '#f57c00'\n            },\n            info: {\n                main: '#2196f3',\n                light: '#64b5f6',\n                dark: '#1976d2'\n            }\n        },\n        typography: {\n            fontFamily: '\"Playfair Display\", \"Georgia\", \"Times New Roman\", serif',\n            h1: {\n                fontSize: config.typography.fontSizes.xxl,\n                fontWeight: config.typography.fontWeights.bold,\n                lineHeight: 1.3,\n                letterSpacing: '-0.02em',\n                marginBottom: '1.5rem'\n            },\n            h2: {\n                fontSize: config.typography.fontSizes.xl,\n                fontWeight: config.typography.fontWeights.bold,\n                lineHeight: 1.4,\n                letterSpacing: '-0.01em',\n                marginBottom: '1.25rem'\n            },\n            h3: {\n                fontSize: config.typography.fontSizes.lg,\n                fontWeight: config.typography.fontWeights.semibold,\n                lineHeight: 1.5,\n                marginBottom: '1rem'\n            },\n            h4: {\n                fontSize: config.typography.fontSizes.md,\n                fontWeight: config.typography.fontWeights.semibold,\n                lineHeight: 1.5,\n                marginBottom: '0.875rem'\n            },\n            h5: {\n                fontSize: config.typography.fontSizes.sm,\n                fontWeight: config.typography.fontWeights.medium,\n                lineHeight: 1.6,\n                marginBottom: '0.75rem'\n            },\n            h6: {\n                fontSize: config.typography.fontSizes.xs,\n                fontWeight: config.typography.fontWeights.medium,\n                lineHeight: 1.6,\n                marginBottom: '0.625rem'\n            },\n            body1: {\n                fontSize: config.typography.fontSizes.md,\n                fontWeight: config.typography.fontWeights.normal,\n                lineHeight: 1.7,\n                marginBottom: '1rem'\n            },\n            body2: {\n                fontSize: config.typography.fontSizes.sm,\n                fontWeight: config.typography.fontWeights.normal,\n                lineHeight: 1.7,\n                marginBottom: '0.875rem'\n            },\n            button: {\n                fontSize: config.typography.fontSizes.sm,\n                fontWeight: config.typography.fontWeights.semibold,\n                textTransform: 'none',\n                letterSpacing: '0.02em',\n                lineHeight: 1.5\n            },\n            caption: {\n                fontSize: config.typography.fontSizes.xs,\n                fontWeight: config.typography.fontWeights.normal,\n                lineHeight: 1.5\n            }\n        },\n        spacing: config.spacing.sm,\n        shape: {\n            borderRadius: config.borderRadius.md\n        },\n        components: {\n            MuiButton: {\n                styleOverrides: {\n                    root: {\n                        borderRadius: config.borderRadius.md,\n                        textTransform: 'none',\n                        fontWeight: config.typography.fontWeights.semibold,\n                        padding: `${config.spacing.md}px ${config.spacing.lg}px`,\n                        fontSize: config.typography.fontSizes.sm,\n                        minHeight: '48px',\n                        boxShadow: 'none',\n                        transition: 'all 0.2s ease-in-out',\n                        '&:hover': {\n                            boxShadow: config.shadows.md,\n                            transform: 'translateY(-1px)'\n                        }\n                    },\n                    contained: {\n                        background: config.gradients.primary,\n                        color: '#ffffff !important',\n                        fontWeight: 600,\n                        fontSize: config.typography.fontSizes.sm,\n                        textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n                        '&:hover': {\n                            background: config.gradients.primary,\n                            filter: 'brightness(1.1)',\n                            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',\n                            color: '#ffffff !important'\n                        }\n                    },\n                    outlined: {\n                        borderWidth: 2,\n                        color: primaryColor,\n                        borderColor: primaryColor,\n                        backgroundColor: 'rgba(255,255,255,0.9)',\n                        fontSize: config.typography.fontSizes.sm,\n                        '&:hover': {\n                            borderWidth: 2,\n                            backgroundColor: primaryColor,\n                            color: '#ffffff !important'\n                        }\n                    }\n                }\n            },\n            MuiCard: {\n                styleOverrides: {\n                    root: {\n                        borderRadius: config.borderRadius.lg,\n                        boxShadow: config.shadows.md,\n                        border: `1px solid ${config.colors.border}`,\n                        transition: 'all 0.2s ease-in-out',\n                        '&:hover': {\n                            boxShadow: config.shadows.lg,\n                            transform: 'translateY(-2px)'\n                        }\n                    }\n                }\n            },\n            MuiTextField: {\n                styleOverrides: {\n                    root: {\n                        '& .MuiOutlinedInput-root': {\n                            borderRadius: config.borderRadius.md,\n                            fontSize: config.typography.fontSizes.sm,\n                            minHeight: '56px',\n                            '& input': {\n                                padding: `${config.spacing.md}px ${config.spacing.md}px`,\n                                fontSize: config.typography.fontSizes.sm,\n                                lineHeight: 1.5\n                            },\n                            '&:hover .MuiOutlinedInput-notchedOutline': {\n                                borderColor: primaryColor\n                            },\n                            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                                borderColor: primaryColor,\n                                borderWidth: 2\n                            }\n                        },\n                        '& .MuiInputLabel-root': {\n                            fontSize: config.typography.fontSizes.sm,\n                            lineHeight: 1.5\n                        }\n                    }\n                }\n            },\n            MuiChip: {\n                styleOverrides: {\n                    root: {\n                        borderRadius: config.borderRadius.full,\n                        fontWeight: config.typography.fontWeights.medium\n                    },\n                    filled: {\n                        background: config.gradients.accent,\n                        color: '#ffffff'\n                    }\n                }\n            },\n            MuiDialog: {\n                styleOverrides: {\n                    paper: {\n                        borderRadius: config.borderRadius.lg,\n                        boxShadow: config.shadows.lg\n                    }\n                }\n            },\n            MuiAppBar: {\n                styleOverrides: {\n                    root: {\n                        background: config.gradients.primary,\n                        boxShadow: config.shadows.md\n                    }\n                }\n            },\n            MuiFab: {\n                styleOverrides: {\n                    root: {\n                        background: config.gradients.primary,\n                        boxShadow: config.shadows.md,\n                        '&:hover': {\n                            background: config.gradients.primary,\n                            filter: 'brightness(1.1)',\n                            boxShadow: config.shadows.lg\n                        }\n                    }\n                }\n            }\n        },\n        custom: {\n            gradients: {\n                primary: config.gradients.primary,\n                secondary: config.gradients.secondary,\n                accent: config.gradients.accent\n            },\n            shadows: {\n                card: config.shadows.md,\n                button: config.shadows.sm,\n                modal: config.shadows.lg\n            },\n            animations: {\n                transition: 'all 0.2s ease-in-out',\n                hover: 'transform 0.2s ease-in-out'\n            },\n            dating: {\n                likeColor: '#4caf50',\n                passColor: '#f44336',\n                superLikeColor: '#2196f3',\n                matchColor: '#ff9800'\n            }\n        }\n    };\n    return (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__.createTheme)(themeOptions);\n};\n// Предустановленные темы для быстрого доступа\nconst PRESET_THEMES = {\n    likesLove: '#ff6b9d',\n    romantic: '#e91e63',\n    elegant: '#673ab7',\n    modern: '#00bcd4',\n    dark: '#424242'\n};\n// Создание темы по названию\nconst createThemeByName = (themeName)=>{\n    const baseConfig = {\n        id: themeName,\n        name: themeName,\n        displayName: themeName,\n        primaryColor: PRESET_THEMES[themeName],\n        secondaryColor: '#4ecdc4',\n        gradients: {\n            primary: `linear-gradient(135deg, ${PRESET_THEMES[themeName]} 0%, #4ecdc4 100%)`,\n            secondary: 'linear-gradient(135deg, #4ecdc4 0%, #45b7aa 100%)',\n            accent: `linear-gradient(135deg, ${PRESET_THEMES[themeName]} 0%, ${lightenColor(PRESET_THEMES[themeName], 0.2)} 100%)`\n        },\n        colors: {\n            background: themeName === 'dark' ? '#121212' : '#ffffff',\n            surface: themeName === 'dark' ? '#1e1e1e' : '#f8f9fa',\n            text: {\n                primary: themeName === 'dark' ? '#ffffff' : '#2d3748',\n                secondary: themeName === 'dark' ? '#b0b0b0' : '#718096',\n                accent: PRESET_THEMES[themeName]\n            },\n            border: themeName === 'dark' ? '#333333' : '#e2e8f0',\n            shadow: themeName === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'\n        },\n        spacing: {\n            xs: 4,\n            sm: 8,\n            md: 16,\n            lg: 24,\n            xl: 32\n        },\n        borderRadius: {\n            sm: 8,\n            md: 12,\n            lg: 16,\n            full: 9999\n        },\n        typography: {\n            fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',\n            fontSizes: {\n                xs: 12,\n                sm: 14,\n                md: 16,\n                lg: 18,\n                xl: 24,\n                xxl: 32\n            },\n            fontWeights: {\n                normal: 400,\n                medium: 500,\n                semibold: 600,\n                bold: 700\n            }\n        },\n        shadows: {\n            sm: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',\n            md: '0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1)',\n            lg: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'\n        }\n    };\n    return createLikesLoveTheme(baseConfig);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9zdHlsZXMvdGhlbWUtZmFjdG9yeS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3RTtBQWF4RSwrQkFBK0I7QUFDL0IsTUFBTUMsZUFBZSxDQUFDQyxPQUFlQztJQUNuQyxNQUFNQyxNQUFNRixNQUFNRyxPQUFPLENBQUMsS0FBSztJQUMvQixNQUFNQyxNQUFNQyxTQUFTSCxLQUFLO0lBQzFCLE1BQU1JLElBQUlDLEtBQUtDLEdBQUcsQ0FBQyxLQUFLRCxLQUFLRSxLQUFLLENBQUMsQ0FBQ0wsT0FBTyxFQUFDLElBQUssQ0FBQyxNQUFPQSxDQUFBQSxPQUFPLEVBQUMsQ0FBQyxJQUFLSDtJQUN2RSxNQUFNUyxJQUFJSCxLQUFLQyxHQUFHLENBQUMsS0FBS0QsS0FBS0UsS0FBSyxDQUFDLENBQUMsT0FBUSxJQUFLLE1BQUssSUFBSyxDQUFDLE1BQU8sUUFBUSxJQUFLLE1BQUssQ0FBQyxJQUFLUjtJQUMzRixNQUFNVSxJQUFJSixLQUFLQyxHQUFHLENBQUMsS0FBS0QsS0FBS0UsS0FBSyxDQUFDLENBQUNMLE1BQU0sUUFBTyxJQUFLLENBQUMsTUFBT0EsQ0FBQUEsTUFBTSxRQUFPLENBQUMsSUFBS0g7SUFDakYsT0FBTyxDQUFDLENBQUMsRUFBRSxDQUFDSyxLQUFLLEtBQUtJLEtBQUssSUFBSUMsQ0FBQUEsRUFBR0MsUUFBUSxDQUFDLElBQUlDLFFBQVEsQ0FBQyxHQUFHLE1BQU07QUFDbkU7QUFFQSxNQUFNQyxjQUFjLENBQUNkLE9BQWVDO0lBQ2xDLE1BQU1DLE1BQU1GLE1BQU1HLE9BQU8sQ0FBQyxLQUFLO0lBQy9CLE1BQU1DLE1BQU1DLFNBQVNILEtBQUs7SUFDMUIsTUFBTUksSUFBSUMsS0FBS1EsR0FBRyxDQUFDLEdBQUdSLEtBQUtFLEtBQUssQ0FBQyxDQUFDTCxPQUFPLEVBQUMsSUFBTSxLQUFJSCxNQUFLO0lBQ3pELE1BQU1TLElBQUlILEtBQUtRLEdBQUcsQ0FBQyxHQUFHUixLQUFLRSxLQUFLLENBQUMsQ0FBQyxPQUFRLElBQUssTUFBSyxJQUFNLEtBQUlSLE1BQUs7SUFDbkUsTUFBTVUsSUFBSUosS0FBS1EsR0FBRyxDQUFDLEdBQUdSLEtBQUtFLEtBQUssQ0FBQyxDQUFDTCxNQUFNLFFBQU8sSUFBTSxLQUFJSCxNQUFLO0lBQzlELE9BQU8sQ0FBQyxDQUFDLEVBQUUsQ0FBQ0ssS0FBSyxLQUFLSSxLQUFLLElBQUlDLENBQUFBLEVBQUdDLFFBQVEsQ0FBQyxJQUFJQyxRQUFRLENBQUMsR0FBRyxNQUFNO0FBQ25FO0FBRUEsb0RBQW9EO0FBQ3BELE1BQU1HLGtCQUFrQixDQUFDQztJQUN2QixNQUFNZixNQUFNZSxnQkFBZ0JkLE9BQU8sQ0FBQyxLQUFLO0lBQ3pDLE1BQU1HLElBQUlELFNBQVNILElBQUlnQixNQUFNLENBQUMsR0FBRyxJQUFJO0lBQ3JDLE1BQU1SLElBQUlMLFNBQVNILElBQUlnQixNQUFNLENBQUMsR0FBRyxJQUFJO0lBQ3JDLE1BQU1QLElBQUlOLFNBQVNILElBQUlnQixNQUFNLENBQUMsR0FBRyxJQUFJO0lBRXJDLG1DQUFtQztJQUNuQyxNQUFNQyxhQUFhLENBQUNiLElBQUksTUFBTUksSUFBSSxNQUFNQyxJQUFJLEdBQUUsSUFBSztJQUVuRCw0REFBNEQ7SUFDNUQsT0FBT1EsYUFBYSxNQUFNLFlBQVk7QUFDeEM7QUF1RUEsdUNBQXVDO0FBQ2hDLE1BQU1DLHVCQUF1QixDQUNsQ0MsUUFDQUM7SUFFQSxNQUFNQyxlQUFlRCxnQkFBZ0JDLGdCQUFnQkYsT0FBT0UsWUFBWTtJQUN4RSxNQUFNQyxpQkFBaUJGLGdCQUFnQkUsa0JBQWtCSCxPQUFPRyxjQUFjO0lBRTlFLE1BQU1DLGVBQTZCO1FBQ2pDQyxTQUFTO1lBQ1BDLE1BQU1OLE9BQU9PLEVBQUUsS0FBSyxTQUFTLFNBQVM7WUFDdENDLFNBQVM7Z0JBQ1BDLE1BQU1QO2dCQUNOUSxPQUFPaEMsYUFBYXdCLGNBQWM7Z0JBQ2xDUyxNQUFNbEIsWUFBWVMsY0FBYztnQkFDaENVLGNBQWNqQixnQkFBZ0JPO1lBQ2hDO1lBQ0FXLFdBQVc7Z0JBQ1RKLE1BQU1OO2dCQUNOTyxPQUFPaEMsYUFBYXlCLGdCQUFnQjtnQkFDcENRLE1BQU1sQixZQUFZVSxnQkFBZ0I7Z0JBQ2xDUyxjQUFjakIsZ0JBQWdCUTtZQUNoQztZQUNBVyxZQUFZO2dCQUNWQyxTQUFTZixPQUFPZ0IsTUFBTSxDQUFDRixVQUFVO2dCQUNqQ0csT0FBT2pCLE9BQU9nQixNQUFNLENBQUNFLE9BQU87WUFDOUI7WUFDQUMsTUFBTTtnQkFDSlgsU0FBU1IsT0FBT2dCLE1BQU0sQ0FBQ0csSUFBSSxDQUFDWCxPQUFPO2dCQUNuQ0ssV0FBV2IsT0FBT2dCLE1BQU0sQ0FBQ0csSUFBSSxDQUFDTixTQUFTO1lBQ3pDO1lBQ0FPLFVBQVU7Z0JBQ1JaLFNBQVNSLE9BQU9xQixTQUFTLENBQUNiLE9BQU87Z0JBQ2pDSyxXQUFXYixPQUFPcUIsU0FBUyxDQUFDUixTQUFTO2dCQUNyQ1MsUUFBUXRCLE9BQU9xQixTQUFTLENBQUNDLE1BQU07WUFDakM7WUFDQUMsU0FBUztnQkFDUGQsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsTUFBTTtZQUNSO1lBQ0FhLE9BQU87Z0JBQ0xmLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE1BQU07WUFDUjtZQUNBYyxTQUFTO2dCQUNQaEIsTUFBTTtnQkFDTkMsT0FBTztnQkFDUEMsTUFBTTtZQUNSO1lBQ0FlLE1BQU07Z0JBQ0pqQixNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxNQUFNO1lBQ1I7UUFDRjtRQUNBZ0IsWUFBWTtZQUNWQyxZQUFZO1lBQ1pDLElBQUk7Z0JBQ0ZDLFVBQVU5QixPQUFPMkIsVUFBVSxDQUFDSSxTQUFTLENBQUNDLEdBQUc7Z0JBQ3pDQyxZQUFZakMsT0FBTzJCLFVBQVUsQ0FBQ08sV0FBVyxDQUFDQyxJQUFJO2dCQUM5Q0MsWUFBWTtnQkFDWkMsZUFBZTtnQkFDZkMsY0FBYztZQUNoQjtZQUNBQyxJQUFJO2dCQUNGVCxVQUFVOUIsT0FBTzJCLFVBQVUsQ0FBQ0ksU0FBUyxDQUFDUyxFQUFFO2dCQUN4Q1AsWUFBWWpDLE9BQU8yQixVQUFVLENBQUNPLFdBQVcsQ0FBQ0MsSUFBSTtnQkFDOUNDLFlBQVk7Z0JBQ1pDLGVBQWU7Z0JBQ2ZDLGNBQWM7WUFDaEI7WUFDQUcsSUFBSTtnQkFDRlgsVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ1csRUFBRTtnQkFDeENULFlBQVlqQyxPQUFPMkIsVUFBVSxDQUFDTyxXQUFXLENBQUNTLFFBQVE7Z0JBQ2xEUCxZQUFZO2dCQUNaRSxjQUFjO1lBQ2hCO1lBQ0FNLElBQUk7Z0JBQ0ZkLFVBQVU5QixPQUFPMkIsVUFBVSxDQUFDSSxTQUFTLENBQUNjLEVBQUU7Z0JBQ3hDWixZQUFZakMsT0FBTzJCLFVBQVUsQ0FBQ08sV0FBVyxDQUFDUyxRQUFRO2dCQUNsRFAsWUFBWTtnQkFDWkUsY0FBYztZQUNoQjtZQUNBUSxJQUFJO2dCQUNGaEIsVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ2dCLEVBQUU7Z0JBQ3hDZCxZQUFZakMsT0FBTzJCLFVBQVUsQ0FBQ08sV0FBVyxDQUFDYyxNQUFNO2dCQUNoRFosWUFBWTtnQkFDWkUsY0FBYztZQUNoQjtZQUNBVyxJQUFJO2dCQUNGbkIsVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ21CLEVBQUU7Z0JBQ3hDakIsWUFBWWpDLE9BQU8yQixVQUFVLENBQUNPLFdBQVcsQ0FBQ2MsTUFBTTtnQkFDaERaLFlBQVk7Z0JBQ1pFLGNBQWM7WUFDaEI7WUFDQWEsT0FBTztnQkFDTHJCLFVBQVU5QixPQUFPMkIsVUFBVSxDQUFDSSxTQUFTLENBQUNjLEVBQUU7Z0JBQ3hDWixZQUFZakMsT0FBTzJCLFVBQVUsQ0FBQ08sV0FBVyxDQUFDa0IsTUFBTTtnQkFDaERoQixZQUFZO2dCQUNaRSxjQUFjO1lBQ2hCO1lBQ0FlLE9BQU87Z0JBQ0x2QixVQUFVOUIsT0FBTzJCLFVBQVUsQ0FBQ0ksU0FBUyxDQUFDZ0IsRUFBRTtnQkFDeENkLFlBQVlqQyxPQUFPMkIsVUFBVSxDQUFDTyxXQUFXLENBQUNrQixNQUFNO2dCQUNoRGhCLFlBQVk7Z0JBQ1pFLGNBQWM7WUFDaEI7WUFDQWdCLFFBQVE7Z0JBQ054QixVQUFVOUIsT0FBTzJCLFVBQVUsQ0FBQ0ksU0FBUyxDQUFDZ0IsRUFBRTtnQkFDeENkLFlBQVlqQyxPQUFPMkIsVUFBVSxDQUFDTyxXQUFXLENBQUNTLFFBQVE7Z0JBQ2xEWSxlQUFlO2dCQUNmbEIsZUFBZTtnQkFDZkQsWUFBWTtZQUNkO1lBQ0FvQixTQUFTO2dCQUNQMUIsVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ21CLEVBQUU7Z0JBQ3hDakIsWUFBWWpDLE9BQU8yQixVQUFVLENBQUNPLFdBQVcsQ0FBQ2tCLE1BQU07Z0JBQ2hEaEIsWUFBWTtZQUNkO1FBQ0Y7UUFDQXFCLFNBQVN6RCxPQUFPeUQsT0FBTyxDQUFDVixFQUFFO1FBQzFCVyxPQUFPO1lBQ0xDLGNBQWMzRCxPQUFPMkQsWUFBWSxDQUFDZCxFQUFFO1FBQ3RDO1FBQ0FlLFlBQVk7WUFDVkMsV0FBVztnQkFDVEMsZ0JBQWdCO29CQUNkQyxNQUFNO3dCQUNKSixjQUFjM0QsT0FBTzJELFlBQVksQ0FBQ2QsRUFBRTt3QkFDcENVLGVBQWU7d0JBQ2Z0QixZQUFZakMsT0FBTzJCLFVBQVUsQ0FBQ08sV0FBVyxDQUFDUyxRQUFRO3dCQUNsRHFCLFNBQVMsR0FBR2hFLE9BQU95RCxPQUFPLENBQUNaLEVBQUUsQ0FBQyxHQUFHLEVBQUU3QyxPQUFPeUQsT0FBTyxDQUFDZixFQUFFLENBQUMsRUFBRSxDQUFDO3dCQUN4RFosVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ2dCLEVBQUU7d0JBQ3hDa0IsV0FBVzt3QkFDWEMsV0FBVzt3QkFDWEMsWUFBWTt3QkFDWixXQUFXOzRCQUNURCxXQUFXbEUsT0FBT29FLE9BQU8sQ0FBQ3ZCLEVBQUU7NEJBQzVCd0IsV0FBVzt3QkFDYjtvQkFDRjtvQkFDQUMsV0FBVzt3QkFDVHhELFlBQVlkLE9BQU9xQixTQUFTLENBQUNiLE9BQU87d0JBQ3BDN0IsT0FBTzt3QkFDUHNELFlBQVk7d0JBQ1pILFVBQVU5QixPQUFPMkIsVUFBVSxDQUFDSSxTQUFTLENBQUNnQixFQUFFO3dCQUN4Q3dCLFlBQVk7d0JBQ1osV0FBVzs0QkFDVHpELFlBQVlkLE9BQU9xQixTQUFTLENBQUNiLE9BQU87NEJBQ3BDZ0UsUUFBUTs0QkFDUk4sV0FBVzs0QkFDWHZGLE9BQU87d0JBQ1Q7b0JBQ0Y7b0JBQ0E4RixVQUFVO3dCQUNSQyxhQUFhO3dCQUNiL0YsT0FBT3VCO3dCQUNQeUUsYUFBYXpFO3dCQUNiTixpQkFBaUI7d0JBQ2pCa0MsVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ2dCLEVBQUU7d0JBQ3hDLFdBQVc7NEJBQ1QyQixhQUFhOzRCQUNiOUUsaUJBQWlCTTs0QkFDakJ2QixPQUFPO3dCQUNUO29CQUNGO2dCQUNGO1lBQ0Y7WUFDQWlHLFNBQVM7Z0JBQ1BkLGdCQUFnQjtvQkFDZEMsTUFBTTt3QkFDSkosY0FBYzNELE9BQU8yRCxZQUFZLENBQUNqQixFQUFFO3dCQUNwQ3dCLFdBQVdsRSxPQUFPb0UsT0FBTyxDQUFDdkIsRUFBRTt3QkFDNUJnQyxRQUFRLENBQUMsVUFBVSxFQUFFN0UsT0FBT2dCLE1BQU0sQ0FBQzZELE1BQU0sRUFBRTt3QkFDM0NWLFlBQVk7d0JBQ1osV0FBVzs0QkFDVEQsV0FBV2xFLE9BQU9vRSxPQUFPLENBQUMxQixFQUFFOzRCQUM1QjJCLFdBQVc7d0JBQ2I7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUNBUyxjQUFjO2dCQUNaaEIsZ0JBQWdCO29CQUNkQyxNQUFNO3dCQUNKLDRCQUE0Qjs0QkFDMUJKLGNBQWMzRCxPQUFPMkQsWUFBWSxDQUFDZCxFQUFFOzRCQUNwQ2YsVUFBVTlCLE9BQU8yQixVQUFVLENBQUNJLFNBQVMsQ0FBQ2dCLEVBQUU7NEJBQ3hDa0IsV0FBVzs0QkFDWCxXQUFXO2dDQUNURCxTQUFTLEdBQUdoRSxPQUFPeUQsT0FBTyxDQUFDWixFQUFFLENBQUMsR0FBRyxFQUFFN0MsT0FBT3lELE9BQU8sQ0FBQ1osRUFBRSxDQUFDLEVBQUUsQ0FBQztnQ0FDeERmLFVBQVU5QixPQUFPMkIsVUFBVSxDQUFDSSxTQUFTLENBQUNnQixFQUFFO2dDQUN4Q1gsWUFBWTs0QkFDZDs0QkFDQSw0Q0FBNEM7Z0NBQzFDdUMsYUFBYXpFOzRCQUNmOzRCQUNBLGtEQUFrRDtnQ0FDaER5RSxhQUFhekU7Z0NBQ2J3RSxhQUFhOzRCQUNmO3dCQUNGO3dCQUNBLHlCQUF5Qjs0QkFDdkI1QyxVQUFVOUIsT0FBTzJCLFVBQVUsQ0FBQ0ksU0FBUyxDQUFDZ0IsRUFBRTs0QkFDeENYLFlBQVk7d0JBQ2Q7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUNBMkMsU0FBUztnQkFDUGpCLGdCQUFnQjtvQkFDZEMsTUFBTTt3QkFDSkosY0FBYzNELE9BQU8yRCxZQUFZLENBQUNxQixJQUFJO3dCQUN0Qy9DLFlBQVlqQyxPQUFPMkIsVUFBVSxDQUFDTyxXQUFXLENBQUNjLE1BQU07b0JBQ2xEO29CQUNBaUMsUUFBUTt3QkFDTm5FLFlBQVlkLE9BQU9xQixTQUFTLENBQUNDLE1BQU07d0JBQ25DM0MsT0FBTztvQkFDVDtnQkFDRjtZQUNGO1lBQ0F1RyxXQUFXO2dCQUNUcEIsZ0JBQWdCO29CQUNkN0MsT0FBTzt3QkFDTDBDLGNBQWMzRCxPQUFPMkQsWUFBWSxDQUFDakIsRUFBRTt3QkFDcEN3QixXQUFXbEUsT0FBT29FLE9BQU8sQ0FBQzFCLEVBQUU7b0JBQzlCO2dCQUNGO1lBQ0Y7WUFDQXlDLFdBQVc7Z0JBQ1RyQixnQkFBZ0I7b0JBQ2RDLE1BQU07d0JBQ0pqRCxZQUFZZCxPQUFPcUIsU0FBUyxDQUFDYixPQUFPO3dCQUNwQzBELFdBQVdsRSxPQUFPb0UsT0FBTyxDQUFDdkIsRUFBRTtvQkFDOUI7Z0JBQ0Y7WUFDRjtZQUNBdUMsUUFBUTtnQkFDTnRCLGdCQUFnQjtvQkFDZEMsTUFBTTt3QkFDSmpELFlBQVlkLE9BQU9xQixTQUFTLENBQUNiLE9BQU87d0JBQ3BDMEQsV0FBV2xFLE9BQU9vRSxPQUFPLENBQUN2QixFQUFFO3dCQUM1QixXQUFXOzRCQUNUL0IsWUFBWWQsT0FBT3FCLFNBQVMsQ0FBQ2IsT0FBTzs0QkFDcENnRSxRQUFROzRCQUNSTixXQUFXbEUsT0FBT29FLE9BQU8sQ0FBQzFCLEVBQUU7d0JBQzlCO29CQUNGO2dCQUNGO1lBQ0Y7UUFDRjtRQUNBMkMsUUFBUTtZQUNOaEUsV0FBVztnQkFDVGIsU0FBU1IsT0FBT3FCLFNBQVMsQ0FBQ2IsT0FBTztnQkFDakNLLFdBQVdiLE9BQU9xQixTQUFTLENBQUNSLFNBQVM7Z0JBQ3JDUyxRQUFRdEIsT0FBT3FCLFNBQVMsQ0FBQ0MsTUFBTTtZQUNqQztZQUNBOEMsU0FBUztnQkFDUGtCLE1BQU10RixPQUFPb0UsT0FBTyxDQUFDdkIsRUFBRTtnQkFDdkJTLFFBQVF0RCxPQUFPb0UsT0FBTyxDQUFDckIsRUFBRTtnQkFDekJ3QyxPQUFPdkYsT0FBT29FLE9BQU8sQ0FBQzFCLEVBQUU7WUFDMUI7WUFDQThDLFlBQVk7Z0JBQ1ZyQixZQUFZO2dCQUNac0IsT0FBTztZQUNUO1lBQ0FDLFFBQVE7Z0JBQ05DLFdBQVc7Z0JBQ1hDLFdBQVc7Z0JBQ1hDLGdCQUFnQjtnQkFDaEJDLFlBQVk7WUFDZDtRQUNGO0lBQ0Y7SUFFQSxPQUFPckgsaUVBQVdBLENBQUMyQjtBQUNyQixFQUFFO0FBSUYsOENBQThDO0FBQ3ZDLE1BQU0yRixnQkFBZ0I7SUFDM0JDLFdBQVc7SUFDWEMsVUFBVTtJQUNWQyxTQUFTO0lBQ1RDLFFBQVE7SUFDUnhGLE1BQU07QUFDUixFQUFFO0FBRUYsNEJBQTRCO0FBQ3JCLE1BQU15RixvQkFBb0IsQ0FBQ0M7SUFDaEMsTUFBTUMsYUFBYTtRQUNqQi9GLElBQUk4RjtRQUNKRSxNQUFNRjtRQUNORyxhQUFhSDtRQUNibkcsY0FBYzZGLGFBQWEsQ0FBQ00sVUFBVTtRQUN0Q2xHLGdCQUFnQjtRQUNoQmtCLFdBQVc7WUFDVGIsU0FBUyxDQUFDLHdCQUF3QixFQUFFdUYsYUFBYSxDQUFDTSxVQUFVLENBQUMsa0JBQWtCLENBQUM7WUFDaEZ4RixXQUFXO1lBQ1hTLFFBQVEsQ0FBQyx3QkFBd0IsRUFBRXlFLGFBQWEsQ0FBQ00sVUFBVSxDQUFDLEtBQUssRUFBRTNILGFBQWFxSCxhQUFhLENBQUNNLFVBQVUsRUFBRSxLQUFLLE1BQU0sQ0FBQztRQUN4SDtRQUNBckYsUUFBUTtZQUNORixZQUFZdUYsY0FBYyxTQUFTLFlBQVk7WUFDL0NuRixTQUFTbUYsY0FBYyxTQUFTLFlBQVk7WUFDNUNsRixNQUFNO2dCQUNKWCxTQUFTNkYsY0FBYyxTQUFTLFlBQVk7Z0JBQzVDeEYsV0FBV3dGLGNBQWMsU0FBUyxZQUFZO2dCQUM5Qy9FLFFBQVF5RSxhQUFhLENBQUNNLFVBQVU7WUFDbEM7WUFDQXhCLFFBQVF3QixjQUFjLFNBQVMsWUFBWTtZQUMzQ0ksUUFBUUosY0FBYyxTQUFTLDZCQUE2QjtRQUM5RDtRQUNBNUMsU0FBUztZQUFFUCxJQUFJO1lBQUdILElBQUk7WUFBR0YsSUFBSTtZQUFJSCxJQUFJO1lBQUlGLElBQUk7UUFBRztRQUNoRG1CLGNBQWM7WUFBRVosSUFBSTtZQUFHRixJQUFJO1lBQUlILElBQUk7WUFBSXNDLE1BQU07UUFBSztRQUNsRHJELFlBQVk7WUFDVkMsWUFBWTtZQUNaRyxXQUFXO2dCQUFFbUIsSUFBSTtnQkFBSUgsSUFBSTtnQkFBSUYsSUFBSTtnQkFBSUgsSUFBSTtnQkFBSUYsSUFBSTtnQkFBSVIsS0FBSztZQUFHO1lBQzdERSxhQUFhO2dCQUFFa0IsUUFBUTtnQkFBS0osUUFBUTtnQkFBS0wsVUFBVTtnQkFBS1IsTUFBTTtZQUFJO1FBQ3BFO1FBQ0FpQyxTQUFTO1lBQ1ByQixJQUFJO1lBQ0pGLElBQUk7WUFDSkgsSUFBSTtRQUNOO0lBQ0Y7SUFFQSxPQUFPM0MscUJBQXFCdUc7QUFDOUIsRUFBRSIsInNvdXJjZXMiOlsiRjpcXEN1cnNvclxcbGwuY29tXFxpbnN0YWxsXFx3ZWJcXHNyY1xcc3R5bGVzXFx0aGVtZS1mYWN0b3J5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVRoZW1lLCBUaGVtZSwgVGhlbWVPcHRpb25zIH0gZnJvbSAnQG11aS9tYXRlcmlhbC9zdHlsZXMnO1xuaW1wb3J0IHsgVGhlbWVDb25maWcgfSBmcm9tICcuLi9jb25maWcvZGVzaWduLXN5c3RlbSc7XG5cbi8vINCY0L3RgtC10YDRhNC10LnRgSDQtNC70Y8g0LrQsNGB0YLQvtC80LjQt9Cw0YbQuNC5INGC0LXQvNGLXG5leHBvcnQgaW50ZXJmYWNlIFRoZW1lQ3VzdG9taXphdGlvbnMge1xuICBwcmltYXJ5Q29sb3I/OiBzdHJpbmc7XG4gIHNlY29uZGFyeUNvbG9yPzogc3RyaW5nO1xuICBiYWNrZ3JvdW5kSW1hZ2U/OiBzdHJpbmc7XG4gIGxvZ29Vcmw/OiBzdHJpbmc7XG4gIGZhdmljb24/OiBzdHJpbmc7XG4gIGN1c3RvbUNzcz86IHN0cmluZztcbn1cblxuLy8g0KPRgtC40LvQuNGC0Ysg0LTQu9GPINGA0LDQsdC+0YLRiyDRgSDRhtCy0LXRgtCw0LzQuFxuY29uc3QgbGlnaHRlbkNvbG9yID0gKGNvbG9yOiBzdHJpbmcsIGFtb3VudDogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgY29uc3QgaGV4ID0gY29sb3IucmVwbGFjZSgnIycsICcnKTtcbiAgY29uc3QgbnVtID0gcGFyc2VJbnQoaGV4LCAxNik7XG4gIGNvbnN0IHIgPSBNYXRoLm1pbigyNTUsIE1hdGguZmxvb3IoKG51bSA+PiAxNikgKyAoMjU1IC0gKG51bSA+PiAxNikpICogYW1vdW50KSk7XG4gIGNvbnN0IGcgPSBNYXRoLm1pbigyNTUsIE1hdGguZmxvb3IoKChudW0gPj4gOCkgJiAweDAwRkYpICsgKDI1NSAtICgobnVtID4+IDgpICYgMHgwMEZGKSkgKiBhbW91bnQpKTtcbiAgY29uc3QgYiA9IE1hdGgubWluKDI1NSwgTWF0aC5mbG9vcigobnVtICYgMHgwMDAwRkYpICsgKDI1NSAtIChudW0gJiAweDAwMDBGRikpICogYW1vdW50KSk7XG4gIHJldHVybiBgIyR7KHIgPDwgMTYgfCBnIDw8IDggfCBiKS50b1N0cmluZygxNikucGFkU3RhcnQoNiwgJzAnKX1gO1xufTtcblxuY29uc3QgZGFya2VuQ29sb3IgPSAoY29sb3I6IHN0cmluZywgYW1vdW50OiBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICBjb25zdCBoZXggPSBjb2xvci5yZXBsYWNlKCcjJywgJycpO1xuICBjb25zdCBudW0gPSBwYXJzZUludChoZXgsIDE2KTtcbiAgY29uc3QgciA9IE1hdGgubWF4KDAsIE1hdGguZmxvb3IoKG51bSA+PiAxNikgKiAoMSAtIGFtb3VudCkpKTtcbiAgY29uc3QgZyA9IE1hdGgubWF4KDAsIE1hdGguZmxvb3IoKChudW0gPj4gOCkgJiAweDAwRkYpICogKDEgLSBhbW91bnQpKSk7XG4gIGNvbnN0IGIgPSBNYXRoLm1heCgwLCBNYXRoLmZsb29yKChudW0gJiAweDAwMDBGRikgKiAoMSAtIGFtb3VudCkpKTtcbiAgcmV0dXJuIGAjJHsociA8PCAxNiB8IGcgPDwgOCB8IGIpLnRvU3RyaW5nKDE2KS5wYWRTdGFydCg2LCAnMCcpfWA7XG59O1xuXG4vLyDQpNGD0L3QutGG0LjRjyDQtNC70Y8g0L7Qv9GA0LXQtNC10LvQtdC90LjRjyDQutC+0L3RgtGA0LDRgdGC0L3QvtCz0L4g0YbQstC10YLQsCDRgtC10LrRgdGC0LBcbmNvbnN0IGdldENvbnRyYXN0VGV4dCA9IChiYWNrZ3JvdW5kQ29sb3I6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IGhleCA9IGJhY2tncm91bmRDb2xvci5yZXBsYWNlKCcjJywgJycpO1xuICBjb25zdCByID0gcGFyc2VJbnQoaGV4LnN1YnN0cigwLCAyKSwgMTYpO1xuICBjb25zdCBnID0gcGFyc2VJbnQoaGV4LnN1YnN0cigyLCAyKSwgMTYpO1xuICBjb25zdCBiID0gcGFyc2VJbnQoaGV4LnN1YnN0cig0LCAyKSwgMTYpO1xuXG4gIC8vINCS0YvRh9C40YHQu9GP0LXQvCDRj9GA0LrQvtGB0YLRjCDQv9C+INGE0L7RgNC80YPQu9C1IFczQ1xuICBjb25zdCBicmlnaHRuZXNzID0gKHIgKiAyOTkgKyBnICogNTg3ICsgYiAqIDExNCkgLyAxMDAwO1xuXG4gIC8vINCV0YHQu9C4INGG0LLQtdGCINGB0LLQtdGC0LvRi9C5LCDQuNGB0L/QvtC70YzQt9GD0LXQvCDRgtC10LzQvdGL0Lkg0YLQtdC60YHRgiwg0LjQvdCw0YfQtSDRgdCy0LXRgtC70YvQuVxuICByZXR1cm4gYnJpZ2h0bmVzcyA+IDEyOCA/ICcjMmQzNzQ4JyA6ICcjZmZmZmZmJztcbn07XG5cbi8vINCg0LDRgdGI0LjRgNC10L3QuNC1INGC0LXQvNGLIE1hdGVyaWFsLVVJINC00LvRjyDQvdCw0YjQuNGFINC90YPQttC0XG5kZWNsYXJlIG1vZHVsZSAnQG11aS9tYXRlcmlhbC9zdHlsZXMnIHtcbiAgaW50ZXJmYWNlIFRoZW1lIHtcbiAgICBjdXN0b206IHtcbiAgICAgIGdyYWRpZW50czoge1xuICAgICAgICBwcmltYXJ5OiBzdHJpbmc7XG4gICAgICAgIHNlY29uZGFyeTogc3RyaW5nO1xuICAgICAgICBhY2NlbnQ6IHN0cmluZztcbiAgICAgIH07XG4gICAgICBzaGFkb3dzOiB7XG4gICAgICAgIGNhcmQ6IHN0cmluZztcbiAgICAgICAgYnV0dG9uOiBzdHJpbmc7XG4gICAgICAgIG1vZGFsOiBzdHJpbmc7XG4gICAgICB9O1xuICAgICAgYW5pbWF0aW9uczoge1xuICAgICAgICB0cmFuc2l0aW9uOiBzdHJpbmc7XG4gICAgICAgIGhvdmVyOiBzdHJpbmc7XG4gICAgICB9O1xuICAgICAgZGF0aW5nOiB7XG4gICAgICAgIGxpa2VDb2xvcjogc3RyaW5nO1xuICAgICAgICBwYXNzQ29sb3I6IHN0cmluZztcbiAgICAgICAgc3VwZXJMaWtlQ29sb3I6IHN0cmluZztcbiAgICAgICAgbWF0Y2hDb2xvcjogc3RyaW5nO1xuICAgICAgfTtcbiAgICB9O1xuICB9XG5cbiAgaW50ZXJmYWNlIFRoZW1lT3B0aW9ucyB7XG4gICAgY3VzdG9tPzoge1xuICAgICAgZ3JhZGllbnRzPzoge1xuICAgICAgICBwcmltYXJ5Pzogc3RyaW5nO1xuICAgICAgICBzZWNvbmRhcnk/OiBzdHJpbmc7XG4gICAgICAgIGFjY2VudD86IHN0cmluZztcbiAgICAgIH07XG4gICAgICBzaGFkb3dzPzoge1xuICAgICAgICBjYXJkPzogc3RyaW5nO1xuICAgICAgICBidXR0b24/OiBzdHJpbmc7XG4gICAgICAgIG1vZGFsPzogc3RyaW5nO1xuICAgICAgfTtcbiAgICAgIGFuaW1hdGlvbnM/OiB7XG4gICAgICAgIHRyYW5zaXRpb24/OiBzdHJpbmc7XG4gICAgICAgIGhvdmVyPzogc3RyaW5nO1xuICAgICAgfTtcbiAgICAgIGRhdGluZz86IHtcbiAgICAgICAgbGlrZUNvbG9yPzogc3RyaW5nO1xuICAgICAgICBwYXNzQ29sb3I/OiBzdHJpbmc7XG4gICAgICAgIHN1cGVyTGlrZUNvbG9yPzogc3RyaW5nO1xuICAgICAgICBtYXRjaENvbG9yPzogc3RyaW5nO1xuICAgICAgfTtcbiAgICB9O1xuICB9XG5cbiAgaW50ZXJmYWNlIFBhbGV0dGUge1xuICAgIGdyYWRpZW50OiB7XG4gICAgICBwcmltYXJ5OiBzdHJpbmc7XG4gICAgICBzZWNvbmRhcnk6IHN0cmluZztcbiAgICAgIGFjY2VudDogc3RyaW5nO1xuICAgIH07XG4gIH1cblxuICBpbnRlcmZhY2UgUGFsZXR0ZU9wdGlvbnMge1xuICAgIGdyYWRpZW50Pzoge1xuICAgICAgcHJpbWFyeT86IHN0cmluZztcbiAgICAgIHNlY29uZGFyeT86IHN0cmluZztcbiAgICAgIGFjY2VudD86IHN0cmluZztcbiAgICB9O1xuICB9XG59XG5cbi8vINCh0L7Qt9C00LDQvdC40LUg0YLQtdC80Ysg0L3QsCDQvtGB0L3QvtCy0LUg0LrQvtC90YTQuNCz0YPRgNCw0YbQuNC4XG5leHBvcnQgY29uc3QgY3JlYXRlTGlrZXNMb3ZlVGhlbWUgPSAoXG4gIGNvbmZpZzogVGhlbWVDb25maWcsXG4gIGN1c3RvbWl6YXRpb25zPzogVGhlbWVDdXN0b21pemF0aW9uc1xuKTogVGhlbWUgPT4ge1xuICBjb25zdCBwcmltYXJ5Q29sb3IgPSBjdXN0b21pemF0aW9ucz8ucHJpbWFyeUNvbG9yIHx8IGNvbmZpZy5wcmltYXJ5Q29sb3I7XG4gIGNvbnN0IHNlY29uZGFyeUNvbG9yID0gY3VzdG9taXphdGlvbnM/LnNlY29uZGFyeUNvbG9yIHx8IGNvbmZpZy5zZWNvbmRhcnlDb2xvcjtcblxuICBjb25zdCB0aGVtZU9wdGlvbnM6IFRoZW1lT3B0aW9ucyA9IHtcbiAgICBwYWxldHRlOiB7XG4gICAgICBtb2RlOiBjb25maWcuaWQgPT09ICdkYXJrJyA/ICdkYXJrJyA6ICdsaWdodCcsXG4gICAgICBwcmltYXJ5OiB7XG4gICAgICAgIG1haW46IHByaW1hcnlDb2xvcixcbiAgICAgICAgbGlnaHQ6IGxpZ2h0ZW5Db2xvcihwcmltYXJ5Q29sb3IsIDAuMyksXG4gICAgICAgIGRhcms6IGRhcmtlbkNvbG9yKHByaW1hcnlDb2xvciwgMC4zKSxcbiAgICAgICAgY29udHJhc3RUZXh0OiBnZXRDb250cmFzdFRleHQocHJpbWFyeUNvbG9yKVxuICAgICAgfSxcbiAgICAgIHNlY29uZGFyeToge1xuICAgICAgICBtYWluOiBzZWNvbmRhcnlDb2xvcixcbiAgICAgICAgbGlnaHQ6IGxpZ2h0ZW5Db2xvcihzZWNvbmRhcnlDb2xvciwgMC4zKSxcbiAgICAgICAgZGFyazogZGFya2VuQ29sb3Ioc2Vjb25kYXJ5Q29sb3IsIDAuMyksXG4gICAgICAgIGNvbnRyYXN0VGV4dDogZ2V0Q29udHJhc3RUZXh0KHNlY29uZGFyeUNvbG9yKVxuICAgICAgfSxcbiAgICAgIGJhY2tncm91bmQ6IHtcbiAgICAgICAgZGVmYXVsdDogY29uZmlnLmNvbG9ycy5iYWNrZ3JvdW5kLFxuICAgICAgICBwYXBlcjogY29uZmlnLmNvbG9ycy5zdXJmYWNlXG4gICAgICB9LFxuICAgICAgdGV4dDoge1xuICAgICAgICBwcmltYXJ5OiBjb25maWcuY29sb3JzLnRleHQucHJpbWFyeSxcbiAgICAgICAgc2Vjb25kYXJ5OiBjb25maWcuY29sb3JzLnRleHQuc2Vjb25kYXJ5XG4gICAgICB9LFxuICAgICAgZ3JhZGllbnQ6IHtcbiAgICAgICAgcHJpbWFyeTogY29uZmlnLmdyYWRpZW50cy5wcmltYXJ5LFxuICAgICAgICBzZWNvbmRhcnk6IGNvbmZpZy5ncmFkaWVudHMuc2Vjb25kYXJ5LFxuICAgICAgICBhY2NlbnQ6IGNvbmZpZy5ncmFkaWVudHMuYWNjZW50XG4gICAgICB9LFxuICAgICAgc3VjY2Vzczoge1xuICAgICAgICBtYWluOiAnIzRjYWY1MCcsXG4gICAgICAgIGxpZ2h0OiAnIzgxYzc4NCcsXG4gICAgICAgIGRhcms6ICcjMzg4ZTNjJ1xuICAgICAgfSxcbiAgICAgIGVycm9yOiB7XG4gICAgICAgIG1haW46ICcjZjQ0MzM2JyxcbiAgICAgICAgbGlnaHQ6ICcjZTU3MzczJyxcbiAgICAgICAgZGFyazogJyNkMzJmMmYnXG4gICAgICB9LFxuICAgICAgd2FybmluZzoge1xuICAgICAgICBtYWluOiAnI2ZmOTgwMCcsXG4gICAgICAgIGxpZ2h0OiAnI2ZmYjc0ZCcsXG4gICAgICAgIGRhcms6ICcjZjU3YzAwJ1xuICAgICAgfSxcbiAgICAgIGluZm86IHtcbiAgICAgICAgbWFpbjogJyMyMTk2ZjMnLFxuICAgICAgICBsaWdodDogJyM2NGI1ZjYnLFxuICAgICAgICBkYXJrOiAnIzE5NzZkMidcbiAgICAgIH1cbiAgICB9LFxuICAgIHR5cG9ncmFwaHk6IHtcbiAgICAgIGZvbnRGYW1pbHk6ICdcIlBsYXlmYWlyIERpc3BsYXlcIiwgXCJHZW9yZ2lhXCIsIFwiVGltZXMgTmV3IFJvbWFuXCIsIHNlcmlmJyxcbiAgICAgIGgxOiB7XG4gICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMueHhsLFxuICAgICAgICBmb250V2VpZ2h0OiBjb25maWcudHlwb2dyYXBoeS5mb250V2VpZ2h0cy5ib2xkLFxuICAgICAgICBsaW5lSGVpZ2h0OiAxLjMsXG4gICAgICAgIGxldHRlclNwYWNpbmc6ICctMC4wMmVtJyxcbiAgICAgICAgbWFyZ2luQm90dG9tOiAnMS41cmVtJ1xuICAgICAgfSxcbiAgICAgIGgyOiB7XG4gICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMueGwsXG4gICAgICAgIGZvbnRXZWlnaHQ6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRXZWlnaHRzLmJvbGQsXG4gICAgICAgIGxpbmVIZWlnaHQ6IDEuNCxcbiAgICAgICAgbGV0dGVyU3BhY2luZzogJy0wLjAxZW0nLFxuICAgICAgICBtYXJnaW5Cb3R0b206ICcxLjI1cmVtJ1xuICAgICAgfSxcbiAgICAgIGgzOiB7XG4gICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMubGcsXG4gICAgICAgIGZvbnRXZWlnaHQ6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRXZWlnaHRzLnNlbWlib2xkLFxuICAgICAgICBsaW5lSGVpZ2h0OiAxLjUsXG4gICAgICAgIG1hcmdpbkJvdHRvbTogJzFyZW0nXG4gICAgICB9LFxuICAgICAgaDQ6IHtcbiAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy5tZCxcbiAgICAgICAgZm9udFdlaWdodDogY29uZmlnLnR5cG9ncmFwaHkuZm9udFdlaWdodHMuc2VtaWJvbGQsXG4gICAgICAgIGxpbmVIZWlnaHQ6IDEuNSxcbiAgICAgICAgbWFyZ2luQm90dG9tOiAnMC44NzVyZW0nXG4gICAgICB9LFxuICAgICAgaDU6IHtcbiAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy5zbSxcbiAgICAgICAgZm9udFdlaWdodDogY29uZmlnLnR5cG9ncmFwaHkuZm9udFdlaWdodHMubWVkaXVtLFxuICAgICAgICBsaW5lSGVpZ2h0OiAxLjYsXG4gICAgICAgIG1hcmdpbkJvdHRvbTogJzAuNzVyZW0nXG4gICAgICB9LFxuICAgICAgaDY6IHtcbiAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy54cyxcbiAgICAgICAgZm9udFdlaWdodDogY29uZmlnLnR5cG9ncmFwaHkuZm9udFdlaWdodHMubWVkaXVtLFxuICAgICAgICBsaW5lSGVpZ2h0OiAxLjYsXG4gICAgICAgIG1hcmdpbkJvdHRvbTogJzAuNjI1cmVtJ1xuICAgICAgfSxcbiAgICAgIGJvZHkxOiB7XG4gICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMubWQsXG4gICAgICAgIGZvbnRXZWlnaHQ6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRXZWlnaHRzLm5vcm1hbCxcbiAgICAgICAgbGluZUhlaWdodDogMS43LFxuICAgICAgICBtYXJnaW5Cb3R0b206ICcxcmVtJ1xuICAgICAgfSxcbiAgICAgIGJvZHkyOiB7XG4gICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMuc20sXG4gICAgICAgIGZvbnRXZWlnaHQ6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRXZWlnaHRzLm5vcm1hbCxcbiAgICAgICAgbGluZUhlaWdodDogMS43LFxuICAgICAgICBtYXJnaW5Cb3R0b206ICcwLjg3NXJlbSdcbiAgICAgIH0sXG4gICAgICBidXR0b246IHtcbiAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy5zbSxcbiAgICAgICAgZm9udFdlaWdodDogY29uZmlnLnR5cG9ncmFwaHkuZm9udFdlaWdodHMuc2VtaWJvbGQsXG4gICAgICAgIHRleHRUcmFuc2Zvcm06ICdub25lJyxcbiAgICAgICAgbGV0dGVyU3BhY2luZzogJzAuMDJlbScsXG4gICAgICAgIGxpbmVIZWlnaHQ6IDEuNVxuICAgICAgfSxcbiAgICAgIGNhcHRpb246IHtcbiAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy54cyxcbiAgICAgICAgZm9udFdlaWdodDogY29uZmlnLnR5cG9ncmFwaHkuZm9udFdlaWdodHMubm9ybWFsLFxuICAgICAgICBsaW5lSGVpZ2h0OiAxLjVcbiAgICAgIH1cbiAgICB9LFxuICAgIHNwYWNpbmc6IGNvbmZpZy5zcGFjaW5nLnNtLFxuICAgIHNoYXBlOiB7XG4gICAgICBib3JkZXJSYWRpdXM6IGNvbmZpZy5ib3JkZXJSYWRpdXMubWRcbiAgICB9LFxuICAgIGNvbXBvbmVudHM6IHtcbiAgICAgIE11aUJ1dHRvbjoge1xuICAgICAgICBzdHlsZU92ZXJyaWRlczoge1xuICAgICAgICAgIHJvb3Q6IHtcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogY29uZmlnLmJvcmRlclJhZGl1cy5tZCxcbiAgICAgICAgICAgIHRleHRUcmFuc2Zvcm06ICdub25lJyxcbiAgICAgICAgICAgIGZvbnRXZWlnaHQ6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRXZWlnaHRzLnNlbWlib2xkLFxuICAgICAgICAgICAgcGFkZGluZzogYCR7Y29uZmlnLnNwYWNpbmcubWR9cHggJHtjb25maWcuc3BhY2luZy5sZ31weGAsXG4gICAgICAgICAgICBmb250U2l6ZTogY29uZmlnLnR5cG9ncmFwaHkuZm9udFNpemVzLnNtLFxuICAgICAgICAgICAgbWluSGVpZ2h0OiAnNDhweCcsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICdub25lJyxcbiAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlLWluLW91dCcsXG4gICAgICAgICAgICAnJjpob3Zlcic6IHtcbiAgICAgICAgICAgICAgYm94U2hhZG93OiBjb25maWcuc2hhZG93cy5tZCxcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgtMXB4KSdcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LFxuICAgICAgICAgIGNvbnRhaW5lZDoge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogY29uZmlnLmdyYWRpZW50cy5wcmltYXJ5LFxuICAgICAgICAgICAgY29sb3I6ICcjZmZmZmZmICFpbXBvcnRhbnQnLFxuICAgICAgICAgICAgZm9udFdlaWdodDogNjAwLFxuICAgICAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy5zbSxcbiAgICAgICAgICAgIHRleHRTaGFkb3c6ICcwIDFweCAycHggcmdiYSgwLDAsMCwwLjMpJyxcbiAgICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBjb25maWcuZ3JhZGllbnRzLnByaW1hcnksXG4gICAgICAgICAgICAgIGZpbHRlcjogJ2JyaWdodG5lc3MoMS4xKScsXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgOHB4IDI1cHggcmdiYSgwLDAsMCwwLjE1KScsXG4gICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZmZmZiAhaW1wb3J0YW50J1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgb3V0bGluZWQ6IHtcbiAgICAgICAgICAgIGJvcmRlcldpZHRoOiAyLFxuICAgICAgICAgICAgY29sb3I6IHByaW1hcnlDb2xvcixcbiAgICAgICAgICAgIGJvcmRlckNvbG9yOiBwcmltYXJ5Q29sb3IsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOSknLFxuICAgICAgICAgICAgZm9udFNpemU6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRTaXplcy5zbSxcbiAgICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgICBib3JkZXJXaWR0aDogMixcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBwcmltYXJ5Q29sb3IsXG4gICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZmZmZiAhaW1wb3J0YW50J1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIE11aUNhcmQ6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICByb290OiB7XG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6IGNvbmZpZy5ib3JkZXJSYWRpdXMubGcsXG4gICAgICAgICAgICBib3hTaGFkb3c6IGNvbmZpZy5zaGFkb3dzLm1kLFxuICAgICAgICAgICAgYm9yZGVyOiBgMXB4IHNvbGlkICR7Y29uZmlnLmNvbG9ycy5ib3JkZXJ9YCxcbiAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlLWluLW91dCcsXG4gICAgICAgICAgICAnJjpob3Zlcic6IHtcbiAgICAgICAgICAgICAgYm94U2hhZG93OiBjb25maWcuc2hhZG93cy5sZyxcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgtMnB4KSdcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBNdWlUZXh0RmllbGQ6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICByb290OiB7XG4gICAgICAgICAgICAnJiAuTXVpT3V0bGluZWRJbnB1dC1yb290Jzoge1xuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IGNvbmZpZy5ib3JkZXJSYWRpdXMubWQsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMuc20sXG4gICAgICAgICAgICAgIG1pbkhlaWdodDogJzU2cHgnLFxuICAgICAgICAgICAgICAnJiBpbnB1dCc6IHtcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiBgJHtjb25maWcuc3BhY2luZy5tZH1weCAke2NvbmZpZy5zcGFjaW5nLm1kfXB4YCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogY29uZmlnLnR5cG9ncmFwaHkuZm9udFNpemVzLnNtLFxuICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEuNVxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAnJjpob3ZlciAuTXVpT3V0bGluZWRJbnB1dC1ub3RjaGVkT3V0bGluZSc6IHtcbiAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogcHJpbWFyeUNvbG9yXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICcmLk11aS1mb2N1c2VkIC5NdWlPdXRsaW5lZElucHV0LW5vdGNoZWRPdXRsaW5lJzoge1xuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBwcmltYXJ5Q29sb3IsXG4gICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDJcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICcmIC5NdWlJbnB1dExhYmVsLXJvb3QnOiB7XG4gICAgICAgICAgICAgIGZvbnRTaXplOiBjb25maWcudHlwb2dyYXBoeS5mb250U2l6ZXMuc20sXG4gICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEuNVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIE11aUNoaXA6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICByb290OiB7XG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6IGNvbmZpZy5ib3JkZXJSYWRpdXMuZnVsbCxcbiAgICAgICAgICAgIGZvbnRXZWlnaHQ6IGNvbmZpZy50eXBvZ3JhcGh5LmZvbnRXZWlnaHRzLm1lZGl1bVxuICAgICAgICAgIH0sXG4gICAgICAgICAgZmlsbGVkOiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBjb25maWcuZ3JhZGllbnRzLmFjY2VudCxcbiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZmZmZidcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBNdWlEaWFsb2c6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICBwYXBlcjoge1xuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBjb25maWcuYm9yZGVyUmFkaXVzLmxnLFxuICAgICAgICAgICAgYm94U2hhZG93OiBjb25maWcuc2hhZG93cy5sZ1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIE11aUFwcEJhcjoge1xuICAgICAgICBzdHlsZU92ZXJyaWRlczoge1xuICAgICAgICAgIHJvb3Q6IHtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGNvbmZpZy5ncmFkaWVudHMucHJpbWFyeSxcbiAgICAgICAgICAgIGJveFNoYWRvdzogY29uZmlnLnNoYWRvd3MubWRcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBNdWlGYWI6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICByb290OiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBjb25maWcuZ3JhZGllbnRzLnByaW1hcnksXG4gICAgICAgICAgICBib3hTaGFkb3c6IGNvbmZpZy5zaGFkb3dzLm1kLFxuICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6IGNvbmZpZy5ncmFkaWVudHMucHJpbWFyeSxcbiAgICAgICAgICAgICAgZmlsdGVyOiAnYnJpZ2h0bmVzcygxLjEpJyxcbiAgICAgICAgICAgICAgYm94U2hhZG93OiBjb25maWcuc2hhZG93cy5sZ1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgY3VzdG9tOiB7XG4gICAgICBncmFkaWVudHM6IHtcbiAgICAgICAgcHJpbWFyeTogY29uZmlnLmdyYWRpZW50cy5wcmltYXJ5LFxuICAgICAgICBzZWNvbmRhcnk6IGNvbmZpZy5ncmFkaWVudHMuc2Vjb25kYXJ5LFxuICAgICAgICBhY2NlbnQ6IGNvbmZpZy5ncmFkaWVudHMuYWNjZW50XG4gICAgICB9LFxuICAgICAgc2hhZG93czoge1xuICAgICAgICBjYXJkOiBjb25maWcuc2hhZG93cy5tZCxcbiAgICAgICAgYnV0dG9uOiBjb25maWcuc2hhZG93cy5zbSxcbiAgICAgICAgbW9kYWw6IGNvbmZpZy5zaGFkb3dzLmxnXG4gICAgICB9LFxuICAgICAgYW5pbWF0aW9uczoge1xuICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMgZWFzZS1pbi1vdXQnLFxuICAgICAgICBob3ZlcjogJ3RyYW5zZm9ybSAwLjJzIGVhc2UtaW4tb3V0J1xuICAgICAgfSxcbiAgICAgIGRhdGluZzoge1xuICAgICAgICBsaWtlQ29sb3I6ICcjNGNhZjUwJyxcbiAgICAgICAgcGFzc0NvbG9yOiAnI2Y0NDMzNicsXG4gICAgICAgIHN1cGVyTGlrZUNvbG9yOiAnIzIxOTZmMycsXG4gICAgICAgIG1hdGNoQ29sb3I6ICcjZmY5ODAwJ1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICByZXR1cm4gY3JlYXRlVGhlbWUodGhlbWVPcHRpb25zKTtcbn07XG5cblxuXG4vLyDQn9GA0LXQtNGD0YHRgtCw0L3QvtCy0LvQtdC90L3Ri9C1INGC0LXQvNGLINC00LvRjyDQsdGL0YHRgtGA0L7Qs9C+INC00L7RgdGC0YPQv9CwXG5leHBvcnQgY29uc3QgUFJFU0VUX1RIRU1FUyA9IHtcbiAgbGlrZXNMb3ZlOiAnI2ZmNmI5ZCcsXG4gIHJvbWFudGljOiAnI2U5MWU2MycsXG4gIGVsZWdhbnQ6ICcjNjczYWI3JyxcbiAgbW9kZXJuOiAnIzAwYmNkNCcsXG4gIGRhcms6ICcjNDI0MjQyJ1xufTtcblxuLy8g0KHQvtC30LTQsNC90LjQtSDRgtC10LzRiyDQv9C+INC90LDQt9Cy0LDQvdC40Y5cbmV4cG9ydCBjb25zdCBjcmVhdGVUaGVtZUJ5TmFtZSA9ICh0aGVtZU5hbWU6IGtleW9mIHR5cGVvZiBQUkVTRVRfVEhFTUVTKTogVGhlbWUgPT4ge1xuICBjb25zdCBiYXNlQ29uZmlnID0ge1xuICAgIGlkOiB0aGVtZU5hbWUsXG4gICAgbmFtZTogdGhlbWVOYW1lLFxuICAgIGRpc3BsYXlOYW1lOiB0aGVtZU5hbWUsXG4gICAgcHJpbWFyeUNvbG9yOiBQUkVTRVRfVEhFTUVTW3RoZW1lTmFtZV0sXG4gICAgc2Vjb25kYXJ5Q29sb3I6ICcjNGVjZGM0JyxcbiAgICBncmFkaWVudHM6IHtcbiAgICAgIHByaW1hcnk6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke1BSRVNFVF9USEVNRVNbdGhlbWVOYW1lXX0gMCUsICM0ZWNkYzQgMTAwJSlgLFxuICAgICAgc2Vjb25kYXJ5OiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzRlY2RjNCAwJSwgIzQ1YjdhYSAxMDAlKScsXG4gICAgICBhY2NlbnQ6IGBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAke1BSRVNFVF9USEVNRVNbdGhlbWVOYW1lXX0gMCUsICR7bGlnaHRlbkNvbG9yKFBSRVNFVF9USEVNRVNbdGhlbWVOYW1lXSwgMC4yKX0gMTAwJSlgXG4gICAgfSxcbiAgICBjb2xvcnM6IHtcbiAgICAgIGJhY2tncm91bmQ6IHRoZW1lTmFtZSA9PT0gJ2RhcmsnID8gJyMxMjEyMTInIDogJyNmZmZmZmYnLFxuICAgICAgc3VyZmFjZTogdGhlbWVOYW1lID09PSAnZGFyaycgPyAnIzFlMWUxZScgOiAnI2Y4ZjlmYScsXG4gICAgICB0ZXh0OiB7XG4gICAgICAgIHByaW1hcnk6IHRoZW1lTmFtZSA9PT0gJ2RhcmsnID8gJyNmZmZmZmYnIDogJyMyZDM3NDgnLFxuICAgICAgICBzZWNvbmRhcnk6IHRoZW1lTmFtZSA9PT0gJ2RhcmsnID8gJyNiMGIwYjAnIDogJyM3MTgwOTYnLFxuICAgICAgICBhY2NlbnQ6IFBSRVNFVF9USEVNRVNbdGhlbWVOYW1lXVxuICAgICAgfSxcbiAgICAgIGJvcmRlcjogdGhlbWVOYW1lID09PSAnZGFyaycgPyAnIzMzMzMzMycgOiAnI2UyZThmMCcsXG4gICAgICBzaGFkb3c6IHRoZW1lTmFtZSA9PT0gJ2RhcmsnID8gJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKScgOiAncmdiYSgwLCAwLCAwLCAwLjEpJ1xuICAgIH0sXG4gICAgc3BhY2luZzogeyB4czogNCwgc206IDgsIG1kOiAxNiwgbGc6IDI0LCB4bDogMzIgfSxcbiAgICBib3JkZXJSYWRpdXM6IHsgc206IDgsIG1kOiAxMiwgbGc6IDE2LCBmdWxsOiA5OTk5IH0sXG4gICAgdHlwb2dyYXBoeToge1xuICAgICAgZm9udEZhbWlseTogJ0ludGVyLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsIHNhbnMtc2VyaWYnLFxuICAgICAgZm9udFNpemVzOiB7IHhzOiAxMiwgc206IDE0LCBtZDogMTYsIGxnOiAxOCwgeGw6IDI0LCB4eGw6IDMyIH0sXG4gICAgICBmb250V2VpZ2h0czogeyBub3JtYWw6IDQwMCwgbWVkaXVtOiA1MDAsIHNlbWlib2xkOiA2MDAsIGJvbGQ6IDcwMCB9XG4gICAgfSxcbiAgICBzaGFkb3dzOiB7XG4gICAgICBzbTogJzAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMTIpLCAwIDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjI0KScsXG4gICAgICBtZDogJzAgNHB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMDcpLCAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcbiAgICAgIGxnOiAnMCAxMHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggcmdiYSgwLCAwLCAwLCAwLjA1KSdcbiAgICB9XG4gIH0gYXMgVGhlbWVDb25maWc7XG5cbiAgcmV0dXJuIGNyZWF0ZUxpa2VzTG92ZVRoZW1lKGJhc2VDb25maWcpO1xufTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVUaGVtZSIsImxpZ2h0ZW5Db2xvciIsImNvbG9yIiwiYW1vdW50IiwiaGV4IiwicmVwbGFjZSIsIm51bSIsInBhcnNlSW50IiwiciIsIk1hdGgiLCJtaW4iLCJmbG9vciIsImciLCJiIiwidG9TdHJpbmciLCJwYWRTdGFydCIsImRhcmtlbkNvbG9yIiwibWF4IiwiZ2V0Q29udHJhc3RUZXh0IiwiYmFja2dyb3VuZENvbG9yIiwic3Vic3RyIiwiYnJpZ2h0bmVzcyIsImNyZWF0ZUxpa2VzTG92ZVRoZW1lIiwiY29uZmlnIiwiY3VzdG9taXphdGlvbnMiLCJwcmltYXJ5Q29sb3IiLCJzZWNvbmRhcnlDb2xvciIsInRoZW1lT3B0aW9ucyIsInBhbGV0dGUiLCJtb2RlIiwiaWQiLCJwcmltYXJ5IiwibWFpbiIsImxpZ2h0IiwiZGFyayIsImNvbnRyYXN0VGV4dCIsInNlY29uZGFyeSIsImJhY2tncm91bmQiLCJkZWZhdWx0IiwiY29sb3JzIiwicGFwZXIiLCJzdXJmYWNlIiwidGV4dCIsImdyYWRpZW50IiwiZ3JhZGllbnRzIiwiYWNjZW50Iiwic3VjY2VzcyIsImVycm9yIiwid2FybmluZyIsImluZm8iLCJ0eXBvZ3JhcGh5IiwiZm9udEZhbWlseSIsImgxIiwiZm9udFNpemUiLCJmb250U2l6ZXMiLCJ4eGwiLCJmb250V2VpZ2h0IiwiZm9udFdlaWdodHMiLCJib2xkIiwibGluZUhlaWdodCIsImxldHRlclNwYWNpbmciLCJtYXJnaW5Cb3R0b20iLCJoMiIsInhsIiwiaDMiLCJsZyIsInNlbWlib2xkIiwiaDQiLCJtZCIsImg1Iiwic20iLCJtZWRpdW0iLCJoNiIsInhzIiwiYm9keTEiLCJub3JtYWwiLCJib2R5MiIsImJ1dHRvbiIsInRleHRUcmFuc2Zvcm0iLCJjYXB0aW9uIiwic3BhY2luZyIsInNoYXBlIiwiYm9yZGVyUmFkaXVzIiwiY29tcG9uZW50cyIsIk11aUJ1dHRvbiIsInN0eWxlT3ZlcnJpZGVzIiwicm9vdCIsInBhZGRpbmciLCJtaW5IZWlnaHQiLCJib3hTaGFkb3ciLCJ0cmFuc2l0aW9uIiwic2hhZG93cyIsInRyYW5zZm9ybSIsImNvbnRhaW5lZCIsInRleHRTaGFkb3ciLCJmaWx0ZXIiLCJvdXRsaW5lZCIsImJvcmRlcldpZHRoIiwiYm9yZGVyQ29sb3IiLCJNdWlDYXJkIiwiYm9yZGVyIiwiTXVpVGV4dEZpZWxkIiwiTXVpQ2hpcCIsImZ1bGwiLCJmaWxsZWQiLCJNdWlEaWFsb2ciLCJNdWlBcHBCYXIiLCJNdWlGYWIiLCJjdXN0b20iLCJjYXJkIiwibW9kYWwiLCJhbmltYXRpb25zIiwiaG92ZXIiLCJkYXRpbmciLCJsaWtlQ29sb3IiLCJwYXNzQ29sb3IiLCJzdXBlckxpa2VDb2xvciIsIm1hdGNoQ29sb3IiLCJQUkVTRVRfVEhFTUVTIiwibGlrZXNMb3ZlIiwicm9tYW50aWMiLCJlbGVnYW50IiwibW9kZXJuIiwiY3JlYXRlVGhlbWVCeU5hbWUiLCJ0aGVtZU5hbWUiLCJiYXNlQ29uZmlnIiwibmFtZSIsImRpc3BsYXlOYW1lIiwic2hhZG93Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/styles/theme-factory.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/utils/contacts.ts":
/*!*******************************!*\
  !*** ./src/utils/contacts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToContacts: () => (/* binding */ addToContacts),\n/* harmony export */   checkContactPermission: () => (/* binding */ checkContactPermission),\n/* harmony export */   getStoredContacts: () => (/* binding */ getStoredContacts),\n/* harmony export */   removeContacts: () => (/* binding */ removeContacts),\n/* harmony export */   storeContact: () => (/* binding */ storeContact)\n/* harmony export */ });\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logger */ \"(pages-dir-node)/./src/utils/logger.ts\");\n// Добавляем типы для Contacts API\n\nconst CONTACTS_STORAGE_KEY = 'likes-love-contacts';\nasync function checkContactPermission() {\n    try {\n        // Check if auto contacts is enabled in settings\n        const autoContactsEnabled = localStorage.getItem('autoContactsEnabled');\n        if (autoContactsEnabled === 'false') {\n            return false;\n        }\n        // Check if the Permissions API is available\n        if (!navigator.permissions) {\n            return false;\n        }\n        // Query for contacts permission\n        const result = await navigator.permissions.query({\n            name: 'contacts'\n        });\n        return result.state === 'granted';\n    } catch (error) {\n        // If the permission query fails (e.g., not supported in the country/browser)\n        return false;\n    }\n}\nasync function getStoredContacts() {\n    const contactsJson = localStorage.getItem(CONTACTS_STORAGE_KEY);\n    return contactsJson ? JSON.parse(contactsJson) : [];\n}\nasync function storeContact(contact) {\n    const contacts = await getStoredContacts();\n    contacts.push(contact);\n    localStorage.setItem(CONTACTS_STORAGE_KEY, JSON.stringify(contacts));\n}\nasync function removeContacts(contactIds) {\n    if (!contactIds) {\n        // Remove all contacts\n        localStorage.removeItem(CONTACTS_STORAGE_KEY);\n        return;\n    }\n    const contacts = await getStoredContacts();\n    const filteredContacts = contacts.filter((contact)=>!contactIds.includes(contact.id));\n    localStorage.setItem(CONTACTS_STORAGE_KEY, JSON.stringify(filteredContacts));\n}\nasync function addToContacts(contactShare, skipPermissionCheck = false) {\n    try {\n        // Check permission unless explicitly skipped\n        if (!skipPermissionCheck) {\n            const hasPermission = await checkContactPermission();\n            if (!hasPermission) {\n                return false;\n            }\n        }\n        // Create Contact object with ID and timestamp\n        const contact = {\n            id: `contact-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n            name: contactShare.name,\n            phoneNumbers: contactShare.phoneNumbers || [],\n            isIncognito: contactShare.isIncognito,\n            profileUrl: contactShare.profileUrl,\n            addedAt: new Date().toISOString()\n        };\n        // Store contact in local storage\n        await storeContact(contact);\n        // Try using the modern Contacts API first\n        if ('contacts' in navigator && navigator.contacts) {\n            const properties = await navigator.contacts.getProperties();\n            const supportedProps = [\n                'name',\n                'tel',\n                'url'\n            ];\n            // Check if the necessary properties are supported\n            if (!supportedProps.every((prop)=>properties.includes(prop))) {\n                throw new Error('Required contact properties not supported');\n            }\n            const contacts = await navigator.contacts.select(supportedProps);\n            if (!contacts.length) {\n                return false;\n            }\n            const contactToUpdate = contacts[0];\n            await contactToUpdate.update({\n                name: [\n                    contactShare.name\n                ],\n                tel: contactShare.phoneNumbers || [],\n                url: contactShare.isIncognito ? [] : [\n                    contactShare.profileUrl\n                ]\n            });\n            return true;\n        }\n        // Fall back to vCard download if Contacts API is not available\n        const vCardLines = [\n            'BEGIN:VCARD',\n            'VERSION:3.0',\n            `FN:${contactShare.name}`,\n            ...(contactShare.phoneNumbers || []).map((phone)=>`TEL;TYPE=CELL:${phone}`)\n        ];\n        // Add profile URL only for non-incognito contacts\n        if (!contactShare.isIncognito) {\n            vCardLines.push(`URL:${contactShare.profileUrl}`);\n        }\n        vCardLines.push('END:VCARD');\n        const vCard = vCardLines.join('\\n');\n        const blob = new Blob([\n            vCard\n        ], {\n            type: 'text/vcard'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${contactShare.name}.vcf`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        return true;\n    } catch (error) {\n        // Используем профессиональный логгер вместо console.error\n        (0,_logger__WEBPACK_IMPORTED_MODULE_0__.logError)('Failed to add contact to device', error, 'ContactsAPI', {\n            contactName: contactShare.name,\n            isIncognito: contactShare.isIncognito,\n            hasPhoneNumbers: Boolean(contactShare.phoneNumbers?.length),\n            skipPermissionCheck\n        });\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/contacts.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/utils/logger.ts":
/*!*****************************!*\
  !*** ./src/utils/logger.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   logDebug: () => (/* binding */ logDebug),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarn: () => (/* binding */ logWarn),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/**\n * Professional logging utility for web application\n * Provides structured logging with different levels and proper error handling\n */ const LogLevel = {\n    ERROR: 'error',\n    WARN: 'warn',\n    INFO: 'info',\n    DEBUG: 'debug'\n};\nclass Logger {\n    constructor(){\n        this.isDevelopment = \"development\" === 'development';\n        this.isTest = \"development\" === 'test';\n    }\n    createLogEntry(level, message, context, error, metadata) {\n        return {\n            level,\n            message,\n            timestamp: new Date().toISOString(),\n            context,\n            error,\n            metadata\n        };\n    }\n    shouldLog(level) {\n        // В тестах логируем только критические ошибки\n        if (this.isTest) {\n            return level === LogLevel.ERROR && this.isDevelopment;\n        }\n        return true;\n    }\n    formatMessage(entry) {\n        const { level, message, timestamp, context, error, metadata } = entry;\n        let formattedMessage = `[${timestamp}] ${level.toUpperCase()}`;\n        if (context) {\n            formattedMessage += ` [${context}]`;\n        }\n        formattedMessage += `: ${message}`;\n        if (error) {\n            formattedMessage += `\\nError: ${error.message}`;\n            if (this.isDevelopment && error.stack) {\n                formattedMessage += `\\nStack: ${error.stack}`;\n            }\n        }\n        if (metadata && Object.keys(metadata).length > 0) {\n            formattedMessage += `\\nMetadata: ${JSON.stringify(metadata, null, 2)}`;\n        }\n        return formattedMessage;\n    }\n    log(entry) {\n        if (!this.shouldLog(entry.level)) {\n            return;\n        }\n        const formattedMessage = this.formatMessage(entry);\n        switch(entry.level){\n            case LogLevel.ERROR:\n                console.error(formattedMessage);\n                break;\n            case LogLevel.WARN:\n                console.warn(formattedMessage);\n                break;\n            case LogLevel.INFO:\n                console.info(formattedMessage);\n                break;\n            case LogLevel.DEBUG:\n                console.debug(formattedMessage);\n                break;\n        }\n    }\n    error(message, error, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.ERROR, message, context, error, metadata);\n        this.log(entry);\n    }\n    warn(message, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.WARN, message, context, undefined, metadata);\n        this.log(entry);\n    }\n    info(message, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.INFO, message, context, undefined, metadata);\n        this.log(entry);\n    }\n    debug(message, context, metadata) {\n        const entry = this.createLogEntry(LogLevel.DEBUG, message, context, undefined, metadata);\n        this.log(entry);\n    }\n    // Специальный метод для тестов - не выводит в консоль\n    logForTest(level, message, error) {\n        return this.createLogEntry(level, message, 'TEST', error);\n    }\n}\n// Singleton instance\nconst logger = new Logger();\n// Convenience exports\nconst logError = (message, error, context, metadata)=>logger.error(message, error, context, metadata);\nconst logWarn = (message, context, metadata)=>logger.warn(message, context, metadata);\nconst logInfo = (message, context, metadata)=>logger.info(message, context, metadata);\nconst logDebug = (message, context, metadata)=>logger.debug(message, context, metadata);\n// Enterprise logging interface\nconst log = {\n    error: (message, context, error)=>logger.error(message, error, context),\n    warn: (message, context, metadata)=>logger.warn(message, context, metadata),\n    info: (message, context, metadata)=>logger.info(message, context, metadata),\n    debug: (message, context, metadata)=>logger.debug(message, context, metadata),\n    // Context-specific loggers\n    auth: (message, data)=>logger.info(message, 'Auth', data),\n    api: (message, data)=>logger.debug(message, 'API', data),\n    ui: (message, data)=>logger.debug(message, 'UI', data),\n    performance: (message, data)=>logger.info(message, 'Performance', data),\n    security: (message, data)=>logger.warn(message, 'Security', data),\n    userAction: (action, data)=>logger.info(`User action: ${action}`, 'UserAction', data),\n    apiError: (endpoint, error, requestData)=>{\n        logger.error(`API Error: ${endpoint}`, error instanceof Error ? error : new Error(String(error)), 'API', {\n            endpoint,\n            requestData,\n            timestamp: Date.now()\n        });\n    },\n    performanceMetric: (metric, value, unit = 'ms')=>{\n        logger.info(`Performance metric: ${metric}`, 'Performance', {\n            metric,\n            value,\n            unit,\n            timestamp: Date.now()\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/logger.ts\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js":
/*!****************************************************************************************!*\
  !*** __barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* reexport default from dynamic */ _Alert__WEBPACK_IMPORTED_MODULE_0___default.a),\n/* harmony export */   Snackbar: () => (/* reexport default from dynamic */ _Snackbar__WEBPACK_IMPORTED_MODULE_1___default.a)\n/* harmony export */ });\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Alert */ \"(pages-dir-node)/./node_modules/@mui/material/node/Alert/index.js\");\n/* harmony import */ var _Alert__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Alert__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Snackbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Snackbar */ \"(pages-dir-node)/./node_modules/@mui/material/node/Snackbar/index.js\");\n/* harmony import */ var _Snackbar__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Snackbar__WEBPACK_IMPORTED_MODULE_1__);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUFsZXJ0LFNuYWNrYmFyIT0hLi9ub2RlX21vZHVsZXMvQG11aS9tYXRlcmlhbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQzBDIiwic291cmNlcyI6WyJGOlxcQ3Vyc29yXFxsbC5jb21cXGluc3RhbGxcXHdlYlxcbm9kZV9tb2R1bGVzXFxAbXVpXFxtYXRlcmlhbFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFsZXJ0IH0gZnJvbSBcIi4vQWxlcnRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTbmFja2JhciB9IGZyb20gXCIuL1NuYWNrYmFyXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Alert,Snackbar!=!./node_modules/@mui/material/index.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=CssBaseline!=!./node_modules/@mui/material/index.js":
/*!*************************************************************************************!*\
  !*** __barrel_optimize__?names=CssBaseline!=!./node_modules/@mui/material/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CssBaseline: () => (/* reexport safe */ _CssBaseline__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _CssBaseline__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CssBaseline */ "(pages-dir-node)/./node_modules/@mui/material/node/CssBaseline/index.js");



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=useMediaQuery!=!./node_modules/@mui/material/index.js":
/*!***************************************************************************************!*\
  !*** __barrel_optimize__?names=useMediaQuery!=!./node_modules/@mui/material/index.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useMediaQuery: () => (/* reexport safe */ _useMediaQuery__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _useMediaQuery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useMediaQuery */ "(pages-dir-node)/./node_modules/@mui/material/node/useMediaQuery/index.js");



/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/react":
/*!*********************************!*\
  !*** external "@emotion/react" ***!
  \*********************************/
/***/ ((module) => {

module.exports = import("@emotion/react");;

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useMediaQuery":
/*!********************************************!*\
  !*** external "@mui/system/useMediaQuery" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useMediaQuery");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/utils":
/*!*****************************!*\
  !*** external "@mui/utils" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("@mui/utils");

/***/ }),

/***/ "@mui/utils/appendOwnerState":
/*!**********************************************!*\
  !*** external "@mui/utils/appendOwnerState" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/appendOwnerState");

/***/ }),

/***/ "@mui/utils/capitalize":
/*!****************************************!*\
  !*** external "@mui/utils/capitalize" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/capitalize");

/***/ }),

/***/ "@mui/utils/chainPropTypes":
/*!********************************************!*\
  !*** external "@mui/utils/chainPropTypes" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/chainPropTypes");

/***/ }),

/***/ "@mui/utils/composeClasses":
/*!********************************************!*\
  !*** external "@mui/utils/composeClasses" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/composeClasses");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/elementAcceptingRef":
/*!*************************************************!*\
  !*** external "@mui/utils/elementAcceptingRef" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementAcceptingRef");

/***/ }),

/***/ "@mui/utils/elementTypeAcceptingRef":
/*!*****************************************************!*\
  !*** external "@mui/utils/elementTypeAcceptingRef" ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/elementTypeAcceptingRef");

/***/ }),

/***/ "@mui/utils/extractEventHandlers":
/*!**************************************************!*\
  !*** external "@mui/utils/extractEventHandlers" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/extractEventHandlers");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "@mui/utils/generateUtilityClasses":
/*!****************************************************!*\
  !*** external "@mui/utils/generateUtilityClasses" ***!
  \****************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/generateUtilityClasses");

/***/ }),

/***/ "@mui/utils/getReactElementRef":
/*!************************************************!*\
  !*** external "@mui/utils/getReactElementRef" ***!
  \************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/getReactElementRef");

/***/ }),

/***/ "@mui/utils/integerPropType":
/*!*********************************************!*\
  !*** external "@mui/utils/integerPropType" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/integerPropType");

/***/ }),

/***/ "@mui/utils/mergeSlotProps":
/*!********************************************!*\
  !*** external "@mui/utils/mergeSlotProps" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/mergeSlotProps");

/***/ }),

/***/ "@mui/utils/refType":
/*!*************************************!*\
  !*** external "@mui/utils/refType" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/refType");

/***/ }),

/***/ "@mui/utils/resolveComponentProps":
/*!***************************************************!*\
  !*** external "@mui/utils/resolveComponentProps" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/resolveComponentProps");

/***/ }),

/***/ "@mui/utils/useEventCallback":
/*!**********************************************!*\
  !*** external "@mui/utils/useEventCallback" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useEventCallback");

/***/ }),

/***/ "@mui/utils/useForkRef":
/*!****************************************!*\
  !*** external "@mui/utils/useForkRef" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useForkRef");

/***/ }),

/***/ "@mui/utils/useIsFocusVisible":
/*!***********************************************!*\
  !*** external "@mui/utils/useIsFocusVisible" ***!
  \***********************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useIsFocusVisible");

/***/ }),

/***/ "@mui/utils/useSlotProps":
/*!******************************************!*\
  !*** external "@mui/utils/useSlotProps" ***!
  \******************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useSlotProps");

/***/ }),

/***/ "@mui/utils/useTimeout":
/*!****************************************!*\
  !*** external "@mui/utils/useTimeout" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@mui/utils/useTimeout");

/***/ }),

/***/ "@tanstack/react-query":
/*!****************************************!*\
  !*** external "@tanstack/react-query" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query");;

/***/ }),

/***/ "@tanstack/react-query-devtools":
/*!*************************************************!*\
  !*** external "@tanstack/react-query-devtools" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = import("@tanstack/react-query-devtools");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("clsx");

/***/ }),

/***/ "next-i18next":
/*!*******************************!*\
  !*** external "next-i18next" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("next-i18next");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("react-hot-toast");;

/***/ }),

/***/ "react-transition-group":
/*!*****************************************!*\
  !*** external "react-transition-group" ***!
  \*****************************************/
/***/ ((module) => {

module.exports = require("react-transition-group");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui","vendor-chunks/@babel"], () => (__webpack_exec__("(pages-dir-node)/./pages/_app.tsx")));
module.exports = __webpack_exports__;

})();