{"c": ["webpack"], "r": ["pages/settings"], "m": ["(pages-dir-browser)/./node_modules/@mui/icons-material/esm/AccountCircle.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowBack.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ChevronRight.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Diamond.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Info.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Lock.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Palette.js", "(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Warning.js", "(pages-dir-browser)/./node_modules/@mui/material/Switch/Switch.js", "(pages-dir-browser)/./node_modules/@mui/material/Switch/index.js", "(pages-dir-browser)/./node_modules/@mui/material/Switch/switchClasses.js", "(pages-dir-browser)/./node_modules/@mui/material/internal/SwitchBase.js", "(pages-dir-browser)/./node_modules/@mui/material/internal/switchBaseClasses.js", "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=F%3A%5CCursor%5Cll.com%5Cinstall%5Cweb%5Cpages%5Csettings%5Cindex.tsx&page=%2Fsettings!", "(pages-dir-browser)/./pages/settings/index.tsx", "(pages-dir-browser)/__barrel_optimize__?names=AccountCircle,ArrowBack,ChevronRight,Diamond,Info,LocationOn,Lock,Notifications,Palette,Schedule,Security,Settings,Star,Verified,Warning!=!./node_modules/@mui/icons-material/esm/index.js", "(pages-dir-browser)/__barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,Container,Divider,Fade,FormControl,Grid,InputLabel,List,ListItem,ListItemIcon,ListItemSecondaryAction,ListItemText,MenuItem,Paper,Select,Switch,Typography,useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js"]}