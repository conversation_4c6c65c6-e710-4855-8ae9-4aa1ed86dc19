import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Privacy as PrivacyIcon,
  Language as LanguageIcon,
  Palette as ThemeIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  AccountCircle as AccountIcon,
  ChevronRight as ChevronRightIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Star as StarIcon,
  Diamond as DiamondIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { useSettings } from '../../src/contexts/SettingsContext';

const SettingsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { 
    settings, 
    loading, 
    error, 
    loadSettings,
    updatePreferences 
  } = useSettings();

  const [quickSettingsLoading, setQuickSettingsLoading] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
  }, [user, router]);

  const handleQuickToggle = async (setting: string, value: boolean) => {
    try {
      setQuickSettingsLoading(setting);
      
      const updates: any = {};
      
      switch (setting) {
        case 'theme':
          updates.theme = value ? 'dark' : 'light';
          break;
        case 'notifications':
          updates.notifications = { ...settings?.notifications, push: { ...settings?.notifications.push, enabled: value } };
          break;
        case 'location':
          updates.showLocation = value;
          break;
        case 'online':
          updates.showOnlineStatus = value;
          break;
      }
      
      if (setting === 'notifications') {
        // This would be handled by updateNotificationSettings
        // For now, just show success
        setSuccess('Настройки уведомлений обновлены');
      } else {
        await updatePreferences({ preferences: updates });
        setSuccess('Настройки обновлены');
      }
      
    } catch (err: any) {
      // Error handled by context
    } finally {
      setQuickSettingsLoading(null);
    }
  };

  const settingsMenuItems = [
    {
      id: 'account',
      title: 'Аккаунт',
      description: 'Профиль, верификация, подписка',
      icon: <AccountIcon color="primary" />,
      path: '/settings/account',
      badge: user?.isVerified ? 'Верифицирован' : null,
      badgeColor: 'success' as const
    },
    {
      id: 'privacy',
      title: 'Приватность',
      description: 'Видимость профиля, блокировки',
      icon: <PrivacyIcon color="primary" />,
      path: '/settings/privacy',
      badge: settings?.privacy.incognitoMode ? 'Инкогнито' : null,
      badgeColor: 'warning' as const
    },
    {
      id: 'notifications',
      title: 'Уведомления',
      description: 'Push, email, SMS уведомления',
      icon: <NotificationsIcon color="primary" />,
      path: '/settings/notifications',
      badge: settings?.notifications.push.enabled ? 'Включены' : 'Отключены',
      badgeColor: settings?.notifications.push.enabled ? 'success' as const : 'default' as const
    },
    {
      id: 'security',
      title: 'Безопасность',
      description: 'Пароль, 2FA, активные сессии',
      icon: <SecurityIcon color="primary" />,
      path: '/settings/security',
      badge: settings?.security.twoFactorAuth.enabled ? '2FA' : null,
      badgeColor: 'success' as const
    },
    {
      id: 'blocked',
      title: 'Заблокированные',
      description: 'Управление заблокированными пользователями',
      icon: <WarningIcon color="primary" />,
      path: '/settings/blocked',
      badge: settings?.security.blockedUsers.length ? `${settings.security.blockedUsers.length}` : null,
      badgeColor: 'error' as const
    },
    {
      id: 'data',
      title: 'Мои данные',
      description: 'Экспорт, удаление аккаунта',
      icon: <InfoIcon color="primary" />,
      path: '/settings/data',
      badge: null,
      badgeColor: 'default' as const
    }
  ];

  const quickSettings = [
    {
      id: 'theme',
      title: 'Темная тема',
      description: 'Переключить на темную тему',
      enabled: settings?.preferences.theme === 'dark',
      icon: <ThemeIcon />
    },
    {
      id: 'notifications',
      title: 'Push уведомления',
      description: 'Получать уведомления на устройство',
      enabled: settings?.notifications.push.enabled || false,
      icon: <NotificationsIcon />
    },
    {
      id: 'location',
      title: 'Показывать местоположение',
      description: 'Отображать ваше местоположение в профиле',
      enabled: settings?.privacy.showLocation || false,
      icon: <LocationIcon />
    },
    {
      id: 'online',
      title: 'Статус онлайн',
      description: 'Показывать когда вы в сети',
      enabled: settings?.privacy.showOnlineStatus || false,
      icon: <ScheduleIcon />
    }
  ];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Настройки - Likes & Love</title>
        <meta 
          name="description" 
          content="Настройки аккаунта в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <SettingsIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                Настройки
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                {success}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка настроек...
                </Typography>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* User Info Card */}
                  <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                      <Avatar
                        src={user.photos?.[0]?.url}
                        sx={{ width: 80, height: 80 }}
                      >
                        {user.firstName?.[0]}
                      </Avatar>
                      
                      <Box sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <Typography variant="h6">
                            {user.firstName} {user.lastName}
                          </Typography>
                          {user.isVerified && (
                            <VerifiedIcon color="primary" fontSize="small" />
                          )}
                          {user.subscription?.plan && (
                            <Chip
                              label={user.subscription.plan.displayName}
                              color="primary"
                              size="small"
                              icon={user.subscription.plan.name === 'vip' ? <DiamondIcon /> : <StarIcon />}
                            />
                          )}
                        </Box>
                        
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {user.email}
                        </Typography>
                        
                        <Typography variant="caption" color="text.secondary">
                          Аккаунт создан: {new Date(user.createdAt).toLocaleDateString('ru-RU')}
                        </Typography>
                      </Box>
                      
                      <Button
                        variant="outlined"
                        onClick={() => router.push('/profile/edit')}
                      >
                        Редактировать
                      </Button>
                    </Box>
                  </Paper>

                  {/* Quick Settings */}
                  <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Быстрые настройки
                    </Typography>

                    <List>
                      {quickSettings.map((setting, index) => (
                        <React.Fragment key={setting.id}>
                          <ListItem sx={{ px: 0 }}>
                            <ListItemIcon>
                              {setting.icon}
                            </ListItemIcon>
                            <ListItemText
                              primary={setting.title}
                              secondary={setting.description}
                            />
                            <ListItemSecondaryAction>
                              <Switch
                                checked={setting.enabled}
                                onChange={(e) => handleQuickToggle(setting.id, e.target.checked)}
                                disabled={quickSettingsLoading === setting.id}
                              />
                            </ListItemSecondaryAction>
                          </ListItem>
                          {index < quickSettings.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  </Paper>

                  {/* Settings Menu */}
                  <Paper elevation={2} sx={{ mb: 4 }}>
                    <List>
                      {settingsMenuItems.map((item, index) => (
                        <React.Fragment key={item.id}>
                          <ListItem
                            button
                            onClick={() => router.push(item.path)}
                            sx={{ py: 2 }}
                          >
                            <ListItemIcon>
                              {item.icon}
                            </ListItemIcon>
                            <ListItemText
                              primary={item.title}
                              secondary={item.description}
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {item.badge && (
                                <Chip
                                  label={item.badge}
                                  size="small"
                                  color={item.badgeColor}
                                  variant="outlined"
                                />
                              )}
                              <ChevronRightIcon color="action" />
                            </Box>
                          </ListItem>
                          {index < settingsMenuItems.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  </Paper>

                  {/* App Preferences */}
                  <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Предпочтения приложения
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Язык</InputLabel>
                          <Select
                            value={settings?.preferences.language || 'ru'}
                            label="Язык"
                            disabled
                          >
                            <MenuItem value="ru">Русский</MenuItem>
                            <MenuItem value="en">English</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Часовой пояс</InputLabel>
                          <Select
                            value={settings?.preferences.timezone || 'Europe/Moscow'}
                            label="Часовой пояс"
                            disabled
                          >
                            <MenuItem value="Europe/Moscow">Москва (UTC+3)</MenuItem>
                            <MenuItem value="Europe/Kiev">Киев (UTC+2)</MenuItem>
                            <MenuItem value="Asia/Almaty">Алматы (UTC+6)</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Формат даты</InputLabel>
                          <Select
                            value={settings?.preferences.dateFormat || 'DD/MM/YYYY'}
                            label="Формат даты"
                            disabled
                          >
                            <MenuItem value="DD/MM/YYYY">ДД/ММ/ГГГГ</MenuItem>
                            <MenuItem value="MM/DD/YYYY">ММ/ДД/ГГГГ</MenuItem>
                            <MenuItem value="YYYY-MM-DD">ГГГГ-ММ-ДД</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth size="small">
                          <InputLabel>Формат времени</InputLabel>
                          <Select
                            value={settings?.preferences.timeFormat || '24h'}
                            label="Формат времени"
                            disabled
                          >
                            <MenuItem value="24h">24 часа</MenuItem>
                            <MenuItem value="12h">12 часов</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>

                    <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                      Для изменения этих настроек перейдите в соответствующие разделы
                    </Typography>
                  </Paper>

                  {/* App Info */}
                  <Paper elevation={1} sx={{ p: 3, backgroundColor: 'background.default' }}>
                    <Typography variant="h6" gutterBottom>
                      О приложении
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Версия
                        </Typography>
                        <Typography variant="body2">
                          1.0.0
                        </Typography>
                      </Grid>

                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Сборка
                        </Typography>
                        <Typography variant="body2">
                          2024.01.15
                        </Typography>
                      </Grid>

                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => router.push('/help')}
                          >
                            Помощь
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => router.push('/about')}
                          >
                            О нас
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => router.push('/privacy-policy')}
                          >
                            Политика
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SettingsPage;
