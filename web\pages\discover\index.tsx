import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Favorite as LikeIcon,
  Close as PassIcon,
  Star as SuperLikeIcon,
  Refresh as RefreshIcon,
  Tune as FilterIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Verified as VerifiedIcon,
  Undo as UndoIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getDiscoverFeed,
  swipeUser,
  undoLastSwipe,
  getSuperLikesCount,
  refreshDiscoverFeed
} from '../../src/services/discoverService';
import { 
  DiscoverUser, 
  SwipeAction, 
  SwipeResult 
} from '../../src/types/discover.types';

const DiscoverPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [users, setUsers] = useState<DiscoverUser[]>([]);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [swiping, setSwiping] = useState(false);
  const [superLikesCount, setSuperLikesCount] = useState(0);
  const [matchDialog, setMatchDialog] = useState<{
    open: boolean;
    match?: SwipeResult['match'];
  }>({ open: false });
  const [superLikeDialog, setSuperLikeDialog] = useState(false);
  const [superLikeMessage, setSuperLikeMessage] = useState('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadUsers();
    loadSuperLikesCount();
  }, [user, router]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getDiscoverFeed();
      setUsers(response.data);
      setCurrentUserIndex(0);
    } catch (err: any) {
      setError('Ошибка загрузки пользователей');
    } finally {
      setLoading(false);
    }
  };

  const loadSuperLikesCount = async () => {
    try {
      const count = await getSuperLikesCount();
      setSuperLikesCount(count.available);
    } catch (err: any) {
      // Не показываем ошибку для счетчика
    }
  };

  const handleSwipe = async (action: 'like' | 'pass' | 'super_like', message?: string) => {
    const currentUser = users[currentUserIndex];
    if (!currentUser || swiping) return;

    try {
      setSwiping(true);
      setError(null);

      const swipeAction: SwipeAction = {
        userId: currentUser.id,
        action,
        timestamp: new Date().toISOString(),
        message
      };

      const result = await swipeUser(swipeAction);
      
      if (result.success) {
        if (result.isMatch && result.match) {
          setMatchDialog({ open: true, match: result.match });
        }
        
        if (action === 'super_like') {
          setSuperLikesCount(result.remainingSuperLikes || 0);
        }
        
        // Move to next user
        setCurrentUserIndex(prev => prev + 1);
        
        // Load more users if running low
        if (currentUserIndex >= users.length - 3) {
          loadMoreUsers();
        }
      } else {
        setError(result.error || 'Ошибка при свайпе');
      }
    } catch (err: any) {
      setError('Ошибка при свайпе');
    } finally {
      setSwiping(false);
    }
  };

  const loadMoreUsers = async () => {
    try {
      const response = await getDiscoverFeed();
      setUsers(prev => [...prev, ...response.data]);
    } catch (err: any) {
      // Не показываем ошибку для дозагрузки
    }
  };

  const handleUndo = async () => {
    try {
      const result = await undoLastSwipe();
      if (result.success) {
        setCurrentUserIndex(prev => Math.max(0, prev - 1));
        setSuccess(result.message);
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка отмены действия');
    }
  };

  const handleRefresh = async () => {
    try {
      setLoading(true);
      const newUsers = await refreshDiscoverFeed();
      setUsers(newUsers);
      setCurrentUserIndex(0);
    } catch (err: any) {
      setError('Ошибка обновления ленты');
    } finally {
      setLoading(false);
    }
  };

  const handleSuperLike = () => {
    if (superLikesCount <= 0) {
      setError('У вас закончились супер-лайки');
      return;
    }
    setSuperLikeDialog(true);
  };

  const handleSuperLikeConfirm = () => {
    handleSwipe('super_like', superLikeMessage.trim() || undefined);
    setSuperLikeDialog(false);
    setSuperLikeMessage('');
  };

  const currentUser = users[currentUserIndex];

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Знакомства - Likes & Love</title>
        <meta 
          name="description" 
          content="Найдите свою любовь в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="index, follow" />
        <link rel="canonical" href="https://likeslove.ru/discover" />
      </Head>
      
      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant={isMobile ? "h5" : "h4"}>
                Знакомства
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => router.push('/discover/filters')}>
                  <FilterIcon />
                </IconButton>
                <IconButton onClick={handleRefresh} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
                <IconButton onClick={handleUndo}>
                  <UndoIcon />
                </IconButton>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Super Likes Counter */}
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Chip
                icon={<SuperLikeIcon />}
                label={`${superLikesCount} супер-лайков`}
                color="warning"
                variant="outlined"
              />
            </Box>

            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка анкет...
                </Typography>
              </Box>
            ) : !currentUser ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <Typography variant="h6" gutterBottom>
                  Анкеты закончились
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Попробуйте изменить фильтры или зайдите позже
                </Typography>
                <Button
                  variant="contained"
                  onClick={handleRefresh}
                >
                  Обновить ленту
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Box>
                  {/* User Card */}
                  <Card sx={{ 
                    maxWidth: 400, 
                    mx: 'auto', 
                    mb: 3,
                    position: 'relative',
                    overflow: 'visible'
                  }}>
                    {/* Verification Badge */}
                    {currentUser.verificationStatus.phone && (
                      <Box sx={{
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        zIndex: 1,
                        backgroundColor: 'primary.main',
                        borderRadius: '50%',
                        p: 0.5
                      }}>
                        <VerifiedIcon sx={{ color: 'white', fontSize: 20 }} />
                      </Box>
                    )}

                    {/* Online Indicator */}
                    {currentUser.isOnline && (
                      <Box sx={{
                        position: 'absolute',
                        top: 16,
                        left: 16,
                        zIndex: 1,
                        backgroundColor: 'success.main',
                        color: 'white',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem'
                      }}>
                        Онлайн
                      </Box>
                    )}

                    <CardMedia
                      component="img"
                      height="500"
                      image={currentUser.photos[0]?.url || '/default-avatar.png'}
                      alt={currentUser.firstName}
                      sx={{ objectFit: 'cover' }}
                    />

                    <CardContent>
                      <Typography variant="h5" gutterBottom>
                        {currentUser.firstName}, {currentUser.age}
                      </Typography>

                      {currentUser.location && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {currentUser.location.city}
                            {currentUser.location.distance && ` • ${currentUser.location.distance} км`}
                          </Typography>
                        </Box>
                      )}

                      {currentUser.occupation && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <WorkIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {currentUser.occupation}
                          </Typography>
                        </Box>
                      )}

                      {currentUser.education && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {currentUser.education}
                          </Typography>
                        </Box>
                      )}

                      {currentUser.bio && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {currentUser.bio}
                        </Typography>
                      )}

                      {currentUser.interests.length > 0 && (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {currentUser.interests.slice(0, 5).map((interest, index) => (
                            <Chip
                              key={index}
                              label={interest}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                          {currentUser.interests.length > 5 && (
                            <Chip
                              label={`+${currentUser.interests.length - 5}`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      )}

                      {currentUser.compatibility && (
                        <Box sx={{ mt: 2 }}>
                          <Chip
                            label={`${currentUser.compatibility.score}% совместимость`}
                            color="primary"
                            size="small"
                          />
                        </Box>
                      )}
                    </CardContent>

                    {/* Action Buttons */}
                    <CardActions sx={{ justifyContent: 'center', pb: 3 }}>
                      <IconButton
                        size="large"
                        onClick={() => handleSwipe('pass')}
                        disabled={swiping}
                        sx={{
                          backgroundColor: 'grey.200',
                          '&:hover': { backgroundColor: 'grey.300' },
                          width: 56,
                          height: 56
                        }}
                      >
                        <PassIcon />
                      </IconButton>

                      <IconButton
                        size="large"
                        onClick={handleSuperLike}
                        disabled={swiping || superLikesCount <= 0}
                        sx={{
                          backgroundColor: 'warning.light',
                          color: 'warning.dark',
                          '&:hover': { backgroundColor: 'warning.main' },
                          width: 56,
                          height: 56,
                          mx: 2
                        }}
                      >
                        <SuperLikeIcon />
                      </IconButton>

                      <IconButton
                        size="large"
                        onClick={() => handleSwipe('like')}
                        disabled={swiping}
                        sx={{
                          backgroundColor: 'error.light',
                          color: 'error.dark',
                          '&:hover': { backgroundColor: 'error.main' },
                          width: 56,
                          height: 56
                        }}
                      >
                        <LikeIcon />
                      </IconButton>
                    </CardActions>
                  </Card>
                </Box>
              </Fade>
            )}
          </Box>
        </Container>

        {/* Match Dialog */}
        <Dialog
          open={matchDialog.open}
          onClose={() => setMatchDialog({ open: false })}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ textAlign: 'center' }}>
            🎉 Это совпадение!
          </DialogTitle>
          <DialogContent sx={{ textAlign: 'center' }}>
            <Typography variant="h6" gutterBottom>
              Вы понравились друг другу!
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Теперь вы можете начать общение
            </Typography>
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
            <Button onClick={() => setMatchDialog({ open: false })}>
              Продолжить просмотр
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                setMatchDialog({ open: false });
                router.push(`/matches/${matchDialog.match?.id}`);
              }}
            >
              Написать сообщение
            </Button>
          </DialogActions>
        </Dialog>

        {/* Super Like Dialog */}
        <Dialog
          open={superLikeDialog}
          onClose={() => setSuperLikeDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Отправить супер-лайк
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Добавьте сообщение к супер-лайку (необязательно)
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Напишите что-то интересное..."
              value={superLikeMessage}
              onChange={(e) => setSuperLikeMessage(e.target.value)}
              inputProps={{ maxLength: 200 }}
              helperText={`${superLikeMessage.length}/200 символов`}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSuperLikeDialog(false)}>
              Отмена
            </Button>
            <Button
              variant="contained"
              onClick={handleSuperLikeConfirm}
              startIcon={<SuperLikeIcon />}
            >
              Отправить супер-лайк
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default DiscoverPage;
