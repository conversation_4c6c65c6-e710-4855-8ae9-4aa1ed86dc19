import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Rating,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  Map as MapIcon,
  Share as ShareIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Verified as VerifiedIcon,
  Event as EventIcon,
  People as MeetingIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getFavoritePlaces,
  removeFromFavorites
} from '../../src/services/placesService';
import { Place } from '../../src/types/places.types';

const FavoritePlacesPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [places, setPlaces] = useState<Place[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'distance' | 'added'>('added');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadFavoritePlaces();
  }, [user, router]);

  const loadFavoritePlaces = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const favoritesData = await getFavoritePlaces();
      setPlaces(favoritesData.data);
    } catch (err: any) {
      setError('Ошибка загрузки избранных мест');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, place: Place) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedPlace(place);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPlace(null);
  };

  const handleRemoveFromFavorites = async (place: Place) => {
    if (!place) return;
    
    try {
      setActionLoading(place.id);
      setError(null);

      await removeFromFavorites(place.id);
      setSuccess('Место удалено из избранного');
      
      // Remove from local state
      setPlaces(places.filter(p => p.id !== place.id));
    } catch (err: any) {
      setError('Ошибка удаления из избранного');
    } finally {
      setActionLoading(null);
      handleMenuClose();
    }
  };

  const handleSort = (newSortBy: typeof sortBy) => {
    setSortBy(newSortBy);
    
    const sortedPlaces = [...places].sort((a, b) => {
      switch (newSortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'rating':
          return b.rating.average - a.rating.average;
        case 'distance':
          if (!a.distance || !b.distance) return 0;
          return a.distance.value - b.distance.value;
        case 'added':
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });
    
    setPlaces(sortedPlaces);
  };

  const formatPriceRange = (priceRange: Place['priceRange']) => {
    switch (priceRange) {
      case '$':
        return 'Бюджетно';
      case '$$':
        return 'Умеренно';
      case '$$$':
        return 'Дорого';
      case '$$$$':
        return 'Очень дорого';
      default:
        return priceRange;
    }
  };

  const formatDistance = (distance?: Place['distance']) => {
    if (!distance) return null;
    
    return `${distance.value.toFixed(1)} ${distance.unit === 'km' ? 'км' : 'миль'}`;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Избранные места - Likes & Love</title>
        <meta 
          name="description" 
          content="Ваши избранные места для встреч и свиданий в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                <FavoriteIcon sx={{ mr: 2, verticalAlign: 'middle', color: 'error.main' }} />
                Избранные места ({places.length})
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => router.push('/places/map?favorites=true')}>
                  <MapIcon />
                </IconButton>
                <IconButton onClick={() => router.push('/places?filters=true')}>
                  <FilterIcon />
                </IconButton>
                <IconButton onClick={loadFavoritePlaces} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Sort Controls */}
            {places.length > 0 && (
              <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <SortIcon sx={{ color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    Сортировать по:
                  </Typography>
                  <Button
                    variant={sortBy === 'added' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => handleSort('added')}
                  >
                    Дате добавления
                  </Button>
                  <Button
                    variant={sortBy === 'name' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => handleSort('name')}
                  >
                    Названию
                  </Button>
                  <Button
                    variant={sortBy === 'rating' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => handleSort('rating')}
                  >
                    Рейтингу
                  </Button>
                  <Button
                    variant={sortBy === 'distance' ? 'contained' : 'outlined'}
                    size="small"
                    onClick={() => handleSort('distance')}
                  >
                    Расстоянию
                  </Button>
                </Box>
              </Paper>
            )}

            {/* Content */}
            {loading ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <CircularProgress size={60} />
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Загрузка избранных мест...
                </Typography>
              </Box>
            ) : places.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <FavoriteIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  У вас пока нет избранных мест
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Добавляйте места в избранное, чтобы быстро находить их для встреч
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => router.push('/places')}
                >
                  Найти места
                </Button>
              </Box>
            ) : (
              <Fade in timeout={600}>
                <Grid container spacing={3}>
                  {places.map((place) => (
                    <Grid item xs={12} sm={6} md={4} key={place.id}>
                      <Card 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          flexDirection: 'column',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: theme.shadows[8]
                          },
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onClick={() => router.push(`/places/${place.id}`)}
                      >
                        <Box sx={{ position: 'relative' }}>
                          <CardMedia
                            component="img"
                            height="200"
                            image={place.photos[0]?.url || '/placeholder-place.jpg'}
                            alt={place.name}
                            sx={{ objectFit: 'cover' }}
                          />
                          
                          {/* Favorite Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveFromFavorites(place);
                              }}
                              disabled={actionLoading === place.id}
                              sx={{ 
                                backgroundColor: 'rgba(255,255,255,0.9)',
                                '&:hover': { backgroundColor: 'rgba(255,255,255,1)' }
                              }}
                            >
                              <FavoriteIcon color="error" />
                            </IconButton>
                          </Box>

                          {/* Menu Button */}
                          <Box sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            zIndex: 1
                          }}>
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, place)}
                              sx={{ 
                                backgroundColor: 'rgba(0,0,0,0.5)', 
                                color: 'white',
                                '&:hover': { backgroundColor: 'rgba(0,0,0,0.7)' }
                              }}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Box>

                          {/* Verification Badge */}
                          {place.verification.isVerified && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              left: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                icon={<VerifiedIcon />}
                                label="Проверено"
                                size="small"
                                color="primary"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}

                          {/* Distance Badge */}
                          {place.distance && (
                            <Box sx={{
                              position: 'absolute',
                              bottom: 8,
                              right: 8,
                              zIndex: 1
                            }}>
                              <Chip
                                label={formatDistance(place.distance)}
                                size="small"
                                sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                              />
                            </Box>
                          )}
                        </Box>

                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" component="h3" gutterBottom>
                            {place.name}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Rating
                              value={place.rating.average}
                              precision={0.1}
                              size="small"
                              readOnly
                            />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              {place.rating.average.toFixed(1)} ({place.rating.count})
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {place.location.district || place.location.city}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <AttachMoney fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {formatPriceRange(place.priceRange)}
                            </Typography>
                          </Box>

                          {place.shortDescription && (
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {place.shortDescription.length > 100 
                                ? `${place.shortDescription.substring(0, 100)}...`
                                : place.shortDescription
                              }
                            </Typography>
                          )}

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            <Chip
                              label={place.category.name}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            {place.tags.slice(0, 2).map((tag, index) => (
                              <Chip
                                key={index}
                                label={tag}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                            {place.tags.length > 2 && (
                              <Chip
                                label={`+${place.tags.length - 2}`}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </CardContent>

                        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              size="small"
                              startIcon={<MeetingIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/meetings/create?placeId=${place.id}`);
                              }}
                            >
                              Встреча
                            </Button>
                            <Button
                              size="small"
                              startIcon={<EventIcon />}
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/events/create?placeId=${place.id}`);
                              }}
                            >
                              Событие
                            </Button>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                            <Typography variant="caption" color="text.secondary">
                              {place.workingHours.isAlwaysOpen ? '24/7' : 'Работает'}
                            </Typography>
                          </Box>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Fade>
            )}
          </Box>
        </Container>

        {/* Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => {
            if (selectedPlace) {
              router.push(`/places/${selectedPlace.id}`);
            }
            handleMenuClose();
          }}>
            Подробности
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              router.push(`/meetings/create?placeId=${selectedPlace.id}`);
            }
            handleMenuClose();
          }}>
            Назначить встречу
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              router.push(`/events/create?placeId=${selectedPlace.id}`);
            }
            handleMenuClose();
          }}>
            Создать событие
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              router.push(`/places/map?placeId=${selectedPlace.id}`);
            }
            handleMenuClose();
          }}>
            Показать на карте
          </MenuItem>
          
          <MenuItem onClick={() => {
            if (selectedPlace) {
              // Share place logic
            }
            handleMenuClose();
          }}>
            <ShareIcon sx={{ mr: 1 }} />
            Поделиться
          </MenuItem>
          
          <MenuItem 
            onClick={() => {
              if (selectedPlace) {
                handleRemoveFromFavorites(selectedPlace);
              }
            }}
            sx={{ color: 'error.main' }}
          >
            Удалить из избранного
          </MenuItem>
        </Menu>
      </Layout>
    </>
  );
};

export default FavoritePlacesPage;
