import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Pagination,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  Block as BlockIcon,
  PersonAdd as UnblockIcon,
  Report as ReportIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getBlockedUsers, 
  blockUser, 
  unblockUser 
} from '../../src/services/profileService';
import { BlockedUser, BlockUserRequest, FilterOptions } from '../../src/types/profile.types';

const blockSchema = yup.object({
  reason: yup.string().oneOf(['spam', 'inappropriate', 'harassment', 'fake', 'other']).required('Выберите причину'),
  customReason: yup.string().when('reason', {
    is: 'other',
    then: (schema) => schema.required('Укажите причину').min(10, 'Минимум 10 символов'),
    otherwise: (schema) => schema.notRequired()
  }),
  reportToModerators: yup.boolean()
});

const BlockedUsersPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { blockUserId } = router.query;

  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [blockDialogOpen, setBlockDialogOpen] = useState(false);
  const [unblockDialogOpen, setUnblockDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<BlockedUser | null>(null);
  const [blocking, setBlocking] = useState(false);
  const [unblocking, setUnblocking] = useState(false);

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const limit = 20;

  const { control, handleSubmit, watch, reset, formState: { errors, isValid } } = useForm<BlockUserRequest>({
    resolver: yupResolver(blockSchema),
    defaultValues: {
      userId: '',
      reason: 'spam',
      customReason: '',
      reportToModerators: false
    },
    mode: 'onChange'
  });

  const watchedReason = watch('reason');

  const reasonOptions = [
    { value: 'spam', label: 'Спам или реклама', description: 'Пользователь отправляет спам или рекламу' },
    { value: 'inappropriate', label: 'Неподходящий контент', description: 'Неприемлемые фото или сообщения' },
    { value: 'harassment', label: 'Домогательства', description: 'Навязчивое поведение или домогательства' },
    { value: 'fake', label: 'Поддельный профиль', description: 'Подозрение на фейковый аккаунт' },
    { value: 'other', label: 'Другая причина', description: 'Укажите причину в поле ниже' }
  ];

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    loadBlockedUsers();

    // Если передан ID пользователя для блокировки
    if (blockUserId && typeof blockUserId === 'string') {
      handleBlockUser(blockUserId);
    }
  }, [user, router, page, blockUserId]);

  const loadBlockedUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {
        page,
        limit,
        sortBy: 'blockedAt',
        sortOrder: 'desc'
      };

      const response = await getBlockedUsers(filters);
      setBlockedUsers(response.data);
      setTotalPages(response.pagination.totalPages);
    } catch (err: any) {
      setError('Ошибка загрузки заблокированных пользователей');
    } finally {
      setLoading(false);
    }
  };

  const handleBlockUser = (userId: string) => {
    reset({
      userId,
      reason: 'spam',
      customReason: '',
      reportToModerators: false
    });
    setBlockDialogOpen(true);
  };

  const handleUnblockUser = (blockedUser: BlockedUser) => {
    setSelectedUser(blockedUser);
    setUnblockDialogOpen(true);
  };

  const onSubmitBlock = async (data: BlockUserRequest) => {
    try {
      setBlocking(true);
      setError(null);

      const result = await blockUser(data);
      
      if (result.success) {
        setSuccess('Пользователь заблокирован');
        setBlockDialogOpen(false);
        reset();
        loadBlockedUsers(); // Перезагружаем список
        
        // Убираем параметр из URL
        if (blockUserId) {
          router.replace('/profile/blocked', undefined, { shallow: true });
        }
      } else {
        setError(result.message);
      }
    } catch (err: any) {
      setError('Ошибка блокировки пользователя');
    } finally {
      setBlocking(false);
    }
  };

  const confirmUnblock = async () => {
    if (!selectedUser) return;

    try {
      setUnblocking(true);
      await unblockUser(selectedUser.userId);
      
      setSuccess('Пользователь разблокирован');
      setUnblockDialogOpen(false);
      setSelectedUser(null);
      
      // Удаляем из локального состояния
      setBlockedUsers(prev => prev.filter(u => u.id !== selectedUser.id));
    } catch (err: any) {
      setError('Ошибка разблокировки пользователя');
    } finally {
      setUnblocking(false);
    }
  };

  const formatBlockDate = (blockedAt: string) => {
    return new Date(blockedAt).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getReasonText = (reason: string) => {
    const option = reasonOptions.find(opt => opt.value === reason);
    return option ? option.label : reason;
  };

  if (!user) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Заблокированные пользователи - Likes & Love</title>
        <meta 
          name="description" 
          content="Управляйте списком заблокированных пользователей в приложении знакомств Likes & Love" 
        />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/profile/blocked" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            <Paper elevation={3} sx={{ p: { xs: 3, md: 4 } }}>
              {/* Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                  <Typography variant={isMobile ? "h5" : "h4"} gutterBottom>
                    <BlockIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
                    Заблокированные пользователи
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {blockedUsers.length > 0 ? `${blockedUsers.length} заблокированных пользователей` : 'Нет заблокированных пользователей'}
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<BlockIcon />}
                  onClick={() => handleBlockUser('')}
                  size={isMobile ? "small" : "medium"}
                >
                  Заблокировать пользователя
                </Button>
              </Box>

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {success && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  {success}
                </Alert>
              )}

              {loading ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CircularProgress size={60} />
                  <Typography variant="body1" sx={{ mt: 2 }}>
                    Загрузка заблокированных пользователей...
                  </Typography>
                </Box>
              ) : blockedUsers.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <BlockIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Нет заблокированных пользователей
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Вы можете заблокировать пользователей, которые нарушают правила или ведут себя неподобающе
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<BlockIcon />}
                    onClick={() => handleBlockUser('')}
                  >
                    Заблокировать пользователя
                  </Button>
                </Box>
              ) : (
                <Fade in timeout={600}>
                  <Box>
                    {/* Blocked users grid */}
                    <Grid container spacing={2}>
                      {blockedUsers.map((blockedUser) => (
                        <Grid item xs={12} sm={6} md={4} lg={3} key={blockedUser.id}>
                          <Card>
                            <CardContent>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Avatar
                                  src={blockedUser.user.avatarUrl}
                                  sx={{ width: 48, height: 48, mr: 2 }}
                                >
                                  {blockedUser.user.firstName[0]}
                                </Avatar>
                                <Box sx={{ flexGrow: 1 }}>
                                  <Typography variant="subtitle2" noWrap>
                                    {blockedUser.user.firstName} {blockedUser.user.lastName}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Заблокирован {formatBlockDate(blockedUser.blockedAt)}
                                  </Typography>
                                </Box>
                              </Box>

                              <Box sx={{ mb: 2 }}>
                                <Chip
                                  label={getReasonText(blockedUser.reason)}
                                  size="small"
                                  color="error"
                                  variant="outlined"
                                />
                              </Box>

                              {blockedUser.customReason && (
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                  "{blockedUser.customReason}"
                                </Typography>
                              )}
                            </CardContent>

                            <CardActions>
                              <Button
                                size="small"
                                startIcon={<UnblockIcon />}
                                onClick={() => handleUnblockUser(blockedUser)}
                                fullWidth
                              >
                                Разблокировать
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>
                      ))}
                    </Grid>

                    {/* Pagination */}
                    {totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                        <Pagination
                          count={totalPages}
                          page={page}
                          onChange={(_, newPage) => setPage(newPage)}
                          color="primary"
                          size={isMobile ? "small" : "medium"}
                        />
                      </Box>
                    )}
                  </Box>
                </Fade>
              )}

              {/* Info section */}
              <Alert severity="info" sx={{ mt: 4 }}>
                <Typography variant="body2">
                  ℹ️ <strong>Информация:</strong> Заблокированные пользователи не смогут видеть ваш профиль, 
                  отправлять вам сообщения или лайки. Вы также не будете видеть их в результатах поиска.
                </Typography>
              </Alert>
            </Paper>
          </Box>
        </Container>

        {/* Block user dialog */}
        <Dialog 
          open={blockDialogOpen} 
          onClose={() => setBlockDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <form onSubmit={handleSubmit(onSubmitBlock)}>
            <DialogTitle>Заблокировать пользователя</DialogTitle>
            <DialogContent>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Укажите причину блокировки. Эта информация поможет нам улучшить безопасность платформы.
              </Typography>

              {!blockUserId && (
                <Controller
                  name="userId"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="ID или имя пользователя"
                      placeholder="Введите ID пользователя"
                      error={!!errors.userId}
                      helperText={errors.userId?.message}
                      sx={{ mb: 3 }}
                    />
                  )}
                />
              )}

              <FormControl component="fieldset" sx={{ mb: 3 }}>
                <FormLabel component="legend">Причина блокировки</FormLabel>
                <Controller
                  name="reason"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field}>
                      {reasonOptions.map((option) => (
                        <FormControlLabel
                          key={option.value}
                          value={option.value}
                          control={<Radio />}
                          label={
                            <Box>
                              <Typography variant="body2">{option.label}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.description}
                              </Typography>
                            </Box>
                          }
                        />
                      ))}
                    </RadioGroup>
                  )}
                />
              </FormControl>

              {watchedReason === 'other' && (
                <Controller
                  name="customReason"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Опишите причину"
                      multiline
                      rows={3}
                      placeholder="Подробно опишите причину блокировки"
                      error={!!errors.customReason}
                      helperText={errors.customReason?.message}
                      sx={{ mb: 3 }}
                    />
                  )}
                />
              )}

              <Controller
                name="reportToModerators"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value} />}
                    label={
                      <Box>
                        <Typography variant="body2">Сообщить модераторам</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Отправить жалобу на этого пользователя модераторам для проверки
                        </Typography>
                      </Box>
                    }
                  />
                )}
              />

              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
            </DialogContent>
            <DialogActions>
              <Button 
                onClick={() => setBlockDialogOpen(false)}
                disabled={blocking}
              >
                Отмена
              </Button>
              <Button 
                type="submit"
                variant="contained"
                color="error"
                disabled={!isValid || blocking}
                startIcon={blocking ? <CircularProgress size={20} /> : <BlockIcon />}
              >
                {blocking ? 'Блокировка...' : 'Заблокировать'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Unblock confirmation dialog */}
        <Dialog open={unblockDialogOpen} onClose={() => setUnblockDialogOpen(false)}>
          <DialogTitle>Разблокировать пользователя?</DialogTitle>
          <DialogContent>
            <Typography>
              Вы уверены, что хотите разблокировать пользователя{' '}
              <strong>{selectedUser?.user.firstName} {selectedUser?.user.lastName}</strong>?
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              После разблокировки этот пользователь снова сможет видеть ваш профиль и отправлять сообщения.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUnblockDialogOpen(false)} disabled={unblocking}>
              Отмена
            </Button>
            <Button 
              onClick={confirmUnblock}
              variant="contained"
              disabled={unblocking}
              startIcon={unblocking ? <CircularProgress size={20} /> : <UnblockIcon />}
            >
              {unblocking ? 'Разблокировка...' : 'Разблокировать'}
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default BlockedUsersPage;
