import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Typography,
  Box,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade,
  IconButton,
  Breadcrumbs,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  ArrowBack,
  Computer as ComputerIcon,
  Smartphone as SmartphoneIcon,
  Tablet as TabletIcon,
  Logout as LogoutIcon,
  Warning as WarningIcon,
  Home as HomeIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  LocationOn as LocationIcon,
  Schedule as ScheduleIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import Layout from '../../../components/Layout/Layout';
import { useAuth } from '../../../src/providers/AuthProvider';

// Типы для сессий
interface LoginSession {
  id: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  deviceName: string;
  browser: string;
  operatingSystem: string;
  location: {
    city: string;
    country: string;
    ip: string;
  };
  lastActive: string;
  createdAt: string;
  isCurrent: boolean;
}

const SessionsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [sessions, setSessions] = useState<LoginSession[]>([]);
  const [terminateAllDialogOpen, setTerminateAllDialogOpen] = useState(false);
  const [terminateSessionId, setTerminateSessionId] = useState<string | null>(null);

  // Загрузка сессий при монтировании компонента
  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для получения активных сессий
      // const response = await getActiveSessions();
      
      // Мок данные
      const mockSessions: LoginSession[] = [
        {
          id: '1',
          deviceType: 'desktop',
          deviceName: 'MacBook Pro',
          browser: 'Chrome 120.0',
          operatingSystem: 'macOS 14.2',
          location: {
            city: 'Москва',
            country: 'Россия',
            ip: '*************'
          },
          lastActive: new Date().toISOString(),
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isCurrent: true
        },
        {
          id: '2',
          deviceType: 'mobile',
          deviceName: 'iPhone 15 Pro',
          browser: 'Safari Mobile',
          operatingSystem: 'iOS 17.2',
          location: {
            city: 'Москва',
            country: 'Россия',
            ip: '*************'
          },
          lastActive: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          isCurrent: false
        },
        {
          id: '3',
          deviceType: 'desktop',
          deviceName: 'Windows PC',
          browser: 'Firefox 121.0',
          operatingSystem: 'Windows 11',
          location: {
            city: 'Санкт-Петербург',
            country: 'Россия',
            ip: '***********'
          },
          lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          isCurrent: false
        }
      ];

      setSessions(mockSessions);
    } catch (err: any) {
      setError(err.message || 'Ошибка при загрузке сессий');
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для завершения сессии
      // await terminateSession(sessionId);

      setSessions(prev => prev.filter(session => session.id !== sessionId));
      setSuccess('Сессия успешно завершена');
      setTerminateSessionId(null);
    } catch (err: any) {
      setError(err.message || 'Ошибка при завершении сессии');
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateAllSessions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Здесь будет вызов API для завершения всех сессий кроме текущей
      // await terminateAllSessions();

      setSessions(prev => prev.filter(session => session.isCurrent));
      setSuccess('Все сессии успешно завершены');
      setTerminateAllDialogOpen(false);
    } catch (err: any) {
      setError(err.message || 'Ошибка при завершении сессий');
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <SmartphoneIcon />;
      case 'tablet':
        return <TabletIcon />;
      default:
        return <ComputerIcon />;
    }
  };

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Сейчас';
    if (diffInMinutes < 60) return `${diffInMinutes} мин назад`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ч назад`;
    return `${Math.floor(diffInMinutes / 1440)} дн назад`;
  };

  const handleBack = () => {
    router.push('/settings/security');
  };

  if (!user) {
    return (
      <Layout title="Активные сессии">
        <Container maxWidth="lg">
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Активные сессии - Likes & Love</title>
        <meta name="description" content="Управление активными сессиями и устройствами в приложении знакомств Likes & Love" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout title="Активные сессии">
        <Container maxWidth="lg">
          <Box sx={{ py: 3 }}>
            {/* Хлебные крошки */}
            <Breadcrumbs sx={{ mb: 3 }}>
              <Link 
                color="inherit" 
                href="/" 
                onClick={(e) => { e.preventDefault(); router.push('/'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <HomeIcon fontSize="small" />
                Главная
              </Link>
              <Link 
                color="inherit" 
                href="/settings" 
                onClick={(e) => { e.preventDefault(); router.push('/settings'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SettingsIcon fontSize="small" />
                Настройки
              </Link>
              <Link 
                color="inherit" 
                href="/settings/security" 
                onClick={(e) => { e.preventDefault(); router.push('/settings/security'); }}
                sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
              >
                <SecurityIcon fontSize="small" />
                Безопасность
              </Link>
              <Typography color="text.primary">Сессии</Typography>
            </Breadcrumbs>

            {/* Заголовок */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <IconButton 
                  onClick={handleBack}
                  sx={{ display: { xs: 'flex', md: 'none' } }}
                >
                  <ArrowBack />
                </IconButton>
                <ComputerIcon color="primary" sx={{ fontSize: 32 }} />
                <Typography variant="h4" component="h1" fontWeight="bold">
                  Активные сессии
                </Typography>
                <Chip 
                  label={`${sessions.length} ${sessions.length === 1 ? 'устройство' : 'устройств'}`} 
                  color="primary" 
                />
              </Box>
              <Typography variant="h6" color="text.secondary">
                Управляйте устройствами, с которых выполнен вход в ваш аккаунт
              </Typography>
            </Box>

            {/* Уведомления */}
            {error && (
              <Fade in={!!error}>
                <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
                  {error}
                </Alert>
              </Fade>
            )}

            {success && (
              <Fade in={!!success}>
                <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
                  {success}
                </Alert>
              </Fade>
            )}

            {/* Предупреждение */}
            <Alert severity="warning" sx={{ mb: 4 }}>
              <Typography variant="body2">
                <strong>Безопасность:</strong> Если вы видите незнакомые устройства или подозрительную активность, 
                немедленно завершите их сессии и смените пароль.
              </Typography>
            </Alert>

            {/* Действия */}
            <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
              <Button
                onClick={loadSessions}
                startIcon={<RefreshIcon />}
                disabled={loading}
              >
                Обновить
              </Button>
              <Button
                onClick={() => setTerminateAllDialogOpen(true)}
                startIcon={<LogoutIcon />}
                color="error"
                variant="outlined"
                disabled={loading || sessions.filter(s => !s.isCurrent).length === 0}
              >
                Завершить все сессии
              </Button>
            </Box>

            {/* Список сессий */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Card>
                <CardContent sx={{ p: 0 }}>
                  {isMobile ? (
                    // Мобильный вид - список
                    <List>
                      {sessions.map((session, index) => (
                        <React.Fragment key={session.id}>
                          <ListItem>
                            <ListItemIcon>
                              {getDeviceIcon(session.deviceType)}
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                                  <Typography variant="subtitle1" fontWeight="bold">
                                    {session.deviceName}
                                  </Typography>
                                  {session.isCurrent && (
                                    <Chip label="Текущая" size="small" color="primary" />
                                  )}
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="body2" color="text.secondary">
                                    {session.browser} • {session.operatingSystem}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    📍 {session.location.city}, {session.location.country}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    🕒 {formatLastActive(session.lastActive)}
                                  </Typography>
                                </Box>
                              }
                            />
                            {!session.isCurrent && (
                              <IconButton
                                onClick={() => setTerminateSessionId(session.id)}
                                color="error"
                                size="small"
                              >
                                <LogoutIcon />
                              </IconButton>
                            )}
                          </ListItem>
                          {index < sessions.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  ) : (
                    // Desktop вид - таблица
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Устройство</TableCell>
                            <TableCell>Браузер/ОС</TableCell>
                            <TableCell>Местоположение</TableCell>
                            <TableCell>Последняя активность</TableCell>
                            <TableCell>Действия</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {sessions.map((session) => (
                            <TableRow key={session.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                  {getDeviceIcon(session.deviceType)}
                                  <Box>
                                    <Typography variant="body1" fontWeight="bold">
                                      {session.deviceName}
                                    </Typography>
                                    {session.isCurrent && (
                                      <Chip label="Текущая сессия" size="small" color="primary" />
                                    )}
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {session.browser}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {session.operatingSystem}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <LocationIcon fontSize="small" color="action" />
                                  <Box>
                                    <Typography variant="body2">
                                      {session.location.city}, {session.location.country}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      IP: {session.location.ip}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <ScheduleIcon fontSize="small" color="action" />
                                  <Typography variant="body2">
                                    {formatLastActive(session.lastActive)}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                {!session.isCurrent && (
                                  <Button
                                    onClick={() => setTerminateSessionId(session.id)}
                                    color="error"
                                    size="small"
                                    startIcon={<LogoutIcon />}
                                  >
                                    Завершить
                                  </Button>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Диалог подтверждения завершения всех сессий */}
            <Dialog
              open={terminateAllDialogOpen}
              onClose={() => setTerminateAllDialogOpen(false)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <WarningIcon color="warning" />
                  Завершить все сессии
                </Box>
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  Вы уверены, что хотите завершить все активные сессии кроме текущей?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Это действие выйдет из аккаунта на всех других устройствах. 
                  Вам потребуется войти заново на этих устройствах.
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setTerminateAllDialogOpen(false)}>
                  Отмена
                </Button>
                <Button
                  onClick={handleTerminateAllSessions}
                  color="error"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <LogoutIcon />}
                >
                  {loading ? 'Завершение...' : 'Завершить все'}
                </Button>
              </DialogActions>
            </Dialog>

            {/* Диалог подтверждения завершения одной сессии */}
            <Dialog
              open={!!terminateSessionId}
              onClose={() => setTerminateSessionId(null)}
              maxWidth="sm"
              fullWidth
            >
              <DialogTitle>Завершить сессию</DialogTitle>
              <DialogContent>
                <Typography variant="body1">
                  Вы уверены, что хотите завершить эту сессию?
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setTerminateSessionId(null)}>
                  Отмена
                </Button>
                <Button
                  onClick={() => terminateSessionId && handleTerminateSession(terminateSessionId)}
                  color="error"
                  variant="contained"
                  disabled={loading}
                >
                  Завершить
                </Button>
              </DialogActions>
            </Dialog>
          </Box>
        </Container>
      </Layout>
    </>
  );
};

export default SessionsPage;
