import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Rating,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Divider,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Place as PlaceIcon,
  LocationOn as LocationIcon,
  Star as StarIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Share as ShareIcon,
  Edit as EditIcon,
  Report as ReportIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Wifi as WifiIcon,
  LocalParking as ParkingIcon,
  Accessible as AccessibleIcon,
  CheckIn as CheckInIcon,
  RateReview as ReviewIcon,
  Map as MapIcon,
  Verified as VerifiedIcon,
  Event as EventIcon,
  People as MeetingIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getPlace,
  addToFavorites,
  removeFromFavorites,
  createReview,
  checkInToPlace,
  reportPlace,
  getPlaceReviews
} from '../../src/services/placesService';
import { 
  Place,
  PlaceReview,
  CreateReviewRequest 
} from '../../src/types/places.types';

const PlaceDetailsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { id } = router.query;

  const [place, setPlace] = useState<Place | null>(null);
  const [reviews, setReviews] = useState<PlaceReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Dialog states
  const [reviewDialog, setReviewDialog] = useState(false);
  const [checkInDialog, setCheckInDialog] = useState(false);
  const [reportDialog, setReportDialog] = useState(false);
  
  // Form states
  const [reviewForm, setReviewForm] = useState<Partial<CreateReviewRequest>>({
    rating: 5,
    title: '',
    comment: '',
    aspects: {
      atmosphere: 5,
      service: 5,
      cleanliness: 5,
      value: 5,
      location: 5
    }
  });
  const [checkInNote, setCheckInNote] = useState('');
  const [reportReason, setReportReason] = useState('');
  const [reportDetails, setReportDetails] = useState('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    if (id && typeof id === 'string') {
      loadPlace(id);
    }
  }, [user, router, id]);

  const loadPlace = async (placeId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const [placeData, reviewsData] = await Promise.all([
        getPlace(placeId),
        getPlaceReviews(placeId, 1, 5)
      ]);
      
      setPlace(placeData);
      setReviews(reviewsData.data);
    } catch (err: any) {
      setError('Ошибка загрузки места');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFavorite = async () => {
    if (!place) return;
    
    try {
      setActionLoading('favorite');
      setError(null);

      if (place.userInteraction?.isFavorite) {
        await removeFromFavorites(place.id);
        setSuccess('Место удалено из избранного');
        setPlace({
          ...place,
          userInteraction: {
            ...place.userInteraction,
            isFavorite: false
          }
        });
      } else {
        await addToFavorites(place.id);
        setSuccess('Место добавлено в избранное');
        setPlace({
          ...place,
          userInteraction: {
            ...place.userInteraction,
            isFavorite: true
          }
        });
      }
    } catch (err: any) {
      setError('Ошибка обновления избранного');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCheckIn = async () => {
    if (!place) return;
    
    try {
      setActionLoading('checkin');
      setError(null);

      await checkInToPlace(place.id, checkInNote.trim() || undefined);
      setSuccess('Отметка о посещении добавлена');
      setCheckInDialog(false);
      setCheckInNote('');
      
      // Update place statistics
      setPlace({
        ...place,
        statistics: {
          ...place.statistics,
          checkins: place.statistics.checkins + 1
        }
      });
    } catch (err: any) {
      setError('Ошибка отметки о посещении');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCreateReview = async () => {
    if (!place || !reviewForm.rating || !reviewForm.comment) return;
    
    try {
      setActionLoading('review');
      setError(null);

      const reviewRequest: CreateReviewRequest = {
        placeId: place.id,
        rating: reviewForm.rating,
        title: reviewForm.title,
        comment: reviewForm.comment,
        aspects: reviewForm.aspects!
      };

      const newReview = await createReview(reviewRequest);
      setSuccess('Отзыв добавлен');
      setReviewDialog(false);
      setReviews([newReview, ...reviews]);
      
      // Reset form
      setReviewForm({
        rating: 5,
        title: '',
        comment: '',
        aspects: {
          atmosphere: 5,
          service: 5,
          cleanliness: 5,
          value: 5,
          location: 5
        }
      });
    } catch (err: any) {
      setError('Ошибка добавления отзыва');
    } finally {
      setActionLoading(null);
    }
  };

  const handleReportPlace = async () => {
    if (!place || !reportReason.trim()) return;
    
    try {
      setActionLoading('report');
      setError(null);

      await reportPlace(place.id, reportReason.trim(), reportDetails.trim() || undefined);
      setSuccess('Жалоба отправлена');
      setReportDialog(false);
      setReportReason('');
      setReportDetails('');
    } catch (err: any) {
      setError('Ошибка отправки жалобы');
    } finally {
      setActionLoading(null);
    }
  };

  const formatPriceRange = (priceRange: Place['priceRange']) => {
    switch (priceRange) {
      case '$':
        return 'Бюджетно';
      case '$$':
        return 'Умеренно';
      case '$$$':
        return 'Дорого';
      case '$$$$':
        return 'Очень дорого';
      default:
        return priceRange;
    }
  };

  const formatWorkingHours = (day: string) => {
    if (!place?.workingHours) return 'Не указано';
    
    if (place.workingHours.isAlwaysOpen) return '24/7';
    
    const dayHours = place.workingHours[day as keyof typeof place.workingHours];
    if (!dayHours || typeof dayHours !== 'object') return 'Не указано';
    
    if (dayHours.isClosed) return 'Закрыто';
    
    return `${dayHours.open} - ${dayHours.close}`;
  };

  const getCurrentDayHours = () => {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const today = days[new Date().getDay()];
    return formatWorkingHours(today);
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка места - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 }, textAlign: 'center' }}>
              <CircularProgress size={60} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Загрузка места...
              </Typography>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (error && !place) {
    return (
      <>
        <Head>
          <title>Ошибка загрузки места - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Alert severity="error">
                {error}
              </Alert>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (!place) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{`${place.name} - Места - Likes & Love`}</title>
        <meta 
          name="description" 
          content={place.shortDescription || place.description || `Место "${place.name}" в приложении знакомств Likes & Love`} 
        />
        <meta name="robots" content="index, follow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                {place.name}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton
                  onClick={handleToggleFavorite}
                  disabled={actionLoading === 'favorite'}
                  color={place.userInteraction?.isFavorite ? 'error' : 'default'}
                >
                  {place.userInteraction?.isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                </IconButton>
                <IconButton onClick={() => setReportDialog(true)}>
                  <ReportIcon />
                </IconButton>
                <IconButton>
                  <ShareIcon />
                </IconButton>
                <IconButton onClick={() => router.push(`/places/map?placeId=${place.id}`)}>
                  <MapIcon />
                </IconButton>
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Grid container spacing={4}>
              {/* Main Content */}
              <Grid item xs={12} md={8}>
                <Paper elevation={3} sx={{ overflow: 'hidden' }}>
                  {/* Place Images */}
                  {place.photos.length > 0 && (
                    <Box sx={{ position: 'relative' }}>
                      <img
                        src={place.photos[0].url}
                        alt={place.name}
                        style={{
                          width: '100%',
                          height: '400px',
                          objectFit: 'cover'
                        }}
                      />
                      
                      {/* Verification Badge */}
                      {place.verification.isVerified && (
                        <Box sx={{
                          position: 'absolute',
                          top: 16,
                          left: 16,
                          zIndex: 1
                        }}>
                          <Chip
                            icon={<VerifiedIcon />}
                            label="Проверено"
                            color="primary"
                            sx={{ backgroundColor: 'rgba(0,0,0,0.7)', color: 'white' }}
                          />
                        </Box>
                      )}
                    </Box>
                  )}

                  <Box sx={{ p: 4 }}>
                    {/* Rating and Category */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                      <Rating
                        value={place.rating.average}
                        precision={0.1}
                        readOnly
                      />
                      <Typography variant="body1">
                        {place.rating.average.toFixed(1)} ({place.rating.count} отзывов)
                      </Typography>
                      <Chip
                        label={place.category.name}
                        color="primary"
                        variant="outlined"
                      />
                      <Chip
                        label={formatPriceRange(place.priceRange)}
                        variant="outlined"
                      />
                    </Box>

                    {/* Description */}
                    <Typography variant="h6" gutterBottom>
                      Описание
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {place.description}
                    </Typography>

                    {/* Amenities */}
                    <Typography variant="h6" gutterBottom>
                      Удобства
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                      {place.hasWifi && (
                        <Chip icon={<WifiIcon />} label="Wi-Fi" variant="outlined" />
                      )}
                      {place.hasParking && (
                        <Chip icon={<ParkingIcon />} label="Парковка" variant="outlined" />
                      )}
                      {place.isAccessible && (
                        <Chip icon={<AccessibleIcon />} label="Доступно для инвалидов" variant="outlined" />
                      )}
                      {place.acceptsCards && (
                        <Chip label="Принимает карты" variant="outlined" />
                      )}
                      {place.acceptsCash && (
                        <Chip label="Принимает наличные" variant="outlined" />
                      )}
                      {place.isPetFriendly && (
                        <Chip label="Можно с питомцами" variant="outlined" />
                      )}
                    </Box>

                    {/* Tags */}
                    {place.tags.length > 0 && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Теги
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {place.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    )}

                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 4 }}>
                      <Button
                        variant="contained"
                        startIcon={<MeetingIcon />}
                        onClick={() => router.push(`/meetings/create?placeId=${place.id}`)}
                      >
                        Назначить встречу
                      </Button>
                      
                      <Button
                        variant="outlined"
                        startIcon={<EventIcon />}
                        onClick={() => router.push(`/events/create?placeId=${place.id}`)}
                      >
                        Создать событие
                      </Button>
                      
                      <Button
                        variant="outlined"
                        startIcon={<CheckInIcon />}
                        onClick={() => setCheckInDialog(true)}
                        disabled={actionLoading === 'checkin'}
                      >
                        Отметиться
                      </Button>
                      
                      <Button
                        variant="outlined"
                        startIcon={<ReviewIcon />}
                        onClick={() => setReviewDialog(true)}
                        disabled={place.userInteraction?.hasReviewed}
                      >
                        {place.userInteraction?.hasReviewed ? 'Отзыв оставлен' : 'Оставить отзыв'}
                      </Button>
                    </Box>
                  </Box>
                </Paper>

                {/* Reviews Section */}
                <Paper elevation={3} sx={{ mt: 3, p: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    Отзывы ({place.rating.count})
                  </Typography>
                  
                  {reviews.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" sx={{ py: 4, textAlign: 'center' }}>
                      Пока нет отзывов. Будьте первым!
                    </Typography>
                  ) : (
                    <List>
                      {reviews.map((review, index) => (
                        <React.Fragment key={review.id}>
                          <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                            <ListItemAvatar>
                              <Avatar src={review.authorAvatar}>
                                {review.authorName[0]}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                                  <Typography variant="subtitle1">
                                    {review.authorName}
                                  </Typography>
                                  <Rating value={review.rating} size="small" readOnly />
                                  <Typography variant="caption" color="text.secondary">
                                    {new Date(review.createdAt).toLocaleDateString('ru-RU')}
                                  </Typography>
                                </Box>
                              }
                              secondary={
                                <Box>
                                  {review.title && (
                                    <Typography variant="subtitle2" gutterBottom>
                                      {review.title}
                                    </Typography>
                                  )}
                                  <Typography variant="body2">
                                    {review.comment}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          {index < reviews.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  )}
                  
                  {place.rating.count > reviews.length && (
                    <Box sx={{ textAlign: 'center', mt: 2 }}>
                      <Button
                        variant="outlined"
                        onClick={() => router.push(`/places/${place.id}/reviews`)}
                      >
                        Показать все отзывы
                      </Button>
                    </Box>
                  )}
                </Paper>
              </Grid>

              {/* Sidebar */}
              <Grid item xs={12} md={4}>
                {/* Contact Info */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Контакты
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">
                        {place.location.address}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {place.location.city}
                      </Typography>
                    </Box>
                  </Box>

                  {place.contact.phone && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PhoneIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {place.contact.phone}
                      </Typography>
                    </Box>
                  )}

                  {place.contact.website && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <WebsiteIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <Typography
                        variant="body2"
                        component="a"
                        href={place.contact.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{ color: 'primary.main', textDecoration: 'none' }}
                      >
                        Сайт
                      </Typography>
                    </Box>
                  )}
                </Paper>

                {/* Working Hours */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Часы работы
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ScheduleIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">
                        Сегодня: {getCurrentDayHours()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {place.workingHours.isAlwaysOpen ? 'Работает круглосуточно' : 'Обычные часы работы'}
                      </Typography>
                    </Box>
                  </Box>
                </Paper>

                {/* Statistics */}
                <Paper elevation={3} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Статистика
                  </Typography>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Просмотры:
                    </Typography>
                    <Typography variant="body2">
                      {place.statistics.views}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      В избранном:
                    </Typography>
                    <Typography variant="body2">
                      {place.statistics.favorites}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Отметки:
                    </Typography>
                    <Typography variant="body2">
                      {place.statistics.checkins}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2" color="text.secondary">
                      Встречи:
                    </Typography>
                    <Typography variant="body2">
                      {place.statistics.meetingsHeld}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </Container>

        {/* Dialogs */}
        {/* Check-in Dialog */}
        <Dialog open={checkInDialog} onClose={() => setCheckInDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Отметиться в месте</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Заметка (необязательно)"
              value={checkInNote}
              onChange={(e) => setCheckInNote(e.target.value)}
              placeholder="Поделитесь впечатлениями..."
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCheckInDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              onClick={handleCheckIn}
              disabled={actionLoading === 'checkin'}
            >
              {actionLoading === 'checkin' ? 'Отмечаюсь...' : 'Отметиться'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Review Dialog */}
        <Dialog open={reviewDialog} onClose={() => setReviewDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>Оставить отзыв</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                Общая оценка:
              </Typography>
              <Rating
                value={reviewForm.rating}
                onChange={(_, value) => setReviewForm({ ...reviewForm, rating: value || 1 })}
                size="large"
                sx={{ mb: 3 }}
              />
              
              <TextField
                fullWidth
                label="Заголовок отзыва"
                value={reviewForm.title}
                onChange={(e) => setReviewForm({ ...reviewForm, title: e.target.value })}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Ваш отзыв"
                value={reviewForm.comment}
                onChange={(e) => setReviewForm({ ...reviewForm, comment: e.target.value })}
                placeholder="Поделитесь своими впечатлениями..."
                required
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReviewDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              onClick={handleCreateReview}
              disabled={!reviewForm.comment || actionLoading === 'review'}
            >
              {actionLoading === 'review' ? 'Отправка...' : 'Отправить отзыв'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Report Dialog */}
        <Dialog open={reportDialog} onClose={() => setReportDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Пожаловаться на место</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="Причина жалобы"
              value={reportReason}
              onChange={(e) => setReportReason(e.target.value)}
              sx={{ mt: 2, mb: 2 }}
              required
            />
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Подробности (необязательно)"
              value={reportDetails}
              onChange={(e) => setReportDetails(e.target.value)}
              placeholder="Опишите проблему подробнее..."
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReportDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              color="error"
              onClick={handleReportPlace}
              disabled={!reportReason.trim() || actionLoading === 'report'}
            >
              {actionLoading === 'report' ? 'Отправка...' : 'Отправить жалобу'}
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default PlaceDetailsPage;
