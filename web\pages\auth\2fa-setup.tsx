import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  Container, 
  Paper, 
  Button, 
  Typography, 
  Box, 
  Alert,
  CircularProgress,
  <PERSON>ack,
  <PERSON><PERSON>ield,
  Stepper,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Divider,
  Chip
} from '@mui/material';
import { 
  Security, 
  CheckCircle, 
  QrCode2, 
  Phone,
  Download,
  Verified
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import axios from 'axios';

interface TwoFASetupData {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

const TwoFASetupPage: React.FC = () => {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [setupData, setSetupData] = useState<TwoFASetupData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const steps = [
    'Установить приложение',
    'Сканировать QR-код',
    'Подтвердить настройку'
  ];

  const authenticatorApps = [
    { name: 'Google Authenticator', android: 'com.google.android.apps.authenticator2', ios: 'id388497605' },
    { name: 'Microsoft Authenticator', android: 'com.azure.authenticator', ios: 'id983156458' },
    { name: 'Authy', android: 'com.authy.authy', ios: 'id494168017' },
    { name: 'Яндекс.Ключ', android: 'ru.yandex.key', ios: 'id957324562' }
  ];

  useEffect(() => {
    if (user?.settings?.notifications && !user.settings?.notifications.email) {
      router.push('/settings/security');
      return;
    }
    
    initializeTwoFA();
  }, []);

  const initializeTwoFA = async () => {
    try {
      setLoading(true);
      const response = await axios.post('/api/auth/2fa/setup');
      setSetupData(response.data);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Ошибка инициализации 2FA');
    } finally {
      setLoading(false);
    }
  };

  const verifyAndEnable2FA = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Введите 6-значный код');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const response = await axios.post('/api/auth/2fa/verify', {
        secret: setupData?.secret,
        code: verificationCode
      });

      if (response.data.success) {
        setSuccess(true);
        
        // Обновляем пользователя
        if (user) {
          updateUser({
            ...user,
            // Добавляем информацию о включенной 2FA
          });
        }

        setTimeout(() => {
          router.push('/settings/security');
        }, 3000);
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Неверный код подтверждения');
    } finally {
      setLoading(false);
    }
  };

  const downloadBackupCodes = () => {
    if (!setupData?.backupCodes) return;

    const codesText = setupData.backupCodes.join('\n');
    const blob = new Blob([codesText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  if (loading && !setupData) {
    return (
      <Layout>
        <Container maxWidth="md">
          <Box sx={{ mt: 8, display: 'flex', justifyContent: 'center' }}>
            <CircularProgress />
          </Box>
        </Container>
      </Layout>
    );
  }

  if (success) {
    return (
      <Layout>
        <Container maxWidth="sm">
          <Box sx={{ mt: 8, mb: 4 }}>
            <Paper elevation={3} sx={{ p: 4 }}>
              <Stack spacing={3} alignItems="center">
                <CheckCircle color="success" sx={{ fontSize: 80 }} />
                <Typography variant="h5" color="success.main">
                  2FA успешно настроена!
                </Typography>
                <Typography variant="body1" textAlign="center">
                  Двухфакторная аутентификация активирована для вашего аккаунта. 
                  Теперь ваш аккаунт защищен дополнительным уровнем безопасности.
                </Typography>
                <Button
                  variant="contained"
                  onClick={() => router.push('/settings/security')}
                >
                  Перейти к настройкам безопасности
                </Button>
              </Stack>
            </Paper>
          </Box>
        </Container>
      </Layout>
    );
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Stack spacing={3}>
            <Typography variant="h6">
              Установите приложение-аутентификатор
            </Typography>
            <Typography variant="body2">
              Для настройки двухфакторной аутентификации вам потребуется установить 
              одно из приложений-аутентификаторов на ваш смартфон:
            </Typography>
            <Stack spacing={2}>
              {authenticatorApps.map((app) => (
                <Box 
                  key={app.name}
                  sx={{ 
                    p: 2, 
                    border: 1, 
                    borderColor: 'divider', 
                    borderRadius: 1,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <Typography variant="body1">{app.name}</Typography>
                  <Stack direction="row" spacing={1}>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => window.open(`https://play.google.com/store/apps/details?id=${app.android}`, '_blank')}
                    >
                      Android
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => window.open(`https://apps.apple.com/app/${app.ios}`, '_blank')}
                    >
                      iOS
                    </Button>
                  </Stack>
                </Box>
              ))}
            </Stack>
          </Stack>
        );

      case 1:
        return (
          <Stack spacing={3} alignItems="center">
            <Typography variant="h6">
              Сканируйте QR-код
            </Typography>
            <Typography variant="body2" textAlign="center">
              Откройте приложение-аутентификатор и отсканируйте этот QR-код:
            </Typography>
            
            {setupData?.qrCode ? (
              <Box sx={{ p: 2, bgcolor: 'white', borderRadius: 2 }}>
                <img 
                  src={setupData.qrCode} 
                  alt="QR Code for 2FA setup" 
                  style={{ width: 200, height: 200 }}
                />
              </Box>
            ) : (
              <CircularProgress />
            )}

            <Divider sx={{ width: '100%' }}>или</Divider>

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" gutterBottom>
                Введите код вручную:
              </Typography>
              <Chip 
                label={setupData?.secret}
                variant="outlined" 
                sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}
              />
            </Box>
          </Stack>
        );

      case 2:
        return (
          <Stack spacing={3}>
            <Typography variant="h6" textAlign="center">
              Подтвердите настройку
            </Typography>
            <Typography variant="body2" textAlign="center">
              Введите 6-значный код из вашего приложения-аутентификатора:
            </Typography>
            
            <TextField
              label="Код подтверждения"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              variant="outlined"
              fullWidth
              inputProps={{
                maxLength: 6,
                style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
              }}
              placeholder="000000"
            />

            {setupData?.backupCodes && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Резервные коды
                </Typography>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Сохраните эти резервные коды в безопасном месте. 
                  Они помогут восстановить доступ к аккаунту, если вы потеряете устройство.
                </Alert>
                <Box sx={{ 
                  p: 2, 
                  bgcolor: 'grey.100', 
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.9rem'
                }}>
                  {setupData.backupCodes.map((code, index) => (
                    <Typography key={index} sx={{ fontFamily: 'inherit' }}>
                      {code}
                    </Typography>
                  ))}
                </Box>
                <Button
                  startIcon={<Download />}
                  onClick={downloadBackupCodes}
                  size="small"
                  sx={{ mt: 1 }}
                >
                  Скачать коды
                </Button>
              </Box>
            )}
          </Stack>
        );

      default:
        return null;
    }
  };

  return (
    <Layout>
      <Container maxWidth="md">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={3} sx={{ p: 4 }}>
            <Stack spacing={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Security color="primary" sx={{ fontSize: 60, mb: 2 }} />
                <Typography variant="h4" gutterBottom>
                  Настройка двухфакторной аутентификации
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Добавьте дополнительный уровень защиты для вашего аккаунта
                </Typography>
              </Box>

              {error && (
                <Alert severity="error">
                  {error}
                </Alert>
              )}

              <Stepper activeStep={activeStep} alternativeLabel>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              <Box sx={{ minHeight: 400 }}>
                {renderStepContent()}
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                >
                  Назад
                </Button>

                <Box>
                  {activeStep === steps.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={verifyAndEnable2FA}
                      disabled={loading || !verificationCode}
                      startIcon={loading ? <CircularProgress size={20} /> : <Verified />}
                    >
                      {loading ? 'Проверка...' : 'Включить 2FA'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={handleNext}
                    >
                      Далее
                    </Button>
                  )}
                </Box>
              </Box>

              <Box sx={{ textAlign: 'center' }}>
                <Button
                  variant="text"
                  onClick={() => router.push('/settings/security')}
                  size="small"
                >
                  Отменить настройку
                </Button>
              </Box>
            </Stack>
          </Paper>
        </Box>
      </Container>
    </Layout>
  );
};

export default TwoFASetupPage;
