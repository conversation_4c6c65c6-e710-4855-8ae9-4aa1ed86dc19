import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Avatar,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  useTheme,
  useMediaQuery,
  Fade
} from '@mui/material';
import {
  ArrowBack,
  Event as EventIcon,
  LocationOn as LocationIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  Share as ShareIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PersonAdd as PersonAddIcon,
  ExitToApp as LeaveIcon,
  Chat as ChatIcon,
  Star as StarIcon,
  Report as ReportIcon,
  Verified as VerifiedIcon,
  Phone as PhoneIcon,
  Message as MessageIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { useAuth } from '../../src/providers/AuthProvider';
import { 
  getMeeting,
  joinMeeting,
  leaveMeeting,
  deleteMeeting,
  inviteToMeeting,
  rateMeeting,
  reportMeeting
} from '../../src/services/meetingsService';
import { Meeting } from '../../src/types/meetings.types';

const MeetingDetailsPage: React.FC = () => {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { id } = router.query;

  const [meeting, setMeeting] = useState<Meeting | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  
  // Dialog states
  const [inviteDialog, setInviteDialog] = useState(false);
  const [ratingDialog, setRatingDialog] = useState(false);
  const [reportDialog, setReportDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  
  // Form states
  const [inviteMessage, setInviteMessage] = useState('');
  const [rating, setRating] = useState(5);
  const [ratingComment, setRatingComment] = useState('');
  const [reportReason, setReportReason] = useState('');
  const [reportDetails, setReportDetails] = useState('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }
    if (id && typeof id === 'string') {
      loadMeeting(id);
    }
  }, [user, router, id]);

  const loadMeeting = async (meetingId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const meetingData = await getMeeting(meetingId);
      setMeeting(meetingData);
    } catch (err: any) {
      setError('Ошибка загрузки встречи');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinMeeting = async () => {
    if (!meeting) return;
    
    try {
      setActionLoading('join');
      setError(null);

      await joinMeeting(meeting.id);
      setSuccess('Вы присоединились к встрече');
      loadMeeting(meeting.id); // Reload meeting data
    } catch (err: any) {
      setError('Ошибка присоединения к встрече');
    } finally {
      setActionLoading(null);
    }
  };

  const handleLeaveMeeting = async () => {
    if (!meeting) return;
    
    try {
      setActionLoading('leave');
      setError(null);

      await leaveMeeting(meeting.id);
      setSuccess('Вы покинули встречу');
      loadMeeting(meeting.id); // Reload meeting data
    } catch (err: any) {
      setError('Ошибка выхода из встречи');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteMeeting = async () => {
    if (!meeting) return;
    
    try {
      setActionLoading('delete');
      setError(null);

      await deleteMeeting(meeting.id);
      setSuccess('Встреча удалена');
      router.push('/meetings');
    } catch (err: any) {
      setError('Ошибка удаления встречи');
    } finally {
      setActionLoading(null);
      setDeleteDialog(false);
    }
  };

  const handleInviteUsers = async () => {
    if (!meeting) return;
    
    try {
      setActionLoading('invite');
      setError(null);

      // This would typically involve selecting users from a list
      // For now, we'll just close the dialog
      setSuccess('Приглашения отправлены');
      setInviteDialog(false);
      setInviteMessage('');
    } catch (err: any) {
      setError('Ошибка отправки приглашений');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRateMeeting = async () => {
    if (!meeting) return;
    
    try {
      setActionLoading('rate');
      setError(null);

      await rateMeeting(meeting.id, rating, ratingComment.trim() || undefined);
      setSuccess('Отзыв отправлен');
      setRatingDialog(false);
      setRating(5);
      setRatingComment('');
    } catch (err: any) {
      setError('Ошибка отправки отзыва');
    } finally {
      setActionLoading(null);
    }
  };

  const handleReportMeeting = async () => {
    if (!meeting || !reportReason.trim()) return;
    
    try {
      setActionLoading('report');
      setError(null);

      await reportMeeting(meeting.id, reportReason.trim(), reportDetails.trim() || undefined);
      setSuccess('Жалоба отправлена');
      setReportDialog(false);
      setReportReason('');
      setReportDetails('');
    } catch (err: any) {
      setError('Ошибка отправки жалобы');
    } finally {
      setActionLoading(null);
    }
  };

  const formatMeetingDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatMeetingTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMeetingTypeLabel = (type: Meeting['type']) => {
    switch (type) {
      case 'date':
        return 'Свидание';
      case 'group_meeting':
        return 'Групповая встреча';
      case 'activity':
        return 'Активность';
      case 'casual':
        return 'Неформальная';
      case 'business':
        return 'Деловая';
      default:
        return type;
    }
  };

  const isUserParticipant = () => {
    return meeting?.participants.some(p => p.id === user?.id);
  };

  const isUserOrganizer = () => {
    return meeting?.organizer.id === user?.id;
  };

  const canJoinMeeting = () => {
    if (!meeting) return false;
    if (isUserParticipant() || isUserOrganizer()) return false;
    if (meeting.maxParticipants && meeting.participants.length >= meeting.maxParticipants) return false;
    return meeting.status === 'scheduled';
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <>
        <Head>
          <title>Загрузка встречи - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 }, textAlign: 'center' }}>
              <CircularProgress size={60} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Загрузка встречи...
              </Typography>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (error && !meeting) {
    return (
      <>
        <Head>
          <title>Ошибка загрузки встречи - Likes & Love</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        
        <Layout>
          <Container maxWidth="lg">
            <Box sx={{ py: { xs: 2, md: 4 } }}>
              <Alert severity="error">
                {error}
              </Alert>
            </Box>
          </Container>
        </Layout>
      </>
    );
  }

  if (!meeting) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{`${meeting.title} - Встречи - Likes & Love`}</title>
        <meta 
          name="description" 
          content={meeting.description || `Встреча "${meeting.title}" в приложении знакомств Likes & Love`} 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <Layout>
        <Container maxWidth="lg">
          <Box sx={{ py: { xs: 2, md: 4 } }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
              <Button
                startIcon={<ArrowBack />}
                onClick={() => router.back()}
                sx={{ mr: 2 }}
              >
                Назад
              </Button>
              <Typography variant={isMobile ? "h5" : "h4"} sx={{ flexGrow: 1 }}>
                {meeting.title}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <IconButton onClick={() => setReportDialog(true)}>
                  <ReportIcon />
                </IconButton>
                <IconButton>
                  <ShareIcon />
                </IconButton>
                {isUserOrganizer() && (
                  <>
                    <IconButton onClick={() => router.push(`/meetings/${meeting.id}/edit`)}>
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => setDeleteDialog(true)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </>
                )}
              </Box>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Grid container spacing={4}>
              {/* Main Content */}
              <Grid item xs={12} md={8}>
                <Paper elevation={3} sx={{ p: 4 }}>
                  {/* Meeting Images */}
                  {meeting.photos.length > 0 && (
                    <Box sx={{ mb: 4 }}>
                      <img
                        src={meeting.photos[0]}
                        alt={meeting.title}
                        style={{
                          width: '100%',
                          height: '300px',
                          objectFit: 'cover',
                          borderRadius: theme.shape.borderRadius
                        }}
                      />
                    </Box>
                  )}

                  {/* Meeting Info */}
                  <Box sx={{ mb: 4 }}>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
                      <Chip
                        label={getMeetingTypeLabel(meeting.type)}
                        color="primary"
                      />
                      <Chip
                        label={meeting.category}
                        variant="outlined"
                      />
                      {meeting.privacy === 'private' && (
                        <Chip
                          label="Приватная"
                          color="warning"
                          variant="outlined"
                        />
                      )}
                    </Box>

                    <Typography variant="h6" gutterBottom>
                      Описание
                    </Typography>
                    <Typography variant="body1" paragraph>
                      {meeting.description || 'Описание не указано'}
                    </Typography>

                    {meeting.tags.length > 0 && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Теги
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {meeting.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    )}
                  </Box>

                  {/* Action Buttons */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 4 }}>
                    {canJoinMeeting() && (
                      <Button
                        variant="contained"
                        size="large"
                        onClick={handleJoinMeeting}
                        disabled={actionLoading === 'join'}
                        startIcon={actionLoading === 'join' ? <CircularProgress size={20} /> : <PersonAddIcon />}
                      >
                        Присоединиться
                      </Button>
                    )}

                    {isUserParticipant() && !isUserOrganizer() && (
                      <Button
                        variant="outlined"
                        color="error"
                        onClick={handleLeaveMeeting}
                        disabled={actionLoading === 'leave'}
                        startIcon={actionLoading === 'leave' ? <CircularProgress size={20} /> : <LeaveIcon />}
                      >
                        Покинуть встречу
                      </Button>
                    )}

                    {isUserOrganizer() && (
                      <Button
                        variant="outlined"
                        onClick={() => setInviteDialog(true)}
                        startIcon={<PersonAddIcon />}
                      >
                        Пригласить
                      </Button>
                    )}

                    {meeting.chat?.enabled && (isUserParticipant() || isUserOrganizer()) && (
                      <Button
                        variant="outlined"
                        onClick={() => router.push(`/meetings/${meeting.id}/chat`)}
                        startIcon={<ChatIcon />}
                      >
                        Чат встречи
                      </Button>
                    )}

                    {meeting.status === 'completed' && (isUserParticipant() || isUserOrganizer()) && (
                      <Button
                        variant="outlined"
                        onClick={() => setRatingDialog(true)}
                        startIcon={<StarIcon />}
                      >
                        Оценить встречу
                      </Button>
                    )}

                    <Button
                      variant="outlined"
                      startIcon={<CalendarIcon />}
                    >
                      Добавить в календарь
                    </Button>
                  </Box>
                </Paper>
              </Grid>

              {/* Sidebar */}
              <Grid item xs={12} md={4}>
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Детали встречи
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <ScheduleIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">
                        {formatMeetingDate(meeting.scheduledAt)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {formatMeetingTime(meeting.scheduledAt)} ({meeting.duration} мин.)
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2">
                        {meeting.location.name}
                      </Typography>
                      {meeting.location.address && (
                        <Typography variant="body2" color="text.secondary">
                          {meeting.location.address}
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PeopleIcon sx={{ mr: 2, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      {meeting.participants.length}
                      {meeting.maxParticipants && ` / ${meeting.maxParticipants}`} участников
                    </Typography>
                  </Box>

                  {meeting.cost && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        Стоимость:
                      </Typography>
                      <Typography variant="body2">
                        {meeting.cost.type === 'free' ? 'Бесплатно' : 
                         `${meeting.cost.amount} ${meeting.cost.currency}`}
                      </Typography>
                    </Box>
                  )}
                </Paper>

                {/* Organizer */}
                <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Организатор
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={meeting.organizer.avatarUrl}
                      sx={{ width: 48, height: 48, mr: 2 }}
                    >
                      {meeting.organizer.firstName[0]}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {meeting.organizer.firstName} {meeting.organizer.lastName}
                        </Typography>
                        {meeting.organizer.verificationStatus.phone && (
                          <VerifiedIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                        )}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {meeting.organizer.isOnline ? 'Онлайн' : 'Не в сети'}
                      </Typography>
                    </Box>
                  </Box>
                  
                  {!isUserOrganizer() && (
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<MessageIcon />}
                        onClick={() => router.push(`/chat/new?userId=${meeting.organizer.id}`)}
                      >
                        Написать
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<PhoneIcon />}
                      >
                        Позвонить
                      </Button>
                    </Box>
                  )}
                </Paper>

                {/* Participants */}
                <Paper elevation={3} sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Участники ({meeting.participants.length})
                  </Typography>
                  <List dense>
                    {meeting.participants.slice(0, 5).map((participant) => (
                      <ListItem key={participant.id} sx={{ px: 0 }}>
                        <ListItemAvatar>
                          <Avatar
                            src={participant.avatarUrl}
                            sx={{ width: 32, height: 32 }}
                          >
                            {participant.firstName[0]}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2">
                                {participant.firstName}
                              </Typography>
                              {participant.verificationStatus.phone && (
                                <VerifiedIcon sx={{ fontSize: 12, color: 'primary.main' }} />
                              )}
                            </Box>
                          }
                          secondary={
                            <Chip
                              label={participant.status === 'confirmed' ? 'Подтвержден' : 'Ожидает'}
                              size="small"
                              color={participant.status === 'confirmed' ? 'success' : 'default'}
                              variant="outlined"
                            />
                          }
                        />
                      </ListItem>
                    ))}
                    {meeting.participants.length > 5 && (
                      <ListItem sx={{ px: 0 }}>
                        <ListItemText
                          primary={
                            <Typography variant="body2" color="text.secondary">
                              И еще {meeting.participants.length - 5} участников...
                            </Typography>
                          }
                        />
                      </ListItem>
                    )}
                  </List>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        </Container>

        {/* Dialogs */}
        {/* Invite Dialog */}
        <Dialog open={inviteDialog} onClose={() => setInviteDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Пригласить участников</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Сообщение приглашения"
              value={inviteMessage}
              onChange={(e) => setInviteMessage(e.target.value)}
              placeholder="Добавьте персональное сообщение к приглашению..."
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setInviteDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              onClick={handleInviteUsers}
              disabled={actionLoading === 'invite'}
            >
              {actionLoading === 'invite' ? 'Отправка...' : 'Отправить приглашения'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Rating Dialog */}
        <Dialog open={ratingDialog} onClose={() => setRatingDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Оценить встречу</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                Оценка (1-5 звезд):
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <IconButton
                    key={star}
                    onClick={() => setRating(star)}
                    color={star <= rating ? 'primary' : 'default'}
                  >
                    <StarIcon />
                  </IconButton>
                ))}
              </Box>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Комментарий (необязательно)"
                value={ratingComment}
                onChange={(e) => setRatingComment(e.target.value)}
                placeholder="Поделитесь впечатлениями о встрече..."
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setRatingDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              onClick={handleRateMeeting}
              disabled={actionLoading === 'rate'}
            >
              {actionLoading === 'rate' ? 'Отправка...' : 'Отправить отзыв'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Report Dialog */}
        <Dialog open={reportDialog} onClose={() => setReportDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Пожаловаться на встречу</DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="Причина жалобы"
              value={reportReason}
              onChange={(e) => setReportReason(e.target.value)}
              sx={{ mt: 2, mb: 2 }}
              required
            />
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Подробности (необязательно)"
              value={reportDetails}
              onChange={(e) => setReportDetails(e.target.value)}
              placeholder="Опишите проблему подробнее..."
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReportDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              color="error"
              onClick={handleReportMeeting}
              disabled={!reportReason.trim() || actionLoading === 'report'}
            >
              {actionLoading === 'report' ? 'Отправка...' : 'Отправить жалобу'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Dialog */}
        <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
          <DialogTitle>Удалить встречу</DialogTitle>
          <DialogContent>
            <Typography>
              Вы уверены, что хотите удалить эту встречу? Это действие нельзя отменить.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialog(false)}>Отмена</Button>
            <Button 
              variant="contained" 
              color="error"
              onClick={handleDeleteMeeting}
              disabled={actionLoading === 'delete'}
            >
              {actionLoading === 'delete' ? 'Удаление...' : 'Удалить'}
            </Button>
          </DialogActions>
        </Dialog>
      </Layout>
    </>
  );
};

export default MeetingDetailsPage;
